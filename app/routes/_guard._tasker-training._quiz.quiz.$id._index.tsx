import { json } from '@remix-run/node';
import {
  isRouteErrorResponse,
  useNavigate,
  useRouteError,
} from '@remix-run/react';
import { PERMISSIONS, ROUTE_NAME } from 'btaskee-constants';
import { useGlobalStore, useLoaderDataSafely } from 'btaskee-hooks';
import {
  AspectRatio,
  Breadcrumbs,
  BtaskeeResponseError,
  Button,
  Card,
  CardContent,
  CardHeader,
  Grid,
  Switch,
  Typography,
  toast,
} from 'btaskee-ui';
import { momentTz } from 'btaskee-utils';
import { useTranslation } from 'react-i18next';
import { BlockDescription } from '~/components/tasker-common';
import { hoc404, hocLoader } from '~/hoc/remix';
import { getUserSession } from '~/services/helpers.server';
import { getQuizDetail } from '~/services/tasker-training.server';

export function ErrorBoundary() {
  const error = useRouteError() as { message?: string };
  const { t: tCommon } = useTranslation('common');

  if (isRouteErrorResponse(error)) {
    if (error?.message) {
      toast({ description: JSON.stringify(error.message) });
    }

    return <BtaskeeResponseError t={tCommon} errorStatus={error.status} />;
  }

  return <BtaskeeResponseError t={tCommon} errorStatus={500} />;
}
export const loader = hocLoader(async ({ request, params }) => {
  const { isoCode } = await getUserSession({
    headers: request.headers,
  });

  const quizDetail = await hoc404(() =>
    getQuizDetail({
      isoCode,
      _id: params.id || '',
    }),
  );

  return json({ quizId: params.id || '', quizDetail });
}, PERMISSIONS.READ_TRAINING_QUIZ);

export default function EditQuiz() {
  const { t } = useTranslation('quiz');
  const navigate = useNavigate();
  const permissions = useGlobalStore(state => state.permissions);
  const loaderData = useLoaderDataSafely<typeof loader>();

  return (
    <>
      <div className="flex flex-wrap bg-secondary rounded-md p-4 justify-between items-center min-h-24 mb-6">
        <Grid className="gap-3">
          <Typography className="capitalize" variant="h2">
            {t('QUIZ_DETAIL')}
          </Typography>
          <Breadcrumbs />
        </Grid>
        <Button
          disabled={!permissions.includes(PERMISSIONS.WRITE_TRAINING_QUIZ)}
          onClick={() =>
            navigate(
              `${ROUTE_NAME.QUIZ}/${loaderData?.quizId}${ROUTE_NAME.EDIT}`,
            )
          }>
          {t('UPDATE_QUIZ')}
        </Button>
      </div>
      <div className="flex flex-col gap-6">
        <Card className="bg-gray-50">
          <CardHeader>
            <Typography
              variant="h4"
              className="min-w-fit w-1/5 border-b-gray-200 border-b pb-3">
              {t('QUESTION_INFORMATION')}
            </Typography>
          </CardHeader>
          <CardContent className="grid grid-cols-3 gap-6">
            <BlockDescription
              desc={{
                label: t('QUIZ_CODE'),
                value: loaderData.quizDetail?.code,
              }}
            />
            <BlockDescription
              desc={{
                label: t('CREATED_BY'),
                value: loaderData.quizDetail?.createdByUsername,
              }}
            />
            <BlockDescription
              desc={{
                label: t('CREATED_AT'),
                value: momentTz(loaderData.quizDetail?.createdAt).format(
                  'HH:mm - DD/MM/YYYY',
                ),
              }}
            />
            <BlockDescription
              desc={{
                label: t('QUESTION'),
                value: loaderData.quizDetail?.title,
              }}
            />
            <BlockDescription
              desc={{
                label: t('UPDATED_BY'),
                value: loaderData.quizDetail?.updatedByUsername,
              }}
            />
            <BlockDescription
              desc={{
                label: t('UPDATED_AT'),
                value: loaderData.quizDetail?.updatedAt
                  ? momentTz(loaderData.quizDetail?.updatedAt).format(
                      'HH:mm - DD/MM/YYYY',
                    )
                  : '',
              }}
            />
          </CardContent>
        </Card>
        <Card className="bg-gray-50">
          <CardHeader className={'pb-0'}>
            <Typography
              variant="h4"
              className="min-w-fit w-1/5 border-b-gray-200 border-b pb-3">
              {t('IMAGE')}
            </Typography>
          </CardHeader>
          <CardContent className="p-6 flex flex-col gap-6">
            {loaderData.quizDetail?.image ? (
              <div
                className={
                  'p-6 border border-dashed border-gray-300 rounded-md bg-gray-100 w-3/4'
                }>
                <div className={'flex gap-5 items-start'}>
                  <div className={'w-full'}>
                    <AspectRatio ratio={16 / 9}>
                      <img
                        src={loaderData.quizDetail?.image.url}
                        alt={loaderData.quizDetail?.image.description}
                        className="object-cover h-full rounded-md"
                      />
                    </AspectRatio>
                  </div>
                  <BlockDescription
                    className={'w-full'}
                    desc={{
                      label: t('DESCRIPTION'),
                      value: loaderData.quizDetail?.image.description,
                    }}
                  />
                </div>
              </div>
            ) : (
              <Typography
                variant="p"
                affects="removePMargin"
                className="text-gray-700 text-center">
                {t('THIS_QUIZ_HAS_NO_IMAGE')}
              </Typography>
            )}
          </CardContent>
        </Card>
        <Card className="bg-gray-50">
          <CardHeader className={'pb-0'}>
            <Typography
              variant="h4"
              className="min-w-fit w-1/5 border-b-gray-200 border-b pb-3">
              {t('ANSWER')}
            </Typography>
          </CardHeader>
          <CardContent className="p-6 flex flex-col gap-5">
            <div className="flex items-center gap-4">
              <Switch
                checked={!!loaderData.quizDetail?.isRandomAnswer}
                className="cursor-default opacity-50"
              />
              <Typography
                variant="p"
                affects="removePMargin"
                className="text-gray-600 text-sm">
                {t('RANDOM_ANSWER')}
              </Typography>
            </div>
            <div className="grid grid-cols-3 gap-6">
              {loaderData.quizDetail?.answers.map(
                (
                  answer: {
                    content: string;
                    isCorrect: boolean;
                  },
                  idx: number,
                ) => (
                  <Card
                    key={idx}
                    className={`border-dashed ${answer.isCorrect ? 'border-secondary-foreground bg-secondary' : 'border-red-500 bg-red-50'}`}>
                    <CardContent className="p-6">
                      <BlockDescription
                        desc={{
                          label: t('ANSWER_INDEX', { index: idx + 1 }),
                          value: answer.content,
                        }}
                      />
                    </CardContent>
                  </Card>
                ),
              )}
            </div>
          </CardContent>
        </Card>
      </div>
    </>
  );
}
