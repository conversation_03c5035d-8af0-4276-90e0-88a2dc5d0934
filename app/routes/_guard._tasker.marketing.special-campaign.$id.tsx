import { json } from '@remix-run/node';
import { PERMISSIONS, ROUTE_NAME } from 'btaskee-constants';
import { BreadcrumbsLink } from 'btaskee-ui';
import { hocLoader } from '~/hoc/remix';

export const handle = {
  breadcrumb: (data: { specialCampaignId: string }) => {
    return (
      <BreadcrumbsLink
        to={`${ROUTE_NAME.SPECIAL_CAMPAIGN}/${data.specialCampaignId}`}
        label="SPECIAL_CAMPAIGN_DETAIL"
      />
    );
  },
};

export const loader = hocLoader(async ({ params }) => {
  return json({ specialCampaignId: params.id || '' });
}, PERMISSIONS.READ_SPECIAL_CAMPAIGN);
