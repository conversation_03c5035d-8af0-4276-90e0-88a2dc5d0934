import { useOutletContext, useSearchParams } from '@remix-run/react';
import { type ColumnDef } from '@tanstack/react-table';
import { CODE_TYPE } from 'btaskee-constants';
import {
  BTaskeeTable,
  Badge,
  CardStatistic,
  Checkbox,
  DataTableColumnHeader,
  Grid,
} from 'btaskee-ui';
import { getPageSizeAndPageIndex } from 'btaskee-utils';
import { format } from 'date-fns';
import i18n from 'i18next';
import { useMemo, useState } from 'react';
import { useTranslation } from 'react-i18next';

export default function HistoryBRewardIndex() {
  const { t } = useTranslation('history-breward');
  const [searchParams] = useSearchParams();
  const [selectedTotalValue, setSelectedTotalValue] = useState<number>(0);

  const outletData = useOutletContext<{
    total: number;
    historyBReward: HistoryBReward[];
    cities: string[];
    filterValue: {
      search: string;
      rangeDate: { from: Date; to: Date };
      city: string;
      codeType: string;
    };
  }>();

  const columns: ColumnDef<HistoryBReward>[] = useMemo(
    () => [
      {
        accessorKey: '_id',
        header: ({ table }) => (
          <Checkbox
            checked={
              table.getIsAllPageRowsSelected() ||
              (table.getIsSomePageRowsSelected() && 'indeterminate')
            }
            onCheckedChange={value => {
              table.toggleAllPageRowsSelected(!!value);
              const newSum = table.getRowModel().rows.reduce((sum, row) => {
                if (value) {
                  return sum + row.original?.pointTransaction?.point;
                } else {
                  return 0;
                }
              }, 0);
              setSelectedTotalValue(newSum);
            }}
            aria-label="Select all"
            className="translate-y-[2px]"
          />
        ),
        cell: ({ row }) => (
          <Checkbox
            checked={row.getIsSelected()}
            onCheckedChange={value => {
              setSelectedTotalValue(prev => {
                if (value) {
                  return prev + row.original?.pointTransaction?.point;
                }
                return prev - row.original?.pointTransaction?.point;
              });
              row.toggleSelected(!!value);
            }}
            aria-label="Select row"
            className="translate-y-[2px]"
          />
        ),
        enableSorting: false,
        enableHiding: false,
      },
      {
        accessorKey: t('CODE'),
        accessorFn: row => `${row?.promotionCode}`,
      },
      {
        accessorKey: t('TITLE'),
        accessorFn: row => `${row?.title?.[i18n.language || 'en']}`,
      },
      {
        accessorKey: 'pointTransaction.point',
        header: ({ column }) => (
          <DataTableColumnHeader column={column} title={t('POINT')} />
        ),
        accessorFn: row => `${row?.pointTransaction?.point || 0}`,
      },
      {
        accessorKey: 'expired',
        header: ({ column }) => (
          <DataTableColumnHeader column={column} title={t('EXPIRED_DATE')} />
        ),
        accessorFn: row =>
          row?.expired ? format(row?.expired, 'dd/MM/yyyy') : 'N/A',
      },
      {
        accessorKey: t('TASKER_NAME'),
        accessorFn: row => `${row?.tasker?.name}`,
      },
      {
        accessorKey: t('TASKER_PHONE'),
        accessorFn: row => row?.tasker?.phone,
      },
      {
        accessorKey: t('CITY'),
        accessorFn: row => row?.tasker?.city,
      },
      {
        accessorKey: 'createdAt',
        header: ({ column }) => (
          <DataTableColumnHeader column={column} title={t('REDEEM_DATE')} />
        ),
        accessorFn: row =>
          row?.createdAt ? format(row?.createdAt, 'dd/MM/yyyy') : 'N/A',
      },
      {
        accessorKey: 'usedAt',
        header: ({ column }) => (
          <DataTableColumnHeader column={column} title={t('USE_DATE')} />
        ),
        accessorFn: row =>
          row?.usedAt ? format(row?.usedAt, 'dd/MM/yyyy') : 'N/A',
      },
      {
        accessorKey: t('RESPONSIBLE_PERSON'),
        cell: ({ row }) => (
          <Badge className="bg-blue-50 text-blue rounded-md">
            {row.original?.responsiblePerson}
          </Badge>
        ),
      },
    ],
    [t, setSelectedTotalValue],
  );

  return (
    <Grid className="mt-6 gap-6">
      <div className=" md:w-1/4">
        <CardStatistic
          title={t('TOTAL_POINT')}
          icon={t('POINT')}
          content={`${selectedTotalValue} Point`}
          subContent={t('SUB_CONTENT_EXPLAIN')}
        />
      </div>
      <BTaskeeTable
        total={outletData?.total}
        data={outletData?.historyBReward}
        columns={columns}
        pagination={getPageSizeAndPageIndex({
          total: outletData?.total || 0,
          pageSize: Number(searchParams.get('pageSize') || 0),
          pageIndex: Number(searchParams.get('pageIndex') || 0),
        })}
        search={{
          defaultValue: searchParams.get('search') || '',
          name: 'search',
          placeholder: t('SEARCH_TEXT'),
        }}
        filterDate={{
          name: 'createdAt',
          defaultValue: outletData?.filterValue?.rangeDate,
        }}
        filters={[
          {
            placeholder: t('CITY'),
            name: 'city',
            options: outletData?.cities.map(city => ({
              label: city,
              value: city,
            })),
            value: outletData?.filterValue?.city,
          },
          {
            placeholder: t('CODE_TYPE'),
            name: 'codeType',
            options: Object.keys(CODE_TYPE).map(type => ({
              label: t(type),
              value: type,
            })),
            value: outletData?.filterValue?.codeType,
          },
        ]}
      />
    </Grid>
  );
}
