import { Link, Outlet, useLocation } from '@remix-run/react';
import { PERMISSIONS, ROUTE_NAME } from 'btaskee-constants';
import { useGlobalStore } from 'btaskee-hooks';
import { BreadcrumbsLink, Typography, buttonVariants, cn } from 'btaskee-ui';
import {
  FileCog,
  FileSpreadsheetIcon,
  HistoryIcon,
  NetworkIcon,
  UserCircle,
} from 'lucide-react';
import { useMemo } from 'react';
import { useTranslation } from 'react-i18next';

export const handle = {
  breadcrumb: () => (
    <BreadcrumbsLink
      disabled
      to=""
      label="SETTINGS"
      onClick={e => e.preventDefault()}
    />
  ),
  i18n: 'user-settings',
};

export default function Screen() {
  const { t } = useTranslation('user-settings');
  const { pathname } = useLocation();
  const permissions = useGlobalStore(state => state.permissions);

  // not have permission field mean not require permission
  const navigation = useMemo(
    () => [
      {
        title: 'PROFILE',
        href: ROUTE_NAME.PROFILE_SETTING,
        icon: <UserCircle className="h-6 w-6" />,
      },
      {
        title: 'USER_MANAGEMENT',
        href: ROUTE_NAME.USER_SETTING,
        permission: PERMISSIONS.MANAGER,
        icon: <FileSpreadsheetIcon className="h-6 w-6" />,
      },
      {
        title: 'GROUPS',
        href: ROUTE_NAME.GROUP_SETTING,
        permission: PERMISSIONS.READ_GROUP,
        icon: <NetworkIcon className="h-6 w-6" />,
      },
      {
        title: 'ACTIONS_HISTORY',
        href: ROUTE_NAME.ACTION_HISTORY_SETTING,
        permission: PERMISSIONS.MANAGER,
        icon: <HistoryIcon className="h-6 w-6" />,
      },
      {
        title: 'PERMISSION',
        href: ROUTE_NAME.PERMISSION_SETTING,
        permission: PERMISSIONS.MANAGER,
        icon: <FileCog />,
      },
    ],
    [],
  );

  return (
    <>
      <Typography variant="h2">{t('SETTINGS')}</Typography>

      <div className="mt-6 flex h-full flex-col lg:flex-row">
        <aside
          className="rounded-[6px] lg:w-1/4"
          style={{ boxShadow: '0px 0px 13px -5px #00000024' }}>
          <nav className="flex space-x-2 p-4 lg:flex-col lg:space-x-0 lg:space-y-1">
            {navigation.map(item =>
              !item.permission || permissions.includes(item.permission) ? (
                <Link
                  key={item.href}
                  to={item.href}
                  className={cn(
                    buttonVariants({ variant: 'ghost' }),
                    pathname.includes(item.href)
                      ? 'bg-primary-50 font-medium text-primary hover:bg-primary-50 hover:text-primary'
                      : 'font-normal text-gray hover:bg-primary-50',
                    'justify-start gap-2 rounded-[6px] py-3 pl-4 text-base',
                  )}>
                  {item?.icon}
                  {t(item.title)}
                </Link>
              ) : null,
            )}
          </nav>
        </aside>
        <div className="flex-1 pl-6">
          <Outlet />
        </div>
      </div>
    </>
  );
}
