import { json } from '@remix-run/node';
import type { LoaderFunctionArgs, SerializeFrom } from '@remix-run/node';
import {
  Link,
  isRouteErrorResponse,
  useLoaderData,
  useNavigate,
  useRouteError,
  useSearchParams,
} from '@remix-run/react';
import { type ColumnDef } from '@tanstack/react-table';
import { PERMISSIONS, ROUTE_NAME, STATUS, TYPE } from 'btaskee-constants';
import { useGlobalStore } from 'btaskee-hooks';
import {
  BTaskeeTable,
  Breadcrumbs,
  BtaskeeResponseError,
  Button,
  DATE_RANGE_PICKER_OPTIONS,
  DataTableColumnHeader,
  DropdownMenuBase,
  StatusBadge,
  Tooltip,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
  Typography,
  toast,
} from 'btaskee-ui';
import { createUID, getPageSizeAndPageIndex, momentTz } from 'btaskee-utils';
import { format } from 'date-fns';
import i18n from 'i18next';
import { Plus } from 'lucide-react';
import { useEffect, useMemo } from 'react';
import { useTranslation } from 'react-i18next';
import { useOutletTaskerTrainingCourse } from '~/hooks/useGetListTaskerTrainingCourse';
import { commitSession, getSession } from '~/services/session.server';
import type { getListCourses } from '~/services/tasker-training.server';

export const loader = async ({ request }: LoaderFunctionArgs) => {
  const session = await getSession(request.headers.get('Cookie'));

  const message = session.get('flashMessage');

  return json(
    { message },
    {
      headers: {
        'Set-Cookie': await commitSession(session),
      },
    },
  );
};

export function ErrorBoundary() {
  const error = useRouteError() as { message?: string };
  const { t: tCommon } = useTranslation('common');

  if (isRouteErrorResponse(error)) {
    if (error?.message) {
      toast({ description: JSON.stringify(error.message) });
    }

    return <BtaskeeResponseError t={tCommon} errorStatus={error.status} />;
  }

  return <BtaskeeResponseError t={tCommon} errorStatus={500} />;
}

export default function CourseIndex() {
  const { t } = useTranslation('course');
  const navigate = useNavigate();

  const outletData = useOutletTaskerTrainingCourse();
  const permissions = useGlobalStore(state => state.permissions);
  const [searchParams] = useSearchParams();

  const columns: ColumnDef<
    SerializeFrom<ReturnValueIgnorePromise<typeof getListCourses>[0]>
  >[] = useMemo(
    () => [
      {
        accessorKey: 'code',
        header: ({ column }) => (
          <DataTableColumnHeader column={column} title={t('CODE')} />
        ),
        cell: ({ row }) => row?.original?.code,
        size: 120,
      },
      {
        accessorKey: 'courseName',
        header: ({ column }) => (
          <DataTableColumnHeader column={column} title={t('COURSE_NAME')} />
        ),
        cell: ({ row }) => row?.original?.title,
        enableSorting: false,
        size: 329,
      },
      {
        accessorKey: 'type',
        header: ({ column }) => (
          <DataTableColumnHeader column={column} title={t('TYPE')} />
        ),
        cell: ({ row }) => (
          <div className="overflow-hidden text-ellipsis whitespace-nowrap">
            <StatusBadge
              statusClasses={{
                TEST: 'bg-green-500 rounded-md text-center',
                REVIEW: 'bg-primary rounded-md text-center',
              }}
              translationKey="course"
              status={row.original?.type}
            />
          </div>
        ),
        enableSorting: false,
        size: 80,
      },
      {
        accessorKey: 'service',
        header: ({ column }) => (
          <DataTableColumnHeader column={column} title={t('SERVICE')} />
        ),
        cell: ({ row }) => (
          <div className="flex gap-2 flex-wrap">
            {row?.original?.relatedServices?.map(service => {
              const matchedService = outletData?.services?.find(
                sv => sv._id === service._id,
              );
              if (!matchedService) return null;
              return (
                <TooltipProvider key={createUID()}>
                  <Tooltip key={createUID()}>
                    <TooltipTrigger asChild>
                      <div className="w-11 h-11 rounded-[11px] overflow-hidden bg-primary-50">
                        <img
                          className="w-full h-full object-cover"
                          src={matchedService?.icon || ''}
                          alt={matchedService.name}
                        />
                      </div>
                    </TooltipTrigger>
                    <TooltipContent>
                      <Typography variant="p">
                        {`${matchedService?.text?.[i18n.language || 'en']} ${matchedService?.isSubscription ? ' (Subscription)' : ''}`}
                      </Typography>
                    </TooltipContent>
                  </Tooltip>
                </TooltipProvider>
              );
            })}
          </div>
        ),
        enableSorting: false,
        size: 287,
      },
      {
        accessorKey: 'updatedBy',
        header: ({ column }) => (
          <DataTableColumnHeader column={column} title={t('UPDATED_BY')} />
        ),
        accessorFn: row => row?.updatedByUsername || row?.createdByUsername,
      },
      {
        accessorKey: 'updatedAt',
        header: ({ column }) => (
          <DataTableColumnHeader column={column} title={t('UPDATED_AT')} />
        ),
        cell: ({ row }) => (
          <Typography variant="p">
            {row?.original?.updatedAt
              ? format(row?.original?.updatedAt, 'HH:mm - dd/MM/yyyy')
              : null}
          </Typography>
        ),
      },
      {
        accessorKey: 'status',
        header: ({ column }) => (
          <DataTableColumnHeader column={column} title={t('STATUS')} />
        ),
        cell: ({ row }) => (
          <div className="flex text-start">
            <StatusBadge
              status={row.original.status}
              isTesting={!!row.original?.isTesting}
              translationKey="course"
            />
          </div>
        ),
        enableSorting: false,
        size: 60,
      },
      {
        id: 'actions',
        header: ({ column }) => (
          <DataTableColumnHeader column={column} title={t('ACTION')} />
        ),
        cell: ({ row }) => (
          <DropdownMenuBase
            links={[
              {
                link: `${ROUTE_NAME.TEST_AND_REVIEW}/${row.original._id}/detail`,
                label: t('VIEW_DETAIL'),
              },
              {
                link: `${ROUTE_NAME.TEST_AND_REVIEW}/${row.original._id}/edit`,
                label:
                  row?.original?.type === TYPE.REVIEW
                    ? t('UPDATE_REVIEW')
                    : t('UPDATE_TEST'),
              },
              {
                link: `${ROUTE_NAME.TEST_AND_REVIEW}/create?id=${row.original._id}`,
                label:
                  row?.original?.type === TYPE.REVIEW
                    ? t('CREATE_TEST')
                    : t('CREATE_REVIEW'),
              },
            ]}
          />
        ),
        size: 44,
      },
    ],
    [outletData?.services, t],
  );

  const loaderData = useLoaderData<typeof loader>();
  useEffect(() => {
    if (loaderData.message) {
      toast({
        variant: 'success',
        description: loaderData.message,
      });
    }
  }, [loaderData.message]);

  return (
    <>
      <div className="mb-6 flex items-center justify-between rounded-xl bg-secondary p-4 font-sans">
        <div className="grid space-y-2">
          <Typography variant="h2">{t('TEST_AND_REVIEW')}</Typography>
          <Breadcrumbs />
        </div>
        {permissions?.includes(PERMISSIONS.WRITE_TEST_AND_REVIEW) ? (
          <Link to={`${ROUTE_NAME.TEST_AND_REVIEW}/create`}>
            <Button className="gap-2">
              <Plus />
              {t('CREATE')}
            </Button>
          </Link>
        ) : null}
      </div>
      <BTaskeeTable
        isShowClearButton
        onClickRow={course =>
          navigate(`${ROUTE_NAME.TEST_AND_REVIEW}/${course._id}/detail`)
        }
        total={outletData?.total || 0}
        data={outletData?.courses || []}
        columns={columns}
        pagination={getPageSizeAndPageIndex({
          total: outletData?.total || 0,
          pageSize: Number(searchParams.get('pageSize') || 0),
          pageIndex: Number(searchParams.get('pageIndex') || 0),
        })}
        search={{
          defaultValue: searchParams.get('search') || '',
          placeholder: t('SEARCH_BY_CODE_AND_NAME'),
          name: 'search',
        }}
        localeAddress="course"
        // eslint-disable-next-line @typescript-eslint/ban-ts-comment
        // @ts-ignore
        filterDate={{
          name: 'updatedAt',
          defaultValue: outletData?.filterValue?.rangeDate
            ? {
                from: momentTz(
                  outletData?.filterValue?.rangeDate?.from,
                ).toDate(),
                to: momentTz(outletData?.filterValue?.rangeDate?.to).toDate(),
              }
            : undefined,
          defaultRangeDateOptions: DATE_RANGE_PICKER_OPTIONS.NONE,
        }}
        filters={[
          {
            placeholder: t('TYPE'),
            name: 'type',
            options: Object.values(TYPE).map(type => ({
              label: t(type),
              value: type,
            })),
            value: outletData?.filterValue?.type,
          },
          {
            placeholder: t('SERVICE'),
            name: 'service',
            options: outletData?.services?.map(service => ({
              label: `${service?.text?.[i18n?.language || 'en']} ${service?.isSubscription ? '(Subscription)' : ''}`,
              value: service._id,
            })),
            value: outletData?.filterValue?.service,
          },
          {
            placeholder: t('STATUS'),
            name: 'status',
            options: Object.values(STATUS).map(status => ({
              label: t(status),
              value: status,
            })),
            value: outletData?.filterValue?.status,
          },
        ]}
      />
    </>
  );
}
