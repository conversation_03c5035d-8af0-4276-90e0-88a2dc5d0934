import { type SerializeFrom, json } from '@remix-run/node';
import {
  isRouteErrorResponse,
  useLoaderData,
  useOutletContext,
  useRouteError,
} from '@remix-run/react';
import { type ColumnDef } from '@tanstack/react-table';
import {
  BtaskeeResponseError,
  Button,
  Card,
  CardContent,
  CardHeader,
  DataTableBasic,
  DataTableColumnHeader,
  Dialog,
  DialogClose,
  DialogContent,
  DialogHeader,
  DialogTitle,
  Label,
  Separator,
  Typography,
  toast,
} from 'btaskee-ui';
import { copyToClipboard, formatTimeToCompleteFromSecond } from 'btaskee-utils';
import { Copy } from 'lucide-react';
import { useMemo } from 'react';
import { useTranslation } from 'react-i18next';
import { hocLoader } from '~/hoc/remix';
import { getUserSession } from '~/services/helpers.server';
import {
  getListQuizDetailSortByOrder,
  getQuizCollectionDetail,
} from '~/services/tasker-training.server';

export const loader = hocLoader(async ({ request, params }) => {
  const { isoCode } = await getUserSession({
    headers: request.headers,
  });

  const quizCollectionDetail = await getQuizCollectionDetail({
    isoCode,
    _id: params.id || '',
  });

  const quizzesSorted = await getListQuizDetailSortByOrder({
    isoCode,
    quizzes: quizCollectionDetail?.quizzes || [],
  });

  return json({
    quizCollectionDetail,
    quizzesSorted,
  });
});

export function ErrorBoundary() {
  const error = useRouteError() as { message?: string };
  const { t: tCommon } = useTranslation('common');

  if (isRouteErrorResponse(error)) {
    if (error?.message) {
      toast({ description: JSON.stringify(error.message) });
    }

    return <BtaskeeResponseError t={tCommon} errorStatus={error.status} />;
  }

  return <BtaskeeResponseError t={tCommon} errorStatus={500} />;
}

export default function ViewQuizzesDialog() {
  const loaderData = useLoaderData<LoaderTypeWithError<typeof loader>>();

  const { t } = useTranslation('course');

  const parentContext = useOutletContext<{
    open: boolean;
    onOpenChange: (open: boolean) => void;
    onClose: () => void;
    setSelectedQuizzes: (quizzes: MustBeAny) => void; //TODO: MustBeAny needs to be replaced with the correct type
  }>();

  const columns: ColumnDef<
    SerializeFrom<
      ReturnValueIgnorePromise<typeof getListQuizDetailSortByOrder>[0]
    >
  >[] = useMemo(
    () => [
      {
        accessorKey: '_id',
        header: ({ column }) => (
          <DataTableColumnHeader column={column} title={t('NUMBER_ORDER')} />
        ),
        cell: ({ row }) => <span>{row.index + 1}</span>,
        size: 20,
        enableSorting: false,
      },
      {
        accessorKey: 'code',
        header: ({ column }) => (
          <DataTableColumnHeader column={column} title={t('QUIZ_CODE')} />
        ),
        size: 100,
        cell: ({ row }) => (
          <span className="text-gray-800">{row?.original?.code}</span>
        ),
        enableSorting: false,
      },
      {
        accessorKey: 'title',
        header: ({ column }) => (
          <DataTableColumnHeader column={column} title={t('QUESTION')} />
        ),
        size: 100,
        cell: ({ row }) => (
          <span className="text-gray-800">{row?.original?.title}</span>
        ),
        enableSorting: false,
      },
    ],
    [t],
  );

  return (
    <Dialog
      open={parentContext.open}
      onOpenChange={parentContext.onOpenChange}
      defaultOpen={true}>
      <DialogContent
        className="max-w-5xl max-h-[90vh] overflow-y-auto"
        onInteractOutside={parentContext.onClose}>
        <DialogHeader className="space-y-2">
          <DialogTitle className="text-2xl tracking-tighter font-semibold">
            {t('QUIZ_COLLECTION_DETAIL')}
          </DialogTitle>
        </DialogHeader>
        <div className="grid grid-cols-2 gap-4">
          <Card className="bg-gray-50 p-4 gap-3">
            <Label className="text-gray-400">{t('CODE')}</Label>
            <Typography
              variant="p"
              affects="removePMargin"
              className="text-gray-600 font-medium text-lg">
              {loaderData?.quizCollectionDetail?.code || ''}
            </Typography>
            <Separator className="my-3" />
            <Label className="text-gray-400">
              {t('QUIZ_COLLECTION_NAME_DETAIL')}
            </Label>
            <Typography
              variant="p"
              affects="removePMargin"
              className="text-gray-600 font-medium text-lg">
              {loaderData?.quizCollectionDetail?.title || ''}
            </Typography>
            <Separator className="my-3" />
            <Label className="text-gray-400">{t('SUMMARY')}</Label>
            <Typography
              variant="p"
              affects="removePMargin"
              className="text-gray-600 font-medium text-lg">
              {loaderData?.quizCollectionDetail?.description || ''}
            </Typography>
            <Separator className="my-3" />
            <Label className="text-gray-400">
              {t('ESTIMATE_TIME_TO_COMPLETE')}
            </Label>
            <Typography
              variant="p"
              affects="removePMargin"
              className="text-gray-600 font-medium text-lg">
              {formatTimeToCompleteFromSecond(
                loaderData?.quizCollectionDetail?.timeToCompleteByMinutes || 0,
              )}
            </Typography>
          </Card>
          {loaderData?.quizCollectionDetail?.image ? (
            <Card className="bg-gray-50 p-6 flex flex-col gap-5 border border-dashed">
              <img
                src={loaderData?.quizCollectionDetail?.image?.url}
                alt="Collection"
                className="rounded-md"
              />
              <div>
                <Label className="text-gray-400">{t('DESCRIPTION')}</Label>
                <Typography
                  variant="p"
                  affects="removePMargin"
                  className="text-gray-600 font-medium text-lg">
                  {loaderData?.quizCollectionDetail?.image?.description || ''}
                </Typography>
              </div>
            </Card>
          ) : null}
          {loaderData?.quizCollectionDetail?.video ? (
            <Card className="bg-gray-50">
              <CardHeader>
                <Typography variant="h3">{t('VIDEO')}</Typography>
              </CardHeader>
              <CardContent className="flex flex-col gap-3">
                <div>
                  <Label className="text-gray-400">{t('LINK_YOUTUBE')}</Label>
                  <div className="flex items-center">
                    <Typography
                      variant="p"
                      affects="removePMargin"
                      className="text-gray-600 font-medium text-lg underline">
                      {loaderData?.quizCollectionDetail?.video?.url || ''}
                    </Typography>
                    <Button
                      variant="ghost"
                      type="button"
                      className="text-orange-500"
                      onClick={() =>
                        copyToClipboard(
                          loaderData?.quizCollectionDetail?.video?.url || '',
                        )
                      }>
                      <Copy />
                    </Button>
                  </div>
                </div>
                <Separator />
                <div>
                  <Label className="text-gray-400">{t('VIDEO_SUMMARY')}</Label>
                  <Typography
                    variant="p"
                    affects="removePMargin"
                    className="text-gray-600 font-medium text-lg">
                    {loaderData?.quizCollectionDetail?.video?.description || ''}
                  </Typography>
                </div>
              </CardContent>
            </Card>
          ) : null}
        </div>
        <div className="max-h-[325px] overflow-y-auto">
          <DataTableBasic
            manualPagination
            isDisplayPagination={false}
            columns={columns}
            data={loaderData?.quizzesSorted || []}
          />
        </div>
        <div className="flex flex-col items-end gap-3">
          <Button
            className="border-primary text-primary hover:bg-primary-foreground hover:text-primary"
            type="button"
            variant="outline"
            onClick={parentContext.onClose}>
            {t('CANCEL')}
          </Button>
        </div>

        <DialogClose className="absolute z-10 right-4 top-4 rounded-sm w-5 h-5 cursor-default bg-white" />
      </DialogContent>
    </Dialog>
  );
}
