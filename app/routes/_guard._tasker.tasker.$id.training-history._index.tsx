import { DotsHorizontalIcon } from '@radix-ui/react-icons';
import { type SerializeFrom } from '@remix-run/node';
import { useNavigate, useSearchParams } from '@remix-run/react';
import { type ColumnDef } from '@tanstack/react-table';
import {
  COURSE_COMPLETION_IN_TRAINING,
  COURSE_VISIBILITY_IN_TRAINING,
  ROUTE_NAME,
  SUBMISSION_STATUS_IN_TRAINING,
} from 'btaskee-constants';
import {
  BTaskeeTable,
  Breadcrumbs,
  Button,
  DATE_RANGE_PICKER_OPTIONS,
  DataTableColumnHeader,
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
  Grid,
  StatusBadge,
  Tooltip,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
  Typography,
} from 'btaskee-ui';
import { getPageSizeAndPageIndex, momentTz } from 'btaskee-utils';
import { format } from 'date-fns';
import i18n from 'i18next';
import { useMemo } from 'react';
import { useTranslation } from 'react-i18next';
import { useOutletGetTaskerTrainingHistoryProfile } from '~/hooks/useLoaderTaskerTrainingHistory';
import { type getListTrainingHistory } from '~/services/tasker-training.server';

export default function TaskerTrainingHistory() {
  const { t: tTrainingHistory } = useTranslation('course');
  const [searchParams] = useSearchParams();
  const outletData = useOutletGetTaskerTrainingHistoryProfile();
  const navigate = useNavigate();

  const columns: ColumnDef<
    SerializeFrom<
      ReturnValueIgnorePromise<typeof getListTrainingHistory>['data'][0]
    >
  >[] = useMemo(
    () => [
      {
        accessorKey: 'code',
        header: ({ column }) => (
          <DataTableColumnHeader
            column={column}
            title={tTrainingHistory('TEST_CODE')}
          />
        ),
        cell: ({ row }) => (
          <span className="text-gray-800">{row.original.code}</span>
        ),
      },
      {
        accessorKey: 'title',
        header: ({ column }) => (
          <DataTableColumnHeader
            column={column}
            title={tTrainingHistory('COURSE_NAME')}
          />
        ),
        size: 240,
        cell: ({ row }) => (
          <span className="text-gray-800">{row.original.title}</span>
        ),
        enableSorting: false,
      },
      {
        accessorKey: 'services',
        header: ({ column }) => (
          <DataTableColumnHeader
            column={column}
            title={tTrainingHistory('SERVICE')}
          />
        ),
        size: 300,
        cell: ({ row }) => (
          <div className="flex flex-wrap gap-3">
            {row.original?.relatedServices?.map(relatedService => {
              const serviceFoundById = outletData?.services?.find(
                service => relatedService.name === service.name,
              );

              return (
                <TooltipProvider key={relatedService._id}>
                  <Tooltip key={relatedService._id}>
                    <TooltipTrigger asChild>
                      <div className="w-11 h-11 rounded-[11px] overflow-hidden bg-primary-50">
                        <img
                          className="w-full h-full object-cover"
                          src={serviceFoundById?.icon || ''}
                          alt={relatedService.name}
                        />
                      </div>
                    </TooltipTrigger>
                    <TooltipContent>
                      <Typography variant="p">
                        {`${serviceFoundById?.text?.[i18n.language || 'en']} ${serviceFoundById?.isSubscription ? '(Subscription)' : ''}`}
                      </Typography>
                    </TooltipContent>
                  </Tooltip>
                </TooltipProvider>
              );
            })}
          </div>
        ),
        enableSorting: false,
      },
      {
        accessorKey: 'updatedAt',
        header: ({ column }) => (
          <DataTableColumnHeader
            column={column}
            title={tTrainingHistory('UPDATED_AT')}
          />
        ),
        cell: ({ row }) =>
          row.original?.updatedAt ? (
            <Typography
              affects="removePMargin"
              className="whitespace-nowrap"
              variant="p">
              {format(row.original?.updatedAt, 'HH:mm - dd/MM/yyyy')}
            </Typography>
          ) : null,
      },
      {
        accessorKey: 'visibility',
        header: ({ column }) => (
          <DataTableColumnHeader
            className="text-center"
            column={column}
            title={tTrainingHistory('VISIBILITY')}
          />
        ),
        cell: ({ row }) => (
          <div className="text-center">
            <StatusBadge
              statusClasses={{
                [COURSE_VISIBILITY_IN_TRAINING.NOT_OPENED]:
                  'bg-gray-100 text-gray-500 rounded-md',
                [COURSE_VISIBILITY_IN_TRAINING.OPENED]:
                  'text-secondary-foreground bg-secondary rounded-md',
                [COURSE_VISIBILITY_IN_TRAINING.BLOCKED]:
                  'text-red-500 bg-red-50 rounded-md',
              }}
              translationKey="course"
              status={
                row.original?.visibility ||
                COURSE_VISIBILITY_IN_TRAINING.NOT_OPENED
              }
            />
          </div>
        ),
        enableSorting: false,
      },
      {
        accessorKey: 'completion',
        header: ({ column }) => (
          <DataTableColumnHeader
            className="text-center"
            column={column}
            title={tTrainingHistory('COMPLETION')}
          />
        ),
        cell: ({ row }) => (
          <div className="text-center">
            <StatusBadge
              statusClasses={{
                [COURSE_COMPLETION_IN_TRAINING.NONE]:
                  'bg-gray-100 text-gray-500 rounded-md',
                [COURSE_COMPLETION_IN_TRAINING.COMPLETED]:
                  'text-secondary-foreground bg-secondary rounded-md',
                [COURSE_COMPLETION_IN_TRAINING.NOT_STARTED]:
                  'text-yellow-500 bg-yellow-50 rounded-md',
                [COURSE_COMPLETION_IN_TRAINING.EXPIRED]:
                  'text-red-500 bg-red-50 rounded-md',
                [COURSE_COMPLETION_IN_TRAINING.IN_PROGRESS]:
                  'text-blue-500 bg-blue-50 rounded-md',
              }}
              translationKey="course"
              status={
                row.original?.completion || COURSE_COMPLETION_IN_TRAINING.NONE
              }
            />
          </div>
        ),
        enableSorting: false,
      },
      {
        accessorKey: 'status',
        header: ({ column }) => (
          <DataTableColumnHeader
            className="text-center"
            column={column}
            title={tTrainingHistory('PASS')}
          />
        ),
        cell: ({ row }) => (
          <div className="text-center">
            <StatusBadge
              statusClasses={{
                [SUBMISSION_STATUS_IN_TRAINING.NONE]:
                  'bg-gray-100 text-gray-500 rounded-md',
                [SUBMISSION_STATUS_IN_TRAINING.PASSED]:
                  'text-secondary-foreground bg-secondary rounded-md',
                [SUBMISSION_STATUS_IN_TRAINING.FAILED]:
                  'text-red-500 bg-red-50 rounded-md',
              }}
              translationKey="course"
              status={
                row.original?.status || SUBMISSION_STATUS_IN_TRAINING.NONE
              }
            />
          </div>
        ),
        enableSorting: false,
      },
      {
        accessorKey: 'action',
        header: ({ column }) => (
          <DataTableColumnHeader
            className="whitespace-nowrap"
            column={column}
            title={tTrainingHistory('ACTION')}
          />
        ),
        size: 40,
        cell: ({ row }) => (
          <DropdownMenu>
            <DropdownMenuTrigger asChild>
              <Button
                variant="ghost"
                className="mx-auto flex h-8 w-8 p-0 data-[state=open]:bg-muted">
                <DotsHorizontalIcon className="h-4 w-4" />
                <span className="sr-only">{tTrainingHistory('OPEN_MENU')}</span>
              </Button>
            </DropdownMenuTrigger>
            <DropdownMenuContent>
              <DropdownMenuItem
                onClick={() =>
                  navigate(
                    `${ROUTE_NAME.TRAINING_HISTORY}/${row.original._id}${ROUTE_NAME.TASKER}/${outletData?.taskerId}`,
                  )
                }>
                {tTrainingHistory('VIEW_DETAIL')}
              </DropdownMenuItem>
            </DropdownMenuContent>
          </DropdownMenu>
        ),
        enableSorting: false,
      },
    ],
    [tTrainingHistory, outletData, navigate],
  );

  return (
    <>
      <div className="flex bg-secondary p-4 justify-between items-center min-h-24 rounded-md">
        <Grid className="gap-3">
          <Typography variant="h2">
            {tTrainingHistory('TRAINING_HISTORY')}
          </Typography>
          <Breadcrumbs />
        </Grid>
      </div>
      <BTaskeeTable
        columns={columns}
        data={outletData?.data || []}
        isShowClearButton
        total={outletData?.total || 0}
        pagination={getPageSizeAndPageIndex({
          total: outletData?.total || 0,
          pageSize: Number(searchParams.get('pageSize') || 0),
          pageIndex: Number(searchParams.get('pageIndex') || 0),
        })}
        onClickRow={trainingHistory =>
          navigate(
            `${ROUTE_NAME.TRAINING_HISTORY}/${trainingHistory._id}${ROUTE_NAME.TASKER}/${outletData?.taskerId}`,
          )
        }
        search={{
          name: 'search',
          defaultValue: outletData?.filterCourse?.filter.search || '',
          placeholder: tTrainingHistory('SEARCH_BY_CODE_OR_NAME'),
        }}
        filters={[
          {
            name: 'services',
            placeholder: tTrainingHistory('SELECT_SERVICES'),
            options: outletData?.taskerServices?.map(service => ({
              label: `${service?.text?.[i18n.language || 'en'] || ''} ${service?.isSubscription ? '(Subscription)' : ''}`,
              value: service?._id || '',
            })),
            value: outletData?.filterCourse?.filter?.services?.join(',') || '',
          },
          {
            name: 'visibility',
            placeholder: tTrainingHistory('SELECT_VISIBILITY'),
            options: Object.values(COURSE_VISIBILITY_IN_TRAINING)?.map(
              visibility => ({
                label: tTrainingHistory(visibility),
                value: visibility,
              }),
            ),
            value:
              outletData?.filterCourse?.filter?.visibilities?.join(',') || '',
          },
          {
            name: 'completion',
            placeholder: tTrainingHistory('SELECT_COMPLETION'),
            options: Object.values(COURSE_COMPLETION_IN_TRAINING)?.map(
              completion => ({
                label: tTrainingHistory(completion),
                value: completion,
              }),
            ),
            value:
              outletData?.filterCourse?.filter?.completions?.join(',') || '',
          },
          {
            name: 'status',
            placeholder: tTrainingHistory('SELECT_PASS'),
            options: Object.values(SUBMISSION_STATUS_IN_TRAINING)?.map(
              status => ({
                label: tTrainingHistory(status),
                value: status,
              }),
            ),
            value: outletData?.filterCourse?.filter?.statuses?.join(',') || '',
          },
        ]}
        // eslint-disable-next-line @typescript-eslint/ban-ts-comment
        // @ts-ignore
        filterDate={{
          name: 'updatedAt',
          defaultValue: outletData?.filterCourse?.filter?.rangeDate
            ? {
                from: momentTz(
                  outletData?.filterCourse?.filter?.rangeDate?.from,
                ).toDate(),
                to: momentTz(
                  outletData?.filterCourse?.filter?.rangeDate?.to,
                ).toDate(),
              }
            : undefined,
          defaultRangeDateOptions: DATE_RANGE_PICKER_OPTIONS.NONE,
        }}
      />
    </>
  );
}
