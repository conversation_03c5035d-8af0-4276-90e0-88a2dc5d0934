import { DotsHorizontalIcon } from '@radix-ui/react-icons';
import { json, redirect } from '@remix-run/node';
import type { LoaderFunctionArgs } from '@remix-run/node';
import {
  Outlet,
  isRouteErrorResponse,
  useActionData,
  useLoaderData,
  useNavigate,
  useParams,
  useRouteError,
  useSearchParams,
  useSubmit,
} from '@remix-run/react';
import { type ColumnDef } from '@tanstack/react-table';
import {
  ACTION_NAME,
  GROUP_STATUS,
  ROUTE_NAME,
  STATUS,
} from 'btaskee-constants';
import {
  BTaskeeTable,
  BtaskeeResponseError,
  Button,
  DataTableColumnHeader,
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
  Input,
  Label,
  SelectBase,
  StatusBadge,
  toast,
  useConfirm,
} from 'btaskee-ui';
import { momentTz } from 'btaskee-utils';
import { format } from 'date-fns';
import { Plus } from 'lucide-react';
import { useCallback, useEffect, useMemo, useState } from 'react';
import { useForm } from 'react-hook-form';
import { useTranslation } from 'react-i18next';
import { hoc404, hocAction } from '~/hoc/remix';
import { useGetGroupDetail } from '~/hooks/useGetGroupDetail';
import { getUserSession } from '~/services/helpers.server';
import {
  addMultiUserToGroup,
  getGroupDetail,
  removeUserFromGroup,
  updateStatusUserInGroup,
} from '~/services/role-base-access-control.server';
import { commitSession, getSession } from '~/services/session.server';

export const loader = async ({ params, request }: LoaderFunctionArgs) => {
  const groupId = params.id || '';
  const { userId, isSuperUser } = await getUserSession({
    headers: request.headers,
  });

  const groupDetail = await hoc404(() =>
    getGroupDetail<{
      name: GroupDetail['group']['name'];
      description: GroupDetail['group']['description'];
      users: Array<GroupDetail['group']['users'][0] & { addedAt: Date }>;
      status: GroupDetail['group']['status'];
    }>({
      isSuperUser,
      userId,
      groupId,
      projection: {
        users: 1,
        name: 1,
        description: 1,
        parents: 1,
        status: 1,
      },
    }),
  );

  const url = new URL(request.url);

  const search = url.searchParams.get('search') || '';
  const status = url.searchParams.get('status') || '';

  const searchRegex = search ? new RegExp(search, 'i') : null;
  const statusArray = status ? status.split(',') : [];

  // Search at client
  const filteredUsers = groupDetail.users.filter(user => {
    const matchesSearch =
      !searchRegex ||
      (user.username && searchRegex.test(user.username)) ||
      (user.email && searchRegex.test(user.email));

    const matchesStatus = !status || statusArray.includes(user.status);

    return matchesSearch && matchesStatus;
  });

  const session = await getSession(request.headers.get('Cookie'));
  const message = session.get('flashMessage');

  return json(
    {
      groupDetail: { ...groupDetail, users: filteredUsers },
      message,
    },
    {
      headers: {
        'Set-Cookie': await commitSession(session),
      },
    },
  );
};

export const action = hocAction(
  async ({ params, request }, { setInformationActionHistory }) => {
    const formData = await request.clone().formData();
    const userDeleted = formData.get('userDeleted')?.toString() || '';

    const actionName = formData.get('actionName')?.toString() || '';

    if (actionName === 'edit') {
      const status = formData.get('status')?.toString() || '';
      const userId = formData.get('userId')?.toString() || '';

      await updateStatusUserInGroup({
        userId,
        groupId: params.id || '',
        status,
      });

      return json({ message: 'UPDATE_STATUS_SUCCESSFULLY' });
    }

    if (actionName === ACTION_NAME.ADD_MEMBER_INTO_GROUP) {
      const userIdsValue = formData.get('userIds')?.toString() ?? '';

      const [addMultiUserResult, session] = await Promise.all([
        addMultiUserToGroup({
          userIds: userIdsValue ? userIdsValue.split(',') : [],
          groupId: params?.id ?? '',
        }),
        getSession(request.headers.get('cookie')),
      ]);

      setInformationActionHistory({
        action: ACTION_NAME.ADD_MEMBER_INTO_GROUP,
        dataRelated: {
          groupId: params.id,
          userIds: userIdsValue ? userIdsValue.split(',') : [],
        },
      });

      if (addMultiUserResult?.msg) {
        session.flash('flashMessage', addMultiUserResult.msg);
      }

      const newSession = await commitSession(session);

      return redirect(`${ROUTE_NAME.GROUP_SETTING}/${params.id}/member-list`, {
        headers: {
          'Set-Cookie': newSession,
        },
      });
    }

    if (userDeleted) {
      await removeUserFromGroup({
        userId: userDeleted,
        groupId: params.id || '',
      });
      setInformationActionHistory({
        action: 'Remove user from group',
      });

      return json({ message: 'REMOVE_MEMBER_SUCCESSFULLY' });
    }

    return json({ message: '' });
  },
);

export function ErrorBoundary() {
  const error = useRouteError() as { message?: string };
  const { t: tCommon } = useTranslation('common');

  if (isRouteErrorResponse(error)) {
    if (error?.message) {
      toast({ description: JSON.stringify(error.message) });
    }

    return <BtaskeeResponseError t={tCommon} errorStatus={error.status} />;
  }

  return <BtaskeeResponseError t={tCommon} errorStatus={500} />;
}

export default function MemberListScreen() {
  const loaderData = useLoaderData<typeof loader>();
  const actionData = useActionData<ActionTypeWithError<typeof action>>();

  const { t: tUserSettings } = useTranslation('user-settings');
  const outletData = useGetGroupDetail();

  const navigate = useNavigate();
  const params = useParams();

  const confirm = useConfirm();
  const submit = useSubmit();

  const [openDialogAddMember, setOpenDialogAddMember] = useState(true);
  const [openDialogAddAndCreateMember, setOpenDialogAddAndCreateMember] =
    useState(true);
  const [openDialogDetailMember, setOpenDialogDetailMember] = useState(false);

  const [openEditDialog, setOpenEditDialog] = useState(false);
  const [memberDetail, setMemberDetail] =
    useState<GroupDetail['group']['users'][0]>();

  const [searchParams] = useSearchParams();
  const formDetailMember = useForm();

  const editForm = useForm();

  useEffect(() => {
    if (actionData?.message === 'REMOVE_MEMBER_SUCCESSFULLY') {
      toast({
        variant: 'success',
        description: tUserSettings(actionData.message),
      });
    }
    if (actionData?.message === 'UPDATE_STATUS_SUCCESSFULLY') {
      setOpenDialogDetailMember(false);
      setOpenEditDialog(false);
      toast({
        variant: 'success',
        description: tUserSettings('STATUS_UPDATED_SUCCESSFULLY', {
          value: memberDetail?.email,
        }),
      });

      // This trigger a refetch of the loader data to toast the new status
      navigate('.', { replace: true, preventScrollReset: true });
    }
  }, [actionData, memberDetail?.email, navigate, tUserSettings]);

  useEffect(() => {
    if (loaderData.message) {
      toast({
        variant: 'success',
        description: tUserSettings(loaderData.message),
      });
    }
  }, [loaderData.message, tUserSettings]);

  const onDeleteMember = useCallback(
    async (userId: string) => {
      const isConfirmSubmit = await confirm({
        title: tUserSettings('REMOVE_MEMBER'),
        body: tUserSettings('ARE_YOU_SURE_YOU_WANT_TO_DELETE'),
        cancelButton: tUserSettings('CANCEL'),
        actionButton: tUserSettings('CONFIRM'),
      });

      if (isConfirmSubmit) {
        const formData = new FormData();
        formData.append('userDeleted', userId);
        submit(formData, { method: 'post' });
      }
    },
    [confirm, submit, tUserSettings],
  );

  const onSubmitChangeStatusMember = () => {
    const formViewData = new FormData();

    formViewData.append('actionName', 'edit');

    formViewData.append('status', memberDetail?.status || '');
    formViewData.append('userId', memberDetail?._id || '');

    submit(formViewData, { method: 'post' });
  };

  const columns: ColumnDef<
    GroupDetail['group']['users'][0] & { addedAt: Date }
  >[] = useMemo(
    () => [
      {
        accessorKey: 'name',
        header: ({ column }) => (
          <DataTableColumnHeader
            column={column}
            title={tUserSettings('MEMBER_NAME')}
          />
        ),
        cell: ({ row }) => (
          <Label className="text-sm font-normal text-gray-600">
            {row.original?.name || ''}
          </Label>
        ),
        enableSorting: false,
      },
      {
        accessorKey: 'email',
        header: ({ column }) => (
          <DataTableColumnHeader
            column={column}
            title={tUserSettings('EMAIL')}
          />
        ),
        cell: ({ row }) => (
          <Label className="text-sm font-normal text-gray-600">
            {row.original?.email || ''}
          </Label>
        ),
        enableSorting: false,
      },
      {
        accessorKey: 'addedAt',
        header: ({ column }) => (
          <DataTableColumnHeader
            column={column}
            title={tUserSettings('ADDED_DATE')}
          />
        ),
        cell: ({ row }) => format(row.original?.addedAt, 'dd/MM/yyyy'),
        enableSorting: false,
      },
      {
        accessorKey: 'status',
        header: ({ column }) => (
          <DataTableColumnHeader
            column={column}
            title={tUserSettings('STATUS')}
          />
        ),
        cell: ({ row }) => (
          <StatusBadge
            statusClasses={{
              ACTIVE: 'bg-blue-50 text-blue-500 rounded-full text-center',
              INACTIVE: 'bg-gray-50 text-gray-500 rounded-full text-center',
            }}
            status={row?.original?.status}
          />
        ),
        size: 74,
        enableSorting: false,
      },
      {
        accessorKey: 'action',
        header: ({ column }) => (
          <DataTableColumnHeader
            column={column}
            className="text-center"
            title={tUserSettings('ACTION')}
          />
        ),
        cell: ({ row }) => (
          <DropdownMenu>
            <DropdownMenuTrigger asChild>
              <Button
                variant="ghost"
                className="mx-auto flex h-8 w-8 p-0 data-[state=open]:bg-muted"
                onClick={e => {
                  e.stopPropagation();
                }}>
                <DotsHorizontalIcon className="h-4 w-4" />
              </Button>
            </DropdownMenuTrigger>
            <DropdownMenuContent align="end" className="w-[140px]">
              <DropdownMenuItem
                disabled={
                  loaderData?.groupDetail?.status === GROUP_STATUS.INACTIVE
                }
                onClick={e => {
                  if (
                    loaderData?.groupDetail?.status === GROUP_STATUS.INACTIVE
                  ) {
                    e.preventDefault();
                    return;
                  }
                  e.stopPropagation();
                  setMemberDetail(row.original);
                  setOpenEditDialog(true);
                }}>
                {tUserSettings('EDIT')}
              </DropdownMenuItem>
              <DropdownMenuSeparator />
              <DropdownMenuItem
                disabled={
                  loaderData?.groupDetail?.status === GROUP_STATUS.INACTIVE
                }
                onClick={e => {
                  if (
                    loaderData?.groupDetail?.status === GROUP_STATUS.INACTIVE
                  ) {
                    e.preventDefault();
                    return;
                  }
                  e.stopPropagation();
                  onDeleteMember(row.original._id);
                }}
                className="text-red-500">
                {tUserSettings('DELETE')}
              </DropdownMenuItem>
            </DropdownMenuContent>
          </DropdownMenu>
        ),
        size: 44,
        enableSorting: false,
      },
    ],
    [loaderData?.groupDetail?.status, onDeleteMember, tUserSettings],
  );

  return (
    <div className="mt-4">
      <BTaskeeTable
        disableViewOptions
        total={loaderData.groupDetail?.users?.length || 0}
        data={
          loaderData.groupDetail?.users.map(user => ({
            ...user,
            createdAt: momentTz(user.createdAt).toDate(),
            addedAt: momentTz(user.addedAt).toDate(),
          })) || []
        }
        columns={columns}
        toolbarAction={
          <div>
            <Button
              type="button"
              className="gap-2 border border-primary bg-white text-primary hover:bg-primary hover:text-white"
              key="add-member"
              disabled={
                loaderData?.groupDetail?.status === GROUP_STATUS.INACTIVE
              }
              onClick={() => {
                navigate(
                  `${ROUTE_NAME.GROUP_SETTING}/${params.id}/member-list/add-member-dialog`,
                  {
                    replace: true,
                    preventScrollReset: true,
                  },
                );
              }}>
              <Plus />
              {tUserSettings('ADD')}
            </Button>
          </div>
        }
        search={{
          defaultValue: searchParams.get('search') || '',
          name: 'search',
        }}
        filters={[
          {
            name: 'status',
            placeholder: tUserSettings('STATUS'),
            options: [
              {
                label: tUserSettings(STATUS.ACTIVE),
                value: STATUS.ACTIVE,
              },
              {
                label: tUserSettings(STATUS.INACTIVE),
                value: STATUS.INACTIVE,
              },
            ],
            value: searchParams.get('status') || '',
          },
        ]}
        onClickRow={member => {
          if (loaderData?.groupDetail?.status === GROUP_STATUS.ACTIVE) {
            setMemberDetail(member);
            setOpenDialogDetailMember(true);
          }
        }}
      />

      <Dialog
        open={openDialogDetailMember}
        onOpenChange={setOpenDialogDetailMember}>
        <DialogContent
          className="max-h-[90vh] max-w-[548px] overflow-y-auto"
          onInteractOutside={() => setOpenDialogDetailMember(false)}>
          <DialogHeader className="space-y-2">
            <DialogTitle className="text-2xl font-semibold tracking-tighter">
              {tUserSettings('MEMBER_DETAILS')}
            </DialogTitle>
            <DialogDescription>
              {tUserSettings('MEMBER_DETAILS_DESCRIPTION')}
            </DialogDescription>
          </DialogHeader>

          <div className="grid gap-3">
            <Label className="font-medium text-gray-500">
              {tUserSettings('MEMBER_NAME')}
            </Label>
            <Input
              className="bg-gray-300 font-normal text-gray-600"
              value={memberDetail?.name || ''}
            />
            <Label className="font-medium text-gray-500">
              {tUserSettings('EMAIL')}
            </Label>
            <Input
              className="bg-gray-300 font-normal text-gray-600"
              value={memberDetail?.email}
            />
            <Label className="font-medium text-[#0F172A]">
              {tUserSettings('STATUS')}
            </Label>
            <SelectBase
              value={memberDetail?.status ?? ''}
              onValueChange={value => {
                if (
                  memberDetail &&
                  (value === STATUS.ACTIVE || value === STATUS.INACTIVE)
                ) {
                  setMemberDetail({
                    ...memberDetail,
                    status: value,
                  });
                }
              }}
              options={[
                {
                  label: tUserSettings(STATUS.ACTIVE),
                  value: STATUS.ACTIVE,
                },
                {
                  label: tUserSettings(STATUS.INACTIVE),
                  value: STATUS.INACTIVE,
                },
              ]}
            />

            <div className="flex justify-end gap-4 pt-12">
              <Button
                className="border-primary text-primary hover:bg-primary-foreground hover:text-primary"
                type="button"
                variant="outline"
                onClick={() => {
                  setOpenDialogDetailMember(false);
                  setMemberDetail(undefined);
                }}>
                {tUserSettings('CANCEL')}
              </Button>
              <form
                onSubmit={formDetailMember.handleSubmit(
                  onSubmitChangeStatusMember,
                )}>
                <Button type="submit">{tUserSettings('SAVE_CHANGES')}</Button>
              </form>
            </div>
          </div>
        </DialogContent>
      </Dialog>

      <Dialog open={openEditDialog} onOpenChange={setOpenEditDialog}>
        <DialogContent className="max-h-[90vh] max-w-[548px] overflow-y-auto">
          <DialogHeader className="space-y-2">
            <DialogTitle className="text-2xl font-semibold tracking-tighter">
              {tUserSettings('EDIT_MEMBER')}
            </DialogTitle>
            <DialogDescription>
              {tUserSettings('EDIT_MEMBER_DESCRIPTION')}
            </DialogDescription>
          </DialogHeader>
          <div className="grid gap-3">
            <Label className="font-medium text-[#0F172A]">
              {tUserSettings('STATUS')}
            </Label>
            <SelectBase
              value={memberDetail?.status ?? ''}
              onValueChange={value => {
                if (
                  memberDetail &&
                  (value === STATUS.ACTIVE || value === STATUS.INACTIVE)
                ) {
                  setMemberDetail({
                    ...memberDetail,
                    status: value,
                  });
                }
              }}
              options={[
                {
                  label: tUserSettings(STATUS.ACTIVE),
                  value: STATUS.ACTIVE,
                },
                {
                  label: tUserSettings(STATUS.INACTIVE),
                  value: STATUS.INACTIVE,
                },
              ]}
            />

            <div className="flex justify-end gap-4 pt-12">
              <Button
                className="border-primary text-primary hover:bg-primary-foreground hover:text-primary"
                type="button"
                variant="outline"
                onClick={() => {
                  setOpenEditDialog(false);
                  setMemberDetail(undefined);
                }}>
                {tUserSettings('CANCEL')}
              </Button>
              <form
                onSubmit={editForm.handleSubmit(onSubmitChangeStatusMember)}>
                <Button type="submit">{tUserSettings('SAVE_CHANGES')}</Button>
              </form>
            </div>
          </div>
        </DialogContent>
      </Dialog>

      <Outlet
        context={{
          openDialogAddMember,
          setOpenDialogAddMember,
          openDialogAddAndCreateMember,
          setOpenDialogAddAndCreateMember,
          onClose: () => {
            return navigate(
              `${ROUTE_NAME.GROUP_SETTING}/${params.id}/member-list`,
              {
                replace: true,
                preventScrollReset: true,
              },
            );
          },
          groupName: outletData.group.name,
        }}
      />
    </div>
  );
}
