import { Link, Outlet, useLocation } from '@remix-run/react';
import { PERMISSIONS, ROUTE_NAME } from 'btaskee-constants';
import { useGlobalStore } from 'btaskee-hooks';
import {
  BreadcrumbsLink,
  Separator,
  Typography,
  buttonVariants,
  cn,
} from 'btaskee-ui';
import { User } from 'lucide-react';
import { useMemo } from 'react';
import { useTranslation } from 'react-i18next';

export const handle = {
  breadcrumb: () => (
    <BreadcrumbsLink
      to={ROUTE_NAME.CONFIGURATION_PERSONAL_PAGE_MANAGEMENT}
      label="CONFIGURATION"
    />
  ),
  i18n: 'configuration',
};

export default function Screen() {
  const permissions = useGlobalStore(state => state.permissions);
  const { pathname } = useLocation();
  const { t } = useTranslation('configuration');

  const navigation = useMemo(
    () => [
      {
        title: 'PERSONAL_PAGE_MANAGEMENT',
        href: ROUTE_NAME.CONFIGURATION_PERSONAL_PAGE_MANAGEMENT,
        permission: PERMISSIONS.READ_GROUP,
        icon: <User className="h-6 w-6" />,
      },
    ],
    [],
  );

  return (
    <>
      <Typography variant="h2">{t('CONFIGURATION')}</Typography>
      <Separator className="my-6" />
      <div className="flex h-full flex-col lg:flex-row">
        <aside className="-mb-6 bg-gray-50 lg:w-1/4 2xl:h-[calc(100vh-176px)]">
          <nav
            className={cn(
              'flex space-x-2 p-4 lg:flex-col lg:space-x-0 lg:space-y-1',
            )}>
            {navigation.map(item =>
              !item.permission || permissions.includes(item.permission) ? (
                <Link
                  key={item.href}
                  to={item.href}
                  className={cn(
                    buttonVariants({ variant: 'ghost' }),
                    pathname.includes(item.href)
                      ? 'bg-primary-50 font-medium text-primary hover:bg-primary-50 hover:text-primary'
                      : 'font-normal text-gray hover:bg-primary-50',
                    'justify-start gap-2 text-base',
                  )}>
                  {item?.icon}
                  {t(item.title)}
                </Link>
              ) : null,
            )}
          </nav>
        </aside>
        <div className="flex-1 pl-6 pt-6">
          <Outlet />
        </div>
      </div>
    </>
  );
}
