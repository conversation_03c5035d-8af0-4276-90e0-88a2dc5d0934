import { json } from '@remix-run/node';
import {
  isRouteErrorResponse,
  useActionData,
  useNavigate,
  useRouteError,
  useSubmit,
} from '@remix-run/react';
import {
  ACTION_NAME,
  GROUP_STATUS,
  PERMISSIONS,
  ROUTE_NAME,
} from 'btaskee-constants';
import { useLoaderDataSafely } from 'btaskee-hooks';
import {
  ActivityLogo,
  Badge,
  Breadcrumbs,
  BtaskeeResponseError,
  Button,
  Card,
  CardContent,
  CardHeader,
  Dialog,
  DialogClose,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
  GraphLogo,
  Grid,
  HeartLogo,
  Input,
  Label,
  PermissionEditIcon,
  Popover,
  PopoverContent,
  PopoverTrigger,
  SelectBase,
  SendLogo,
  Separator,
  StarLogo,
  StatusBadge,
  Switch,
  Textarea,
  Typography,
  VectorEmptyDataTable,
  cn,
  toast,
  useConfirm,
} from 'btaskee-ui';
import { createUID } from 'btaskee-utils';
import { format } from 'date-fns';
import { ChevronDown, Plus, Users } from 'lucide-react';
import { useEffect, useState } from 'react';
import { useForm } from 'react-hook-form';
import { useTranslation } from 'react-i18next';
import { hoc404, hocAction, hocLoader } from '~/hoc/remix';
import { getUserId, getUserSession } from '~/services/helpers.server';
import {
  createGroup,
  getGroupsForTreeViewByUserId,
  getGroupsOfUser,
  updateGroupInformation,
} from '~/services/role-base-access-control.server';
import { getUserProfile } from '~/services/settings.server';

const iconType = [
  <ActivityLogo key="activity" className="h-11 w-11" />,
  <GraphLogo key="graph" className="h-11 w-11" />,
  <HeartLogo key="heart" className="h-11 w-11" />,
  <SendLogo key="send" className="h-11 w-11" />,
  <StarLogo key="star" className="h-11 w-11" />,
];

export const loader = hocLoader(async ({ request }) => {
  const userId = await getUserId({ request });

  const [userProfile, groups, groupOfUser] = await Promise.all([
    hoc404(() => getUserProfile(userId)),
    getGroupsForTreeViewByUserId(userId),
    getGroupsOfUser({
      userId,
      projection: { name: 1 },
    }),
  ]);

  return json({
    groupOfUser,
    groups,
    userProfile,
  });
});

export const action = hocAction(
  async ({ request }, { setInformationActionHistory }) => {
    const formData = await request.clone().formData();
    const { userId } = await getUserSession(request);

    const actionName = formData.get('actionName')?.toString() || '';
    if (actionName === 'create') {
      const name = formData.get('name')?.toString() || '';
      const description = formData.get('description')?.toString() || '';
      const status = formData.get('status')?.toString() || '';
      const parentId = formData.get('parentId')?.toString() || '';

      await createGroup({
        name,
        description,
        parentId,
        status,
        userId,
      });
      setInformationActionHistory({
        action: ACTION_NAME.CREATE_GROUP,
      });

      return json({ message: 'CREATE_GROUP_SUCCESSFULLY' });
    }

    if (actionName === 'update') {
      const name = formData.get('name')?.toString() || '';
      const description = formData.get('description')?.toString() || '';
      const status = formData.get('status')?.toString() || '';
      const groupId = formData.get('groupId')?.toString() || '';

      await updateGroupInformation({
        name,
        description,
        groupId,
        status,
      });

      setInformationActionHistory({
        action: ACTION_NAME.UPDATE_GROUP,
      });

      return json({ message: 'UPDATE_GROUP_SUCCESSFULLY' });
    }
  },
  PERMISSIONS.WRITE_GROUP,
);

export function ErrorBoundary() {
  const error = useRouteError() as { message?: string };
  const { t: tCommon } = useTranslation('common');

  if (isRouteErrorResponse(error)) {
    if (error?.message) {
      toast({ description: JSON.stringify(error.message) });
    }

    return <BtaskeeResponseError t={tCommon} errorStatus={error.status} />;
  }

  return <BtaskeeResponseError t={tCommon} errorStatus={500} />;
}

export default function Screen() {
  const { t: tUserSettings } = useTranslation('user-settings');
  const actionData = useActionData<ActionTypeWithError<typeof action>>();
  const { groups, userProfile, groupOfUser } =
    useLoaderDataSafely<typeof loader>();

  const [openDialogEditGroup, setOpenDialogEditGroup] = useState(false);
  const [selectedGroup, setSelectedGroup] =
    useState<Pick<Groups, '_id' | 'name' | 'description' | 'status'>>();

  function handleCloseDialogEditGroup() {
    setOpenDialogEditGroup(false);
    formEditGroup.reset();
  }

  const navigate = useNavigate();

  const formEditGroup = useForm({
    defaultValues: {
      name: selectedGroup?.name || '',
      description: selectedGroup?.description || '',
      status: selectedGroup?.status || 'ACTIVE',
    },
  });

  const { control: controlEditGroup } = formEditGroup;

  const formCreateGroup = useForm({
    defaultValues: {
      name: '',
      description: '',
      parentId: '',
      status: GROUP_STATUS.ACTIVE,
    },
  });

  const { control: controlCreateGroup } = formCreateGroup;

  useEffect(() => {
    if (actionData?.error) {
      toast({
        variant: 'error',
        description: actionData.error,
      });
    }
    if (actionData?.message) {
      toast({
        variant: 'success',
        description: tUserSettings(actionData.message),
      });
    }
  }, [actionData, formCreateGroup, formEditGroup, tUserSettings]);

  useEffect(() => {
    if (selectedGroup) {
      formEditGroup.setValue('name', selectedGroup.name);
      formEditGroup.setValue('description', selectedGroup.description);
      formEditGroup.setValue('status', selectedGroup.status);
    }
  }, [formEditGroup, selectedGroup]);

  const [openDialogCreateGroup, setOpenDialogCreateGroup] = useState(false);

  function handleCloseDialogCreateGroup() {
    setOpenDialogCreateGroup(false);
    formCreateGroup.reset();
  }

  const confirm = useConfirm();
  const submit = useSubmit();

  const onSubmit = async (data: MustBeAny) => {
    const formData = new FormData();

    formData.append('actionName', 'update');

    formData.append('status', data.status);
    formData.append('name', data.name);
    formData.append('description', data.description);
    formData.append('groupId', selectedGroup?._id || '');

    const isConfirm = await confirm({
      title: tUserSettings('UPDATE_GROUP'),
      body: tUserSettings('ARE_YOU_SURE_DO_THAT'),
      cancelButton: tUserSettings('CANCEL'),
      actionButton: tUserSettings('CONFIRM'),
    });

    if (isConfirm) {
      submit(formData, { method: 'post' });
    }

    handleCloseDialogEditGroup();
  };

  const onCreate = async (data: MustBeAny) => {
    const formData = new FormData();
    formData.append('actionName', 'create');

    formData.append('status', data.status);
    formData.append('name', data.name);
    formData.append('description', data.description);
    formData.append('parentId', data.parentId);

    const isConfirm = await confirm({
      title: tUserSettings('CREATE_GROUP'),
      body: tUserSettings('ARE_YOU_SURE_DO_THAT'),
      cancelButton: tUserSettings('CANCEL'),
      actionButton: tUserSettings('CONFIRM'),
    });

    if (isConfirm) {
      submit(formData, { method: 'post' });
    }

    handleCloseDialogCreateGroup();
  };

  return (
    <div className="flex h-full flex-1 flex-col space-y-8">
      <div className="flex items-center justify-between rounded-md bg-secondary p-4">
        <Grid>
          <Typography variant="h3">{tUserSettings('GROUPS')}</Typography>
          <Breadcrumbs />
        </Grid>

        <Button
          className="gap-2"
          onClick={() => setOpenDialogCreateGroup(true)}>
          <Plus />
          {tUserSettings('CREATE')}
        </Button>
      </div>
      {groups?.length ? (
        <div className="grid grid-cols-2 gap-8">
          {groups.map((group, index: number) => {
            return (
              <Card
                key={index}
                onClick={() => {
                  navigate(
                    `${ROUTE_NAME.GROUP_SETTING}/${group?._id}/member-list`,
                  );
                }}
                className="h-fit cursor-pointer border-none bg-white !shadow-group-card">
                <CardHeader className="flex-col gap-4 p-4">
                  <div className="flex items-center justify-between gap-4">
                    <div className="flex items-center gap-4">
                      <div className="flex h-11 w-11 items-center justify-center">
                        {iconType[group?.iconType || 0]}
                      </div>
                      <Grid className="gap-1">
                        <Typography
                          variant="h4"
                          className="line-clamp-1 cursor-pointer whitespace-normal break-all text-lg hover:text-primary">
                          {group?.name}
                        </Typography>
                        {group.parentGroupName ? (
                          <Typography
                            variant="p"
                            affects="removePMargin"
                            className="line-clamp-1 break-all font-normal text-gray-500">
                            {tUserSettings('SUB_GROUP_OF')}{' '}
                            <span className="text-sm font-semibold text-primary">
                              {group.parentGroupName}
                            </span>
                          </Typography>
                        ) : null}
                      </Grid>
                    </div>

                    <Button
                      size="icon"
                      className="h-fit w-fit bg-gray-100"
                      onClick={event => {
                        event.stopPropagation();
                        setSelectedGroup({
                          _id: group?._id,
                          name: group?.name || '',
                          description: group?.description || '',
                          status: group?.status,
                        });
                        setOpenDialogEditGroup(true);
                      }}
                      variant="ghost">
                      <PermissionEditIcon />
                    </Button>
                  </div>
                  <div className="flex h-[37px] items-center">
                    {group?.description ? (
                      <Typography
                        className="line-clamp-2 whitespace-normal break-all font-normal text-gray-500"
                        variant="p"
                        affects="removePMargin">
                        {group.description}
                      </Typography>
                    ) : null}
                  </div>
                  <div className="flex justify-between">
                    {group?.createdAt ? (
                      <Badge className="text-normal h-[26px] rounded-[6px] bg-gray-100 px-2 py-1 text-sm font-normal text-black">
                        {format(group.createdAt, 'dd/MM/yyyy')}
                      </Badge>
                    ) : (
                      ''
                    )}
                    <StatusBadge status={group?.status} />
                  </div>
                </CardHeader>
                <Separator
                  className="mx-auto"
                  style={{ width: 'calc(100% - 32px)' }}
                />
                <CardContent className="flex items-center justify-between p-4">
                  <div className="flex items-center gap-2">
                    <Popover>
                      <PopoverTrigger
                        asChild
                        onClick={event => {
                          event.stopPropagation();
                        }}>
                        {group?.children?.length > 0 ? (
                          <button className="flex h-[26px] w-[80px] items-center gap-2 rounded-[6px] bg-[#F5F5F5] px-2 py-[5px] text-sm">
                            <svg
                              width="16"
                              height="16"
                              viewBox="0 0 16 16"
                              fill="none"
                              xmlns="http://www.w3.org/2000/svg">
                              <g clipPath="url(#clip0_9250_17943)">
                                <path
                                  d="M7.3535 3.34644V2.49414C7.35307 2.09645 7.19489 1.71517 6.91368 1.43396C6.63247 1.15275 6.25119 0.994575 5.8535 0.994141H2C1.60231 0.994575 1.22103 1.15275 0.939819 1.43396C0.658609 1.71517 0.500434 2.09645 0.5 2.49414V3.34644C0.500434 3.74413 0.658609 4.12541 0.939819 4.40662C1.22103 4.68783 1.60231 4.846 2 4.84644H5.8535C6.25119 4.846 6.63247 4.68783 6.91368 4.40662C7.19489 4.12541 7.35307 3.74413 7.3535 3.34644ZM1.5 3.34644V2.49414C1.50013 2.36157 1.55285 2.23447 1.64659 2.14073C1.74033 2.04699 1.86743 1.99427 2 1.99414H5.8535C5.98607 1.99427 6.11317 2.04699 6.20691 2.14073C6.30065 2.23447 6.35337 2.36157 6.3535 2.49414V3.34644C6.35337 3.479 6.30065 3.6061 6.20691 3.69984C6.11317 3.79358 5.98607 3.8463 5.8535 3.84644H2C1.86743 3.8463 1.74033 3.79358 1.64659 3.69984C1.55285 3.6061 1.50013 3.479 1.5 3.34644Z"
                                  fill="black"
                                />
                                <path
                                  d="M14 5.16992H10.1465C9.74879 5.17036 9.36751 5.32853 9.0863 5.60974C8.80509 5.89095 8.64692 6.27223 8.64648 6.66992V7.52224C8.64692 7.91993 8.80509 8.30121 9.0863 8.58242C9.36751 8.86363 9.74879 9.02181 10.1465 9.02224H14C14.3977 9.02181 14.779 8.86363 15.0602 8.58242C15.3414 8.30121 15.4996 7.91993 15.5 7.52224V6.66992C15.4996 6.27223 15.3414 5.89095 15.0602 5.60974C14.779 5.32853 14.3977 5.17036 14 5.16992ZM14.5 7.52224C14.4999 7.65481 14.4471 7.78191 14.3534 7.87565C14.2597 7.96939 14.1326 8.02211 14 8.02224H10.1465C10.0139 8.02211 9.88682 7.96939 9.79308 7.87565C9.69934 7.78191 9.64662 7.65481 9.64648 7.52224V6.66992C9.64662 6.53735 9.69934 6.41025 9.79308 6.31651C9.88682 6.22278 10.0139 6.17005 10.1465 6.16992H14C14.1326 6.17005 14.2597 6.22278 14.3534 6.31651C14.4471 6.41025 14.4999 6.53735 14.5 6.66992V7.52224Z"
                                  fill="black"
                                />
                                <path
                                  d="M14 11.1543H10.1465C9.74879 11.1547 9.36751 11.3129 9.0863 11.5941C8.80509 11.8753 8.64692 12.2566 8.64648 12.6543V13.5063C8.64692 13.904 8.80509 14.2853 9.0863 14.5665C9.36751 14.8477 9.74879 15.0059 10.1465 15.0063H14C14.3977 15.0059 14.779 14.8477 15.0602 14.5665C15.3414 14.2853 15.4996 13.904 15.5 13.5063V12.6543C15.4996 12.2566 15.3414 11.8753 15.0602 11.5941C14.779 11.3129 14.3977 11.1547 14 11.1543ZM14.5 13.5063C14.4999 13.6389 14.4472 13.7661 14.3534 13.8598C14.2597 13.9536 14.1326 14.0063 14 14.0063H10.1465C10.0139 14.0063 9.88678 13.9536 9.79303 13.8598C9.69928 13.7661 9.64657 13.6389 9.64648 13.5063V12.6543C9.64657 12.5217 9.69928 12.3946 9.79303 12.3008C9.88678 12.2071 10.0139 12.1544 10.1465 12.1543H14C14.1326 12.1544 14.2597 12.2071 14.3534 12.3008C14.4472 12.3946 14.4999 12.5217 14.5 12.6543V13.5063Z"
                                  fill="black"
                                />
                                <path
                                  d="M7.49316 7.59644C7.62577 7.59644 7.75295 7.54376 7.84672 7.44999C7.94049 7.35622 7.99316 7.22905 7.99316 7.09644C7.99316 6.96383 7.94049 6.83665 7.84672 6.74289C7.75295 6.64912 7.62577 6.59644 7.49316 6.59644H4.42773V5.90234C4.42773 5.76974 4.37506 5.64256 4.28129 5.54879C4.18752 5.45502 4.06034 5.40234 3.92773 5.40234C3.79513 5.40234 3.66795 5.45502 3.57418 5.54879C3.48041 5.64256 3.42773 5.76974 3.42773 5.90234V11.5137C3.42835 12.0614 3.64621 12.5865 4.03351 12.9738C4.42081 13.3611 4.94592 13.579 5.49365 13.5796H7.49218C7.62479 13.5796 7.75197 13.5269 7.84574 13.4331C7.93951 13.3394 7.99218 13.2122 7.99218 13.0796C7.99218 12.947 7.93951 12.8198 7.84574 12.726C7.75197 12.6323 7.62479 12.5796 7.49218 12.5796H5.49365C5.21103 12.5793 4.94007 12.4669 4.74023 12.2671C4.54039 12.0673 4.42801 11.7963 4.42773 11.5137V7.59644H7.49316Z"
                                  fill="black"
                                />
                              </g>
                              <defs>
                                <clipPath id="clip0_9250_17943">
                                  <rect width="16" height="16" fill="white" />
                                </clipPath>
                              </defs>
                            </svg>
                            {group?.children?.length}
                            <ChevronDown className="ml-[7px] h-4 w-4" />
                          </button>
                        ) : null}
                      </PopoverTrigger>
                      <PopoverContent
                        onClick={event => {
                          event.stopPropagation();
                        }}
                        className="grid w-[267px] gap-2 p-4 max-h-40 overflow-y-auto"
                        align="start">
                        {group?.children?.map(child => (
                          <div
                            key={createUID()}
                            className={cn(
                              'cursor-pointer gap-2 rounded-[6px] p-4 text-base font-medium',
                              child.status === GROUP_STATUS.INACTIVE
                                ? 'bg-red-50'
                                : 'bg-green-50',
                            )}>
                            <div className="flex items-center gap-2">
                              <div className="flex h-8 w-8 flex-shrink-0 items-center justify-center">
                                {iconType[group?.iconType || 0]}
                              </div>
                              <p className="line-clamp-1 break-all">
                                {child.name}
                              </p>
                            </div>
                            <div className="flex items-center gap-2 text-base">
                              <div className="h-8 w-8 flex-shrink-0">
                                <Users
                                  width={18}
                                  height={18}
                                  className="m-auto mt-2"
                                />
                              </div>
                              {child.users?.length || 0}
                            </div>
                          </div>
                        ))}
                      </PopoverContent>
                    </Popover>
                    <div className="flex h-[26px] w-[57px] items-center gap-2 rounded-[6px] bg-gray-100 px-2 py-1">
                      <Users className="h-4 w-4" />
                      <Typography
                        className="text-black"
                        affects="removePMargin"
                        variant="p">
                        {group?.users?.length || 0}
                      </Typography>
                    </div>
                  </div>
                  <Typography
                    variant="p"
                    affects="removePMargin"
                    className="rounded-[6px] bg-gray-100 px-2 py-1">
                    {userProfile?.name || userProfile?.username || ''}
                  </Typography>
                </CardContent>
              </Card>
            );
          })}
        </div>
      ) : (
        <div className="!mt-[116px] flex justify-center">
          <VectorEmptyDataTable width="272px" height="244px" />
        </div>
      )}

      <Dialog
        open={openDialogCreateGroup}
        onOpenChange={() => {
          setOpenDialogCreateGroup(false);
        }}
        defaultOpen={true}>
        <DialogContent className="max-h-[90vh] w-[548px]">
          <DialogHeader className="space-y-2">
            <DialogTitle className="text-xl font-semibold tracking-tighter">
              {tUserSettings('CREATE_NEW_GROUP')}
            </DialogTitle>
            <DialogDescription className="text-sm font-normal text-gray">
              {tUserSettings('CREATE_NEW_GROUP_HELPER_TEXT')}
            </DialogDescription>
          </DialogHeader>

          <Form {...formCreateGroup}>
            <form
              className="space-y-2"
              onSubmit={formCreateGroup.handleSubmit(onCreate)}>
              <FormField
                control={controlCreateGroup}
                name="name"
                rules={{
                  required: tUserSettings('THIS_FIELD_IS_REQUIRED'),
                  maxLength: {
                    value: 200,
                    message: tUserSettings('NAME_MAX_LENGTH_EXCEEDED'),
                  },
                }}
                render={({ field }) => (
                  <FormItem>
                    <FormLabel className="text-gray-700">
                      {tUserSettings('GROUP_NAME')}
                    </FormLabel>
                    <FormControl>
                      <Input
                        placeholder={tUserSettings('ENTER_GROUP_NAME')}
                        {...field}
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              {groupOfUser?.length > 1 ? (
                <FormField
                  control={controlCreateGroup}
                  name="parentId"
                  rules={{
                    required: tUserSettings('THIS_FIELD_IS_REQUIRED'),
                  }}
                  render={({ field: { onChange, value } }) => (
                    <FormItem>
                      <FormLabel className="text-gray-700">
                        {tUserSettings('PARENT_GROUP')}
                      </FormLabel>
                      <FormControl>
                        <SelectBase
                          placeholder={tUserSettings('SELECT_PARENT_GROUP')}
                          onValueChange={onChange}
                          defaultValue={value}
                          options={groupOfUser?.map(group => ({
                            label: group?.name,
                            value: group?._id,
                          }))}
                        />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
              ) : null}

              <FormField
                control={controlCreateGroup}
                name="description"
                render={({ field: { onChange, value } }) => (
                  <FormItem>
                    <FormLabel className="text-gray-700">
                      {tUserSettings('DESCRIPTION')}
                    </FormLabel>
                    <FormControl>
                      <Textarea
                        onChange={onChange}
                        value={value}
                        rows={6}
                        placeholder={tUserSettings('ENTER_DESCRIPTION')}
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={controlCreateGroup}
                name="status"
                render={({ field: { onChange, value } }) => (
                  <FormItem>
                    <FormControl>
                      <div className="mt-4 flex flex-row items-center gap-6">
                        <Label className="text-gray-500">
                          {tUserSettings('STATUS')}
                        </Label>

                        <div className="flex flex-row items-center gap-2">
                          <Switch
                            checked={value === 'ACTIVE'}
                            onCheckedChange={checked =>
                              onChange(checked ? 'ACTIVE' : 'INACTIVE')
                            }
                          />
                          <Label className="text-gray-800">
                            {tUserSettings('ACTIVE')}
                          </Label>
                        </div>
                      </div>
                    </FormControl>
                  </FormItem>
                )}
              />

              <div className="flex justify-end gap-4 pt-12">
                <Button
                  className="border-primary text-primary hover:bg-primary-foreground hover:text-primary"
                  type="button"
                  variant="outline"
                  onClick={handleCloseDialogCreateGroup}>
                  {tUserSettings('CANCEL')}
                </Button>
                <Button variant="default" type="submit">
                  {tUserSettings('CONFIRM')}
                </Button>
              </div>
            </form>
          </Form>
        </DialogContent>

        <DialogClose className="absolute right-4 top-4 z-10 h-5 w-5 cursor-default rounded-sm bg-white" />
      </Dialog>

      <Dialog
        open={openDialogEditGroup}
        onOpenChange={() => {
          setOpenDialogEditGroup(false);
        }}
        defaultOpen={true}>
        <DialogContent className="max-h-[90vh] max-w-[548px] overflow-y-auto">
          <DialogHeader className="space-y-2">
            <DialogTitle className="text-xl font-semibold tracking-tighter">
              {tUserSettings('EDIT_GROUP_INFORMATION')}
            </DialogTitle>
            <DialogDescription className="text-sm font-normal text-gray">
              {tUserSettings('EDIT_GROUP_HELPER_TEXT')}
            </DialogDescription>
          </DialogHeader>

          <Form {...formEditGroup}>
            <form
              className="space-y-2"
              onSubmit={formEditGroup.handleSubmit(onSubmit)}>
              <FormField
                control={controlEditGroup}
                name="name"
                rules={{
                  required: tUserSettings('THIS_FIELD_IS_REQUIRED'),
                  maxLength: {
                    value: 200,
                    message: tUserSettings('NAME_MAX_LENGTH_EXCEEDED'),
                  },
                }}
                render={({ field }) => (
                  <FormItem>
                    <FormLabel className="text-gray-700">
                      {tUserSettings('GROUP_NAME')}
                    </FormLabel>
                    <FormControl>
                      <Input
                        placeholder={tUserSettings('ENTER_GROUP_NAME')}
                        {...field}
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={controlEditGroup}
                name="description"
                render={({ field: { onChange, value } }) => (
                  <FormItem>
                    <FormLabel className="text-gray-700">
                      {tUserSettings('DESCRIPTION')}
                    </FormLabel>
                    <FormControl>
                      <Textarea
                        onChange={onChange}
                        value={value}
                        rows={6}
                        placeholder={tUserSettings('ENTER_DESCRIPTION')}
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={controlEditGroup}
                name="status"
                render={({ field: { onChange, value } }) => (
                  <FormItem>
                    <FormControl>
                      <div className="mt-4 flex flex-row items-center gap-6">
                        <Label className="text-gray-500">
                          {tUserSettings('STATUS')}
                        </Label>

                        <div className="flex flex-row items-center gap-2">
                          <Switch
                            checked={value === 'ACTIVE'}
                            onCheckedChange={checked =>
                              onChange(checked ? 'ACTIVE' : 'INACTIVE')
                            }
                          />
                          <Label className="text-gray-800">
                            {tUserSettings('ACTIVE')}
                          </Label>
                        </div>
                      </div>
                    </FormControl>
                  </FormItem>
                )}
              />

              <div className="flex justify-end gap-4 pt-12">
                <Button
                  className="border-primary text-primary hover:bg-primary-foreground hover:text-primary"
                  type="button"
                  variant="outline"
                  onClick={handleCloseDialogEditGroup}>
                  {tUserSettings('CANCEL')}
                </Button>
                <Button variant="default" type="submit">
                  {tUserSettings('CONFIRM')}
                </Button>
              </div>
            </form>
          </Form>
        </DialogContent>

        <DialogClose className="absolute right-4 top-4 z-10 h-5 w-5 cursor-default rounded-sm bg-white" />
      </Dialog>
    </div>
  );
}
