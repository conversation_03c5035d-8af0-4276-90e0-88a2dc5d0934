import { type LoaderFunctionArgs, json } from '@remix-run/node';
import { useLoaderData, useNavigate } from '@remix-run/react';
import { useCopyToClipboard } from 'btaskee-hooks';
import {
  ActivityLogo,
  Avatar,
  AvatarFallback,
  AvatarImage,
  Badge,
  Breadcrumbs,
  Button,
  Card,
  CardContent,
  CardDescription,
  CardFooter,
  CardTitle,
  GraphLogo,
  HeartLogo,
  Label,
  SendLogo,
  Separator,
  StarLogo,
  Typography,
  VectorEmptyDataTable,
} from 'btaskee-ui';
import { Camera, ChevronRight, Copy } from 'lucide-react';
import { useTranslation } from 'react-i18next';
import { getUserId } from '~/services/helpers.server';
import { getGroupsOfUser } from '~/services/role-base-access-control.server';
import { getUserProfile } from '~/services/settings.server';

const iconType = [
  <ActivityLogo key="activity" className="h-11 w-11" />,
  <GraphLogo key="graph" className="h-11 w-11" />,
  <HeartLogo key="heart" className="h-11 w-11" />,
  <SendLogo key="send" className="h-11 w-11" />,
  <StarLogo key="star" className="h-11 w-11" />,
];

export const loader = async ({ request }: LoaderFunctionArgs) => {
  const userId = await getUserId({ request });
  const userProfile = await getUserProfile(userId);
  const groupsWithProjectionByUser = await getGroupsOfUser({
    projection: { _id: 1, name: 1, icon: 1 },
    userId,
  });
  return json({ userProfile, groupsWithProjectionByUser });
};

export default function Screen() {
  const { t } = useTranslation('user-settings');
  const loaderData = useLoaderData<typeof loader>();
  const navigate = useNavigate();

  const { copyToClipboard: copyEmailToClipboard } = useCopyToClipboard();

  return (
    <div className="space-y-6">
      <div className="flex flex-col rounded-2xl bg-secondary p-4">
        <Typography variant="h3">{t('PROFILE')}</Typography>
        <Breadcrumbs />
      </div>

      <Card className="border-none p-4 !shadow-group-badge">
        <div className="flex items-center space-x-10">
          <div className="relative">
            <Avatar className="h-[110px] w-[110px]">
              <AvatarImage src={loaderData.userProfile?.avatarUrl} />
              <AvatarFallback>
                <svg
                  width="110"
                  height="110"
                  viewBox="0 0 110 110"
                  fill="none"
                  xmlns="http://www.w3.org/2000/svg">
                  <g clipPath="url(#clip0_10136_27632)">
                    <rect width="110" height="110" rx="55" fill="#FAFAFA" />
                    <path
                      d="M54.9909 19.9512C42.3345 19.9512 32.0742 30.2114 32.0742 42.8678C32.0742 55.5243 42.3345 65.7845 54.9909 65.7845C67.6473 65.7845 77.9076 55.5243 77.9076 42.8678C77.9076 30.2114 67.6473 19.9512 54.9909 19.9512ZM38.8062 71.8001C26.824 75.0749 18.3242 85.4937 18.3242 97.8678V107.034C18.3242 109.566 20.3762 111.618 22.9076 111.618H87.0742C89.6056 111.618 91.6576 109.566 91.6576 107.034V97.8678C91.6576 85.4937 83.1578 75.0749 71.1755 71.8001C70.1484 71.5192 69.1392 71.6489 68.168 72.0866C63.9719 73.9777 59.505 74.9512 54.9909 74.9512C50.4768 74.9512 46.0098 73.9777 41.8138 72.0866C40.8426 71.6489 39.8333 71.5192 38.8062 71.8001Z"
                      fill="#D4D4D4"
                    />
                  </g>
                  <defs>
                    <clipPath id="clip0_10136_27632">
                      <rect width="110" height="110" rx="55" fill="white" />
                    </clipPath>
                  </defs>
                </svg>
              </AvatarFallback>
            </Avatar>
            <div className="absolute bottom-[-4px] right-[17px] rounded-full bg-white p-1 shadow-md hover:cursor-pointer hover:bg-primary hover:text-white">
              <Camera className="h-4 w-4" />
            </div>
          </div>
          <div className="flex flex-col gap-6">
            <Typography variant="h4" className="font-semibold text-black">
              {loaderData.userProfile?.name ||
                loaderData.userProfile?.username ||
                ''}
            </Typography>
            <div className="grid grid-cols-2">
              <div className="grid gap-1">
                <Label className="font-normal text-gray-400">
                  {t('USERNAME')}
                </Label>
                <Typography
                  variant="h4"
                  affects="large"
                  className="text-gray-600">
                  {loaderData.userProfile?.username}
                </Typography>
              </div>
              <div className="grid gap-1">
                <Label className="font-normal text-gray-400">
                  {t('EMAIL')}
                </Label>
                <div className="flex items-center">
                  <Typography
                    variant="h4"
                    affects="large"
                    className="text-gray-600">
                    {loaderData.userProfile?.email}
                  </Typography>
                  <Button
                    variant="ghost"
                    className="text-orange-500"
                    onClick={() =>
                      copyEmailToClipboard(loaderData.userProfile?.email || '')
                    }>
                    <Copy />
                  </Button>
                </div>
              </div>
            </div>
          </div>
        </div>
      </Card>

      <div className="grid grid-cols-2 gap-6">
        <Card className="border-none !shadow-group-badge">
          <div className="grid p-6">
            <CardTitle className="text-lg font-semibold text-black">
              {t('AUTHORIZATION')}
            </CardTitle>
            <CardDescription className="mt-2 text-sm font-normal text-gray-400">
              {t('AUTHORIZATION_TEXT_HELPER')}
            </CardDescription>
            <Separator className="mt-4 w-[200px]" />
          </div>
          <CardContent className="grid gap-2">
            <Label className="font-normal text-gray-400">{t('CITY')}</Label>
            <div className="flex flex-wrap gap-2">
              {loaderData.userProfile?.cities?.map((city, index) => {
                return (
                  <Badge
                    className="rounded-md bg-blue-50 px-3 py-[6px] text-center text-sm font-normal text-blue"
                    key={index}>
                    {city}
                  </Badge>
                );
              })}
            </div>
          </CardContent>
        </Card>

        <Card className="border-none !shadow-group-badge">
          <div className="grid p-6">
            <CardTitle className="text-lg font-semibold text-black">
              {t('PERMISSION')}
            </CardTitle>
            <CardDescription className="mt-2 text-sm font-normal text-gray-400">
              {t('AUTHORIZATION_PERMISSION_TEXT_HELPER')}
            </CardDescription>
            <Separator className="mt-4 w-[200px]" />
          </div>
          {loaderData?.groupsWithProjectionByUser?.length ? (
            <>
              <CardContent className="grid gap-2">
                <Label className="font-normal text-gray-500">
                  {t('YOUR_GROUPS')}
                </Label>
                <div className="flex flex-wrap gap-2">
                  {loaderData.groupsWithProjectionByUser?.map(
                    (group, index) => {
                      return (
                        <Badge
                          className="rounded-md bg-gray-50 p-2 text-center text-sm font-normal text-black"
                          key={index}>
                          <div className="flex max-w-[195px] items-center gap-2">
                            <div className="h-11 w-11 flex-shrink-0">
                              {group?.iconType
                                ? iconType[group?.iconType]
                                : iconType[index % iconType.length]}
                            </div>
                            <Typography
                              className="truncate font-medium"
                              variant="p"
                              affects="removePMargin">
                              {group.name}
                            </Typography>
                          </div>
                        </Badge>
                      );
                    },
                  )}
                </div>
              </CardContent>
              <CardFooter>
                <Button
                  className="justify-start bg-primary-50 font-normal text-primary hover:bg-primary hover:text-white"
                  onClick={() => {
                    navigate('/settings/profile/list-permissions');
                  }}>
                  {t('VIEW_YOUR_PERMISSION_DETAILS')}
                  <ChevronRight />
                </Button>
              </CardFooter>
            </>
          ) : (
            <div className="flex flex-col items-center justify-center">
              <VectorEmptyDataTable height="132px" width="146px" />
              <Typography className="mt-4 text-sm font-normal leading-[18px] text-gray-400">
                {t('NO_ASSIGNED_GROUP')}
              </Typography>
            </div>
          )}
        </Card>
      </div>
    </div>
  );
}
