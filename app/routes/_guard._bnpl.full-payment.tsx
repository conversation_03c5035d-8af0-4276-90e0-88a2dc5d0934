import { Outlet, json, useLoaderData } from '@remix-run/react';
import { PERMISSIONS, ROUTE_NAME } from 'btaskee-constants';
import { BreadcrumbsLink } from 'btaskee-ui';
import {
  DEFAULT_RANGE_DATE_CURRENT_DAY,
  convertSortString,
  getPageSizeAndPageIndex,
  getSkipAndLimit,
} from 'btaskee-utils';
import { hocLoader } from '~/hoc/remix';
import { getFullPayment, getTotalFullPayment } from '~/services/bnpl.server';
import {
  getCities,
  getSettingCountryByIsoCode,
  getUserSession,
} from '~/services/helpers.server';
import { getServices } from '~/services/service.server';

export const handle = {
  breadcrumb: () => (
    <BreadcrumbsLink to={ROUTE_NAME.FULL_PAYMENT} label="FULL_PAYMENT" />
  ),
};

export const loader = hocLoader(async ({ request }) => {
  const { isoCode } = await getUserSession({ headers: request.headers });
  const url = new URL(request.url);

  const search = url.searchParams.get('search') || '';
  const rangeDate = url.searchParams.get('createdAt') || '';
  const service = url.searchParams.get('service') || '';
  const city = url.searchParams.get('city') || '';
  const sort = url.searchParams.get('sort') || '';

  const filterValue = {
    search,
    service,
    city,
    rangeDate: DEFAULT_RANGE_DATE_CURRENT_DAY(rangeDate),
  };

  const total = await getTotalFullPayment({
    isoCode,
    filter: filterValue,
  });

  const { limit, skip } = getSkipAndLimit(
    getPageSizeAndPageIndex({
      total,
      pageSize: Number(url.searchParams.get('pageSize')) || 0,
      pageIndex: Number(url.searchParams.get('pageIndex')) || 0,
    }),
  );

  const [fullPayment, settingCountry, services, cities] = await Promise.all([
    getFullPayment({
      isoCode,
      filter: filterValue,
      limit,
      skip,
      sort: convertSortString({
        sortString: sort,
        defaultValue: { createdAt: -1 },
      }),
    }),
    getSettingCountryByIsoCode({ isoCode, projection: { currency: 1 } }),
    getServices({
      isoCode,
      projection: { name: 1, text: 1, icon: 1, isSubscription: 1 },
    }),
    getCities(isoCode),
  ]);

  return json({
    fullPayment,
    total,
    settingCountry,
    services,
    filterValue,
    cities,
  });
}, PERMISSIONS.READ_FULL_PAYMENT);

export default function FullPaymentScreen() {
  const loaderData = useLoaderData<LoaderTypeWithError<typeof loader>>();

  return <Outlet context={loaderData} />;
}
