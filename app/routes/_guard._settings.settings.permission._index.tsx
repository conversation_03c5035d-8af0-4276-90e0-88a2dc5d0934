import { json } from '@remix-run/node';
import { useActionData, useLoaderData, useSubmit } from '@remix-run/react';
import { PERMISSIONS, ROUTE_NAME } from 'btaskee-constants';
import {
  AutosizeTextarea,
  Breadcrumbs,
  BreadcrumbsLink,
  Button,
  Dialog,
  DialogClose,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
  Grid,
  Input,
  PermissionEditIcon,
  Typography,
  VectorEmptyDataTable,
  cn,
  toast,
} from 'btaskee-ui';
import { groupPermissionsByModule } from 'btaskee-utils';
import { useEffect, useState } from 'react';
import { useForm } from 'react-hook-form';
import { useTranslation } from 'react-i18next';
import { hocAction, hocLoader } from '~/hoc/remix';
import {
  getAllPermissions,
  updatePermission,
} from '~/services/role-base-access-control.server';

export const handle = {
  breadcrumb: () => (
    <BreadcrumbsLink to={ROUTE_NAME.PERMISSION_SETTING} label="PERMISSION" />
  ),
  i18n: 'user-settings',
};

export const loader = hocLoader(async ({ request }) => {
  const permissions = await getAllPermissions();

  return json({ permissionModules: groupPermissionsByModule(permissions) });
}, PERMISSIONS.MANAGER);

export const action = hocAction(
  async ({ request }, { setInformationActionHistory }) => {
    const formData = await request.clone().formData();

    await updatePermission({
      params: {
        description: formData.get('description')?.toString() || '',
        name: formData.get('name')?.toString() || '',
      },
      permissionId: formData.get('permissionId')?.toString() || '',
    });

    setInformationActionHistory({
      action: 'Update permissions',
    });

    return json({
      isUpdatedSuccess: true,
    });
  },
  PERMISSIONS.MANAGER,
);

export default function PermissionListScreen() {
  const { t: translateKeyUserSettings } = useTranslation('user-settings');
  const submitPermissionData = useSubmit();

  const loaderData = useLoaderData<LoaderTypeWithError<typeof loader>>();

  const [openDialog, setOpenDialog] = useState(false);
  const [selectedPermission, setSelectedPermission] =
    useState<Omit<BtaskeePermissions, 'module'>>();

  const actionData = useActionData<ActionTypeWithError<typeof action>>();
  useEffect(() => {
    if (actionData?.error) {
      toast({ description: actionData.error });
    }
    if (actionData?.isUpdatedSuccess) {
      toast({
        variant: 'success',
        description: translateKeyUserSettings(
          'UPDATE_PERMISSIONS_SUCCESSFULLY',
        ),
      });
    }
  }, [actionData, translateKeyUserSettings]);

  function handleCloseDialog() {
    setOpenDialog(false);
  }

  const formEditPermission = useForm({
    defaultValues: {
      name: selectedPermission?.name || '',
      description: selectedPermission?.description || '',
    },
  });

  useEffect(() => {
    if (selectedPermission) {
      formEditPermission.setValue('name', selectedPermission.name);
      formEditPermission.setValue(
        'description',
        selectedPermission.description,
      );
    }
  }, [formEditPermission, selectedPermission]);

  const onSubmitPermissionData = (
    data: Pick<BtaskeePermissions, 'name' | 'description'>,
  ) => {
    const formPermissionData = new FormData();

    formPermissionData.append('name', data.name);
    formPermissionData.append('description', data.description);
    formPermissionData.append('permissionId', selectedPermission?._id || '');

    submitPermissionData(formPermissionData, { method: 'post' });
    handleCloseDialog();
  };

  return (
    <>
      <Grid className="mb-6 min-h-[90px] rounded-2xl bg-secondary p-4">
        <Typography variant="h3">
          {translateKeyUserSettings('PERMISSION')}
        </Typography>
        <Breadcrumbs />
      </Grid>
      {loaderData?.permissionModules?.length ? (
        loaderData.permissionModules.map((module, idx) => {
          return (
            <div
              key={`${module}-${idx}`}
              className={cn(
                'rounded-md border border-gray-200 p-4',
                idx !== 0 ? 'mt-4' : '',
              )}>
              <Typography className="mb-2 text-base font-semibold leading-5 text-primary">
                {module.module}
              </Typography>
              <div className="grid grid-cols-3 gap-2">
                {module.actions.map((permission, idx) => {
                  return (
                    <Grid
                      key={`${permission}-${idx}`}
                      className="mt-2 h-fit max-w-[318px] gap-2 rounded-md bg-gray-50 p-2">
                      <div className="flex items-center justify-between">
                        <Typography className="mr-1 line-clamp-1 text-sm font-normal leading-[18px]">
                          {permission.name || ''}
                        </Typography>
                        <Button
                          size="icon"
                          className="h-fit w-fit"
                          onClick={() => {
                            setSelectedPermission(permission);
                            setOpenDialog(true);
                          }}
                          variant="ghost">
                          <PermissionEditIcon />
                        </Button>
                      </div>
                      <Typography className="line-clamp-2 text-justify text-sm font-normal leading-[18px] text-gray-400">
                        {permission.description || ''}
                      </Typography>
                    </Grid>
                  );
                })}
              </div>
            </div>
          );
        })
      ) : (
        <div className="mt-[116px] flex justify-center">
          <VectorEmptyDataTable width="272px" height="244px" />
        </div>
      )}
      <Dialog
        open={openDialog}
        onOpenChange={() => setOpenDialog(true)}
        defaultOpen={true}>
        <DialogContent className="max-h-[90vh] max-w-[548px] overflow-y-auto">
          <DialogHeader className="space-y-2">
            <DialogTitle className="text-xl font-semibold tracking-tighter">
              {translateKeyUserSettings('EDIT_PERMISSION')}
            </DialogTitle>
            <DialogDescription className="text-sm font-normal text-gray">
              {translateKeyUserSettings('EDIT_PERMISSIONS_HELPER_TEXT')}
            </DialogDescription>
          </DialogHeader>
          <div className="grid gap-3">
            <Form {...formEditPermission}>
              <form
                onSubmit={formEditPermission.handleSubmit(
                  onSubmitPermissionData,
                )}
                className="space-y-2">
                <FormField
                  name="name"
                  control={formEditPermission.control}
                  rules={{
                    required: translateKeyUserSettings(
                      'THIS_FIELD_IS_REQUIRED',
                    ),
                  }}
                  render={({ field: { onChange, value } }) => (
                    <FormItem>
                      <FormLabel className="text-[#0F172A]">
                        {translateKeyUserSettings('PERMISSION_NAME')}
                      </FormLabel>
                      <FormControl>
                        <Input
                          value={value}
                          onChange={onChange}
                          placeholder={translateKeyUserSettings(
                            'ENTER_PERMISSION_NAME',
                          )}
                        />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
                <FormField
                  name="description"
                  control={formEditPermission.control}
                  rules={{
                    required: translateKeyUserSettings(
                      'THIS_FIELD_IS_REQUIRED',
                    ),
                  }}
                  render={({ field: { onChange, value } }) => (
                    <FormItem>
                      <FormLabel className="text-[#0F172A]">
                        {translateKeyUserSettings('DESCRIPTION')}
                      </FormLabel>
                      <FormControl>
                        <AutosizeTextarea
                          rows={3}
                          value={value}
                          onChange={onChange}
                          placeholder={translateKeyUserSettings(
                            'ENTER_DESCRIPTION',
                          )}
                        />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
                <div className="flex justify-end gap-4 pt-12">
                  <Button
                    className="border-primary text-primary hover:bg-primary-foreground hover:text-primary"
                    type="button"
                    variant="outline"
                    onClick={handleCloseDialog}>
                    {translateKeyUserSettings('CANCEL')}
                  </Button>
                  <Button variant="default" type="submit">
                    {translateKeyUserSettings('CONFIRM')}
                  </Button>
                </div>
              </form>
            </Form>
          </div>
          <DialogClose className="absolute right-4 top-4 z-10 h-5 w-5 cursor-default rounded-sm bg-white" />
        </DialogContent>
      </Dialog>
    </>
  );
}
