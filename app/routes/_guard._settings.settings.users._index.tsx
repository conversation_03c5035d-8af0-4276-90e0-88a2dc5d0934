import { DotsHorizontalIcon } from '@radix-ui/react-icons';
import { type SerializeFrom, json } from '@remix-run/node';
import { Link, useNavigate, useSearchParams } from '@remix-run/react';
import { type ColumnDef } from '@tanstack/react-table';
import { PERMISSIONS, ROUTE_NAME } from 'btaskee-constants';
import { useGlobalStore, useLoaderDataSafely } from 'btaskee-hooks';
import {
  ActivityLogo,
  BTaskeeTable,
  Badge,
  Breadcrumbs,
  Button,
  DataTableColumnHeader,
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
  GraphLogo,
  HeartLogo,
  SendLogo,
  StarLogo,
  StatusBadge,
  Typography,
  toast,
} from 'btaskee-ui';
import {
  convertSortString,
  getPageSizeAndPageIndex,
  getSkipAndLimit,
} from 'btaskee-utils';
import { Plus } from 'lucide-react';
import { useEffect, useMemo } from 'react';
import { useTranslation } from 'react-i18next';
import { hocLoader } from '~/hoc/remix';
import { getCities, getUserSession } from '~/services/helpers.server';
import { commitSession, getSession } from '~/services/session.server';
import {
  getTotalUsersManagedByManagerId,
  getUsersManagedByManagerId,
} from '~/services/settings.server';

export const iconType = [
  <ActivityLogo key="activity" className="h-6 w-6" />,
  <GraphLogo key="graph" className="h-6 w-6" />,
  <HeartLogo key="heart" className="h-6 w-6" />,
  <SendLogo key="send" className="h-6 w-6" />,
  <StarLogo key="star" className="h-6 w-6" />,
];

export const loader = hocLoader(async ({ request }) => {
  const url = new URL(request.url);

  const {
    userId: managerId,
    isoCode,
    isSuperUser,
  } = await getUserSession({
    headers: request.headers,
  });

  const search = url.searchParams.get('search') || '';
  const sort = url.searchParams.get('sort') || '';

  const totalUsers = await getTotalUsersManagedByManagerId(
    managerId,
    search,
    isSuperUser,
  );

  const { limit, skip } = getSkipAndLimit(
    getPageSizeAndPageIndex({
      total: totalUsers,
      pageSize: Number(url.searchParams.get('pageSize')) || 0,
      pageIndex: Number(url.searchParams.get('pageIndex')) || 0,
    }),
  );

  const [users, cities, session] = await Promise.all([
    getUsersManagedByManagerId({
      skip,
      limit,
      projection: { _id: 1, cities: 1, username: 1, email: 1, status: 1 },
      managerId,
      search,
      sort: convertSortString({
        sortString: sort,
        defaultValue: { createdAt: -1 },
      }),
      isSuperUser,
    }),
    getCities(isoCode),
    getSession(request.headers.get('Cookie')),
  ]);

  const message = session.get('flashMessage');

  return json(
    { users, cities, total: totalUsers, message },
    {
      headers: {
        'Set-Cookie': await commitSession(session),
      },
    },
  );
});

export default function UserManagementScreen() {
  const loaderData = useLoaderDataSafely<typeof loader>();
  const navigate = useNavigate();

  const { t: tUserSettings } = useTranslation('user-settings');
  const [searchParams] = useSearchParams();
  const permissions = useGlobalStore(state => state.permissions);

  const columns: ColumnDef<
    SerializeFrom<
      ReturnValueIgnorePromise<typeof getUsersManagedByManagerId>[0]
    >
  >[] = useMemo(
    () => [
      {
        accessorKey: 'username',
        header: ({ column }) => (
          <DataTableColumnHeader
            className="text-black"
            column={column}
            title={tUserSettings('USERNAME')}
          />
        ),
        cell: ({ row }) => (
          <p className="line-clamp-1 whitespace-normal break-all text-sm font-normal text-gray-600">
            {row.getValue('username')}
          </p>
        ),
        size: 70,
        enableSorting: false,
      },
      {
        accessorKey: 'email',
        header: ({ column }) => (
          <DataTableColumnHeader
            className="text-black"
            column={column}
            title={tUserSettings('EMAIL')}
          />
        ),
        cell: ({ row }) => (
          <p className="line-clamp-1 whitespace-normal break-all text-sm font-normal text-gray-600">
            {row.getValue('email')}
          </p>
        ),
        enableSorting: false,
        size: 180,
      },
      {
        accessorKey: 'groups',
        header: ({ column }) => (
          <DataTableColumnHeader
            className="text-black"
            column={column}
            title={tUserSettings('GROUPS')}
          />
        ),
        cell: ({ row }) => {
          return (
            <div className="flex flex-wrap gap-2">
              {row.original.groupOfUser?.map((group, index) => (
                <Badge
                  className="gap-2 rounded-[6px] bg-gray-50 px-2 py-1 text-sm font-normal text-black"
                  key={index}>
                  {iconType[group?.iconType]}
                  <span className="line-clamp-1 max-w-32 whitespace-normal break-all">
                    {group?.name}
                  </span>
                </Badge>
              ))}
            </div>
          );
        },
        enableSorting: false,
        size: 300,
      },
      {
        accessorKey: 'status',
        header: ({ column }) => (
          <DataTableColumnHeader
            className="text-black"
            column={column}
            title={tUserSettings('STATUS')}
          />
        ),
        cell: ({ row }) => <StatusBadge status={row.original.status} />,
        enableSorting: false,
        size: 70,
      },
      {
        accessorKey: 'actions',
        header: ({ column }) => (
          <DataTableColumnHeader
            className="text-center text-black"
            column={column}
            title={tUserSettings('ACTION')}
          />
        ),
        cell: ({ row }) => (
          <DropdownMenu>
            <DropdownMenuTrigger asChild>
              <Button
                variant="ghost"
                className="mx-auto flex h-8 w-8 p-0 data-[state=open]:bg-muted">
                <DotsHorizontalIcon className="h-4 w-4" />
                <span className="sr-only">Open menu</span>
              </Button>
            </DropdownMenuTrigger>
            <DropdownMenuContent align="end" className="w-[160px]">
              <DropdownMenuItem
                disabled={!permissions?.includes(PERMISSIONS.MANAGER)}
                onClick={e => {
                  if (!permissions?.includes(PERMISSIONS.MANAGER)) {
                    e.preventDefault();
                    return;
                  }
                  navigate(
                    `${ROUTE_NAME.USER_SETTING}/${row.original._id}/edit`,
                  );
                }}>
                {tUserSettings('EDIT')}
              </DropdownMenuItem>
            </DropdownMenuContent>
          </DropdownMenu>
        ),
        enableSorting: false,
        enableHiding: false,
        size: 100,
      },
    ],
    [navigate, permissions, tUserSettings],
  );

  useEffect(() => {
    if (loaderData?.message) {
      toast({ variant: 'success', description: loaderData.message });
    }
  }, [loaderData.message]);

  return (
    <div className="flex h-full flex-1 flex-col space-y-8">
      <div className="flex items-center justify-between space-y-2 rounded-2xl bg-secondary p-4">
        <div className="grid space-y-2">
          <Typography variant="h3">
            {tUserSettings('USER_MANAGEMENT')}
          </Typography>
          <Breadcrumbs />
        </div>
        <Button
          className="gap-2 text-sm font-medium"
          variant="default"
          asChild={permissions.includes(PERMISSIONS.MANAGER)}
          disabled={!permissions.includes(PERMISSIONS.MANAGER)}>
          <Link
            className="flex items-center gap-2"
            to={`${ROUTE_NAME.USER_SETTING}/create`}>
            <Plus />
            {tUserSettings('ADD')}
          </Link>
        </Button>
      </div>

      <BTaskeeTable
        isShowClearButton
        total={loaderData.total}
        data={loaderData.users}
        columns={columns}
        pagination={getPageSizeAndPageIndex({
          total: loaderData?.total || 0,
          pageSize: Number(searchParams.get('pageSize') || 0),
          pageIndex: Number(searchParams.get('pageIndex') || 0),
        })}
        search={{
          defaultValue: searchParams.get('search') || '',
          name: 'search',
          placeholder: tUserSettings('SEARCH_TEXT'),
        }}
        localeAddress="user-settings"
      />
    </div>
  );
}
