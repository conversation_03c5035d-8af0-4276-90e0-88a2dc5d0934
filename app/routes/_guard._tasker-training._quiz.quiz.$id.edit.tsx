import { zodResolver } from '@hookform/resolvers/zod';
import type { SerializeFrom } from '@remix-run/node';
import { json } from '@remix-run/node';
import {
  isRouteErrorResponse,
  useActionData,
  useRouteError,
  useSubmit,
} from '@remix-run/react';
import { PERMISSIONS, ROUTE_NAME } from 'btaskee-constants';
import { useLoaderDataSafely } from 'btaskee-hooks';
import {
  Breadcrumbs,
  BreadcrumbsLink,
  BtaskeeResponseError,
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
  Input,
  Separator,
  Switch,
  Typography,
  toast,
  useConfirm,
} from 'btaskee-ui';
import { createUID } from 'btaskee-utils';
import { useEffect, useState } from 'react';
import type { FieldArrayWithId } from 'react-hook-form';
import { useFieldArray, useForm } from 'react-hook-form';
import { useTranslation } from 'react-i18next';
import { z } from 'zod';
import { DashedBorderButton } from '~/components/form/DashedBorderButton';
import { FormFooterQuiz } from '~/components/form/TrainingComponents';
import { AnswerCard } from '~/components/form/cards/AnswerCard';
import ImageUploaderCard from '~/components/form/cards/ImageUploaderCard';
import { hocAction, hocLoader } from '~/hoc/remix';
import {
  zodAnswersSchema,
  zodCodeSchema,
  zodImageWithDescriptionSchema,
} from '~/schemas/zodSchema';
import { getUserSession } from '~/services/helpers.server';
import { getQuizDetail } from '~/services/tasker-training.server';
import { handleQuizAction } from '~/utils/tasker-training/quizAction';

export function ErrorBoundary() {
  const error = useRouteError() as { message?: string };
  const { t: tCommon } = useTranslation('common');

  if (isRouteErrorResponse(error)) {
    if (error?.message) {
      toast({ description: JSON.stringify(error.message) });
    }

    return <BtaskeeResponseError t={tCommon} errorStatus={error.status} />;
  }

  return <BtaskeeResponseError t={tCommon} errorStatus={500} />;
}

export const handle = {
  breadcrumb: (data: { quizId: string }) => {
    return (
      <BreadcrumbsLink
        to={`${ROUTE_NAME.QUIZ}/${data.quizId}/edit`}
        label="EDIT_TRAINING_QUIZ"
      />
    );
  },
  i18n: 'quiz',
};

export const loader = hocLoader(async ({ request, params }) => {
  const { isoCode } = await getUserSession({
    headers: request.headers,
  });

  const quizDetail = await getQuizDetail({
    isoCode,
    _id: params.id || '',
  });

  return json({ quizId: params.id || '', quizDetail });
}, PERMISSIONS.WRITE_TRAINING_QUIZ);

export const action = hocAction(
  async ({ request, params }, { setInformationActionHistory }) => {
    return handleQuizAction({
      request,
      isEdit: true,
      setInformationActionHistory,
      quizId: params?.id ?? '',
    });
  },
  PERMISSIONS.WRITE_TRAINING_QUIZ,
);

export default function EditQuiz() {
  const { t } = useTranslation('quiz');

  const confirm = useConfirm();
  const submit = useSubmit();

  const loaderData = useLoaderDataSafely<typeof loader>();
  const actionData = useActionData<ActionTypeWithError<typeof action>>();
  useEffect(() => {
    if (actionData?.error) {
      toast({ description: actionData.error });
    }
  }, [actionData?.error]);

  const [isShowImage, setIsShowImage] = useState(
    !!loaderData.quizDetail?.image,
  );

  const [selectedCorrectAnswer, setSelectedCorrectAnswer] = useState<
    NonNullable<
      SerializeFrom<ReturnValueIgnorePromise<typeof getQuizDetail>>
    >['_id']
  >(loaderData.quizDetail?.answers.find(answer => answer.isCorrect)?._id || '');
  const resolverSchema = zodResolver(
    z.object({
      code: zodCodeSchema(t),
      name: z.string().min(3, t('MINIMUM_CHARACTERS', { number: 3 })),
      isRandomAnswer: z.boolean(),
      answers: zodAnswersSchema(t),
      image: zodImageWithDescriptionSchema(t).updateImage,
    }),
  );

  const form = useForm<FormQuiz>({
    resolver: resolverSchema,
    defaultValues: {
      code: loaderData.quizDetail?.code || '',
      name: loaderData.quizDetail?.title || '',
      isRandomAnswer: loaderData.quizDetail?.isRandomAnswer || false,
      answers: loaderData.quizDetail?.answers || [
        {
          _id: createUID(),
          content: '',
          isCorrect: false,
        },
        {
          _id: createUID(),
          content: '',
          isCorrect: false,
        },
      ],
      image: loaderData.quizDetail?.image
        ? {
            value: loaderData.quizDetail?.image?.url || '',
            description: loaderData.quizDetail?.image?.description || '',
          }
        : null,
    },
  });

  const { control, setValue, handleSubmit } = form;

  const { fields, append, remove } = useFieldArray({
    control,
    name: 'answers',
  });

  useEffect(() => {
    if (selectedCorrectAnswer) {
      fields.forEach(
        (field: FieldArrayWithId<FormQuiz, 'answers', 'id'>, index) => {
          if (
            field._id === selectedCorrectAnswer ||
            field.id === selectedCorrectAnswer
          ) {
            setValue(`answers.${index}.isCorrect`, true);
          } else {
            setValue(`answers.${index}.isCorrect`, false);
          }
        },
      );
    }
  }, [fields, selectedCorrectAnswer, setValue]);

  const onSubmit = async (data: FormQuiz) => {
    if (
      await confirm({
        title: t('CONFIRM'),
        body: t('ARE_YOU_SURE_YOUR_INFORMATION_IS_CORRECT'),
        actionButton: t('SUBMIT'),
        cancelButton: t('CANCEL'),
      })
    ) {
      const formData = new FormData();

      if (data.image?.value instanceof Blob) {
        formData.append('image', data.image.value);
      }

      formData.append('data', JSON.stringify(data));

      submit(formData, { method: 'post', encType: 'multipart/form-data' });
    }
  };

  return (
    <div>
      <div className="flex bg-secondary p-4 justify-between align-middle rounded-md">
        <div className="grid space-y-2 rounded-xl">
          <div className="flex">
            <Typography className="capitalize" variant="h2">
              {t('UPDATE_QUIZ')}
            </Typography>
          </div>
          <Breadcrumbs />
        </div>
      </div>
      <Form {...form}>
        <form
          onSubmit={handleSubmit(onSubmit)}
          encType={'multipart/form-data'}
          className={'flex flex-col gap-8 !mt-6'}>
          <section className={'flex flex-col gap-4'}>
            <Typography variant={'h4'} className={'tracking-tighter'}>
              {t('QUESTION')}
            </Typography>
            <div className="grid grid-cols-2 gap-6">
              <FormField
                name={'code'}
                control={control}
                render={({ field }) => (
                  <FormItem>
                    <FormLabel className={'text-gray-700'}>
                      {t('QUIZ_CODE')}
                    </FormLabel>
                    <FormControl>
                      <Input {...field} placeholder={t('ENTER_QUIZ_CODE')} />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
              <FormField
                name={'name'}
                control={control}
                render={({ field }) => (
                  <FormItem>
                    <FormLabel className={'text-gray-700'}>
                      {t('QUESTION')}
                    </FormLabel>
                    <FormControl>
                      <Input {...field} placeholder={t('ENTER_QUESTION')} />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <div className="space-y-2 mt-2">
                <div className="flex justify-between items-center h-6">
                  <FormLabel className={'text-gray-700'}>
                    {t('IMAGE')}
                  </FormLabel>
                  <Typography
                    variant="p"
                    affects="removePMargin"
                    className="text-sm text-gray-400 leading-tight">
                    {t('OPTIONAL')}
                  </Typography>
                </div>

                {!isShowImage && (
                  <DashedBorderButton
                    className={'py-[23px]'}
                    label={t('ADD_IMAGE')}
                    onClick={() => {
                      setIsShowImage(true);
                      setValue('image', {
                        value: null,
                        description: '',
                      });
                    }}
                  />
                )}

                {isShowImage && (
                  <ImageUploaderCard
                    defaultValue={{
                      url: loaderData.quizDetail?.image?.url || '',
                      description:
                        loaderData.quizDetail?.image?.description || '',
                    }}
                    onClose={() => {
                      setIsShowImage(false);
                      setValue('image', null);
                    }}
                    form={form}
                  />
                )}
              </div>
            </div>

            <Separator className="my-2" />

            <div className="flex flex-col items-start gap-6 mb-2">
              <Typography variant={'h4'} className={'tracking-tighter'}>
                {t('ANSWER')}
              </Typography>

              <FormField
                name={'isRandomAnswer'}
                control={control}
                render={({ field }) => (
                  <FormItem className="flex items-center gap-4">
                    <FormControl>
                      <Switch
                        checked={!!field.value}
                        onCheckedChange={field.onChange}
                      />
                    </FormControl>
                    <FormLabel className={'text-gray-700 !mt-0'}>
                      {t('RANDOM_ANSWER')}
                    </FormLabel>
                    <FormMessage />
                  </FormItem>
                )}
              />
            </div>

            <div className="max-h-[380px] overflow-y-auto">
              <div className="grid grid-cols-2 gap-6 items-start">
                {fields.map(
                  (
                    field: FieldArrayWithId<FormQuiz, 'answers', 'id'>,
                    index,
                  ) => (
                    <AnswerCard
                      key={field.id}
                      fieldId={field._id || field.id}
                      control={control}
                      index={index}
                      remove={remove}
                      selectedCorrectAnswer={selectedCorrectAnswer}
                      setSelectedCorrectAnswer={setSelectedCorrectAnswer}
                    />
                  ),
                )}

                <DashedBorderButton
                  label={t('ADD_ANSWER')}
                  className="mt-0 py-[23px]"
                  onClick={() =>
                    append({
                      content: '',
                      isCorrect: false,
                      _id: createUID(),
                    })
                  }
                />
              </div>
            </div>

            <Separator className="my-2" />

            <FormFooterQuiz form={form} t={t} isEdit={true} />
          </section>
        </form>
      </Form>
    </div>
  );
}
