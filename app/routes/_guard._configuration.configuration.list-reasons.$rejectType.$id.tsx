import { json, redirect } from '@remix-run/node';
import {
  isRouteErrorResponse,
  useActionData,
  useOutletContext,
  useRouteError,
  useSubmit,
} from '@remix-run/react';
import type { TASKER_PROFILE_REJECT_STATUS } from 'btaskee-constants';
import { ACTION_NAME, PERMISSIONS, ROUTE_NAME } from 'btaskee-constants';
import { useLoaderDataSafely } from 'btaskee-hooks';
import {
  BtaskeeResponseError,
  Button,
  Dialog,
  DialogClose,
  DialogContent,
  DialogTitle,
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
  Input,
  toast,
  useConfirm,
} from 'btaskee-ui';
import type { Dispatch, SetStateAction } from 'react';
import { useEffect } from 'react';
import { useForm } from 'react-hook-form';
import { useTranslation } from 'react-i18next';
import { hocAction, hocLoader } from '~/hoc/remix';
import i18next from '~/i18next.server';
import { getUserSession } from '~/services/helpers.server';
import {
  editRejectReason,
  getRejectReasonById,
} from '~/services/reject-reason.server';
import { commitSession, getSession } from '~/services/session.server';

export const loader = hocLoader(async ({ request, params }) => {
  const { isoCode } = await getUserSession({ headers: request.headers });

  const rejectReason = await getRejectReasonById({
    isoCode,
    id: params?.id || '',
  });

  return json({
    rejectReason,
  });
}, PERMISSIONS.READ_LIST_REASONS);

export function ErrorBoundary() {
  const error = useRouteError() as { message?: string };
  const { t: tCommon } = useTranslation('common');

  if (isRouteErrorResponse(error)) {
    if (error?.message) {
      toast({ description: JSON.stringify(error.message) });
    }

    return <BtaskeeResponseError t={tCommon} errorStatus={error.status} />;
  }

  return <BtaskeeResponseError t={tCommon} errorStatus={500} />;
}

export const action = hocAction(
  async ({ request, params }, { setInformationActionHistory }) => {
    const rejectType = params.rejectType || '';
    const { isoCode, username } = await getUserSession({
      headers: request.headers,
    });

    const formData = await request.clone().formData();

    await editRejectReason({
      isoCode,
      id: params?.id || '',
      updatedBy: username,
      updateData: {
        name: formData.get('name')?.toString() || '',
        type: rejectType,
      },
    });

    setInformationActionHistory({
      action: ACTION_NAME.UPDATED_REJECT_REASON,
    });

    const session = await getSession(request.headers.get('cookie'));
    const t = await i18next.getFixedT(request, 'reject-reason');
    session.flash('flashMessage', t('REASON_UPDATED_SUCCESSFULLY'));

    const newSession = await commitSession(session);

    return redirect(`${ROUTE_NAME.LIST_REASONS}/${rejectType}`, {
      headers: {
        'Set-Cookie': newSession,
      },
    });
  },
  PERMISSIONS.WRITE_REJECT_REASON,
);

interface RejectReasonForm {
  name: string;
  type: TASKER_PROFILE_REJECT_STATUS;
}

export default function EditReasonScreen() {
  const submit = useSubmit();
  const confirm = useConfirm();
  const { t } = useTranslation('reject-reason');

  const loaderData = useLoaderDataSafely<typeof loader>();
  const actionData = useActionData<ActionTypeWithError<typeof action>>();

  const parentContext = useOutletContext<{
    openEditReasonDialog: boolean;
    setOpen: Dispatch<SetStateAction<boolean>>;
    onClose: () => void;
  }>();

  useEffect(() => {
    if (actionData?.error) {
      toast({ description: actionData.error });
    }
  }, [actionData]);

  const form = useForm<RejectReasonForm>({
    defaultValues: {
      name: loaderData.rejectReason?.name || '',
    },
  });

  const onSubmit = async (data: RejectReasonForm) => {
    const formData = new FormData();
    formData.append('name', data.name);

    const isConfirm = await confirm({
      title: t('EDIT_REASON'),
      body: t('ARE_YOU_SURE_YOU_WANT_TO_EDIT_REASON'),
      cancelButton: t('CANCEL'),
      actionButton: t('CONFIRM'),
    });

    if (isConfirm) submit(formData, { method: 'post' });
  };

  return (
    <Dialog
      open={parentContext.openEditReasonDialog}
      onOpenChange={parentContext.setOpen}>
      <DialogContent className="max-w-xl max-h-[90vh] overflow-y-auto">
        <DialogTitle className="text-2xl tracking-tighter font-semibold">
          {t('EDIT_REASON')}
        </DialogTitle>
        <Form {...form}>
          <form onSubmit={form.handleSubmit(onSubmit)}>
            <FormField
              name="name"
              control={form.control}
              rules={{ required: t('THIS_FIELD_IS_REQUIRED') }}
              render={({ field }) => (
                <FormItem>
                  <FormLabel className={'text-gray-700'}>
                    {t('REASON')}
                  </FormLabel>
                  <FormControl>
                    <Input {...field} placeholder={t('ENTER_REASON')} />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />
            <div className="flex justify-end gap-4 mt-4">
              <Button
                type="button"
                variant="outline"
                className="border-primary text-primary hover:text-primary"
                onClick={parentContext.onClose}>
                {t('CANCEL')}
              </Button>
              <Button type="submit" className="bg-primary hover:bg-primary-600">
                {t('SAVE_CHANGES')}
              </Button>
            </div>
          </form>
        </Form>
        <DialogClose className="absolute z-10 right-4 top-4 rounded-sm w-5 h-5 cursor-default bg-white" />
      </DialogContent>
    </Dialog>
  );
}
