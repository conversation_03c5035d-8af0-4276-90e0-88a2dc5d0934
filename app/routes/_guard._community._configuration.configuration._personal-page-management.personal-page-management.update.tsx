import { zod<PERSON>esolver } from '@hookform/resolvers/zod';
import type { UploadHandler } from '@remix-run/node';
import {
  json,
  redirect,
  unstable_composeUploadHandlers,
  unstable_createMemoryUploadHandler,
  unstable_parseMultipartFormData,
} from '@remix-run/node';
import {
  isRouteErrorResponse,
  useActionData,
  useLoaderData,
  useNavigate,
  useRouteError,
  useSubmit,
} from '@remix-run/react';
import {
  ACTION_NAME,
  MAXIMUM_IMAGE_FILE_LENGTH_BTASKEE_PROFILE,
  PERMISSIONS,
  ROUTE_NAME,
} from 'btaskee-constants';
import {
  Breadcrumbs,
  BreadcrumbsLink,
  BtaskeeResponseError,
  Button,
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
  Input,
  Textarea,
  Typography,
  toast,
  useConfirm,
} from 'btaskee-ui';
import { useEffect } from 'react';
import { useForm } from 'react-hook-form';
import { useTranslation } from 'react-i18next';
import { z } from 'zod';
import { MediaUploaderCard } from '~/components/form/MediaUploaderCard';
import { SimpleImageUpload } from '~/components/form/SimpleImageUpload';
import { hocAction, hocLoader } from '~/hoc/remix';
import i18next from '~/i18next.server';
import {
  getBtaskeeCommunityDetail,
  updateBtaskeeCommunity,
} from '~/services/community-configuration.server';
import { getUserSession } from '~/services/helpers.server';
import { commitSession, getSession } from '~/services/session.server';
import { s3UploadHandler } from '~/third-party/s3.server';

export const handle = {
  breadcrumb: () => (
    <BreadcrumbsLink
      to={ROUTE_NAME.CONFIGURATION_PERSONAL_PAGE_MANAGEMENT_UPDATE}
      label="UPDATE_PERSONAL_PAGE_MANAGEMENT"
    />
  ),
};

export const loader = hocLoader(
  async ({ request }) => {
    const { isoCode, username } = await getUserSession({
      headers: request.headers,
    });

    const btaskeeProfileData = await getBtaskeeCommunityDetail({
      isoCode,
    });

    return json({
      isoCode,
      username,
      btaskeeProfileData,
    });
  },
  [PERMISSIONS.WRITE_BTASKEE_PROFILE],
);

export const action = hocAction(
  async ({ request }, { setInformationActionHistory }) => {
    // TODO: create a common function to handle upload image
    const uploadHandler: UploadHandler = unstable_composeUploadHandlers(
      async formField => {
        if (formField.name === 'bTaskeeAvatar') {
          return await s3UploadHandler(formField);
        }

        return undefined;
      },
      unstable_createMemoryUploadHandler(),
    );

    const formData = await unstable_parseMultipartFormData(
      request.clone(),
      uploadHandler,
    );

    const dataSubmit = JSON.parse(
      formData.get('bTaskeeInfoUpdate')?.toString() || '{}',
    );

    await updateBtaskeeCommunity({
      ...dataSubmit,
      updateInfo: {
        ...dataSubmit.updateInfo,
        avatar:
          // when user uploads a new photo, I will receive a link from s3 and if user does not uploads a new photo, the old link will be retained
          formData.get('bTaskeeAvatar')?.toString() ||
          dataSubmit.updateInfo.avatar,
      },
    });

    setInformationActionHistory({
      action: ACTION_NAME.UPDATE_BTASKEE_PROFILE,
    });

    const session = await getSession(request.headers.get('cookie'));
    const t = await i18next.getFixedT(
      request,
      'configuration-personal-page-management-update',
    );
    session.flash('flashMessage', t('UPDATE_BTASKEE_PROFILE_SUCCESS'));
    const newSession = await commitSession(session);

    return redirect(ROUTE_NAME.CONFIGURATION_PERSONAL_PAGE_MANAGEMENT, {
      headers: {
        'Set-Cookie': newSession,
      },
    });
  },
  PERMISSIONS.WRITE_BTASKEE_PROFILE,
);

export function ErrorBoundary() {
  const error = useRouteError() as { message?: string };
  const { t: tCommon } = useTranslation('common');

  if (isRouteErrorResponse(error)) {
    if (error?.message) {
      toast({ description: JSON.stringify(error.message) });
    }

    return <BtaskeeResponseError t={tCommon} errorStatus={error.status} />;
  }

  return <BtaskeeResponseError t={tCommon} errorStatus={500} />;
}

export default function Screen() {
  const { t } = useTranslation('configuration-personal-page-management-update');
  const loaderData = useLoaderData<LoaderTypeWithError<typeof loader>>();
  const actionData = useActionData<ActionTypeWithError<typeof action>>();

  useEffect(() => {
    if (actionData?.error) {
      toast({
        description: actionData.error,
      });
    }
  }, [actionData?.error]);

  const navigate = useNavigate();
  const confirm = useConfirm();
  const submit = useSubmit();

  const FormSchema = z.object({
    avatar: z.custom(),
    name: z.string().min(1, t('NAME_REQUIRE')),
    bio: z.string().min(1, t('BIO_REQUIRE')),
  });

  const form = useForm<ProfileData>({
    resolver: zodResolver(FormSchema),
    defaultValues: {
      // loaderData.btaskeeProfileData has more than 3 fields so that I don't use spread syntax
      avatar: loaderData?.btaskeeProfileData?.avatar || '',
      name: loaderData?.btaskeeProfileData?.name || '',
      bio: loaderData?.btaskeeProfileData?.bio || '',
    },
  });

  const onSubmit = async (dataFormEdit: ProfileData) => {
    const isConfirmSubmit = await confirm({
      title: t('CONFIRM'),
      body: t('ARE_YOU_SURE_INPUT_INFORMATION_IS_CORRECT'),
    });

    if (isConfirmSubmit) {
      const formData = new FormData();

      formData.append(
        'bTaskeeInfoUpdate',
        JSON.stringify({
          isoCode: loaderData?.isoCode || '',
          updateInfo: dataFormEdit,
          updatedByUserName: loaderData?.username || '',
        }),
      );

      if (dataFormEdit.avatar instanceof Blob) {
        formData.append('bTaskeeAvatar', dataFormEdit.avatar);
      }
      submit(formData, { method: 'post', encType: 'multipart/form-data' });
    }
  };

  return (
    <>
      <div className="flex flex-col gap-2 p-4 rounded-2xl bg-secondary">
        <Typography variant="h3">
          {t('UPDATE_PERSONAL_PAGE_MANAGEMENT')}
        </Typography>
        <Breadcrumbs />
      </div>
      <Form {...form}>
        <form onSubmit={form.handleSubmit(onSubmit)} className="mt-6">
          <div className="flex gap-6">
            <MediaUploaderCard
              className="pb-4 h-fit w-[330px]"
              isShowCloseButton={false}>
              <FormField
                control={form.control}
                name="avatar"
                render={({ field: { onChange, ref, value } }) => (
                  <FormItem className={`flex flex-col items-center h-fit`}>
                    <FormControl>
                      <SimpleImageUpload
                        fieldRef={ref}
                        // avatar has 2 types: string | Blob
                        avatarUrl={value.toString()}
                        cardTitle={t('AVATAR')}
                        onFileChange={file => onChange(file)}
                        maxContentLength={
                          MAXIMUM_IMAGE_FILE_LENGTH_BTASKEE_PROFILE.VALUE
                        }
                        description={t('UPLOAD_IMAGE_REQUIRE')}
                        subDescription={t('RATIO', { ratio: '(1:1)' })}
                        ratio={1}
                        formValue={value}
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
            </MediaUploaderCard>
            <FormField
              control={form.control}
              name="name"
              render={({ field }) => (
                <FormItem className="flex-1">
                  <FormLabel className="text-gray-700">{t('NAME')}</FormLabel>
                  <FormControl>
                    <Input {...field} placeholder={t('ENTER_NAME')} />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />
            <FormField
              control={form.control}
              name="bio"
              render={({ field }) => (
                <FormItem className="flex-1">
                  <FormLabel className={'text-gray-700'}>{t('BIO')}</FormLabel>
                  <FormControl>
                    <Textarea
                      placeholder={t('ENTER_BIO')}
                      {...field}
                      className="min-h-10 h-10"
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />
          </div>
          <div className="flex justify-end gap-4 mt-6">
            <Button
              type="button"
              variant="outline"
              className="border-primary text-primary hover:text-primary"
              onClick={() =>
                navigate(ROUTE_NAME.CONFIGURATION_PERSONAL_PAGE_MANAGEMENT)
              }>
              {t('CANCEL')}
            </Button>
            <Button>{t('SAVE_CHANGES')}</Button>
          </div>
        </form>
      </Form>
    </>
  );
}
