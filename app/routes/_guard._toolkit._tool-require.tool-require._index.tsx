import { DotsHorizontalIcon } from '@radix-ui/react-icons';
import type { SerializeFrom } from '@remix-run/node';
import { json, redirect } from '@remix-run/node';
import {
  Link,
  isRouteErrorResponse,
  useLoaderData,
  useRouteError,
  useSearchParams,
  useSubmit,
} from '@remix-run/react';
import type { ColumnDef } from '@tanstack/react-table';
import { ACTION_NAME, PERMISSIONS, ROUTE_NAME } from 'btaskee-constants';
import { useGlobalStore } from 'btaskee-hooks';
import {
  AspectRatio,
  BTaskeeTable,
  Breadcrumbs,
  BtaskeeResponseError,
  Button,
  DataTableColumnHeader,
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
  Grid,
  Tooltip,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
  Typography,
  toast,
  useConfirm,
} from 'btaskee-ui';
import {
  DEFAULT_RANGE_DATE_CURRENT_DAY,
  convertSortString,
  getOrderNumber,
  getPageSizeAndPageIndex,
  getSkipAndLimit,
  momentTz,
} from 'btaskee-utils';
import { format } from 'date-fns';
import i18n from 'i18next';
import { Plus } from 'lucide-react';
import { useCallback, useEffect } from 'react';
import { useTranslation } from 'react-i18next';
import {
  IndonesiaFlagIcon,
  SouthKoreaFlagIcon,
  ThailandFlagIcon,
  USAFlagIcon,
  VietnamFlagIcon,
} from '~/components/common/FigmaIcon';
import { hocAction, hocLoader } from '~/hoc/remix';
import i18next from '~/i18next.server';
import {
  getSettingCountryByIsoCode,
  getUserSession,
} from '~/services/helpers.server';
import { commitSession, getSession } from '~/services/session.server';
import {
  deleteToolItem,
  getListToolItems,
  getTotalToolItems,
} from '~/services/tool-require.server';

export function ErrorBoundary() {
  const error = useRouteError() as { message?: string };
  const { t: tCommon } = useTranslation('common');

  if (isRouteErrorResponse(error)) {
    if (error?.message) {
      toast({ description: JSON.stringify(error.message) });
    }

    return <BtaskeeResponseError t={tCommon} errorStatus={error.status} />;
  }

  return <BtaskeeResponseError t={tCommon} errorStatus={500} />;
}

export const loader = hocLoader(
  async ({ request }) => {
    const url = new URL(request.url);

    const search = url.searchParams.get('search') || '';
    const sort = url.searchParams.get('sort') || '';
    const rangeDate = url.searchParams.get('rangeDate') || '';

    const { isoCode } = await getUserSession({
      headers: request.headers,
    });

    const filters = {
      isoCode,
      search,
      rangeDate: DEFAULT_RANGE_DATE_CURRENT_DAY(rangeDate),
    };

    const total = await getTotalToolItems(filters);

    const { limit, skip } = getSkipAndLimit(
      getPageSizeAndPageIndex({
        total,
        pageSize: Number(url.searchParams.get('pageSize')) || 0,
        pageIndex: Number(url.searchParams.get('pageIndex')) || 0,
      }),
    );

    const [ListToolkitItems, settingCountry] = await Promise.all([
      getListToolItems({
        ...filters,
        skip,
        limit,
        sort: convertSortString({
          sortString: sort,
          defaultValue: { createdAt: -1 },
        }),
      }),
      getSettingCountryByIsoCode({ isoCode, projection: { currency: 1 } }),
    ]);

    const session = await getSession(request.headers.get('cookie'));
    const flashMessage = await session.get('flashMessage');

    return json({
      data: ListToolkitItems,
      total,
      settingCountry,
      filters,
      flashMessage,
    });
  },
  [PERMISSIONS.READ_TOOL_REQUIRE],
);

export const action = hocAction(
  async ({ request }, { setInformationActionHistory }) => {
    const formData = await request.clone().formData();
    const { isoCode } = await getUserSession({
      headers: request.headers,
    });
    const toolkitItemId = formData.get('toolId')?.toString() || '';

    await deleteToolItem({
      isoCode,
      toolkitItemId,
    });

    setInformationActionHistory({
      action: ACTION_NAME.DELETE_TOOL,
    });

    const session = await getSession(request.headers.get('cookie'));
    const t = await i18next.getFixedT(request, 'tool-require-index');
    session.flash('flashMessage', t('DELETE_TOOL_SUCCESS'));
    const newSession = await commitSession(session);

    return redirect(ROUTE_NAME.TOOL_REQUIRE, {
      headers: {
        'Set-Cookie': newSession,
      },
    });
  },
  PERMISSIONS.WRITE_TOOL_REQUIRE,
);

export default function ToolRequire() {
  const { t } = useTranslation('tool-require-index');
  const [searchParams] = useSearchParams();

  const loaderData = useLoaderData<LoaderTypeWithError<typeof loader>>();
  const permissions = useGlobalStore(store => store.permissions);

  useEffect(() => {
    if (loaderData?.flashMessage) {
      toast({
        variant: 'success',
        description: loaderData.flashMessage,
      });
    }
  }, [loaderData?.flashMessage]);

  const getLanguageIcon = useCallback((lang: string) => {
    switch (lang) {
      case 'vi':
        return <VietnamFlagIcon />;
      case 'en':
        return <USAFlagIcon />;
      case 'ko':
        return <SouthKoreaFlagIcon />;
      case 'th':
        return <ThailandFlagIcon />;
      case 'id':
        return <IndonesiaFlagIcon />;
      default:
        return null;
    }
  }, []);

  const confirm = useConfirm();
  const submit = useSubmit();

  const handleDeleteTool = async (id: string) => {
    const formData = new FormData();

    if (
      await confirm({
        title: t('DELETE'),
        body: t('ARE_YOU_SURE_DELETE'),
        actionButton: t('DELETE'),
      })
    ) {
      formData.append('toolId', id);
      submit(formData, { method: 'delete', encType: 'multipart/form-data' });
    }
  };

  const columns: ColumnDef<SerializeFrom<ToolKitItem>>[] = [
    {
      accessorKey: 'no',
      header: ({ column }) => (
        <DataTableColumnHeader column={column} title={t('NO')} />
      ),
      cell: ({ row }) => (
        <Typography variant="p">
          {getOrderNumber({
            pageSize: Number(searchParams.get('pageSize') || 10),
            pageIndex: Number(searchParams.get('pageIndex') || 0),
            orderColumn: row.index || 0,
          })}
        </Typography>
      ),
      size: 40,
      enableSorting: false,
    },
    {
      accessorKey: 'image',
      header: ({ column }) => (
        <DataTableColumnHeader column={column} title={t('IMAGE')} />
      ),
      cell: ({ row }) => (
        <div className="max-w-20 max-h-20">
          <AspectRatio ratio={1 / 1}>
            <img
              src={row.original?.image || ''}
              alt={row.original?.text?.[i18n.language || 'en'] || ''}
              className="object-cover h-full rounded-md w-20"
            />
          </AspectRatio>
        </div>
      ),
      size: 80,
      enableSorting: false,
    },
    {
      accessorKey: 'text',
      header: ({ column }) => (
        <DataTableColumnHeader column={column} title={t('TOOL_NAME')} />
      ),
      cell: ({ row }) => {
        return (
          <div className="flex items-center gap-8">
            {Object.keys(row.original?.text).map((lang: string) => (
              <div key={lang} className="flex-1 flex items-center gap-3">
                {getLanguageIcon(lang)}
                <TooltipProvider>
                  <Tooltip>
                    <TooltipTrigger asChild>
                      <Typography
                        variant="p"
                        className="!m-0 max-w-[86px] flex-1 whitespace-nowrap text-ellipsis overflow-hidden">
                        {row.original.text[lang]}
                      </Typography>
                    </TooltipTrigger>
                    <TooltipContent>
                      <Typography variant="p">
                        {row.original.text[lang]}
                      </Typography>
                    </TooltipContent>
                  </Tooltip>
                </TooltipProvider>
              </div>
            ))}
          </div>
        );
      },
      size: 600,
      enableSorting: false,
    },
    {
      accessorKey: 'price',
      header: ({ column }) => (
        <DataTableColumnHeader column={column} title={t('PRICE')} />
      ),
      cell: ({ row }) => (
        <Typography variant="p">
          {(row.original?.price?.toLocaleString() || '') +
            (loaderData?.settingCountry?.currency?.sign || '')}
        </Typography>
      ),
      size: 50,
    },
    {
      accessorKey: 'createdAt',
      header: ({ column }) => (
        <DataTableColumnHeader column={column} title={t('CREATED_AT')} />
      ),
      cell: ({ row }) => (
        <Typography variant="p">
          {row.original?.createdAt
            ? format(row.original.createdAt, 'HH:mm - dd/MM/yyyy')
            : null}
        </Typography>
      ),
      size: 50,
    },
    {
      accessorKey: 'updatedAt',
      header: ({ column }) => (
        <DataTableColumnHeader column={column} title={t('UPDATED_AT')} />
      ),
      cell: ({ row }) => (
        <Typography variant="p">
          {row.original?.updatedAt
            ? format(row.original.updatedAt, 'HH:mm - dd/MM/yyyy')
            : '-'}
        </Typography>
      ),
      size: 50,
    },
    {
      accessorKey: 'action',
      header: ({ column }) => (
        <DataTableColumnHeader column={column} title={t('ACTION')} />
      ),
      cell: ({ row }) => (
        <DropdownMenu>
          <DropdownMenuTrigger asChild>
            <Button
              variant="ghost"
              className="flex h-8 w-8 p-0 data-[state=open]:bg-muted">
              <DotsHorizontalIcon className="h-4 w-4" />
            </Button>
          </DropdownMenuTrigger>
          <DropdownMenuContent
            hidden={!permissions.includes(PERMISSIONS.WRITE_TOOL_REQUIRE)}>
            <Link to={`${ROUTE_NAME.TOOL_REQUIRE}/${row.original._id}/update`}>
              <DropdownMenuItem>{t('UPDATE')}</DropdownMenuItem>
            </Link>
            <DropdownMenuItem
              onClick={() => handleDeleteTool(row.original._id)}>
              {t('DELETE')}
            </DropdownMenuItem>
          </DropdownMenuContent>
        </DropdownMenu>
      ),
      size: 30,
      enableSorting: false,
    },
  ];

  return (
    <>
      <div className="flex bg-secondary p-4 justify-between items-center min-h-24 rounded-md mb-6">
        <Grid className="gap-3">
          <Typography variant="h2">{t('TOOL_MANAGEMENT')}</Typography>
          <Breadcrumbs />
        </Grid>
        {permissions.includes(PERMISSIONS.WRITE_TOOL_REQUIRE) ? (
          <Link to={ROUTE_NAME.CREATE_TOOL_REQUIRE}>
            <Button className="gap-2">
              <Plus />
              <Typography variant="p" affects="removePMargin">
                {t('CREATE_TOOL_REQUIRE')}
              </Typography>
            </Button>
          </Link>
        ) : null}
      </div>
      <BTaskeeTable
        isShowClearButton
        columns={columns}
        total={loaderData?.total || 0}
        data={loaderData?.data || []}
        search={{
          defaultValue: searchParams.get('search') || '',
          name: 'search',
          placeholder: t('SEARCH'),
        }}
        pagination={getPageSizeAndPageIndex({
          total: loaderData?.total || 0,
          pageSize: Number(searchParams.get('pageSize') || 0),
          pageIndex: Number(searchParams.get('pageIndex') || 0),
        })}
        localeAddress="tool-require-index"
        filterDate={{
          name: 'rangeDate',
          defaultValue: {
            from: momentTz(loaderData?.filters?.rangeDate?.from).toDate(),
            to: momentTz(loaderData?.filters?.rangeDate?.to).toDate(),
          },
        }}
      />
    </>
  );
}
