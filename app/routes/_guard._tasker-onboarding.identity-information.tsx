import { Outlet } from '@remix-run/react';
import { PERMISSIONS, ROUTE_NAME } from 'btaskee-constants';
import { useLoaderDataSafely } from 'btaskee-hooks';
import { BreadcrumbsLink, toast } from 'btaskee-ui';
import { useEffect } from 'react';
import { hocLoader } from '~/hoc/remix';
import { willBecomeIdentityInformationLoader } from '~/hooks/useIdentityInformation';

export const handle = {
  breadcrumb: () => {
    return (
      <BreadcrumbsLink
        to={ROUTE_NAME.IDENTITY_INFORMATION_FOR_TASKER}
        label="IDENTITY_INFORMATION_FOR_TASKER"
      />
    );
  },
  i18n: 'identity-information',
};

export const loader = hocLoader(willBecomeIdentityInformationLoader, [
  PERMISSIONS.READ_APPROVED_IDENTITY_INFORMATION_FOR_TASKER,
  PERMISSIONS.READ_VERIFY_IDENTITY_INFORMATION_FOR_TASKER,
]);

export default function IdentityInformation() {
  const { error: loaderError, ...resetLoaderData } =
    useLoaderDataSafely<typeof loader>();

  useEffect(() => {
    if (loaderError) toast({ description: loaderError });
  }, [loaderError]);

  return <Outlet context={resetLoaderData} />;
}
