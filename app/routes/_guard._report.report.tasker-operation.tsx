import { type LoaderFunction<PERSON>rgs, json } from '@remix-run/node';
import { useLoaderData, useSearchParams } from '@remix-run/react';
import { PERMISSIONS, PIE_CHART_COLOR, TASK_STATUS } from 'btaskee-constants';
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  DateRangePicker,
  Grid,
  MultiSelect,
  SelectBase,
  SimplePieChart,
  StackedBarChart,
  type StackedBarChartProps,
  Typography,
} from 'btaskee-ui';
import { DEFAULT_RANGE_DATE_CURRENT_DAY, momentTz } from 'btaskee-utils';
import i18n from 'i18next';
import { useTranslation } from 'react-i18next';
import { hocLoader } from '~/hoc/remix';
import { getUserByUserId } from '~/services/auth.server';
import {
  getCitiesByUserId,
  getServiceText,
  getUserSession,
} from '~/services/helpers.server';
import { getReportTaskerOperation } from '~/services/report-tasker-operation.server';

export const loader = hocLoader(
  async ({ request }: LoaderFunctionArgs) => {
    const { isoCode, userId, isSuperUser } = await getUserSession({
      headers: request.headers,
    });
    const userFound = await getUserByUserId({ userId });

    const userCities = await getCitiesByUserId({
      userId,
      isManager: isSuperUser,
    });
    const services = await getServiceText(isoCode);

    const url = new URL(request.url);

    const rangeDate = url.searchParams.get('rangeDate') || '';
    const cities = url.searchParams.get('cities') || '';
    const serviceText = url.searchParams.get('serviceText') || '';

    const filterValue = {
      rangeDate: DEFAULT_RANGE_DATE_CURRENT_DAY(rangeDate),
      isoCode,
      cities,
      serviceText,
    };

    const data = await getReportTaskerOperation(filterValue);

    return json({
      data,
      filterValue,
      cities: userCities || [],
      services: services.filter(service => !service.isSubscription),
      language: userFound?.language || 'en',
    });
  },
  [PERMISSIONS.READ_REPORT_TASKER_OPERATION],
);

export default function TaskerOperation() {
  const loaderData = useLoaderData<LoaderTypeWithError<typeof loader>>();

  const [, setSearchParams] = useSearchParams();
  const { t } = useTranslation('report-tasker-operation');

  const getStackedChartConfigByTaskStatus = () => {
    const chartConfig: StackedBarChartProps['chartConfig'] = {};

    Object.values(TASK_STATUS).forEach((status, index) => {
      chartConfig[status] = {
        label: t(status),
        color: PIE_CHART_COLOR[index],
      };
    });

    return chartConfig;
  };

  const getStackedDataChart = () => {
    const dataChart: MustBeAny = [];

    loaderData?.data?.ratioTaskOfDistrict?.forEach(task => {
      const taskOfDistrict: MustBeAny = {
        district: task?._id,
      };

      task?.ratioStatus?.forEach((statusInfo: MustBeAny) => {
        taskOfDistrict[statusInfo?.status] = statusInfo?.count;
      });

      dataChart.push(taskOfDistrict);
    });

    return dataChart;
  };

  return (
    <Grid className="mt-6 gap-6">
      <div className="flex items-center gap-2">
        <DateRangePicker
          initialRangeDate={{
            from: momentTz(loaderData?.filterValue?.rangeDate?.from).toDate(),
            to: momentTz(loaderData?.filterValue?.rangeDate?.to).toDate(),
          }}
          onUpdate={value => {
            setSearchParams(params => {
              params.set('rangeDate', JSON.stringify(value));

              return params;
            });
          }}
        />
        <SelectBase
          onValueChange={serviceText => {
            setSearchParams(params => {
              params.set('serviceText', serviceText);

              return params;
            });
          }}
          defaultValue={loaderData?.filterValue?.serviceText || ''}
          options={
            loaderData?.services?.map(service => ({
              label: service?.text?.[loaderData?.language || 'en'] || '',
              value: service?.text?.[i18n.language || 'en'] || '',
            })) || []
          }
        />
        <MultiSelect<(values: OptionType[]) => void>
          setSelected={cities => {
            setSearchParams(params => {
              params.set('cities', cities.map(city => city.value).join(','));

              return params;
            });
          }}
          selected={
            loaderData?.filterValue?.cities?.length
              ? loaderData?.filterValue?.cities
                  .split(',')
                  .map(city => ({ label: city, value: city }))
              : []
          }
          options={
            loaderData?.cities?.length
              ? loaderData?.cities?.map(city => ({
                  label: city,
                  value: city,
                }))
              : []
          }
        />
      </div>
      <div className="grid grid-cols-2 gap-2">
        <Card>
          <CardHeader>{t('TASK_STATISTIC')}</CardHeader>
          <CardContent>
            <CardDescription className="flex">
              <SimplePieChart
                className="w-[400px] h-[400px]"
                legend={{ layout: 'horizontal' }}
                chartProps={{
                  data:
                    loaderData?.data?.ratioTask.map(ratioTasker => ({
                      label: t(ratioTasker._id),
                      value: ratioTasker.count,
                    })) || [],
                }}
              />
              <div>
                {loaderData?.data?.ratioTask?.map(ratioTasker => (
                  <Typography key={ratioTasker?._id} variant="p">
                    {`${t(ratioTasker?._id)}: ${ratioTasker?.count || 0}`}
                  </Typography>
                ))}
              </div>
            </CardDescription>
          </CardContent>
        </Card>
        <Card>
          <CardHeader>{t('TASKER_STATISTIC')}</CardHeader>
          <CardContent>
            <CardDescription className="flex">
              <SimplePieChart
                className="w-[400px] h-[400px]"
                legend={{ layout: 'horizontal' }}
                chartProps={{
                  data:
                    loaderData?.data?.ratioTasker.map(ratioTasker => ({
                      label: t(ratioTasker.label),
                      value: ratioTasker.value,
                    })) || [],
                }}
              />
              <div>
                <Typography variant="p">
                  {`${t('TOTAL_TASKER')}: ${loaderData?.data?.totalTasker || 0}`}
                </Typography>
                {loaderData?.data?.ratioTasker?.map(ratioTasker => (
                  <Typography key={ratioTasker?.label} variant="p">
                    {`${t(ratioTasker?.label)}: ${ratioTasker?.value || 0}`}
                  </Typography>
                ))}
              </div>
            </CardDescription>
          </CardContent>
        </Card>
      </div>
      <Card>
        <CardHeader>{t('TASK_WORKING_IN_DISTRICT')}</CardHeader>
        <CardContent>
          <CardDescription>
            <StackedBarChart
              chartConfig={getStackedChartConfigByTaskStatus()}
              barChart={{
                data: getStackedDataChart(),
              }}
              barProps={Object.values(TASK_STATUS).map((status, index) => ({
                dataKey: status,
                stackId: 'status',
                fill: `hsl(var(--chart-${index + 1}))`,
              }))}
              // eslint-disable-next-line @typescript-eslint/ban-ts-comment
              //@ts-ignore
              xAxis={{ dataKey: 'district' }}
            />
          </CardDescription>
        </CardContent>
      </Card>
    </Grid>
  );
}
