import { Outlet } from '@remix-run/react';
import { PERMISSIONS, ROUTE_NAME } from 'btaskee-constants';
import { useLoaderDataSafely } from 'btaskee-hooks';
import { BreadcrumbsLink, toast } from 'btaskee-ui';
import { useEffect } from 'react';
import { hocLoader } from '~/hoc/remix';
import { willBecomeTaskerOnboardingProfileLoader } from '~/hooks/useGetTaskerOnboardingProfile';

export const handle = {
  breadcrumb: () => {
    return (
      <BreadcrumbsLink
        to={ROUTE_NAME.TASKER_ONBOARDING}
        label="TASKER_PROFILE"
      />
    );
  },
  i18n: 'tasker-profile',
};

export const loader = hocLoader(willBecomeTaskerOnboardingProfileLoader, [
  PERMISSIONS.READ_VERIFYING_STEP_ON_TASKER_ONBOARDING,
  PERMISSIONS.READ_APPROVED_STEP_ON_TASKER_ONBOARDING,
  PERMISSIONS.READ_ELIMINATED_STEP_ON_TASKER_ONBOARDING,
  PERMISSIONS.READ_REJECTED_STEP_ON_TASKER_ONBOARDING,
]);

export default function TaskerOnboardingRoot() {
  const { error: loaderError, ...restLoaderData } =
    useLoaderDataSafely<typeof loader>();

  useEffect(() => {
    if (loaderError) toast({ description: loaderError });
  }, [loaderError]);

  return <Outlet context={restLoaderData} />;
}
