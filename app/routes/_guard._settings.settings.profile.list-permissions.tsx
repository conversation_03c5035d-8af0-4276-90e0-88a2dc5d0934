import { json } from '@remix-run/node';
import {
  isRouteErrorResponse,
  useLoaderData,
  useRouteError,
} from '@remix-run/react';
import { ROUTE_NAME } from 'btaskee-constants';
import {
  Breadcrumbs,
  BreadcrumbsLink,
  BtaskeeResponseError,
  Separator,
  Typography,
  VectorEmptyDataTable,
  cn,
  toast,
} from 'btaskee-ui';
import { groupPermissionsByModule } from 'btaskee-utils';
import { useTranslation } from 'react-i18next';
import { hocLoader } from '~/hoc/remix';
import { getUserId } from '~/services/helpers.server';
import { getUserPermissionsGlobal } from '~/services/role-base-access-control.server';

export function ErrorBoundary() {
  const error = useRouteError() as { message?: string };
  const { t: tCommon } = useTranslation('common');

  if (isRouteErrorResponse(error)) {
    if (error?.message) {
      toast({ description: JSON.stringify(error.message) });
    }

    return <BtaskeeResponseError t={tCommon} errorStatus={error.status} />;
  }

  return <BtaskeeResponseError t={tCommon} errorStatus={500} />;
}

export const handle = {
  breadcrumb: () => (
    <BreadcrumbsLink
      to={`${ROUTE_NAME.PROFILE_SETTING}/list-permissions`}
      label="LIST_PERMISSIONS"
    />
  ),
  i18n: 'user-settings',
};

export const loader = hocLoader(async ({ request }) => {
  const userId = await getUserId({ request });
  const permissions = await getUserPermissionsGlobal(userId);

  return json({ permissions: groupPermissionsByModule(permissions) });
});

export default function ListPermissionsOfUserScreen() {
  const { t: translateKeyUserSettings } = useTranslation('user-settings');
  const loaderData = useLoaderData<LoaderTypeWithError<typeof loader>>();

  return (
    <>
      <div className="grid gap-2 rounded-[15px] bg-secondary p-4">
        <Typography variant="h3">
          {translateKeyUserSettings('LIST_PERMISSIONS')}
        </Typography>
        <Breadcrumbs />
      </div>
      <div className="mt-6">
        {loaderData?.permissions?.length ? (
          loaderData.permissions.map((actionPermission, index) => (
            <div key={actionPermission.module}>
              <Typography
                className="mb-4 text-base font-bold capitalize text-primary"
                variant="p"
                affects="removePMargin">
                {actionPermission?.module}
              </Typography>
              <div className="grid grid-cols-3 gap-x-10 gap-y-4">
                {actionPermission.actions.map(action => (
                  <div key={action._id} className="flex flex-col gap-1">
                    <Typography
                      affects="removePMargin"
                      className="line-clamp-1 capitalize text-[#000000]"
                      variant="p"
                      title={action.name}>
                      {action.name}
                    </Typography>
                    <Typography
                      affects="removePMargin"
                      className="line-clamp-2 font-normal capitalize text-gray-400"
                      variant="p"
                      title={action.description}>
                      {action.description}
                    </Typography>
                  </div>
                ))}
              </div>
              <Separator
                className={cn(
                  index === loaderData?.permissions?.length - 1 ? 'hidden' : '',
                  'my-4',
                )}
              />
            </div>
          ))
        ) : (
          <div className="mt-[92px] flex justify-center">
            <VectorEmptyDataTable width="272px" height="244px" />
          </div>
        )}
      </div>
    </>
  );
}
