import { MagnifyingGlassIcon } from '@radix-ui/react-icons';
import { type SerializeFrom, json } from '@remix-run/node';
import {
  isRouteErrorResponse,
  useLoaderData,
  useOutletContext,
  useRouteError,
  useSearchParams,
} from '@remix-run/react';
import type { Column, ColumnDef, Row } from '@tanstack/react-table';
import {
  BtaskeeResponseError,
  Button,
  Checkbox,
  DataTableBasic,
  DataTableColumnHeader,
  Dialog,
  DialogClose,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
  Input,
  Separator,
  Typography,
  toast,
} from 'btaskee-ui';
import debounce from 'lodash/debounce.js';
import { Plus, Trash2 } from 'lucide-react';
import { useMemo, useState } from 'react';
import { useTranslation } from 'react-i18next';
import { hocLoader } from '~/hoc/remix';
import { getUserSession } from '~/services/helpers.server';
import {
  getListQuizCollections,
  getTotalQuizCollections,
} from '~/services/tasker-training.server';

export const loader = hocLoader(async ({ request }) => {
  const url = new URL(request.url);
  const { isoCode } = await getUserSession({
    headers: request.headers,
  });

  const search = url.searchParams.get('code_or_name') || '';

  const filter = {
    name: search,
    code: search,
  };

  const total = await getTotalQuizCollections({ isoCode, filter });

  const tableData = await getListQuizCollections({
    isoCode,
    skip: 0,
    limit: total,
    filter,
    sort: { updatedAt: -1 },
  });

  return json({
    data: tableData || [],
    total,
  });
});

export function ErrorBoundary() {
  const error = useRouteError() as { message?: string };
  const { t: tCommon } = useTranslation('common');

  if (isRouteErrorResponse(error)) {
    if (error?.message) {
      toast({ description: JSON.stringify(error.message) });
    }

    return <BtaskeeResponseError t={tCommon} errorStatus={error.status} />;
  }

  return <BtaskeeResponseError t={tCommon} errorStatus={500} />;
}

export default function AddQuizzesDialog() {
  const { t } = useTranslation('course');
  const [searchParams, setSearchParams] = useSearchParams();

  const loaderData = useLoaderData<LoaderTypeWithError<typeof loader>>();

  const parentContext = useOutletContext<{
    open: boolean;
    onOpenChange: (open: boolean) => void;
    onClose: () => void;
    selectedQuizzes: SerializeFrom<
      ReturnValueIgnorePromise<typeof getListQuizCollections>[0]
    >[];
    setSelectedQuizzes: (quizzes: SerializeFrom<QuizCollection[]>) => void;
  }>();

  const [selectedCount, setSelectedCount] = useState(
    parentContext.selectedQuizzes.length,
  );
  const [draftSelectedQuizzes, setDraftSelectedQuizzes] = useState<
    SerializeFrom<ReturnValueIgnorePromise<typeof getListQuizCollections>[0]>[]
  >(parentContext.selectedQuizzes);

  const columns: ColumnDef<
    SerializeFrom<ReturnValueIgnorePromise<typeof getListQuizCollections>[0]>
  >[] = useMemo(
    () => [
      {
        accessorKey: '_id',
        header: ({ table }) => (
          <Checkbox
            checked={
              table.getIsAllRowsSelected() ||
              loaderData?.total === selectedCount
            }
            onCheckedChange={value => {
              table.toggleAllPageRowsSelected(!!value);
              if (value) {
                setDraftSelectedQuizzes(loaderData.data);
                setSelectedCount(loaderData.data?.length);
              } else {
                setDraftSelectedQuizzes([]);
                setSelectedCount(0);
              }
            }}
            aria-label="Select all"
            className="translate-y-[2px]"
          />
        ),
        cell: ({ row }) => (
          <Checkbox
            checked={
              !!draftSelectedQuizzes.find(quiz => quiz._id === row.original._id)
            }
            onCheckedChange={value => {
              row.toggleSelected(!!value);
              const updatedRow = row.original;

              setDraftSelectedQuizzes(prevSelectedQuizzes => {
                const newSelectedQuizzes = value
                  ? [...prevSelectedQuizzes, updatedRow]
                  : prevSelectedQuizzes.filter(
                      item => item._id !== updatedRow._id,
                    );

                setSelectedCount(newSelectedQuizzes.length);

                return newSelectedQuizzes;
              });
            }}
            aria-label="Select row"
            className="translate-y-[2px]"
          />
        ),
        size: 20,
        enableSorting: false,
        enableHiding: false,
      },
      {
        accessorKey: 'code',
        header: ({ column }) => (
          <DataTableColumnHeader column={column} title={t('CODE')} />
        ),
        size: 100,
        cell: ({ row }) => (
          <span className="text-gray-800">{row.original.code}</span>
        ),
        enableSorting: true,
      },
      {
        accessorKey: 'name',
        header: ({ column }) => (
          <DataTableColumnHeader
            column={column}
            title={t('QUIZ_COLLECTION_NAME')}
          />
        ),
        size: 500,
        cell: ({ row }) => (
          <span className="text-gray-800">{row.original.title}</span>
        ),
        enableSorting: false,
      },
      {
        accessorKey: 'numberOfQuiz',
        header: ({
          column,
        }: {
          column: Column<
            SerializeFrom<
              ReturnValueIgnorePromise<typeof getListQuizCollections>[0]
            >
          >;
        }) => (
          <DataTableColumnHeader column={column} title={t('NUMBER_OF_QUIZ')} />
        ),
        cell: ({
          row,
        }: {
          row: Row<
            SerializeFrom<
              ReturnValueIgnorePromise<typeof getListQuizCollections>[0]
            >
          >;
        }) => (
          <span className="text-gray-800">
            {row.original.quizzes?.length || '-'}
          </span>
        ),
        size: 120,
        enableSorting: false,
      },
      {
        accessorKey: 'action',
        header: ({ column }) => (
          <DataTableColumnHeader
            column={column}
            className="text-center"
            title={t('ACTION')}
          />
        ),
        size: 150,
        cell: ({ row }) =>
          draftSelectedQuizzes.find(quiz => quiz._id === row.original._id) ? (
            <div className="w-full flex justify-center">
              <Button
                type="button"
                variant="ghost"
                className="text-gray-500 hover:text-gray-600 hover:bg-gray-200 gap-2 h-6 py-0.5"
                onClick={() => {
                  row.toggleSelected(false);
                  setDraftSelectedQuizzes(prev =>
                    prev.filter(quiz => quiz._id !== row.original._id),
                  );
                  setSelectedCount(prev => prev - 1);
                }}>
                <Trash2 className="w-4 h-4" />
                {t('REMOVE')}
              </Button>
            </div>
          ) : (
            <div className="w-full flex justify-center">
              <Button
                type="button"
                variant="ghost"
                className="text-primary hover:text-primary hover:bg-primary-50 gap-2 h-6 py-0.5"
                onClick={() => {
                  row.toggleSelected(true);
                  setDraftSelectedQuizzes(prev => [...prev, row.original]);
                  setSelectedCount(prev => prev + 1);
                }}>
                <Plus className="w-4 h-4" />
                {t('ADD')}
              </Button>
            </div>
          ),
        enableSorting: false,
      },
    ],
    [
      draftSelectedQuizzes,
      loaderData.data,
      loaderData?.total,
      selectedCount,
      t,
    ],
  );

  return (
    <Dialog
      open={parentContext.open}
      onOpenChange={parentContext.onOpenChange}
      defaultOpen={true}>
      <DialogContent
        className="max-w-5xl max-h-[90vh] overflow-y-auto"
        onInteractOutside={parentContext.onClose}>
        <DialogHeader className="space-y-2">
          <DialogTitle className="text-2xl tracking-tighter font-semibold">
            {t('ADD_QUIZ_COLLECTION')}
          </DialogTitle>
          <DialogDescription>
            {t('SELECT_THE_QUESTIONS_BELOW')}
          </DialogDescription>
        </DialogHeader>
        <div className="relative flex items-center">
          <MagnifyingGlassIcon className="absolute left-2 text-base" />
          <Input
            type="search"
            defaultValue={searchParams.get('code_or_name') || ''}
            placeholder={t('SEARCH_BY_CODE_AND_NAME')}
            onChange={debounce(event => {
              setSearchParams(params => {
                params.set('code_or_name', event.target.value || '');
                return params;
              });
            }, 500)}
            className="block h-8 w-[150px] pl-8 lg:w-[250px]"
          />
        </div>
        <DataTableBasic columns={columns} data={loaderData?.data} />
        <Separator />
        <div className="flex flex-col items-end gap-3">
          <Typography variant="p" className="text-secondary-foreground">
            {selectedCount}/{loaderData?.total} {t('SELECTED')}
          </Typography>

          <div className="flex gap-4">
            <Button
              className="border-primary text-primary hover:bg-primary-foreground hover:text-primary"
              type="button"
              variant="outline"
              onClick={parentContext.onClose}>
              {t('CANCEL')}
            </Button>
            <Button
              variant="default"
              type="button"
              onClick={() => {
                parentContext.setSelectedQuizzes(draftSelectedQuizzes);
                parentContext.onClose();
              }}>
              {t('CONFIRM')}
            </Button>
          </div>
        </div>

        <DialogClose className="absolute z-10 right-4 top-4 rounded-sm w-5 h-5 cursor-default bg-white" />
      </DialogContent>
    </Dialog>
  );
}
