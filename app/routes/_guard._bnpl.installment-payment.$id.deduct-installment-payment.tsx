import {
  isRouteErrorResponse,
  json,
  useNavigate,
  useOutletContext,
  useRouteError,
} from '@remix-run/react';
import {
  DEDUCT_INSTALLMENT_PAYMENT_REASON_IN_TASKER_BNPL,
  PERMISSIONS,
  ROUTE_NAME,
} from 'btaskee-constants';
import {
  BtaskeeResponseError,
  Button,
  Card,
  CardContent,
  Dialog,
  DialogContent,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  Form,
  FormField,
  Label,
  RadioGroup,
  RadioGroupItem,
  Typography,
  cn,
  toast,
  useBtaskeeFormController,
} from 'btaskee-ui';
import { formatNumberWithCommas } from 'btaskee-utils';
import type { Dispatch, SetStateAction } from 'react';
import { useTranslation } from 'react-i18next';
import { z } from 'zod';
import { hocLoader } from '~/hoc/remix';

export function ErrorBoundary() {
  const error = useRouteError() as { message?: string };
  const { t: tCommon } = useTranslation('common');

  if (isRouteErrorResponse(error)) {
    if (error?.message) {
      toast({ description: JSON.stringify(error.message) });
    }

    return <BtaskeeResponseError t={tCommon} errorStatus={error.status} />;
  }

  return <BtaskeeResponseError t={tCommon} errorStatus={500} />;
}

export const loader = hocLoader(async () => {
  return json({});
}, PERMISSIONS.WRITE_INSTALLMENT_PAYMENT);

export default function DeductInstallmentPayment() {
  const { t: tDeductPayment } = useTranslation('deduct-install-payment');
  const navigate = useNavigate();

  const { form, onSubmit } = useBtaskeeFormController<{ reason: string }>({
    zodRaw: {
      reason: z.string().min(1, tDeductPayment('THIS_FIELD_IS_REQUIRED')),
    },
    defaultValues: {
      reason: '',
    },
    confirmParams: {
      title: tDeductPayment('TITLE_CONFIRM_DEDUCT_INSTALLMENT_PAYMENT'),
      body: tDeductPayment('DESCRIPTION_CONFIRM_DEDUCT_INSTALLMENT_PAYMENT'),
      actionButton: tDeductPayment('CONFIRM'),
      cancelButton: tDeductPayment('CANCEL'),
    },
    formDataProvided: data => {
      const formData = new FormData();

      formData.append('reason', data?.reason);

      return formData;
    },
  });

  const outletContext = useOutletContext<{
    open: boolean;
    currency: SettingCountry['currency'];
    onOpenChange: Dispatch<SetStateAction<boolean>>;
    installmentDetail: Pick<InstallmentPayment, '_id' | 'remainingAmount'> & {
      tasker: Pick<Users, 'name' | '_id'>;
    };
  }>();

  return (
    <Dialog
      open={outletContext.open}
      onOpenChange={outletContext.onOpenChange}
      defaultOpen={true}>
      <DialogContent
        onInteractOutside={() =>
          navigate(
            `${ROUTE_NAME.INSTALLMENT_PAYMENT}/${outletContext?.installmentDetail?._id}`,
            {
              replace: true,
              preventScrollReset: true,
            },
          )
        }>
        <DialogHeader className="space-y-2">
          <DialogTitle className="text-2xl tracking-tighter font-semibold">
            {tDeductPayment('DEDUCT_INSTALLMENT_PAYMENTS')}
          </DialogTitle>
        </DialogHeader>
        <Card className="bg-gray-100 my-4">
          <CardContent className="p-3">
            <div className="flex flex-col">
              <Typography
                variant="p"
                affects="removePMargin"
                className="text-xs text-gray-400 font-normal">
                {tDeductPayment('TASKER_NAME')}
              </Typography>
              <Typography
                variant="p"
                affects="removePMargin"
                className="text-base font-normal text-gray-600">
                {outletContext?.installmentDetail?.tasker?.name ?? ''}
              </Typography>
            </div>
          </CardContent>
        </Card>
        <Form {...form}>
          <form onSubmit={form.handleSubmit(onSubmit)}>
            <Typography variant="p" className="mb-4">
              {tDeductPayment('CHOOSE_REASON')}
            </Typography>
            <FormField
              name="reason"
              control={form.control}
              render={({ field: { value, onChange } }) => (
                <RadioGroup
                  className="mb-6"
                  value={value}
                  onValueChange={onChange}>
                  {Object.entries(
                    DEDUCT_INSTALLMENT_PAYMENT_REASON_IN_TASKER_BNPL,
                  ).map(([key, reason]) => (
                    <div
                      key={key}
                      className={cn(
                        'flex items-center h-12 border p-4 rounded-md',
                        reason === value
                          ? 'border-primary bg-primary-50'
                          : 'border-gray-300',
                      )}>
                      <RadioGroupItem
                        className="min-w-4"
                        value={reason || ''}
                        id={reason}
                      />
                      <Label className="cursor-pointer ml-2" htmlFor={reason}>
                        {tDeductPayment(key)}
                      </Label>
                    </div>
                  ))}
                </RadioGroup>
              )}
            />
            <Typography
              variant="p"
              affects="removePMargin"
              className="p-[10px] bg-primary-50 text-primary mb-8">
              {tDeductPayment('NOTE_DEDUCT_INSTALLMENT_PAYMENT', {
                amount: formatNumberWithCommas(
                  outletContext?.installmentDetail?.remainingAmount ?? 0,
                ),
                code: outletContext?.currency?.code ?? '',
              })}
            </Typography>
            <DialogFooter>
              <Button
                className="text-primary border-primary"
                type="button"
                variant="outline"
                onClick={() => navigate(-1)}>
                {tDeductPayment('CANCEL')}
              </Button>
              <Button
                type="submit"
                className={form.formState?.isValid ? '' : 'bg-gray-300'}
                disabled={!form.formState?.isValid}>
                {tDeductPayment('CONFIRM')}
              </Button>
            </DialogFooter>
          </form>
        </Form>
      </DialogContent>
    </Dialog>
  );
}
