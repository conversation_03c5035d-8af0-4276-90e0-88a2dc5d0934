import type { LoaderFunctionArgs, SerializeFrom } from '@remix-run/node';
import { json } from '@remix-run/node';
import {
  Link,
  isRouteErrorResponse,
  useNavigate,
  useRouteError,
  useSearchParams,
} from '@remix-run/react';
import type { ColumnDef } from '@tanstack/react-table';
import { PERMISSIONS, ROUTE_NAME, YES_NO_OPTIONS } from 'btaskee-constants';
import { useGlobalStore, useLoaderDataSafely } from 'btaskee-hooks';
import {
  BTaskeeTable,
  Breadcrumbs,
  BtaskeeResponseError,
  Button,
  DATE_RANGE_PICKER_OPTIONS,
  DataTableColumnHeader,
  DropdownMenuBase,
  Grid,
  Typography,
  toast,
} from 'btaskee-ui';
import {
  DEFAULT_RANGE_DATE_CURRENT_DAY,
  convertSortString,
  getPageSizeAndPageIndex,
  getSkipAndLimit,
  getValuesFromSearchParams,
  momentTz,
} from 'btaskee-utils';
import { Plus } from 'lucide-react';
import { useEffect, useMemo } from 'react';
import { useTranslation } from 'react-i18next';
import { hocLoader } from '~/hoc/remix';
import { getUserSession } from '~/services/helpers.server';
import { getSession } from '~/services/session.server';
import {
  getListQuizzes,
  getTotalQuizzes,
} from '~/services/tasker-training.server';

export function ErrorBoundary() {
  const error = useRouteError() as { message?: string };
  const { t: tCommon } = useTranslation('common');

  if (isRouteErrorResponse(error)) {
    if (error?.message) {
      toast({ description: JSON.stringify(error.message) });
    }

    return <BtaskeeResponseError t={tCommon} errorStatus={error.status} />;
  }

  return <BtaskeeResponseError t={tCommon} errorStatus={500} />;
}

export const loader = hocLoader(async ({ request }: LoaderFunctionArgs) => {
  const session = await getSession(request.headers.get('cookie'));
  const flashMessage = session.get('flashMessage');

  const [
    { sort, isRandomAnswer, isIncludeImage, searchText: search, updatedAt },
    { pageSize, pageIndex },
  ] = getValuesFromSearchParams(new URL(request.url).searchParams, {
    keysString: [
      'sort',
      'isRandomAnswer',
      'isIncludeImage',
      'searchText',
      'updatedAt',
    ],
    keysNumber: ['pageSize', 'pageIndex'],
  });

  const filter = {
    name: search,
    code: search,
    filters: {
      ...(isIncludeImage && {
        image: isIncludeImage,
      }),
      ...(isRandomAnswer && {
        randomAnswer: isRandomAnswer,
      }),
    },
    ...(updatedAt && {
      updatedAt: DEFAULT_RANGE_DATE_CURRENT_DAY(updatedAt),
    }),
  };

  const { isoCode } = await getUserSession({
    headers: request.headers,
  });

  const total = await getTotalQuizzes({ isoCode, filter });
  const { limit, skip } = getSkipAndLimit(
    getPageSizeAndPageIndex({
      total,
      pageSize: pageSize,
      pageIndex: pageIndex,
    }),
  );
  const tableData = await getListQuizzes({
    isoCode,
    skip,
    limit,
    filter,
    sort: convertSortString({
      sortString: sort,
      defaultValue: { updatedAt: -1 },
    }),
  });

  return json({
    total: total || 0,
    tableData: tableData,
    flashMessage: flashMessage || '',
    filter: {
      ...filter,
      isIncludeImage,
      isRandomAnswer,
    },
  });
}, PERMISSIONS.READ_TRAINING_QUIZ);

export default function Quiz() {
  const { t } = useTranslation('quiz');
  const loaderData = useLoaderDataSafely<typeof loader>();

  useEffect(() => {
    if (loaderData?.flashMessage) {
      toast({
        variant: 'success',
        description: loaderData.flashMessage,
      });
    }
  }, [loaderData.flashMessage]);

  const permissions = useGlobalStore(store => store.permissions);
  const [searchParams] = useSearchParams();
  const navigate = useNavigate();

  const columns: ColumnDef<
    SerializeFrom<ReturnValueIgnorePromise<typeof getListQuizzes>[0]>
  >[] = useMemo(
    () => [
      {
        accessorKey: 'code',
        header: ({ column }) => (
          <DataTableColumnHeader column={column} title={t('CODE')} />
        ),
        maxSize: 40,
        cell: ({ row }) => (
          <Typography variant="p" affects="removePMargin">
            {row.original?.code}
          </Typography>
        ),
      },
      {
        size: 400,
        accessorKey: 'question',
        header: ({ column }) => (
          <DataTableColumnHeader
            className="whitespace-nowrap"
            column={column}
            title={t('QUESTION')}
          />
        ),
        cell: ({ row }) => (
          <Typography variant="p" affects="removePMargin">
            {row.original?.title}
          </Typography>
        ),
        enableSorting: false,
      },
      {
        accessorKey: 'image',
        header: ({ column }) => (
          <DataTableColumnHeader column={column} title={t('IMAGE')} />
        ),
        maxSize: 40,
        cell: ({ row }) => (
          <Typography variant="p" affects="removePMargin">
            {row.original?.image ? t('YES') : t('NO')}
          </Typography>
        ),
        enableSorting: false,
      },
      {
        accessorKey: 'isRandomAnswer',
        header: ({ column }) => (
          <DataTableColumnHeader
            className="whitespace-nowrap"
            column={column}
            title={t('RANDOM_ANSWER')}
          />
        ),
        maxSize: 40,
        cell: ({ row }) => (
          <Typography variant="p" affects="removePMargin">
            {row.original?.isRandomAnswer ? t('YES') : t('NO')}
          </Typography>
        ),
        enableSorting: false,
      },
      {
        accessorKey: 'updatedByUsername',
        header: ({ column }) => (
          <DataTableColumnHeader
            className="whitespace-nowrap"
            column={column}
            title={t('UPDATED_BY')}
          />
        ),
        size: 80,
        cell: ({ row }) => (
          <Typography variant="p" affects="removePMargin">
            {row.original?.updatedByUsername || row.original?.createdByUsername}
          </Typography>
        ),
        enableSorting: false,
      },
      {
        accessorKey: 'updatedAt',
        header: ({ column }) => (
          <DataTableColumnHeader
            className="whitespace-nowrap"
            column={column}
            title={t('UPDATED_AT')}
          />
        ),
        size: 40,
        cell: ({ row }) => (
          <Typography
            variant="p"
            className="whitespace-nowrap"
            affects="removePMargin">
            {row.original?.updatedAt
              ? momentTz(row.original.updatedAt).format('HH:mm - DD/MM/YYYY')
              : null}
          </Typography>
        ),
      },
      {
        accessorKey: 'action',
        header: ({ column }) => {
          return (
            <DataTableColumnHeader
              column={column}
              className="whitespace-nowrap text-right"
              title={t('ACTION')}
            />
          );
        },
        size: 40,
        cell: ({ row }) => (
          <DropdownMenuBase
            btnClassName="px-3 w-fit ml-auto"
            align="end"
            links={[
              {
                link: `${ROUTE_NAME.QUIZ}/${row.original?._id}`,
                label: t('VIEW_DETAIL'),
              },
              {
                link: `${ROUTE_NAME.QUIZ}/${row.original?._id}/edit`,
                label: t('UPDATE_QUIZ'),
              },
            ]}
          />
        ),
        enableSorting: false,
      },
    ],
    [t],
  );

  return (
    <>
      <div className="flex bg-secondary p-4 justify-between items-center min-h-24 rounded-md mb-6">
        <Grid className="gap-3">
          <Typography className="capitalize" variant="h2">
            {t('QUIZ')}
          </Typography>
          <Breadcrumbs />
        </Grid>
        <Button
          asChild
          disabled={!permissions.includes(PERMISSIONS.WRITE_TRAINING_QUIZ)}>
          <Link className="flex gap-2 items-center" to={ROUTE_NAME.CREATE_QUIZ}>
            <Plus />
            {t('CREATE_QUIZ')}
          </Link>
        </Button>
      </div>
      <BTaskeeTable
        isShowClearButton
        columns={columns}
        total={loaderData.total || 0}
        data={loaderData.tableData || []}
        onClickRow={row => navigate(`${ROUTE_NAME.QUIZ}/${row._id}`)}
        search={{
          defaultValue: loaderData.filter?.name || '',
          name: 'searchText',
          placeholder: t('SEARCH_BY_CODE_QUESTION'),
        }}
        pagination={getPageSizeAndPageIndex({
          total: loaderData.total || 0,
          pageSize: Number(searchParams.get('pageSize') || 0),
          pageIndex: Number(searchParams.get('pageIndex') || 0),
        })}
        filters={[
          {
            name: 'isIncludeImage',
            placeholder: t('IMAGE'),
            options: Object.values(YES_NO_OPTIONS).map(filter => ({
              label: t(filter),
              value: filter,
            })),
            value: loaderData.filter?.isIncludeImage || '',
          },
          {
            name: 'isRandomAnswer',
            placeholder: t('RANDOM_ANSWER'),
            options: Object.values(YES_NO_OPTIONS).map(filter => ({
              label: t(filter),
              value: filter,
            })),
            value: loaderData.filter?.isRandomAnswer || '',
          },
        ]}
        localeAddress="quiz"
        // eslint-disable-next-line @typescript-eslint/ban-ts-comment
        // @ts-ignore
        filterDate={{
          name: 'updatedAt',
          defaultValue: loaderData?.filter?.updatedAt
            ? {
                from: momentTz(loaderData.filter?.updatedAt?.from).toDate(),
                to: momentTz(loaderData.filter?.updatedAt?.to).toDate(),
              }
            : undefined,
          defaultRangeDateOptions: DATE_RANGE_PICKER_OPTIONS.NONE,
        }}
      />
    </>
  );
}
