import type { SerializeFrom } from '@remix-run/node';
import {
  isRouteErrorResponse,
  useNavigate,
  useRouteError,
  useSearchParams,
} from '@remix-run/react';
import { type ColumnDef } from '@tanstack/react-table';
import {
  TaskerProfileStatus,
  TaskerProfileTabModel,
} from 'app/components/tasker-common';
import {
  PERMISSIONS,
  ROUTE_NAME,
  TASKER_ONBOARDING_PROCESS_STATUS,
  TASKER_PROFILE_TAB_ID,
} from 'btaskee-constants';
import {
  BTaskeeTable,
  Badge,
  Breadcrumbs,
  BtaskeeResponseError,
  DataTableColumnHeader,
  Grid,
  Typography,
  toast,
  Button,
} from 'btaskee-ui';
import { getPageSizeAndPageIndex, momentTz } from 'btaskee-utils';
import { format } from 'date-fns';
import i18n from 'i18next';
import { useMemo } from 'react'; 
import { useTranslation } from 'react-i18next';
import { ServiceIconWithTooltip } from '~/components/common/CommonComponent';
import { useIdentityInformation } from '~/hooks/useIdentityInformation';
import type { getTaskerProfile } from '~/services/tasker-profile.server';

// Local tab configuration for identity verification system
const IDENTITY_VERIFICATION_TABS = [
  {
    title: "VERIFY_IDENTITY_INFORMATION_FOR_TASKER",
    tabId: TASKER_PROFILE_TAB_ID.VERIFYING,
    permission: PERMISSIONS.READ_VERIFY_IDENTITY_INFORMATION_FOR_TASKER,
    status: [
      TASKER_ONBOARDING_PROCESS_STATUS.VERIFYING,
      TASKER_ONBOARDING_PROCESS_STATUS.NEEDS_UPDATE,
      TASKER_ONBOARDING_PROCESS_STATUS.UPDATED,
      // Add "Not Uploaded" status - this will be handled by filtering profiles without identity card data
    ]
  },
  {
    title: "APPROVED_IDENTITY_INFORMATION_FOR_TASKER",
    tabId: TASKER_PROFILE_TAB_ID.APPROVED,
    permission: PERMISSIONS.READ_APPROVED_IDENTITY_INFORMATION_FOR_TASKER,
    status: [TASKER_ONBOARDING_PROCESS_STATUS.APPROVED]
  }
];

export function ErrorBoundary() {
  const error = useRouteError() as { message?: string };
  const { t: tCommon } = useTranslation('identity-information');

  if (isRouteErrorResponse(error)) {
    if (error?.message) {
      toast({ description: JSON.stringify(error.message) });
    }

    return <BtaskeeResponseError t={tCommon} errorStatus={error.status} />;
  }

  return <BtaskeeResponseError t={tCommon} errorStatus={500} />;
}

export default function IdentityInformation() {
  const { t } = useTranslation('identity-information');

  const [searchParams, setSearchParams] = useSearchParams();

  // Get identity information data from the hook
  const identityInfo = useIdentityInformation();
  const {
    data,
    total,
    filterValue,
    tabId,
    status: availableStatus,
    userCities,
    permissions
  } = identityInfo;

  const navigate = useNavigate();

  const columns: ColumnDef<
    SerializeFrom<ReturnValueIgnorePromise<typeof getTaskerProfile>[0]>
  >[] = useMemo(
    () => [
      {
        accessorKey: 'taskerPhone',
        header: ({ column }) => (
          <DataTableColumnHeader column={column} title={t('TASKER_PHONE')} />
        ),
        cell: ({ row }) => (
          <Typography variant="p">{row.original?.taskerPhone}</Typography>
        ),
        enableSorting: false,
      },
      {
        accessorKey: 'taskerName',
        header: ({ column }) => (
          <DataTableColumnHeader column={column} title={t('TASKER_NAME')} />
        ),
        cell: ({ row }) => (
          <Typography variant="p">{row.original?.taskerName}</Typography>
        ),
        enableSorting: false,
      },
      {
        accessorKey: 'services',
        header: ({ column }) => (
          <DataTableColumnHeader column={column} title={t('SERVICES')} />
        ),
        cell: ({ row }) => (
          <div className="flex flex-wrap gap-3">
            {row.original?.services?.map(service => (
              <ServiceIconWithTooltip
                key={service?.text?.[i18n.language || 'en']}
                service={service}
              />
            ))}
          </div>
        ),
        enableSorting: false,
      },
      {
        accessorKey: 'taskerWorkingPlaces',
        header: ({ column }) => (
          <DataTableColumnHeader
            column={column}
            title={t('TASKER_WORKING_PLACES')}
          />
        ),
        cell: ({ row }) => (
          <div className="flex flex-wrap gap-3">
            {row.original?.taskerWorkingPlaces?.map(workingPlace => (
              <Badge
                key={workingPlace.city}
                className="rounded-md bg-blue-50 text-blue">
                {workingPlace.city}
              </Badge>
            ))}
          </div>
        ),
        enableSorting: false,
      },
      {
        accessorKey: 'taskerGender',
        header: ({ column }) => (
          <DataTableColumnHeader column={column} title={t('TASKER_GENDER')} />
        ),
        cell: ({ row }) => (
          <Typography variant="p">{t(row.original?.taskerGender)}</Typography>
        ),
        enableSorting: false,
      },
      {
        accessorKey: 'updatedAt',
        header: ({ column }) => (
          <DataTableColumnHeader column={column} title={t('UPDATED_AT')} />
        ),
        cell: ({ row }) =>
          row.original?.updatedAt ? (
            <div className="whitespace-nowrap">
              {format(row.original.updatedAt, 'HH:mm - dd/MM/yyyy')}
            </div>
          ) : null,
      },
      {
        accessorKey: 'identityCard',
        header: ({ column }) => (
          <DataTableColumnHeader
            column={column}
            title={t('IDENTITY_CARD_STATUS')}
          />
        ),
        cell: ({ row }) => (
          <div className="text-center">
            <Badge
              className={
                row.original?.identityCard?.status === 'APPROVED'
                  ? 'bg-green-100 text-green-800'
                  : row.original?.identityCard?.status === 'REJECTED'
                  ? 'bg-red-100 text-red-800'
                  : 'bg-yellow-100 text-yellow-800'
              }
            >
              {row.original?.identityCard?.status || 'NOT_UPLOADED'}
            </Badge>
          </div>
        ),
        enableSorting: false,
      },
      {
        accessorKey: 'status',
        header: ({ column }) => (
          <DataTableColumnHeader
            className="text-center"
            column={column}
            title={t('STATUS')}
          />
        ),
        cell: ({ row }) => (
          <div className="text-center">
            <TaskerProfileStatus status={row.original?.status} />
          </div>
        ),
        enableSorting: false,
      },
      {
        accessorKey: 'actions',
        header: () => (
          <div className="text-center">
            {t('ACTIONS')}
          </div>
        ),
        cell: ({ row }) => {
          const canApprove = row.original?.status === 'VERIFYING' || row.original?.status === 'UPDATED';
          const canReject = row.original?.status === 'VERIFYING' || row.original?.status === 'UPDATED';

          return (
            <div className="flex justify-center gap-2">
              {canApprove && (
                <Button
                  size="sm"
                  variant="outline"
                  className="text-green-600 border-green-600 hover:bg-green-50"
                  onClick={(e) => {
                    e.stopPropagation();
                    handleApprove(row.original);
                  }}
                >
                  {t('APPROVE')}
                </Button>
              )}
              {canReject && (
                <Button
                  size="sm"
                  variant="outline"
                  className="text-red-600 border-red-600 hover:bg-red-50"
                  onClick={(e) => {
                    e.stopPropagation();
                    handleReject(row.original);
                  }}
                >
                  {t('REJECT')}
                </Button>
              )}
            </div>
          );
        },
        enableSorting: false,
      },
    ],
    [t],
  );

  return (
    <div className="flex flex-col gap-6">
      <Grid className="p-4 bg-secondary">
        <div className="grid rounded-xl">
          <Typography className="mb-3 capitalize" variant="h2">
            {t('IDENTITY_INFORMATION_FOR_TASKER')}
          </Typography>
          <Breadcrumbs />
        </div>
      </Grid>

      {/* Identity Information Data Display */}
      <div className="p-4 bg-white border rounded-lg">
        <Typography variant="h3" className="mb-4">
          Identity Information Context
        </Typography>
        <div className="grid grid-cols-1 gap-4 md:grid-cols-3">
          <div>
            <Typography variant="h4" className="mb-2">
              Available Cities ({userCities.length})
            </Typography>
            <div className="flex flex-wrap gap-2">
              {userCities.map((city, index) => (
                <Badge
                  key={index}
                  className="text-blue-700 bg-blue-50"
                >
                  {city}
                </Badge>
              ))}
            </div>
          </div>

          <div>
            <Typography variant="h4" className="mb-2">
              Status Options
            </Typography>
            <div className="flex flex-wrap gap-2">
              {availableStatus.map((status, index) => (
                <Badge
                  key={index}
                  className={
                    status === 'APPROVED'
                      ? 'bg-green-100 text-green-800'
                      : 'bg-yellow-100 text-yellow-800'
                  }
                >
                  {t(status)}
                </Badge>
              ))}
            </div>
          </div>

          <div>
            <Typography variant="h4" className="mb-2">
              Permissions ({permissions.length})
            </Typography>
            <div className="space-y-1">
              <Typography variant="p" className="text-sm">
                Can verify: {permissions.includes('READ_VERIFY_IDENTITY_INFORMATION_FOR_TASKER') ? 'Yes' : 'No'}
              </Typography>
              <Typography variant="p" className="text-sm">
                Can view approved: {permissions.includes('READ_APPROVED_IDENTITY_INFORMATION_FOR_TASKER') ? 'Yes' : 'No'}
              </Typography>
            </div>
          </div>
        </div>
      </div>
      <TaskerProfileTabModel
        items={IDENTITY_VERIFICATION_TABS}
        permissions={permissions || []}
        value={tabId || ''}
        onValueChange={value => {
          setSearchParams(params => {
            params.set('tabId', value);

            const tabFound = IDENTITY_VERIFICATION_TABS.find(
              tab => tab.tabId === value,
            );

            if (tabFound) {
              params.set('status', tabFound.status?.[0]);
            }

            return params;
          });
        }}
      />
      <BTaskeeTable
        total={total || 0}
        data={data || []}
        columns={columns}
        search={{
          name: 'searchText',
          placeholder: t('SEARCH_NAME_OR_PHONE'),
          defaultValue: filterValue?.searchText || '',
        }}
        isShowClearButton
        filterDate={{
          name: 'createdAt',
          defaultValue: {
            from: momentTz(filterValue?.createdAt.from).toDate(),
            to: momentTz(filterValue?.createdAt.to).toDate(),
          },
        }}
        localeAddress="tasker-profile"
        onClickRow={profile =>
          navigate(
            `${ROUTE_NAME.IDENTITY_INFORMATION_FOR_TASKER}/${profile?._id}`,
          )
        }
        pagination={getPageSizeAndPageIndex({
          total: total || 0,
          pageSize: Number(searchParams.get('pageSize') || 0),
          pageIndex: Number(searchParams.get('pageIndex') || 0),
        })}
        filters={
          availableStatus?.length > 1
            ? [
                {
                  placeholder: t('STATUS'),
                  name: 'status',
                  className: 'w-[129px] h-[42px]',
                  options: availableStatus?.map((status: string) => ({
                    label: t(status),
                    value: status,
                  })),
                  value: filterValue?.status.join(','),
                },
              ]
            : []
        }
      />
    </div>
  );
}
