import type { SerializeFrom } from '@remix-run/node';
import {
  isRouteErrorResponse,
  useNavigate,
  useRouteError,
  useSearchParams,
} from '@remix-run/react';
import { type ColumnDef } from '@tanstack/react-table';
import {
  TaskerProfileStatus,
  TaskerProfileTabModel,
} from 'app/components/tasker-common';
import {
  IDENTITY_INFORMATION_FOR_TASKER_TAB,
  ROUTE_NAME,
} from 'btaskee-constants';
import {
  BTaskeeTable,
  Badge,
  Breadcrumbs,
  BtaskeeResponseError,
  DataTableColumnHeader,
  Grid,
  Typography,
  toast,
} from 'btaskee-ui';
import { getPageSizeAndPageIndex, momentTz } from 'btaskee-utils';
import { format } from 'date-fns';
import i18n from 'i18next';
import { useMemo } from 'react'; 
import { useTranslation } from 'react-i18next';
import { ServiceIconWithTooltip } from '~/components/common/CommonComponent';
import { useOutletGetStaffOnboardingProfile } from '~/hooks/useGetStaffOnboardingProfile';
import { useIdentityInformation } from '~/hooks/useIdentityInformation';
import type { getTaskerProfile } from '~/services/tasker-profile.server';

export function ErrorBoundary() {
  const error = useRouteError() as { message?: string };
  const { t: tCommon } = useTranslation('identity-information');

  if (isRouteErrorResponse(error)) {
    if (error?.message) {
      toast({ description: JSON.stringify(error.message) });
    }

    return <BtaskeeResponseError t={tCommon} errorStatus={error.status} />;
  }

  return <BtaskeeResponseError t={tCommon} errorStatus={500} />;
}

export default function IdentityInformation() {
  const { t } = useTranslation('identity-information');

  const [searchParams, setSearchParams] = useSearchParams();

  const outletData = useOutletGetStaffOnboardingProfile();

  // Get identity information data from the hook
  const identityInfo = useIdentityInformation();
  const { userCities, status: availableStatus, permissions } = identityInfo;

  const navigate = useNavigate();

  const columns: ColumnDef<
    SerializeFrom<ReturnValueIgnorePromise<typeof getTaskerProfile>[0]>
  >[] = useMemo(
    () => [
      {
        accessorKey: 'taskerPhone',
        header: ({ column }) => (
          <DataTableColumnHeader column={column} title={t('TASKER_PHONE')} />
        ),
        cell: ({ row }) => (
          <Typography variant="p">{row.original?.taskerPhone}</Typography>
        ),
        enableSorting: false,
      },
      {
        accessorKey: 'taskerName',
        header: ({ column }) => (
          <DataTableColumnHeader column={column} title={t('TASKER_NAME')} />
        ),
        cell: ({ row }) => (
          <Typography variant="p">{row.original?.taskerName}</Typography>
        ),
        enableSorting: false,
      },
      {
        accessorKey: 'services',
        header: ({ column }) => (
          <DataTableColumnHeader column={column} title={t('SERVICES')} />
        ),
        cell: ({ row }) => (
          <div className="flex flex-wrap gap-3">
            {row.original?.services?.map(service => (
              <ServiceIconWithTooltip
                key={service?.text?.[i18n.language || 'en']}
                service={service}
              />
            ))}
          </div>
        ),
        enableSorting: false,
      },
      {
        accessorKey: 'taskerWorkingPlaces',
        header: ({ column }) => (
          <DataTableColumnHeader
            column={column}
            title={t('TASKER_WORKING_PLACES')}
          />
        ),
        cell: ({ row }) => (
          <div className="flex flex-wrap gap-3">
            {row.original?.taskerWorkingPlaces?.map(workingPlace => (
              <Badge
                key={workingPlace.city}
                className="rounded-md bg-blue-50 text-blue">
                {workingPlace.city}
              </Badge>
            ))}
          </div>
        ),
        enableSorting: false,
      },
      {
        accessorKey: 'taskerGender',
        header: ({ column }) => (
          <DataTableColumnHeader column={column} title={t('TASKER_GENDER')} />
        ),
        cell: ({ row }) => (
          <Typography variant="p">{t(row.original?.taskerGender)}</Typography>
        ),
        enableSorting: false,
      },
      {
        accessorKey: 'updatedAt',
        header: ({ column }) => (
          <DataTableColumnHeader column={column} title={t('UPDATED_AT')} />
        ),
        cell: ({ row }) =>
          row.original?.updatedAt ? (
            <div className="whitespace-nowrap">
              {format(row.original.updatedAt, 'HH:mm - dd/MM/yyyy')}
            </div>
          ) : null,
      },
      {
        accessorKey: 'status',
        header: ({ column }) => (
          <DataTableColumnHeader
            className="text-center"
            column={column}
            title={t('STATUS')}
          />
        ),
        cell: ({ row }) => (
          <div className="text-center">
            <TaskerProfileStatus status={row.original?.status} />
          </div>
        ),
        enableSorting: false,
      },
    ],
    [t],
  );

  return (
    <div className="flex flex-col gap-6">
      <Grid className="p-4 bg-secondary">
        <div className="grid rounded-xl">
          <Typography className="mb-3 capitalize" variant="h2">
            {t('IDENTITY_INFORMATION_FOR_TASKER')}
          </Typography>
          <Breadcrumbs />
        </div>
      </Grid>

      {/* Identity Information Data Display */}
      <div className="p-4 bg-white border rounded-lg">
        <Typography variant="h3" className="mb-4">
          Identity Information Context
        </Typography>
        <div className="grid grid-cols-1 gap-4 md:grid-cols-3">
          <div>
            <Typography variant="h4" className="mb-2">
              Available Cities ({userCities.length})
            </Typography>
            <div className="flex flex-wrap gap-2">
              {userCities.map((city, index) => (
                <Badge
                  key={index}
                  className="text-blue-700 bg-blue-50"
                >
                  {city}
                </Badge>
              ))}
            </div>
          </div>

          <div>
            <Typography variant="h4" className="mb-2">
              Status Options
            </Typography>
            <div className="flex flex-wrap gap-2">
              {availableStatus.map((status, index) => (
                <Badge
                  key={index}
                  className={
                    status === 'APPROVED'
                      ? 'bg-green-100 text-green-800'
                      : 'bg-yellow-100 text-yellow-800'
                  }
                >
                  {t(status)}
                </Badge>
              ))}
            </div>
          </div>

          <div>
            <Typography variant="h4" className="mb-2">
              Permissions ({permissions.length})
            </Typography>
            <div className="space-y-1">
              <Typography variant="p" className="text-sm">
                Can verify: {permissions.includes('READ_VERIFY_IDENTITY_INFORMATION_FOR_TASKER') ? 'Yes' : 'No'}
              </Typography>
              <Typography variant="p" className="text-sm">
                Can view approved: {permissions.includes('READ_APPROVED_IDENTITY_INFORMATION_FOR_TASKER') ? 'Yes' : 'No'}
              </Typography>
            </div>
          </div>
        </div>
      </div>
      <TaskerProfileTabModel
        items={IDENTITY_INFORMATION_FOR_TASKER_TAB}
        permissions={outletData?.permissions || []}
        value={outletData?.tabId || ''}
        onValueChange={value => {
          setSearchParams(params => {
            params.set('tabId', value);

            const tabFound = IDENTITY_INFORMATION_FOR_TASKER_TAB.find(
              tab => tab.tabId === value,
            );

            if (tabFound) {
              params.set('status', tabFound.status?.[0]);
            }

            return params;
          });
        }}
      />
      <BTaskeeTable
        total={outletData?.total || 0}
        data={outletData?.data || []}
        columns={columns}
        search={{
          name: 'searchText',
          placeholder: t('SEARCH_NAME_OR_PHONE'),
          defaultValue: outletData.filterValue?.searchText || '',
        }}
        isShowClearButton
        filterDate={{
          name: 'createdAt',
          defaultValue: {
            from: momentTz(outletData.filterValue?.createdAt.from).toDate(),
            to: momentTz(outletData.filterValue?.createdAt.to).toDate(),
          },
        }}
        localeAddress="tasker-profile"
        onClickRow={profile =>
          navigate(
            `${ROUTE_NAME.IDENTITY_INFORMATION_FOR_TASKER}/${profile?._id}`,
          )
        }
        pagination={getPageSizeAndPageIndex({
          total: outletData?.total || 0,
          pageSize: Number(searchParams.get('pageSize') || 0),
          pageIndex: Number(searchParams.get('pageIndex') || 0),
        })}
        filters={
          outletData.status?.length > 1
            ? [
                {
                  placeholder: t('STATUS'),
                  name: 'status',
                  className: 'w-[129px] h-[42px]',
                  options: outletData.status?.map((status: string) => ({
                    label: t(status),
                    value: status,
                  })),
                  value: outletData?.filterValue?.status.join(','),
                },
              ]
            : []
        }
      />
    </div>
  );
}
