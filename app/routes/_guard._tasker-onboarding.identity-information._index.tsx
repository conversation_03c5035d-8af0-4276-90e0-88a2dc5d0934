import type { SerializeFrom } from '@remix-run/node';
import {
  isRouteErrorResponse,
  useNavigate,
  useRouteError,
  useSearchParams,
} from '@remix-run/react';
import { type ColumnDef } from '@tanstack/react-table';
import {
  ConfirmUpdatingProfile,
  type ConfirmUpdatingProfileFormProps,
  TaskerProfileStatus,
  TaskerProfileTabModel,
} from 'app/components/tasker-common';
import {
  PERMISSIONS,
  ROUTE_NAME,
  TASKER_ONBOARDING_PROCESS_STATUS,
  TASKER_PROFILE_TAB_ID,
} from 'btaskee-constants';
import {
  BTaskeeTable,
  Badge,
  Breadcrumbs,
  BtaskeeResponseError,
  Button,
  DataTableColumnHeader,
  Grid,
  Typography,
  toast,
  useConfirm,
} from 'btaskee-ui';
import { getPageSizeAndPageIndex, momentTz } from 'btaskee-utils';
import { format } from 'date-fns';
import i18n from 'i18next';
import { useCallback, useMemo } from 'react';
import { useForm } from 'react-hook-form';
import { useTranslation } from 'react-i18next';
import { ServiceIconWithTooltip } from '~/components/common/CommonComponent';
import { useIdentityInformation } from '~/hooks/useIdentityInformation';
import type { getTaskerProfile } from '~/services/tasker-profile.server';

// Local tab configuration for identity verification system
const IDENTITY_VERIFICATION_TABS = [
  {
    title: 'VERIFY_IDENTITY_INFORMATION_FOR_TASKER',
    tabId: TASKER_PROFILE_TAB_ID.VERIFYING,
    permission: PERMISSIONS.READ_VERIFY_IDENTITY_INFORMATION_FOR_TASKER,
    status: [
      TASKER_ONBOARDING_PROCESS_STATUS.VERIFYING,
      TASKER_ONBOARDING_PROCESS_STATUS.NEEDS_UPDATE,
      TASKER_ONBOARDING_PROCESS_STATUS.UPDATED,
      // Add "Not Uploaded" status - this will be handled by filtering profiles without identity card data
    ],
  },
  {
    title: 'APPROVED_IDENTITY_INFORMATION_FOR_TASKER',
    tabId: TASKER_PROFILE_TAB_ID.APPROVED,
    permission: PERMISSIONS.READ_APPROVED_IDENTITY_INFORMATION_FOR_TASKER,
    status: [TASKER_ONBOARDING_PROCESS_STATUS.APPROVED],
  },
];

export function ErrorBoundary() {
  const error = useRouteError() as { message?: string };
  const { t: tCommon } = useTranslation('identity-information');

  if (isRouteErrorResponse(error)) {
    if (error?.message) {
      toast({ description: JSON.stringify(error.message) });
    }

    return <BtaskeeResponseError t={tCommon} errorStatus={error.status} />;
  }

  return <BtaskeeResponseError t={tCommon} errorStatus={500} />;
}

export default function IdentityInformation() {
  const { t: tTaskerProfile } = useTranslation('tasker-profile');
  const confirm = useConfirm();

  const [searchParams, setSearchParams] = useSearchParams();

  // Get identity information data from the hook
  const identityInfo = useIdentityInformation();
  const {
    data,
    total,
    filterValue,
    tabId,
    status: availableStatus,
    permissions,
  } = identityInfo;

  const navigate = useNavigate();

  // Form for updating profile status
  const { control, setValue, getValues } =
    useForm<ConfirmUpdatingProfileFormProps>({
      defaultValues: {
        reason: '',
        status: TASKER_ONBOARDING_PROCESS_STATUS.VERIFYING,
      },
    });

  // Mock reasons data - in real implementation, this should come from the loader
  const reasonsFromSetting = useMemo(
    () => [
      { name: 'INVALID_DOCUMENT', type: 'REJECTED' },
      { name: 'UNCLEAR_IMAGE', type: 'REJECTED' },
      { name: 'MISSING_INFORMATION', type: 'REJECTED' },
    ],
    [],
  );

  // Handler functions for approve/reject actions
  const handleApprove = useCallback(
    async (profile: { taskerName: string; username?: string; _id: string }) => {
      setValue('status', TASKER_ONBOARDING_PROCESS_STATUS.APPROVED);

      const isConfirm = await confirm({
        title: tTaskerProfile('CONFIRM_APPROVE_IDENTITY'),
        body: (
          <ConfirmUpdatingProfile
            control={control}
            description={tTaskerProfile('CONFIRM_APPROVE_IDENTITY_DESCRIPTION')}
            setValue={setValue}
            taskerInfo={{
              taskerName: profile.taskerName,
              username: profile.username || profile.taskerName,
            }}
          />
        ),
        cancelButton: tTaskerProfile('CANCEL'),
        actionButton: tTaskerProfile('CONFIRM'),
      });

      if (isConfirm) {
        // TODO: Submit form data to update profile status
        const formData = getValues();
        toast({ description: 'Profile approved successfully' });
        // eslint-disable-next-line no-console
        console.log('Approved profile:', profile._id, formData);
      }
    },
    [confirm, control, setValue, tTaskerProfile, getValues],
  );

  const handleReject = useCallback(
    async (profile: { taskerName: string; username?: string; _id: string }) => {
      setValue('status', TASKER_ONBOARDING_PROCESS_STATUS.NEEDS_UPDATE);
      setValue('reason', reasonsFromSetting[0]?.name);

      const isConfirm = await confirm({
        title: tTaskerProfile('CONFIRM_REJECT_IDENTITY'),
        body: (
          <ConfirmUpdatingProfile
            control={control}
            description={tTaskerProfile('CONFIRM_REJECT_IDENTITY_DESCRIPTION')}
            setValue={setValue}
            taskerInfo={{
              taskerName: profile.taskerName,
              username: profile.username || profile.taskerName,
            }}
            reasons={reasonsFromSetting.map(reason => reason.name)}
          />
        ),
        cancelButton: tTaskerProfile('CANCEL'),
        actionButton: tTaskerProfile('CONFIRM'),
      });

      if (isConfirm) {
        // TODO: Submit form data to update profile status
        const formData = getValues();
        toast({ description: 'Profile rejected successfully' });
        // eslint-disable-next-line no-console
        console.log('Rejected profile:', profile._id, formData);
      }
    },
    [confirm, control, setValue, tTaskerProfile, getValues, reasonsFromSetting],
  );

  const columns: ColumnDef<
    SerializeFrom<ReturnValueIgnorePromise<typeof getTaskerProfile>[0]>
  >[] = useMemo(
    () => [
      {
        accessorKey: 'taskerPhone',
        header: ({ column }) => (
          <DataTableColumnHeader
            column={column}
            title={tTaskerProfile('TASKER_PHONE')}
          />
        ),
        cell: ({ row }) => (
          <Typography variant="p">{row.original?.taskerPhone}</Typography>
        ),
        enableSorting: false,
      },
      {
        accessorKey: 'taskerName',
        header: ({ column }) => (
          <DataTableColumnHeader
            column={column}
            title={tTaskerProfile('TASKER_NAME')}
          />
        ),
        cell: ({ row }) => (
          <Typography variant="p">{row.original?.taskerName}</Typography>
        ),
        enableSorting: false,
      },
      {
        accessorKey: 'services',
        header: ({ column }) => (
          <DataTableColumnHeader
            column={column}
            title={tTaskerProfile('SERVICES')}
          />
        ),
        cell: ({ row }) => (
          <div className="flex flex-wrap gap-3">
            {row.original?.services?.map(service => (
              <ServiceIconWithTooltip
                key={service?.text?.[i18n.language || 'en']}
                service={service}
              />
            ))}
          </div>
        ),
        enableSorting: false,
      },
      {
        accessorKey: 'taskerWorkingPlaces',
        header: ({ column }) => (
          <DataTableColumnHeader
            column={column}
            title={tTaskerProfile('TASKER_WORKING_PLACES')}
          />
        ),
        cell: ({ row }) => (
          <div className="flex flex-wrap gap-3">
            {row.original?.taskerWorkingPlaces?.map(workingPlace => (
              <Badge
                key={workingPlace.city}
                className="rounded-md bg-blue-50 text-blue">
                {workingPlace.city}
              </Badge>
            ))}
          </div>
        ),
        enableSorting: false,
      },
      {
        accessorKey: 'taskerGender',
        header: ({ column }) => (
          <DataTableColumnHeader
            column={column}
            title={tTaskerProfile('TASKER_GENDER')}
          />
        ),
        cell: ({ row }) => (
          <Typography variant="p">
            {tTaskerProfile(row.original?.taskerGender)}
          </Typography>
        ),
        enableSorting: false,
      },
      {
        accessorKey: 'updatedAt',
        header: ({ column }) => (
          <DataTableColumnHeader
            column={column}
            title={tTaskerProfile('UPDATED_AT')}
          />
        ),
        cell: ({ row }) =>
          row.original?.updatedAt ? (
            <div className="whitespace-nowrap">
              {format(row.original.updatedAt, 'HH:mm - dd/MM/yyyy')}
            </div>
          ) : null,
      },
      {
        accessorKey: 'identityCard',
        header: ({ column }) => (
          <DataTableColumnHeader
            column={column}
            title={tTaskerProfile('IDENTITY_CARD')}
          />
        ),
        cell: ({ row }) => (
          <div className="text-center">
            <Badge
              className={
                row.original?.identityCard?.status === 'APPROVED'
                  ? 'bg-green-100 text-green-800'
                  : row.original?.identityCard?.status === 'REJECTED'
                    ? 'bg-red-100 text-red-800'
                    : 'bg-yellow-100 text-yellow-800'
              }>
              {tTaskerProfile(
                row.original?.identityCard?.status || 'NOT_UPLOADED',
              )}
            </Badge>
          </div>
        ),
        enableSorting: false,
      },
      {
        accessorKey: 'status',
        header: ({ column }) => (
          <DataTableColumnHeader
            className="text-center"
            column={column}
            title={tTaskerProfile('STATUS')}
          />
        ),
        cell: ({ row }) => (
          <div className="text-center">
            <TaskerProfileStatus status={row.original?.status} />
          </div>
        ),
        enableSorting: false,
      },
      {
        accessorKey: 'actions',
        header: () => (
          <div className="text-center">{tTaskerProfile('ACTIONS')}</div>
        ),
        cell: ({ row }) => {
          const canApprove =
            row.original?.status === 'VERIFYING' ||
            row.original?.status === 'UPDATED';
          const canReject =
            row.original?.status === 'VERIFYING' ||
            row.original?.status === 'UPDATED';

          return (
            <div className="flex justify-center gap-2">
              {canApprove && (
                <Button
                  size="sm"
                  variant="outline"
                  className="text-green-600 border-green-600 hover:bg-green-50"
                  onClick={e => {
                    e.stopPropagation();
                    handleApprove(row.original);
                  }}>
                  {tTaskerProfile('APPROVE')}
                </Button>
              )}
              {canReject && (
                <Button
                  size="sm"
                  variant="outline"
                  className="text-red-600 border-red-600 hover:bg-red-50"
                  onClick={e => {
                    e.stopPropagation();
                    handleReject(row.original);
                  }}>
                  {tTaskerProfile('REJECT')}
                </Button>
              )}
            </div>
          );
        },
        enableSorting: false,
      },
    ],
    [tTaskerProfile, handleApprove, handleReject],
  );

  return (
    <div className="flex flex-col gap-6">
      <Grid className="p-4 bg-secondary">
        <div className="grid rounded-xl">
          <Typography className="mb-3 capitalize" variant="h2">
            {tTaskerProfile('IDENTITY_INFORMATION_FOR_TASKER')}
          </Typography>
          <Breadcrumbs />
        </div>
      </Grid>
      <TaskerProfileTabModel
        items={IDENTITY_VERIFICATION_TABS}
        permissions={permissions || []}
        value={tabId || ''}
        onValueChange={value => {
          setSearchParams(params => {
            params.set('tabId', value);

            const tabFound = IDENTITY_VERIFICATION_TABS.find(
              tab => tab.tabId === value,
            );

            if (tabFound) {
              params.set('status', tabFound.status?.[0]);
            }

            return params;
          });
        }}
      />
      <BTaskeeTable
        total={total || 0}
        data={data || []}
        columns={columns}
        search={{
          name: 'searchText',
          placeholder: tTaskerProfile('SEARCH_NAME_OR_PHONE'),
          defaultValue: filterValue?.searchText || '',
        }}
        isShowClearButton
        filterDate={{
          name: 'createdAt',
          defaultValue: {
            from: momentTz(filterValue?.createdAt.from).toDate(),
            to: momentTz(filterValue?.createdAt.to).toDate(),
          },
        }}
        localeAddress="tasker-profile"
        onClickRow={profile =>
          navigate(
            `${ROUTE_NAME.IDENTITY_INFORMATION_FOR_TASKER}/${profile?._id}`,
          )
        }
        pagination={getPageSizeAndPageIndex({
          total: total || 0,
          pageSize: Number(searchParams.get('pageSize') || 0),
          pageIndex: Number(searchParams.get('pageIndex') || 0),
        })}
        filters={
          availableStatus?.length > 1
            ? [
                {
                  placeholder: tTaskerProfile('STATUS'),
                  name: 'status',
                  className: 'w-[129px] h-[42px]',
                  options: availableStatus?.map((status: string) => ({
                    label: tTaskerProfile(status),
                    value: status,
                  })),
                  value: filterValue?.status.join(','),
                },
              ]
            : []
        }
      />
    </div>
  );
}
