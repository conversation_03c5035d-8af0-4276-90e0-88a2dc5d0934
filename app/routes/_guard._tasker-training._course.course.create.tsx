import {
  isRouteErrorResponse,
  json,
  redirect,
  useActionData,
  useRouteError,
} from '@remix-run/react';
import {
  ACTION_NAME,
  CONDITION,
  PERMISSIONS,
  ROUTE_NAME,
  STATUS,
  TYPE,
} from 'btaskee-constants';
import { useLoaderDataSafely } from 'btaskee-hooks';
import {
  Breadcrumbs,
  BreadcrumbsLink,
  BtaskeeResponseError,
  Typography,
  toast,
} from 'btaskee-ui';
import { useEffect } from 'react';
import { useForm } from 'react-hook-form';
import { useTranslation } from 'react-i18next';
import CourseForm from '~/components/form/CourseForm';
import { hocAction, hocLoader } from '~/hoc/remix';
import { useOutletTaskerTrainingCourse } from '~/hooks/useGetListTaskerTrainingCourse';
import i18next from '~/i18next.server';
import { getCities, getUserSession } from '~/services/helpers.server';
import { commitSession, getSession } from '~/services/session.server';
import {
  createCourse,
  getAllCourses,
  getCourseDetail,
  getListQuizzesDetailSortByOrder,
} from '~/services/tasker-training.server';

export const handle = {
  breadcrumb: () => {
    return (
      <BreadcrumbsLink
        to={`${ROUTE_NAME.TEST_AND_REVIEW}/create`}
        label="CREATE_TEST_AND_REVIEW"
      />
    );
  },
  i18n: 'course',
};

export const loader = hocLoader(
  async ({ request }) => {
    const url = new URL(request.url);
    const { isoCode } = await getUserSession({ headers: request.headers });

    const id = url.searchParams.get('id') || '';

    const [allCourseExcludeCourseId, course] = await Promise.all([
      getAllCourses({
        isoCode,
        projection: { title: 1, code: 1 },
      }),
      getCourseDetail({ isoCode, _id: id }),
    ]);

    const cities: Array<string> = await getCities(isoCode);

    const quizCollection = await getListQuizzesDetailSortByOrder({
      isoCode,
      quizCollection: course?.quizCollections || [],
    });
    return json({
      allCourseExcludeCourseId,
      course,
      quizCollection,
      cities,
    });
  },
  [PERMISSIONS.WRITE_TEST_AND_REVIEW],
);

export const action = hocAction(
  async ({ request }, { setInformationActionHistory }) => {
    const { isoCode, username, userId } = await getUserSession({
      headers: request.headers,
    });

    const formData = await request.clone().formData();

    const parsedData = JSON.parse(formData.get('data')?.toString() || '{}');

    const newCourse = await createCourse({
      isoCode,
      params: {
        ...parsedData,
        createdByUserId: userId,
        createdByUsername: username,
      },
    });

    const t = await i18next.getFixedT(request, 'course');

    setInformationActionHistory({
      action: ACTION_NAME.CREATE_COURSE,
      dataRelated: { courseId: newCourse._id },
    });

    const session = await getSession(request.headers.get('cookie'));
    session.flash('flashMessage', t('CREATE_COURSE_SUCCESSFULLY'));
    const newSession = await commitSession(session);

    return redirect(`${ROUTE_NAME.TEST_AND_REVIEW}/${newCourse._id}/detail`, {
      headers: {
        'Set-Cookie': newSession,
      },
    });
  },
  [PERMISSIONS.WRITE_TEST_AND_REVIEW],
);

export function ErrorBoundary() {
  const error = useRouteError() as { message?: string };
  const { t: tCommon } = useTranslation('common');

  if (isRouteErrorResponse(error)) {
    if (error?.message) {
      toast({ description: JSON.stringify(error.message) });
    }

    return <BtaskeeResponseError t={tCommon} errorStatus={error.status} />;
  }

  return <BtaskeeResponseError t={tCommon} errorStatus={500} />;
}

export default function CreateCourse() {
  const { t } = useTranslation('course');
  const actionData = useActionData<ActionTypeWithError<typeof action>>();

  useEffect(() => {
    if (actionData?.error) {
      toast({ description: actionData.error });
    }
  }, [actionData]);

  const outletData = useOutletTaskerTrainingCourse();

  const loaderData = useLoaderDataSafely<typeof loader>();

  const form = useForm<FormCreateCourse>({
    // eslint-disable-next-line @typescript-eslint/ban-ts-comment
    // @ts-ignore
    defaultValues: {
      code: '',
      title: loaderData?.course?.title || '',
      status: loaderData?.course?.status || STATUS.ACTIVE,
      relatedServices: loaderData?.course?.relatedServices || undefined,
      type: loaderData?.course?.type === TYPE.TEST ? TYPE.REVIEW : TYPE.TEST,
      condition: {
        ...(loaderData?.course?.condition
          ? {
              optionCondition: (() => {
                if (loaderData?.course.condition.coursesMustBeCompleted) {
                  return CONDITION.WHEN_COMPLETE;
                }
                if (loaderData?.course.condition.byTasker) {
                  return CONDITION.TASKER_STAR;
                }
                if (loaderData?.course.condition.manuallyUnblock) {
                  return CONDITION.MANUALLY_UNBLOCK;
                }
                return CONDITION.NONE;
              })(),
              ...(loaderData?.course?.condition?.coursesMustBeCompleted && {
                coursesMustBeCompleted: {
                  courseIds:
                    loaderData.course.condition.coursesMustBeCompleted
                      ?.courseIds[0] || '',
                },
              }),
              ...(loaderData?.course.condition.byTasker && {
                byTasker: loaderData?.course.condition.byTasker,
              }),
              ...(loaderData?.course.condition.manuallyUnblock && {
                manuallyUnblock: loaderData?.course.condition.manuallyUnblock,
              }),
            }
          : { optionCondition: CONDITION.NONE }),
      },
      selectedQuizzes: [],
      maximumNumberOfRetries: 0,
      // percentageToPass is a number from 50 to 100 - default is 50%
      percentageToPass: 50,
      timeToCompleteByMinutes: '00:00',
      cities: loaderData?.course?.cities || [],
    },
  });

  return (
    <>
      <div className="grid space-y-2 rounded-xl bg-secondary p-4">
        <Typography variant="h2">{t('CREATE_TEST_AND_REVIEW')}</Typography>
        <Breadcrumbs />
      </div>

      <CourseForm
        form={form}
        services={outletData?.services}
        allCourseExcludeCourseId={loaderData?.allCourseExcludeCourseId}
        quizCollection={loaderData?.quizCollection || []}
        cities={loaderData?.cities || []}
      />
    </>
  );
}
