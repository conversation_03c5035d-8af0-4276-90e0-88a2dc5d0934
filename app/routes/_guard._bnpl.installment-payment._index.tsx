import {
  isRouteErrorResponse,
  useNavigate,
  useOutletContext,
  useRouteError,
  useSearchParams,
} from '@remix-run/react';
import { type ColumnDef } from '@tanstack/react-table';
import { ROUTE_NAME, TASKER_BNPL_PROCESS_STATUS } from 'btaskee-constants';
import {
  BTaskeeTable,
  Badge,
  Breadcrumbs,
  BtaskeeResponseError,
  DataTableColumnHeader,
  Label,
  StatusBadge,
  Tooltip,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
  Typography,
  toast,
} from 'btaskee-ui';
import {
  createUID,
  formatNumberWithCommas,
  getPageSizeAndPageIndex,
} from 'btaskee-utils';
import { format } from 'date-fns';
import i18n from 'i18next';
import { useMemo } from 'react';
import { useTranslation } from 'react-i18next';

type OutletContextType = {
  services: Service[];
  settingCountry: SettingCountry;
  installmentPayment: {
    data: InstallmentPayment[];
    totalPaidAmount: number;
    totalAmount: number;
    totalRemainingAmount: number;
  };
  total: number;
  filterValue: {
    status: string;
    service: string;
    rangeDate: {
      from: Date;
      to: Date;
    };
  };
};

export function ErrorBoundary() {
  const error = useRouteError() as { message?: string };
  const { t: tCommon } = useTranslation('common');

  if (isRouteErrorResponse(error)) {
    if (error?.message) {
      toast({ description: JSON.stringify(error.message) });
    }

    return <BtaskeeResponseError t={tCommon} errorStatus={error.status} />;
  }

  return <BtaskeeResponseError t={tCommon} errorStatus={500} />;
}

export default function InstallmentPaymentIndex() {
  const { t } = useTranslation('installment-payment');

  const [searchParams] = useSearchParams();
  const navigate = useNavigate();

  const outletData = useOutletContext<OutletContextType>();

  const currency = outletData?.settingCountry?.currency?.sign || '';

  const columns: ColumnDef<InstallmentPayment>[] = useMemo(
    () => [
      {
        accessorKey: 'status',
        header: ({ column }) => (
          <DataTableColumnHeader
            column={column}
            title={t('STATUS')}
            className="w-auto"
          />
        ),
        cell: ({ row }) => (
          <StatusBadge
            statusClasses={{
              PAYING: 'bg-blue-50 text-blue-500 rounded-md text-center',
              DONE: 'bg-green-50 text-green-500 rounded-md text-center',
              OVER_DUE: 'bg-yellow-50 text-yellow-500 rounded-md text-center',
              STOPPED: 'bg-red-50 text-red-500 rounded-md text-center',
            }}
            status={row.original?.status}
          />
        ),
        enableSorting: false,
      },
      {
        accessorKey: 'tasker.phone',
        header: ({ column }) => (
          <DataTableColumnHeader column={column} title={t('TASKER_PHONE')} />
        ),
        cell: ({ row }) => <div>{row.original?.tasker?.phone}</div>,
        enableSorting: false,
      },
      {
        accessorKey: 'tasker.name',
        header: ({ column }) => (
          <DataTableColumnHeader column={column} title={t('TASKER_NAME')} />
        ),
        cell: ({ row }) => <div>{row.original?.tasker?.name}</div>,
        enableSorting: false,
      },
      {
        accessorKey: 'tasker.registeredServices',
        header: ({ column }) => (
          <DataTableColumnHeader column={column} title={t('SERVICE')} />
        ),
        cell: ({ row }) => (
          <div className="flex flex-wrap gap-1">
            {row.original?.tasker?.registeredServices?.map(
              registeredService => {
                const service = outletData?.services.find(
                  service => service._id === registeredService,
                );

                return (
                  <TooltipProvider key={createUID()}>
                    <Tooltip key={createUID()}>
                      <TooltipTrigger asChild>
                        <div className="w-11 h-11 rounded-[11px] overflow-hidden bg-primary-50">
                          <img
                            className="w-full h-full object-cover"
                            src={service?.icon || ''}
                            alt={registeredService}
                          />
                        </div>
                      </TooltipTrigger>
                      <TooltipContent>
                        <Typography variant="p">
                          {`${service?.text?.[i18n.language || 'en']} ${service?.isSubscription ? ' (Subscription)' : ''}`}
                        </Typography>
                      </TooltipContent>
                    </Tooltip>
                  </TooltipProvider>
                );
              },
            )}
          </div>
        ),
        enableSorting: false,
      },
      {
        accessorKey: 'tasker.city',
        header: ({ column }) => (
          <DataTableColumnHeader column={column} title={t('REGION')} />
        ),
        cell: ({ row }) => <div>{row.original?.tasker?.city}</div>,
        enableSorting: false,
      },
      {
        accessorKey: 'createdAt',
        header: ({ column }) => (
          <DataTableColumnHeader column={column} title={t('CREATED_AT')} />
        ),
        cell: ({ row }) => (
          <div>{format(row.getValue('createdAt'), 'HH:mm - dd/MM/yyyy')}</div>
        ),
        enableSorting: false,
      },
      {
        accessorKey: 'expiredAt',
        header: ({ column }) => (
          <DataTableColumnHeader column={column} title={t('EXPIRED_AT')} />
        ),
        cell: ({ row }) => (
          <div>{format(row.getValue('expiredAt'), 'HH:mm - dd/MM/yyyy')}</div>
        ),
        enableSorting: false,
      },
      {
        accessorKey: 'amount',
        header: ({ column }) => (
          <DataTableColumnHeader
            column={column}
            title={t('INSTALLMENT_AMOUNT')}
          />
        ),
        cell: ({ row }) => (
          <div>{`${formatNumberWithCommas(row.original?.amount ?? 0)}${currency}`}</div>
        ),
        size: 180,
      },
      {
        accessorKey: 'paidAmount',
        header: ({ column }) => (
          <DataTableColumnHeader column={column} title={t('PAID_AMOUNT')} />
        ),
        cell: ({ row }) => {
          const totalAmount = row.original?.amount ?? 0;
          const remainingAmount = row.original?.remainingAmount ?? 0;

          return (
            <Typography variant="p">{`${formatNumberWithCommas(totalAmount - remainingAmount)}${currency}`}</Typography>
          );
        },
        enableSorting: false,
        size: 150,
      },
      {
        accessorKey: 'remainingAmount',
        header: ({ column }) => (
          <DataTableColumnHeader
            column={column}
            title={t('REMAINING_AMOUNT')}
          />
        ),
        cell: ({ row }) => (
          <div>{`${formatNumberWithCommas(row.original?.remainingAmount ?? 0)}${currency}`}</div>
        ),
        size: 180,
      },
    ],
    [currency, outletData?.services, t],
  );

  return (
    <>
      <div className="mb-6 flex items-center justify-between rounded-xl bg-secondary p-4 font-sans">
        <div className="grid space-y-2">
          <Typography variant="h2">{t('INSTALLMENT_PAYMENT')}</Typography>
          <Breadcrumbs />
        </div>
      </div>
      <BTaskeeTable
        isShowClearButton
        columns={columns}
        data={outletData?.installmentPayment?.data}
        total={outletData?.total}
        pagination={getPageSizeAndPageIndex({
          total: outletData?.total || 0,
          pageSize: Number(searchParams.get('pageSize') || 0),
          pageIndex: Number(searchParams.get('pageIndex') || 0),
        })}
        search={{
          defaultValue: searchParams.get('search') || '',
          name: 'search',
          placeholder: t('SEARCH_NAME_PHONE'),
        }}
        filterDate={{
          name: 'createdAt',
          defaultValue: outletData?.filterValue?.rangeDate,
        }}
        localeAddress="installment-payment"
        filters={[
          {
            placeholder: t('SERVICE'),
            name: 'service',
            options: outletData?.services.map(service => ({
              label: `${service?.text?.[i18n.language || 'en']}${service.isSubscription ? ' (Subscription)' : ''}`,
              value: service._id,
            })),
            value: outletData?.filterValue?.service,
          },
          {
            placeholder: t('STATUS'),
            name: 'status',
            options: Object.values(TASKER_BNPL_PROCESS_STATUS).map(status => ({
              label: t(status),
              value: status,
            })),
            value: outletData?.filterValue?.status,
          },
        ]}
        columnPinningFromOutSide={{
          right: ['amount', 'paidAmount', 'remainingAmount'],
        }}
        onClickRow={installment =>
          navigate(`${ROUTE_NAME.INSTALLMENT_PAYMENT}/${installment._id}`)
        }
        extraContent={
          <div className="flex gap-6 justify-end">
            <div className="inline-flex flex-col items-center gap-1">
              <Label className="text-sm font-normal text-gray-400">
                {t('TOTAL_AMOUNT_ON_TASK')}
              </Label>
              <Badge className="bg-blue-50 text-blue-500 rounded-md flex items-center justify-center py-[11px] w-full text-sm">
                {`${formatNumberWithCommas(
                  outletData?.installmentPayment?.totalAmount || 0,
                )}${currency}`}
              </Badge>
            </div>
            <div className="inline-flex flex-col items-center gap-1">
              <Label className="text-sm font-normal text-gray-400">
                {t('TOTAL_AMOUNT_PAID')}
              </Label>
              <Badge className="bg-green-50 text-green-500 rounded-md text-center py-[11px] w-full flex items-center justify-center text-sm">
                {`${formatNumberWithCommas(
                  outletData?.installmentPayment?.totalPaidAmount || 0,
                )}${currency}`}
              </Badge>
            </div>
            <div className="inline-flex flex-col items-center gap-1">
              <Label className="text-sm font-normal text-gray-400">
                {t('TOTAL_REMAINING_AMOUNT')}
              </Label>
              <Badge className="bg-orange-50 text-orange-500 rounded-md text-center py-[11px] w-full flex items-center justify-center text-sm">
                {`${formatNumberWithCommas(
                  outletData?.installmentPayment?.totalRemainingAmount || 0,
                )}${currency}`}
              </Badge>
            </div>
          </div>
        }
      />
    </>
  );
}
