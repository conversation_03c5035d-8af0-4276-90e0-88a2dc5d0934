import { type SerializeFrom, json } from '@remix-run/node';
import {
  isRouteErrorResponse,
  useLoaderData,
  useNavigate,
  useRouteError,
} from '@remix-run/react';
import { type ColumnDef } from '@tanstack/react-table';
import { PERMISSIONS, ROUTE_NAME } from 'btaskee-constants';
import { useCopyToClipboard, useGlobalStore } from 'btaskee-hooks';
import {
  AspectRatio,
  BlockDescription,
  Breadcrumbs,
  BtaskeeResponseError,
  Button,
  Card,
  CardContent,
  CardHeader,
  CardInformation,
  DataTableBasic,
  DataTableColumnHeader,
  Dialog,
  DialogClose,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
  Grid,
  Separator,
  Switch,
  Typography,
  cn,
  toast,
} from 'btaskee-ui';
import { formatTimeToCompleteFromSecond, momentTz } from 'btaskee-utils';
import { CopyCheck } from 'lucide-react';
import { useTranslation } from 'react-i18next';
import {
  CopyWithRoundedBackgroundIcon,
  ExternalDocumentIcon,
} from '~/components/common/FigmaIcon';
import { CardProfileDescription } from '~/components/tasker-common';
import { hocLoader } from '~/hoc/remix';
import { getUserSession } from '~/services/helpers.server';
import { getQuizCollectionDetail } from '~/services/tasker-training.server';

export function ErrorBoundary() {
  const error = useRouteError() as { message?: string };
  const { t: tCommon } = useTranslation('common');

  if (isRouteErrorResponse(error)) {
    if (error?.message) {
      toast({ description: JSON.stringify(error.message) });
    }

    return <BtaskeeResponseError t={tCommon} errorStatus={error.status} />;
  }

  return <BtaskeeResponseError t={tCommon} errorStatus={500} />;
}

export const loader = hocLoader(
  async ({ request, params }) => {
    const { isoCode } = await getUserSession({
      headers: request.headers,
    });

    const quizCollectionDetail = await getQuizCollectionDetail({
      isoCode,
      _id: params?.id || '',
    });

    return json({ data: quizCollectionDetail });
  },
  [PERMISSIONS.READ_TRAINING_QUIZ_COLLECTION],
);

export default function QuizCollectionDetail() {
  const { t } = useTranslation('quiz');
  const {
    isCopied: isCopiedYoutubeLink,
    copyToClipboard: copyYoutubeLinkToClipboard,
  } = useCopyToClipboard();
  const navigate = useNavigate();
  const permissions = useGlobalStore(state => state.permissions);
  const loaderData = useLoaderData<LoaderTypeWithError<typeof loader>>();

  const columns: ColumnDef<
    SerializeFrom<
      ReturnValueIgnorePromise<typeof getQuizCollectionDetail>
    >['quizzes'][0]
  >[] = [
    {
      accessorKey: 'number',
      header: ({ column }) => (
        <DataTableColumnHeader
          className="whitespace-nowrap"
          column={column}
          title={t('NUMBER')}
        />
      ),
      size: 30,
      cell: ({ row }) => (
        <Typography variant="p">{Number(row.original?.order) + 1}</Typography>
      ),
      enableSorting: false,
    },
    {
      accessorKey: 'code',
      header: ({ column }) => (
        <DataTableColumnHeader
          className="whitespace-nowrap"
          column={column}
          title={t('QUIZ_CODE')}
        />
      ),
      cell: ({ row }) => (
        <Typography variant="p">{row.original.code}</Typography>
      ),
      enableSorting: false,
    },
    {
      accessorKey: 'name',
      header: ({ column }) => (
        <DataTableColumnHeader column={column} title={t('QUESTION')} />
      ),
      cell: ({ row }) => (
        <Typography variant="p">{row.original.title}</Typography>
      ),
      enableSorting: false,
    },
    {
      accessorKey: 'action',
      header: ({ column }) => (
        <DataTableColumnHeader
          className="text-right"
          column={column}
          title={t('ACTION')}
        />
      ),
      size: 40,
      cell: ({ row }) => (
        <div className="flex justify-end">
          <Dialog>
            <DialogTrigger>
              <Button
                variant="ghost"
                className="flex items-center gap-2 text-primary hover:text-primary hover:bg-primary-50 px-1 py-0.5 h-8">
                <ExternalDocumentIcon />
                <Typography variant="p" affects="removePMargin">
                  {t('VIEW')}
                </Typography>
              </Button>
            </DialogTrigger>
            <DialogContent className="max-w-5xl">
              <DialogHeader className="space-y-2">
                <DialogTitle>
                  <Typography variant="h3">{t('QUIZ_DETAIL')}</Typography>
                </DialogTitle>
              </DialogHeader>
              <div className="grid gap-4 grid-cols-2">
                <Card>
                  <CardContent className="p-4">
                    <div>
                      <Typography
                        variant="p"
                        affects="removePMargin"
                        className="text-gray-400">
                        {t('QUIZ_CODE')}
                      </Typography>
                      <Typography
                        variant="p"
                        affects="removePMargin"
                        className="text-lg font-semibold text-gray-600">
                        {row.original?.code}
                      </Typography>
                    </div>
                    <Separator className="my-3" />
                    <div>
                      <Typography
                        variant="p"
                        affects="removePMargin"
                        className="text-gray-400">
                        {t('QUESTION')}
                      </Typography>
                      <Typography
                        variant="p"
                        affects="removePMargin"
                        className="text-lg font-semibold text-gray-600 break-all">
                        {row.original?.title}
                      </Typography>
                    </div>
                    <Separator className="my-3" />
                    <div className="flex gap-4 items-center">
                      <Switch
                        disabled
                        checked={!!row.original?.isRandomAnswer}
                      />
                      <Typography
                        className="text-gray-400"
                        variant="p"
                        affects="removePMargin">
                        {t('RANDOM_ANSWER')}
                      </Typography>
                    </div>
                  </CardContent>
                </Card>
                <Card className="border-dashed">
                  <CardContent className="h-full p-6 flex gap-4 flex-col justify-between">
                    {row.original?.image ? (
                      <>
                        <div className="rounded-md overflow-hidden max-h-[192px]">
                          <img
                            className="w-full h-full object-contain bg-center"
                            src={row.original?.image?.url}
                            alt={row.original?.image?.description}
                          />
                        </div>
                        <div>
                          <Typography
                            variant="p"
                            affects="removePMargin"
                            className="text-gray-400">
                            {t('DESCRIPTION')}
                          </Typography>
                          <Typography
                            variant="p"
                            affects="removePMargin"
                            className="text-lg font-semibold text-gray-600 break-all">
                            {row.original?.image?.description}
                          </Typography>
                        </div>
                      </>
                    ) : (
                      <Typography
                        variant="p"
                        className="text-gray-400 text-center">
                        {t('NO_IMAGE')}
                      </Typography>
                    )}
                  </CardContent>
                </Card>
              </div>
              <div className="grid grid-cols-2 gap-4 max-h-[220px] overflow-y-auto">
                {row.original?.answers?.map((answer, index) => (
                  <Card
                    className={cn(
                      'border-dashed',
                      answer?.isCorrect
                        ? 'bg-secondary border-secondary-foreground'
                        : 'bg-red-50 border-red-500',
                    )}
                    key={answer?.content}>
                    <CardContent className="p-6">
                      <Typography
                        variant="p"
                        affects="removePMargin"
                        className="text-gray-400">
                        {t('NUM_OF_ANSWER', { order: index + 1 })}
                      </Typography>
                      <Typography
                        variant="p"
                        affects="removePMargin"
                        className="text-lg font-semibold text-gray-600 break-all">
                        {answer?.content}
                      </Typography>
                    </CardContent>
                  </Card>
                ))}
              </div>
              <DialogClose />
            </DialogContent>
          </Dialog>
        </div>
      ),
      enableSorting: false,
    },
  ];

  return (
    <>
      <div className="flex flex-wrap bg-secondary rounded-md p-4 justify-between items-center min-h-24 mb-6">
        <Grid className="gap-3">
          <Typography variant="h2">{t('QUIZ_COLLECTION_DETAIL')}</Typography>
          <Breadcrumbs />
        </Grid>
        <Button
          disabled={
            !permissions.includes(PERMISSIONS.WRITE_TRAINING_QUIZ_COLLECTION)
          }
          onClick={() =>
            navigate(
              `${ROUTE_NAME.QUIZ_COLLECTION}/${loaderData?.data?._id}${ROUTE_NAME.EDIT}`,
            )
          }>
          {t('UPDATE_QUIZ_COLLECTION')}
        </Button>
      </div>
      <Card className="bg-gray-50">
        <CardHeader>
          <Typography variant="h4" className="border-b-gray-200 border-b pb-2">
            {t('GENERAL_INFORMATION')}
          </Typography>
        </CardHeader>
        <CardContent>
          <CardInformation
            className="grid grid-cols-3 gap-x-24 gap-y-5"
            descriptions={[
              {
                label: t('CODE'),
                value: loaderData?.data?.code,
              },
              {
                label: t('QUIZ_COLLECTION_NAME'),
                value: loaderData?.data?.title,
              },
              {
                label: t('SUMMARY'),
                value: loaderData?.data?.description ?? '',
              },
              {
                label: t('ESTIMATE_TIME_TO_COMPLETE'),
                value: formatTimeToCompleteFromSecond(
                  loaderData?.data?.timeToCompleteByMinutes || 0,
                ),
              },
              {
                label: t('NUMBER_OF_QUESTIONS_DISPLAYED'),
                value: String(loaderData?.data?.numberOfDisplayQuizzes || ''),
              },
              {
                label: t('RANDOM_QUESTIONS'),
                customValueNode: (
                  <div className="h-full flex items-center gap-4">
                    <Switch
                      className="cursor-default opacity-50"
                      checked={loaderData?.data?.isRandomQuizzes}
                    />
                    {t('RANDOM_QUESTIONS')}
                  </div>
                ),
              },
              {
                label: t('CREATED_AT'),
                value: loaderData?.data?.createdAt
                  ? momentTz(loaderData?.data?.createdAt).format(
                      'HH:mm - DD/MM/YYYY',
                    )
                  : '',
              },
              {
                label: t('CREATED_BY'),
                value: loaderData?.data?.createdByUsername,
              },
              {
                label: t('UPDATED_AT'),
                value: loaderData?.data?.updatedAt
                  ? momentTz(loaderData?.data?.updatedAt).format(
                      'HH:mm - DD/MM/YYYY',
                    )
                  : momentTz(loaderData?.data?.createdAt).format(
                      'HH:mm - DD/MM/YYYY',
                    ),
              },
              {
                label: t('UPDATED_BY'),
                value:
                  loaderData?.data?.updatedByUsername ||
                  loaderData?.data?.createdByUsername,
              },
            ]}
          />
        </CardContent>
      </Card>
      {loaderData?.data?.video ? (
        <Card className="bg-gray-50 mt-6">
          <CardHeader>
            <Typography
              variant="h4"
              className="border-b-gray-200 border-b pb-2">
              {t('VIDEO')}
            </Typography>
          </CardHeader>
          <CardContent>
            <CardProfileDescription
              descriptions={[
                {
                  label: t('YOUTUBE_LINK'),
                  value: (
                    <div className="flex items-center gap-2">
                      <Typography
                        className="text-lg font-semibold break-all text-gray-600"
                        affects="removePMargin"
                        variant="p">
                        {loaderData?.data?.video?.url}
                      </Typography>
                      <Button
                        variant="ghost"
                        size="icon"
                        className={cn(
                          'w-[18px] h-[18px]',
                          isCopiedYoutubeLink
                            ? 'text-green-500'
                            : 'text-orange-500',
                        )}
                        onClick={() =>
                          copyYoutubeLinkToClipboard(
                            loaderData?.data?.video?.url || '',
                          )
                        }>
                        {isCopiedYoutubeLink ? (
                          <CopyCheck className="-scale-x-100" />
                        ) : (
                          <CopyWithRoundedBackgroundIcon
                            props={{ className: 'min-h-7, min-w-7' }}
                          />
                        )}
                      </Button>
                    </div>
                  ),
                },
                {
                  label: t('VIDEO_SUMMARY'),
                  value: loaderData?.data?.video?.description,
                  className: 'col-span-2 ',
                },
              ]}
            />
          </CardContent>
        </Card>
      ) : null}
      {loaderData?.data?.image ? (
        <Card className="bg-gray-50 mt-6 px-6 pb-6">
          <CardHeader className="px-0">
            <Typography
              variant="h4"
              className="border-b-gray-200 border-b pb-2">
              {t('IMAGE')}
            </Typography>
          </CardHeader>
          <CardContent className="p-6 border border-dashed border-gray-300 rounded-md bg-gray-100 w-3/4">
            <div className={'flex gap-5 items-start'}>
              <div className={'w-full'}>
                <AspectRatio ratio={16 / 9}>
                  <img
                    src={`${loaderData?.data?.image?.url}?${performance.now()}`}
                    alt={loaderData?.data?.image?.description}
                    className="object-cover h-full rounded-md"
                  />
                </AspectRatio>
              </div>
              <BlockDescription
                className={'w-full'}
                desc={{
                  label: t('DESCRIPTION'),
                  value: loaderData?.data?.image?.description,
                }}
              />
            </div>
          </CardContent>
        </Card>
      ) : null}
      <Card className="bg-gray-50 mt-6">
        <CardHeader>
          <Typography variant="h4" className="border-b-gray-200 border-b pb-2">
            {t('QUIZ_LIST')}
          </Typography>
        </CardHeader>
        <CardContent>
          <div className="max-h-[700px] overflow-y-auto">
            <DataTableBasic
              data={loaderData?.data?.quizzes || []}
              columns={columns}
              manualPagination
              isDisplayPagination={false}
            />
          </div>
        </CardContent>
      </Card>
    </>
  );
}
