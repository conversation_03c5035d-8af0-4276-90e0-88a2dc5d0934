import type { SerializeFrom } from '@remix-run/node';
import {
  isRouteErrorResponse,
  useNavigate,
  useRouteError,
  useSearchParams,
} from '@remix-run/react';
import type { ColumnDef } from '@tanstack/react-table';
import {
  TaskerProfileStatus,
  TaskerProfileTabModel,
} from 'app/components/tasker-common';
import { ROUTE_NAME, SUPPLIER_PROFILE_TAB } from 'btaskee-constants';
import {
  BTaskeeTable,
  Badge,
  Breadcrumbs,
  BtaskeeResponseError,
  DataTableColumnHeader,
  Grid,
  Typography,
  toast,
} from 'btaskee-ui';
import {
  getOverallStatusInProfile,
  getPageSizeAndPageIndex,
  momentTz,
} from 'btaskee-utils';
import { format } from 'date-fns';
import i18n from 'i18next';
import { useMemo } from 'react';
import { useTranslation } from 'react-i18next';
import { ServiceIconWithTooltip } from '~/components/common/CommonComponent';
import { useOutletGetSupplierOnboardingProfile } from '~/hooks/useGetSupplierOnboardingProfile';
import type { getTaskerProfile } from '~/services/tasker-profile.server';
import { formatPhoneNumber } from '~/utils/common';

export function ErrorBoundary() {
  const error = useRouteError() as { message?: string };
  const { t: tCommon } = useTranslation('common');

  if (isRouteErrorResponse(error)) {
    if (error?.message) {
      toast({ description: JSON.stringify(error.message) });
    }

    return <BtaskeeResponseError t={tCommon} errorStatus={error.status} />;
  }

  return <BtaskeeResponseError t={tCommon} errorStatus={500} />;
}

export default function SupplierOnboardingContent() {
  const { t: tSupplierProfile } = useTranslation('supplier-profile');

  const [searchParams, setSearchParams] = useSearchParams();

  const {
    permissions,
    tabId,
    status,
    filteredValue,
    totalProfile,
    supplierProfiles,
  } = useOutletGetSupplierOnboardingProfile();

  const navigate = useNavigate();

  const supplierProfileColumns: ColumnDef<
    SerializeFrom<ReturnValueIgnorePromise<typeof getTaskerProfile>[0]>
  >[] = useMemo(
    () => [
      {
        accessorKey: 'taskerPhone',
        header: ({ column }) => (
          <DataTableColumnHeader
            column={column}
            title={tSupplierProfile('TASKER_PHONE')}
          />
        ),
        cell: ({ row }) =>
          row.original?.taskerPhone ? (
            <Typography variant="p">
              {formatPhoneNumber(row.original.taskerPhone)}
            </Typography>
          ) : null,
        enableSorting: false,
      },
      {
        accessorKey: 'taskerName',
        header: ({ column }) => (
          <DataTableColumnHeader
            column={column}
            title={tSupplierProfile('TASKER_NAME')}
          />
        ),
        cell: ({ row }) => (
          <Typography variant="p">{row.original?.taskerName}</Typography>
        ),
        enableSorting: false,
      },
      {
        accessorKey: 'services',
        header: ({ column }) => (
          <DataTableColumnHeader
            column={column}
            title={tSupplierProfile('SERVICES')}
          />
        ),
        cell: ({ row }) => (
          <div className="flex flex-wrap gap-3">
            {row.original?.services?.map(service => (
              <ServiceIconWithTooltip
                key={service?.text?.[i18n.language || 'en']}
                service={service}
              />
            ))}
          </div>
        ),
        enableSorting: false,
      },
      {
        accessorKey: 'taskerWorkingPlaces',
        header: ({ column }) => (
          <DataTableColumnHeader
            column={column}
            title={tSupplierProfile('TASKER_WORKING_PLACES')}
          />
        ),
        cell: ({ row }) => {
          const filteredDuplicateCities = Array.from(
            new Set(
              row.original?.taskerWorkingPlaces?.map(
                workingPlace => workingPlace.city,
              ) || [],
            ),
          );

          return (
            <div className="flex flex-wrap gap-3">
              {filteredDuplicateCities?.map(city => (
                <Badge key={city} className="bg-blue-50 text-blue rounded-md">
                  {city}
                </Badge>
              ))}
            </div>
          );
        },
        enableSorting: false,
      },
      {
        accessorKey: 'taskerGender',
        header: ({ column }) => (
          <DataTableColumnHeader
            column={column}
            title={tSupplierProfile('TASKER_GENDER')}
          />
        ),
        cell: ({ row }) => (
          <Typography variant="p">
            {tSupplierProfile(row.original?.taskerGender)}
          </Typography>
        ),
        enableSorting: false,
      },
      {
        accessorKey: 'updatedAt',
        header: ({ column }) => (
          <DataTableColumnHeader
            column={column}
            title={tSupplierProfile('UPDATED_AT')}
          />
        ),
        cell: ({ row }) =>
          row.original?.updatedAt ? (
            <div className="whitespace-nowrap">
              {format(row.original?.updatedAt, 'HH:mm - dd/MM/yyyy')}
            </div>
          ) : null,
      },
      {
        accessorKey: 'status',
        header: ({ column }) => (
          <DataTableColumnHeader
            className="text-center"
            column={column}
            title={tSupplierProfile('STATUS')}
          />
        ),
        cell: ({ row }) => (
          <div className="text-center">
            <TaskerProfileStatus
              status={getOverallStatusInProfile({
                processStatus: row.original?.processStatus,
                status: row.original?.status,
                isHaveAppointmentInfo:
                  typeof row.original?.appointmentInfo === 'object',
              })}
            />
          </div>
        ),
        enableSorting: false,
      },
    ],
    [tSupplierProfile],
  );

  return (
    <div className="flex flex-col gap-6">
      <Grid className="bg-secondary p-4">
        <div className="grid rounded-xl">
          <Typography className="capitalize mb-3" variant="h2">
            {tSupplierProfile('SUPPLIER_PROFILE')}
          </Typography>
          <Breadcrumbs />
        </div>
      </Grid>
      <TaskerProfileTabModel
        items={SUPPLIER_PROFILE_TAB}
        permissions={permissions || []}
        value={tabId || ''}
        onValueChange={value => {
          setSearchParams(params => {
            params.set('tabId', value);

            const tabFound = SUPPLIER_PROFILE_TAB.find(
              tab => tab.tabId === value,
            );

            if (tabFound) {
              params.set('status', tabFound.status?.[0]);
            }

            return params;
          });
        }}
      />
      <BTaskeeTable
        total={totalProfile || 0}
        data={supplierProfiles || []}
        columns={supplierProfileColumns}
        search={{
          name: 'searchText',
          placeholder: tSupplierProfile('SEARCH_NAME_OR_PHONE'),
          defaultValue: filteredValue.searchText || '',
        }}
        isShowClearButton
        filterDate={{
          name: 'createdAt',
          defaultValue: {
            from: momentTz(filteredValue?.createdAt?.from).toDate(),
            to: momentTz(filteredValue?.createdAt?.to).toDate(),
          },
        }}
        localeAddress="supplier-profile"
        onClickRow={profile =>
          navigate(`${ROUTE_NAME.SUPPLIER_ONBOARDING}/${profile?._id}`)
        }
        pagination={getPageSizeAndPageIndex({
          total: totalProfile || 0,
          pageSize: Number(searchParams.get('pageSize') || 0),
          pageIndex: Number(searchParams.get('pageIndex') || 0),
        })}
        filters={
          status?.length > 1
            ? [
                {
                  placeholder: tSupplierProfile('STATUS'),
                  name: 'status',
                  className: 'w-[129px] h-[42px]',
                  options:
                    status?.map(status => ({
                      label: tSupplierProfile(status),
                      value: status,
                    })) || [],
                  value: filteredValue?.status?.join(','),
                },
              ]
            : []
        }
      />
    </div>
  );
}
