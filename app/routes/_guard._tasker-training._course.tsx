import { Outlet } from '@remix-run/react';
import { PERMISSIONS, ROUTE_NAME } from 'btaskee-constants';
import { useLoaderDataSafely } from 'btaskee-hooks';
import { BreadcrumbsLink, toast } from 'btaskee-ui';
import { useEffect } from 'react';
import { hocLoader } from '~/hoc/remix';
import { willBecomeTaskerTrainingCourseLoader } from '~/hooks/useGetListTaskerTrainingCourse';

export const handle = {
  breadcrumb: () => (
    <BreadcrumbsLink to={ROUTE_NAME.TEST_AND_REVIEW} label="TEST_AND_REVIEW" />
  ),
};

export const loader = hocLoader(
  willBecomeTaskerTrainingCourseLoader,
  PERMISSIONS.READ_TEST_AND_REVIEW,
);

export default function CourseScreen() {
  const { error: loaderError, ...restLoaderData } =
    useLoaderDataSafely<typeof loader>();

  useEffect(() => {
    if (loaderError) {
      toast({ description: loaderError });
    }
  }, [loaderError]);

  return <Outlet context={restLoaderData} />;
}
