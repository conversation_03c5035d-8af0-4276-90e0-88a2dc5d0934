import { Outlet } from '@remix-run/react';
import { ROUTE_NAME } from 'btaskee-constants';
import { BreadcrumbsLink } from 'btaskee-ui';
import { hocLoader } from '~/hoc/remix';
import {
  useOutletGroupDetail,
  willBecomeLoader,
} from '~/hooks/useGetGroupDetail';

export const loader = hocLoader(willBecomeLoader);

export const handle = {
  breadcrumb: (data: { group: Groups }) => {
    const { group } = data;

    return (
      <BreadcrumbsLink
        to={`${ROUTE_NAME.GROUP_SETTING}/${group._id}/member-list`}
        label={group.name}
      />
    );
  },
};

export default function Screen() {
  const loaderData = useOutletGroupDetail();

  return <Outlet context={loaderData} />;
}
