import { Link, Outlet, useLocation } from '@remix-run/react';
import {
  PERMISSIONS,
  ROUTE_NAME,
  TASKER_PROFILE_REJECT_STATUS,
} from 'btaskee-constants';
import { useGlobalStore } from 'btaskee-hooks';
import {
  BreadcrumbsLink,
  Separator,
  Typography,
  buttonVariants,
  cn,
} from 'btaskee-ui';
import { Building, CalendarX, FileText } from 'lucide-react';
import { useMemo } from 'react';
import { useTranslation } from 'react-i18next';

export const handle = {
  breadcrumb: () => (
    <BreadcrumbsLink
      to=""
      label="CONFIGURATION"
      disabled
      onClick={e => e.preventDefault()}
    />
  ),
  i18n: 'configuration',
};

export default function TaskerOnboardingConfiguration() {
  const permissions = useGlobalStore(state => state.permissions);
  const { pathname } = useLocation();
  const { t } = useTranslation('configuration');

  const navigation = useMemo(
    () => [
      {
        title: 'LIST_OFFICES',
        href: ROUTE_NAME.LIST_OFFICES,
        permission: PERMISSIONS.READ_LIST_OFFICES,
        icon: <Building className="min-h-6 min-w-6" />,
      },
      {
        title: 'LIST_REASONS',
        href: `${ROUTE_NAME.LIST_REASONS}/${TASKER_PROFILE_REJECT_STATUS.NEEDS_UPDATE}`,
        permission: PERMISSIONS.READ_LIST_REASONS,
        icon: <FileText className="min-h-6 min-w-6" />,
      },
      {
        title: 'LIST_HOLIDAYS',
        href: ROUTE_NAME.LIST_HOLIDAYS,
        permission: PERMISSIONS.READ_LIST_HOLIDAYS,
        icon: <CalendarX className="min-h-6 min-w-6" />,
      },
    ],
    [],
  );

  return (
    <>
      <Typography variant="h2">{t('CONFIGURATION')}</Typography>
      <Separator className="mt-6" />
      <div className="flex h-full flex-col lg:flex-row">
        <aside className="-mb-6 bg-gray-50 lg:w-1/5 overflow-x-auto 2xl:h-[calc(100vh-176px)]">
          <nav className="flex space-x-2 p-4 lg:flex-col lg:space-x-0 lg:space-y-1">
            {navigation.map(item =>
              !item.permission || permissions.includes(item.permission) ? (
                <Link
                  key={item.href}
                  to={item.href}
                  className={cn(
                    buttonVariants({ variant: 'ghost' }),
                    pathname.includes(item.href)
                      ? 'bg-primary-50 font-medium text-primary hover:bg-primary-50 hover:text-primary'
                      : 'font-normal text-gray hover:bg-primary-50',
                    'justify-start gap-2 text-base',
                  )}>
                  {item?.icon}
                  {t(item.title)}
                </Link>
              ) : null,
            )}
          </nav>
        </aside>
        <div className="flex-1 pl-6 pt-6 w-4/5">
          <Outlet />
        </div>
      </div>
    </>
  );
}
