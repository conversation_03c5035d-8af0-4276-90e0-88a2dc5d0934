import { Outlet, json, useLoaderData } from '@remix-run/react';
import { PERMISSIONS, ROUTE_NAME } from 'btaskee-constants';
import { BreadcrumbsLink } from 'btaskee-ui';
import {
  DEFAULT_RANGE_DATE_CURRENT_DAY,
  convertSortString,
  getPageSizeAndPageIndex,
  getSkipAndLimit,
} from 'btaskee-utils';
import { hocLoader } from '~/hoc/remix';
import { getCities, getUserSession } from '~/services/helpers.server';
import {
  getHistoryBReward,
  getTotalHistoryBReward,
} from '~/services/history-breward.server';

export const handle = {
  breadcrumb: () => (
    <BreadcrumbsLink
      to={ROUTE_NAME.REPORT_HISTORY_BREWARD}
      label="HISTORY_BREWARD"
    />
  ),
};

export const loader = hocLoader(async ({ request }) => {
  const { isoCode } = await getUserSession({ headers: request.headers });

  const url = new URL(request.url);

  const search = url.searchParams.get('search') || '';
  const rangeDate = url.searchParams.get('createdAt') || '';
  const city = url.searchParams.get('city') || '';
  const sort = url.searchParams.get('sort') || '';
  const codeType = url.searchParams.get('codeType') || '';

  const filterValue = {
    search,
    rangeDate: DEFAULT_RANGE_DATE_CURRENT_DAY(rangeDate),
    city,
    codeType,
  };

  const [total, cities] = await Promise.all([
    getTotalHistoryBReward({ filter: filterValue, isoCode }),
    getCities(isoCode),
  ]);

  const { limit, skip } = getSkipAndLimit(
    getPageSizeAndPageIndex({
      total,
      pageSize: Number(url.searchParams.get('pageSize')) || 0,
      pageIndex: Number(url.searchParams.get('pageIndex')) || 0,
    }),
  );

  const historyBReward = await getHistoryBReward({
    isoCode,
    skip,
    limit,
    sort: convertSortString({
      sortString: sort,
      defaultValue: { usedAt: -1 },
    }),
    filter: filterValue,
  });

  return json({
    historyBReward,
    total,
    cities,
    filterValue,
  });
}, PERMISSIONS.READ_HISTORY_BREWARD);

export default function HistoryBRewardScreen() {
  const loaderData = useLoaderData<LoaderTypeWithError<typeof loader>>();

  return <Outlet context={loaderData} />;
}
