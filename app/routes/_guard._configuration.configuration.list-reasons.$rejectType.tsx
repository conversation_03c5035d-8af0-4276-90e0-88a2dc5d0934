import { DotsHorizontalIcon } from '@radix-ui/react-icons';
import type { SerializeFrom } from '@remix-run/node';
import { json } from '@remix-run/node';
import {
  Link,
  Outlet,
  isRouteErrorResponse,
  useActionData,
  useLocation,
  useNavigate,
  useRouteError,
  useSearchParams,
  useSubmit,
} from '@remix-run/react';
import type { ColumnDef } from '@tanstack/react-table';
import {
  ACTION_NAME,
  PERMISSIONS,
  ROUTE_NAME,
  TASKER_PROFILE_REJECT_STATUS,
} from 'btaskee-constants';
import { useGlobalStore, useLoaderDataSafely } from 'btaskee-hooks';
import {
  BTaskeeTable,
  Breadcrumbs,
  BreadcrumbsLink,
  BtaskeeResponseError,
  Button,
  Card,
  DataTableColumnHeader,
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
  Label,
  Separator,
  Tabs,
  Ta<PERSON>List,
  TabsTrigger,
  Typography,
  toast,
  useConfirm,
} from 'btaskee-ui';
import {
  getOrderNumber,
  getPageSizeAndPageIndex,
  getSkipAndLimit,
} from 'btaskee-utils';
import { Plus } from 'lucide-react';
import { useCallback, useEffect, useMemo, useState } from 'react';
import { useTranslation } from 'react-i18next';
import { hocAction, hocLoader } from '~/hoc/remix';
import i18next from '~/i18next.server';
import { getUserSession } from '~/services/helpers.server';
import {
  deleteRejectReason,
  getListRejectReasons,
  getTotalRejectReasons,
} from '~/services/reject-reason.server';
import { getSession } from '~/services/session.server';

export const handle = {
  breadcrumb: (data: { rejectType: string }) => (
    <BreadcrumbsLink
      to={`${ROUTE_NAME.LIST_REASONS}/${data.rejectType}`}
      label={data.rejectType}
    />
  ),
};

export function ErrorBoundary() {
  const error = useRouteError() as { message?: string };
  const { t: tCommon } = useTranslation('common');

  if (isRouteErrorResponse(error)) {
    if (error?.message) {
      toast({ description: JSON.stringify(error.message) });
    }

    return <BtaskeeResponseError t={tCommon} errorStatus={error.status} />;
  }

  return <BtaskeeResponseError t={tCommon} errorStatus={500} />;
}

export const loader = hocLoader(async ({ request, params }) => {
  const { isoCode } = await getUserSession({
    headers: request.headers,
  });

  const url = new URL(request.url);

  const rejectType =
    params?.rejectType || TASKER_PROFILE_REJECT_STATUS.NEEDS_UPDATE;
  const search = url.searchParams.get('search') || '';

  const total = await getTotalRejectReasons({
    isoCode,
    type: rejectType,
    search,
  });

  const { skip, limit } = getSkipAndLimit(
    getPageSizeAndPageIndex({
      total,
      pageSize: Number(url.searchParams.get('pageSize')) || 10,
      pageIndex: Number(url.searchParams.get('pageIndex')) || 0,
    }),
  );

  const listReasons = await getListRejectReasons({
    isoCode,
    type: rejectType,
    search,
    skip,
    limit,
  });

  const session = await getSession(request.headers.get('Cookie'));
  const flashMessage = session.get('flashMessage');

  return json({
    rejectType,
    listReasons,
    total,
    flashMessage,
  });
}, PERMISSIONS.READ_LIST_REASONS);

export const action = hocAction(
  async ({ request }, { setInformationActionHistory }) => {
    const { isoCode } = await getUserSession({
      headers: request.headers,
    });

    const formData = await request.clone().formData();
    await deleteRejectReason({
      isoCode,
      id: formData.get('reasonId')?.toString() || '',
    });

    setInformationActionHistory({
      action: ACTION_NAME.DELETE_REJECT_REASON,
    });

    const tRejectReason = await i18next.getFixedT(request, 'reject-reason');

    return json({
      flashMessage: tRejectReason('REASON_DELETED_SUCCESSFULLY'),
    });
  },
  PERMISSIONS.WRITE_REJECT_REASON,
);

export default function ListReasonIndex() {
  const { t: tRejectReason } = useTranslation('reject-reason');
  const navigate = useNavigate();
  const confirm = useConfirm();
  const submit = useSubmit();
  const [searchParams] = useSearchParams();
  const permissions = useGlobalStore(state => state.permissions);

  const { pathname } = useLocation();

  const pageSize = useMemo(
    () => Number(searchParams.get('pageSize') || 10),
    [searchParams],
  );
  const pageIndex = useMemo(
    () => Number(searchParams.get('pageIndex') || 0),
    [searchParams],
  );

  const [openEditReasonDialog, setOpenEditReasonDialog] =
    useState<boolean>(true);

  const loaderData = useLoaderDataSafely<typeof loader>();

  useEffect(() => {
    if (loaderData?.flashMessage) {
      toast({ variant: 'success', description: loaderData.flashMessage });
    }
  }, [loaderData.flashMessage]);

  const actionData = useActionData<ActionTypeWithError<typeof action>>();
  useEffect(() => {
    if (actionData?.flashMessage) {
      toast({ variant: 'success', description: actionData.flashMessage });
    }
  }, [actionData]);

  const handleDeleteReason = useCallback(
    async (
      reason: SerializeFrom<
        ReturnValueIgnorePromise<typeof getListRejectReasons>[0]
      >,
    ) => {
      const formData = new FormData();

      if (
        await confirm({
          title: `${tRejectReason('DELETE_REASON')}?`,
          body: (
            <div>
              <Typography variant="p">
                {tRejectReason('CONFIRM_DELETE_REASON')}
              </Typography>
              <Card className="p-4 border border-gray-200 rounded-[6px] bg-gray-50 mt-4">
                <Label className="text-gray-400 font-medium">
                  {tRejectReason('TYPE')}
                </Label>
                <Typography
                  affects="removePMargin"
                  variant="p"
                  className="text-gray-600 font-medium">
                  {tRejectReason(reason.type)}
                </Typography>
                <Separator className="my-3" />
                <Label className="text-gray-400 font-medium">
                  {tRejectReason('REASON')}
                </Label>
                <Typography
                  affects="removePMargin"
                  variant="p"
                  className="text-gray-600 font-medium">
                  {reason.name}
                </Typography>
              </Card>
            </div>
          ),
          actionButton: tRejectReason('CONFIRM'),
          cancelButton: tRejectReason('CANCEL'),
        })
      ) {
        formData.append('reasonId', reason._id);
        submit(formData, { method: 'delete' });
      }
    },
    [tRejectReason, confirm, submit],
  );

  const columns: ColumnDef<
    SerializeFrom<ReturnValueIgnorePromise<typeof getListRejectReasons>[0]>
  >[] = useMemo(() => {
    return [
      {
        accessorKey: 'no',
        header: ({ column }) => (
          <DataTableColumnHeader
            column={column}
            title={tRejectReason('NO')}
            className="text-start"
          />
        ),
        cell: ({ row }) => (
          <Typography variant="p" className="text-start">
            {getOrderNumber({ pageIndex, pageSize, orderColumn: row.index })}
          </Typography>
        ),
        size: 20,
        enableSorting: false,
      },
      {
        accessorKey: 'reasons',
        header: ({ column }) => (
          <DataTableColumnHeader
            column={column}
            title={tRejectReason('REASONS')}
          />
        ),
        cell: ({ row }) => (
          <Typography
            className="whitespace-normal overflow-hidden break-words"
            variant="p">
            {row.original.name}
          </Typography>
        ),
        enableSorting: false,
        size: 550,
      },
      {
        accessorKey: 'action',
        header: ({ column }) => (
          <DataTableColumnHeader
            column={column}
            className="whitespace-nowrap text-end"
            title={tRejectReason('ACTION')}
          />
        ),
        size: 10,
        cell: ({ row }) => (
          <DropdownMenu>
            <DropdownMenuTrigger asChild>
              <Button
                variant="ghost"
                className="ml-auto mr-2 flex h-8 w-8 p-0 data-[state=open]:bg-muted">
                <DotsHorizontalIcon className="h-4 w-4" />
                <span className="sr-only">{tRejectReason('OPEN_MENU')}</span>
              </Button>
            </DropdownMenuTrigger>
            <DropdownMenuContent align="end">
              <DropdownMenuItem
                disabled={
                  !permissions.includes(PERMISSIONS.WRITE_REJECT_REASON)
                }
                onClick={e => {
                  e.stopPropagation();
                  navigate(
                    `${ROUTE_NAME.LIST_REASONS}/${row.original.type}/${row.original._id}`,
                  );
                }}>
                {tRejectReason('EDIT_REASON')}
              </DropdownMenuItem>
              <DropdownMenuItem
                disabled={
                  !permissions.includes(PERMISSIONS.WRITE_REJECT_REASON)
                }
                className="text-red-500"
                onClick={e => {
                  e.stopPropagation();
                  handleDeleteReason(row.original);
                }}>
                {tRejectReason('DELETE_REASON')}
              </DropdownMenuItem>
            </DropdownMenuContent>
          </DropdownMenu>
        ),
        enableSorting: false,
      },
    ];
  }, [
    handleDeleteReason,
    navigate,
    pageIndex,
    pageSize,
    permissions,
    tRejectReason,
  ]);

  return (
    <>
      {!pathname.includes('/create') ? (
        <>
          <Tabs
            value={loaderData?.rejectType}
            onValueChange={value => {
              navigate(`${ROUTE_NAME.LIST_REASONS}/${value}`);
            }}
            className="space-y-4">
            <TabsList>
              {Object.entries(TASKER_PROFILE_REJECT_STATUS).map(
                ([key, value]) => (
                  <TabsTrigger
                    key={key}
                    value={value}
                    className="hover:bg-white hover:shadow-sm mx-1">
                    {tRejectReason(key)}
                  </TabsTrigger>
                ),
              )}
            </TabsList>
          </Tabs>
          <div className="flex justify-between items-center rounded-[15px] bg-secondary p-4 min-h-[90px] my-6">
            <div className="grid gap-2">
              <Typography variant="h3">
                {tRejectReason(loaderData?.rejectType || '')}
              </Typography>
              <Breadcrumbs />
            </div>
            <Button
              asChild={permissions.includes(PERMISSIONS.WRITE_REJECT_REASON)}
              disabled={!permissions.includes(PERMISSIONS.WRITE_REJECT_REASON)}>
              <Link
                className="flex items-center gap-2"
                to={`${ROUTE_NAME.LIST_REASONS}/${loaderData?.rejectType}/create`}>
                <Plus />
                {tRejectReason('ADD_REASON')}
              </Link>
            </Button>
          </div>
          <BTaskeeTable
            onClickRow={row => {
              navigate(
                `${ROUTE_NAME.LIST_REASONS}/${loaderData?.rejectType}/${row._id}`,
              );
            }}
            isShowClearButton
            columns={columns}
            total={loaderData?.total || 0}
            data={loaderData?.listReasons || []}
            search={{
              defaultValue: '',
              name: 'search',
              placeholder: tRejectReason('SEARCH'),
            }}
            pagination={getPageSizeAndPageIndex({
              total: loaderData?.total || 0,
              pageSize,
              pageIndex,
            })}
            localeAddress="reject-reason"
          />
        </>
      ) : null}

      <Outlet
        context={{
          openEditReasonDialog,
          setOpenEditReasonDialog,
          onClose: () => {
            return navigate(
              `${ROUTE_NAME.LIST_REASONS}/${loaderData?.rejectType}`,
              {
                replace: true,
                preventScrollReset: true,
              },
            );
          },
        }}
      />
    </>
  );
}
