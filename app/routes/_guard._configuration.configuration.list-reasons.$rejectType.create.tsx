import { redirect } from '@remix-run/node';
import {
  isRouteErrorResponse,
  useActionData,
  useNavigate,
  useRouteError,
  useSubmit,
} from '@remix-run/react';
import {
  ACTION_NAME,
  PERMISSIONS,
  ROUTE_NAME,
  type TASKER_PROFILE_REJECT_STATUS,
} from 'btaskee-constants';
import {
  Breadcrumbs,
  BreadcrumbsLink,
  BtaskeeResponseError,
  Button,
  Card,
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
  Grid,
  Input,
  Separator,
  Typography,
  toast,
  useConfirm,
} from 'btaskee-ui';
import { Plus, Trash2 } from 'lucide-react';
import { useEffect } from 'react';
import { useFieldArray, useForm } from 'react-hook-form';
import { useTranslation } from 'react-i18next';
import { hocAction } from '~/hoc/remix';
import i18next from '~/i18next.server';
import { getUserSession } from '~/services/helpers.server';
import { createRejectReasons } from '~/services/reject-reason.server';
import { commitSession, getSession } from '~/services/session.server';

export const handle = {
  breadcrumb: () => <BreadcrumbsLink to="" label="ADD_REASON" />,
  i18n: 'reject-reason',
};

export function ErrorBoundary() {
  const error = useRouteError() as { message?: string };
  const { t: tCommon } = useTranslation('common');

  if (isRouteErrorResponse(error)) {
    if (error?.message) {
      toast({ description: JSON.stringify(error.message) });
    }

    return <BtaskeeResponseError t={tCommon} errorStatus={error.status} />;
  }

  return <BtaskeeResponseError t={tCommon} errorStatus={500} />;
}

export const action = hocAction(
  async ({ request, params }, { setInformationActionHistory }) => {
    const rejectType = params.rejectType || '';
    const { isoCode, username } = await getUserSession({
      headers: request.headers,
    });

    const formData = await request.clone().formData();
    const reasonsList: RejectReasonForm['reasons'] = JSON.parse(
      formData.get('reasons')?.toString() || '[]',
    );

    const rejectReasonsData = reasonsList.map(reason => ({
      name: reason.name,
      type: rejectType,
    }));

    await createRejectReasons({
      isoCode,
      createdBy: username,
      rejectReasonsData: rejectReasonsData,
    });

    setInformationActionHistory({
      action: ACTION_NAME.CREATE_REJECT_REASON,
    });

    const session = await getSession(request.headers.get('cookie'));
    const t = await i18next.getFixedT(request, 'reject-reason');
    session.flash('flashMessage', t('REASON_CREATED_SUCCESSFULLY'));

    const newSession = await commitSession(session);

    return redirect(`${ROUTE_NAME.LIST_REASONS}/${rejectType}`, {
      headers: {
        'Set-Cookie': newSession,
      },
    });
  },
  PERMISSIONS.WRITE_REJECT_REASON,
);

interface RejectReasonForm {
  reasons: Array<{
    name: string;
  }>;
  name: string;
  type: TASKER_PROFILE_REJECT_STATUS;
}

export default function CreateNewReasonScreen() {
  const submit = useSubmit();
  const confirm = useConfirm();
  const navigate = useNavigate();
  const { t } = useTranslation('reject-reason');

  const actionData = useActionData<ActionTypeWithError<typeof action>>();
  useEffect(() => {
    if (actionData?.error) {
      toast({ description: actionData.error });
    }
  }, [actionData]);

  const form = useForm<RejectReasonForm>({
    defaultValues: {
      reasons: [
        {
          name: '',
        },
      ],
    },
  });

  const onSubmit = async (data: RejectReasonForm) => {
    const formData = new FormData();
    formData.append('name', data.name);
    formData.append('reasons', JSON.stringify(data.reasons));

    const isConfirm = await confirm({
      title: t('CREATE_REASON'),
      body: t('ARE_YOU_SURE_YOU_WANT_TO_CREATE_REASON'),
      cancelButton: t('CANCEL'),
      actionButton: t('CONFIRM'),
    });

    if (isConfirm) submit(formData, { method: 'post' });
  };

  const { fields, append, remove } = useFieldArray({
    control: form.control,
    name: 'reasons',
  });

  return (
    <>
      <div className="mb-6 grid space-y-2 rounded-2xl bg-secondary p-4 min-h-[90px]">
        <Typography variant="h3">{t('ADD_REASON')}</Typography>
        <Breadcrumbs />
      </div>
      <Form {...form}>
        <form onSubmit={form.handleSubmit(onSubmit)}>
          <Grid className="mb-6 gap-6">
            {fields.map((field, index) => {
              return (
                <FormField
                  key={field.id}
                  name={`reasons.${index}.name`}
                  control={form.control}
                  rules={{ required: t('THIS_FIELD_IS_REQUIRED') }}
                  render={({ field }) => (
                    <FormItem>
                      <div className="flex justify-between items-center">
                        <FormLabel className={'text-gray-700'}>
                          {t('REASON')} {index + 1}
                        </FormLabel>
                        {fields.length === 1 ? null : (
                          <button
                            className="flex gap-2 items-center text-sm text-gray-500 font-medium"
                            type="button"
                            onClick={() => remove(index)}>
                            <Trash2 className="h-4 w-4" />
                            {t('REMOVE')}
                          </button>
                        )}
                      </div>
                      <FormControl>
                        <Input {...field} placeholder={t('ENTER_REASON')} />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
              );
            })}
            <Card
              onClick={() =>
                append({
                  name: '',
                })
              }
              className="flex h-20 cursor-pointer items-center justify-center gap-2 border-dashed border-primary p-10 text-primary text-sm">
              <Plus />
              {t('ADD_NEW_REASON')} {fields.length + 1}
            </Card>
          </Grid>

          <Separator className="my-6" />
          <div className="flex justify-end gap-4">
            <Button
              type="button"
              variant="outline"
              className="border-primary text-primary hover:text-primary"
              onClick={() => navigate(-1)}>
              {t('CANCEL')}
            </Button>
            <Button type="submit" className="bg-primary hover:bg-primary-600">
              {t('SUBMIT')}
            </Button>
          </div>
        </form>
      </Form>
    </>
  );
}
