import type { LoaderFunctionArgs, SerializeFrom } from '@remix-run/node';
import { json } from '@remix-run/node';
import {
  Link,
  isRouteErrorResponse,
  useLoaderData,
  useRouteError,
} from '@remix-run/react';
import type { ColumnDef } from '@tanstack/react-table';
import { PERMISSIONS, ROUTE_NAME } from 'btaskee-constants';
import { useGlobalStore } from 'btaskee-hooks';
import {
  AspectRatio,
  Breadcrumbs,
  BreadcrumbsLink,
  BtaskeeResponseError,
  Button,
  Card,
  CardContent,
  CardHeader,
  DataTableBasic,
  DataTableColumnHeader,
  Grid,
  MultiLanguageSectionView,
  MultiLanguageText,
  Separator,
  Typography,
  toast,
} from 'btaskee-ui';
import { formatNumberWithCommas } from 'btaskee-utils';
import i18n from 'i18next';
import { useEffect, useMemo } from 'react';
import { useTranslation } from 'react-i18next';
import { BlockDescription } from '~/components/tasker-common';
import { hocLoader } from '~/hoc/remix';
import {
  getSettingCountryByIsoCode,
  getUserSession,
} from '~/services/helpers.server';
import { getServices } from '~/services/service.server';
import { getSession } from '~/services/session.server';
import {
  getListToolkitItems,
  getToolkitSettingDetail,
} from '~/services/toolkit.server';

export const handle = {
  breadcrumb: (data: { toolkitId: string }) => {
    return (
      <BreadcrumbsLink
        to={`${ROUTE_NAME.TOOLKIT_SETTING}/${data.toolkitId}`}
        label="TOOLKIT_DETAIL"
      />
    );
  },
  i18n: 'toolkit',
};

export function ErrorBoundary() {
  const error = useRouteError() as { message?: string };
  const { t: tCommon } = useTranslation('common');

  if (isRouteErrorResponse(error)) {
    if (error?.message) {
      toast({ description: JSON.stringify(error.message) });
    }

    return <BtaskeeResponseError t={tCommon} errorStatus={error.status} />;
  }

  return <BtaskeeResponseError t={tCommon} errorStatus={500} />;
}

export const loader = hocLoader(
  async ({ request, params }: LoaderFunctionArgs) => {
    const { isoCode } = await getUserSession({
      headers: request.headers,
    });

    const [toolkitSetting, settingCountry, listToolkitItems, listAllServices] =
      await Promise.all([
        getToolkitSettingDetail({ isoCode, toolkitSettingId: params.id || '' }),
        getSettingCountryByIsoCode({ isoCode, projection: { currency: 1 } }),
        getListToolkitItems({ isoCode }),
        getServices({
          isoCode,
          projection: {
            _id: 1,
            name: 1,
            text: 1,
            isSubscription: 1,
          },
        }),
      ]);

    const session = await getSession(request.headers.get('cookie'));
    const flashMessage = session.get('flashMessage');

    return json({
      flashMessage: flashMessage || '',
      toolkitSetting,
      settingCountry,
      listToolkitItems,
      listAllServices,
    });
  },
  PERMISSIONS.READ_TOOLKIT_SETTING,
);

export default function ToolkitSettingDetail() {
  //TODO: Implement translate for this page
  const { t } = useTranslation('toolkit');

  const loaderData = useLoaderData<LoaderTypeWithError<typeof loader>>();

  useEffect(() => {
    if (loaderData?.flashMessage) {
      toast({
        variant: 'success',
        description: loaderData.flashMessage,
      });
    }
  }, [loaderData.flashMessage]);

  const { toolkitSetting, settingCountry, listToolkitItems, listAllServices } =
    loaderData;
  const permissions = useGlobalStore(state => state.permissions);

  const totalPrice = useMemo(() => {
    return toolkitSetting?.toolKitItems?.reduce((acc, item) => {
      const toolkitItem = listToolkitItems.find(i => i._id === item._id);
      return acc + (toolkitItem?.price || 0) * item.quantity;
    }, 0);
  }, [toolkitSetting?.toolKitItems, listToolkitItems]);

  const columns: ColumnDef<SerializeFrom<ToolKitItem>>[] = useMemo(
    () => [
      {
        accessorKey: 'tool',
        header: ({ column }) => (
          <DataTableColumnHeader column={column} title={t('TOOL')} />
        ),
        cell: ({ row }) => (
          <div className="flex items-center gap-2">
            <div className="w-[40px]">
              <AspectRatio ratio={1 / 1}>
                <img
                  src={row?.original?.image || ''}
                  alt="Tool"
                  className="rounded-sm object-cover w-full h-full"
                />
              </AspectRatio>
            </div>
            <MultiLanguageText
              text={
                listToolkitItems.find(item => item._id === row?.original?._id)
                  ?.text || {}
              }
              layout="row"
            />
          </div>
        ),
        size: 20,
        enableSorting: false,
      },
      {
        accessorKey: 'price',
        header: ({ column }) => (
          <DataTableColumnHeader column={column} title={t('PRICE')} />
        ),
        cell: ({ row }) => (
          <span>
            {formatNumberWithCommas(
              listToolkitItems.find(item => item._id === row?.original?._id)
                ?.price || 0,
            )}
            {settingCountry?.currency?.sign}
          </span>
        ),
        size: 20,
        enableSorting: false,
      },
    ],
    [t, listToolkitItems, settingCountry?.currency?.sign],
  );

  //NOTE: Data schema is defined percentage as decimal but the UI is integer, so we need to multiply by 100 when getting to show on UI
  return (
    <>
      <div className="flex bg-secondary p-4 justify-between items-center min-h-24 rounded-md mb-6">
        <Grid className="gap-3">
          <Typography className="capitalize" variant="h2">
            {t('TOOLKIT_DETAIL')}
          </Typography>
          <Breadcrumbs />
        </Grid>
        <Button
          disabled={!permissions.includes(PERMISSIONS.WRITE_TOOLKIT_SETTING)}
          asChild>
          <Link
            to={`${ROUTE_NAME.TOOLKIT_SETTING}/${toolkitSetting?._id}${ROUTE_NAME.EDIT}`}>
            {t('UPDATE_TOOLKIT')}
          </Link>
        </Button>
      </div>

      <div className="flex flex-col gap-8 mt-6">
        <Card className="bg-gray-50">
          <CardHeader>
            <Typography
              variant="h4"
              className="min-w-fit w-1/5 border-b-gray-200 border-b pb-3">
              {t('GENERAL')}
            </Typography>
          </CardHeader>
          <CardContent className="flex flex-col gap-6">
            <div className="grid grid-cols-3 gap-6 items-start">
              <div className="p-6 border border-dashed rounded-md border-gray-300 bg-gray-100">
                <AspectRatio ratio={2 / 1}>
                  <img
                    src={toolkitSetting?.image || ''}
                    alt="Toolkit Cover"
                    className="rounded-sm object-cover w-full h-full"
                  />
                </AspectRatio>
              </div>
              <MultiLanguageSectionView
                data={{ toolkitName: toolkitSetting?.text || {} }}
                fields={{ toolkitName: 'Toolkit Name' }}
              />
              <div className="flex flex-col gap-6">
                <BlockDescription
                  className="flex-wrap"
                  desc={{
                    label: t('SERVICES'),
                    tags:
                      toolkitSetting?.serviceIds
                        ?.map(serviceId => {
                          const service = listAllServices.find(
                            s => s._id === serviceId,
                          );
                          return `${service?.text?.[i18n.language || 'en']}${service?.isSubscription ? ' (Subscription)' : ''}`;
                        })
                        .filter((tag): tag is string => tag !== undefined) ||
                      [],
                  }}
                />

                <BlockDescription
                  desc={{
                    label: t('PRICE'),
                    value: `${formatNumberWithCommas(totalPrice)} ${settingCountry?.currency?.code || ''}`,
                  }}
                />
              </div>
            </div>
          </CardContent>
        </Card>

        <Card className="bg-gray-50">
          <CardHeader>
            <Typography
              variant="h4"
              className="min-w-fit w-1/5 border-b-gray-200 border-b pb-3">
              {t('TOOLKIT_ITEMS')}
            </Typography>
          </CardHeader>
          <CardContent className="flex flex-col gap-6">
            <DataTableBasic
              manualPagination
              isDisplayPagination={false}
              columns={columns}
              //TODO: Refactor below
              data={
                toolkitSetting?.toolKitItems?.map(item => ({
                  ...item,
                  image:
                    listToolkitItems.find(i => i._id === item._id)?.image || '',
                  price:
                    listToolkitItems.find(i => i._id === item._id)?.price || 0,
                  text:
                    listToolkitItems.find(i => i._id === item._id)?.text || {},
                  createdAt:
                    listToolkitItems.find(i => i._id === item._id)?.createdAt ||
                    '',
                })) || []
              }
            />
          </CardContent>
        </Card>

        <Card className="bg-gray-50">
          <CardHeader>
            <Typography
              variant="h4"
              className="min-w-fit w-1/5 border-b-gray-200 border-b pb-3">
              {t('PAYMENT_DETAIL')}
            </Typography>
          </CardHeader>
          <CardContent className="flex flex-col gap-6">
            <div className="flex flex-col gap-4 bg-gray-100 rounded-md p-6">
              <Typography
                variant="p"
                className="font-semibold text-lg leading-relaxed">
                {t('PAY_NOW')}
              </Typography>
              <div className="grid grid-cols-3 gap-6">
                <BlockDescription
                  desc={{
                    label: t('DISCOUNT_FOR_ONCE_PAY'),
                    value: `${(toolkitSetting?.discountForOncePay || 0) * 100}%`,
                  }}
                />
                <BlockDescription
                  desc={{
                    label: t('MONEY_DISCOUNT_FOR_ONCE_PAY'),
                    value: `${formatNumberWithCommas((toolkitSetting?.discountForOncePay || 0) * totalPrice)} ${settingCountry?.currency?.code || ''}`,
                  }}
                />
              </div>
            </div>

            <div className="flex flex-col gap-2 bg-gray-100 rounded-md p-6">
              <Typography
                variant="p"
                className="font-semibold text-lg leading-relaxed mb-2">
                {t('PAY_LATER')}
              </Typography>
              <div className="grid grid-cols-3 gap-6">
                <BlockDescription
                  desc={{
                    label: t('PERCENT_FOR_FIRST_PAY'),
                    value: `${(toolkitSetting?.BNPLSetting?.firstPayPercent || 0) * 100}%`,
                  }}
                />
                <BlockDescription
                  desc={{
                    label: t('FIRST_PAY_MONEY'),
                    value: `${formatNumberWithCommas((toolkitSetting?.BNPLSetting?.firstPayPercent ? toolkitSetting.BNPLSetting.firstPayPercent : 0) * totalPrice)} ${settingCountry?.currency?.code || ''}`,
                  }}
                />
              </div>

              <Separator />

              <div className="grid grid-cols-3 gap-6">
                <BlockDescription
                  desc={{
                    label: t('PERCENT_BNPL_ON_TASK'),
                    value: `${(toolkitSetting?.BNPLSetting?.percentBNPLOnTask || 0) * 100}%`,
                  }}
                />
                <BlockDescription
                  desc={{
                    label: t('MONEY_BNPL_ON_TASK'),
                    value: `${formatNumberWithCommas((toolkitSetting?.BNPLSetting?.percentBNPLOnTask ? toolkitSetting.BNPLSetting.percentBNPLOnTask : 0) * (totalPrice - (toolkitSetting?.BNPLSetting?.firstPayPercent ? toolkitSetting.BNPLSetting.firstPayPercent : 0) * totalPrice))} ${settingCountry?.currency?.code || ''}`,
                  }}
                />
              </div>

              <Separator />

              <div className="grid grid-cols-3 gap-6">
                <BlockDescription
                  desc={{
                    label: t('PERIOD'),
                    value: `${toolkitSetting?.BNPLSetting?.period}`,
                  }}
                />
              </div>
            </div>
          </CardContent>
        </Card>
      </div>
    </>
  );
}
