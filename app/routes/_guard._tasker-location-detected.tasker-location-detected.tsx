import { Outlet } from '@remix-run/react';
import { PERMISSIONS, ROUTE_NAME } from 'btaskee-constants';
import { useLoaderDataSafely } from 'btaskee-hooks';
import { BreadcrumbsLink, toast } from 'btaskee-ui';
import { useEffect } from 'react';
import { hocLoader } from '~/hoc/remix';
import { willBecomeLoaderTaskerLocationDetected } from '~/hooks/useLoaderTaskerLocationDetected';

export const handle = {
  breadcrumb: () => {
    return (
      <BreadcrumbsLink
        to={ROUTE_NAME.TASKER_LOCATION_DETECTED}
        label="TASKER_LOCATION_DETECTED"
      />
    );
  },
  i18n: 'tasker-location-detected',
};

export const loader = hocLoader(willBecomeLoaderTaskerLocationDetected, [
  PERMISSIONS.READ_TASKER_LOCATION_DETECTED,
]);

export default function TaskerLocationDetectedIndex() {
  const { error: loaderError, ...restLoaderData } =
    useLoaderDataSafely<typeof loader>();

  useEffect(() => {
    if (loaderError) {
      toast({ description: loaderError });
    }
  }, [loaderError]);

  return <Outlet context={restLoaderData} />;
}
