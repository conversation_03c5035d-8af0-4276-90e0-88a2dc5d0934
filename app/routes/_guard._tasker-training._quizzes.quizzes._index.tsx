import { type SerializeFrom, json } from '@remix-run/node';
import {
  Link,
  isRouteErrorResponse,
  useNavigate,
  useRouteError,
  useSearchParams,
} from '@remix-run/react';
import { type ColumnDef } from '@tanstack/react-table';
import {
  FILTER_OPTION_IN_QUIZ_COLLECTION,
  PERMISSIONS,
  ROUTE_NAME,
} from 'btaskee-constants';
import { useGlobalStore, useLoaderDataSafely } from 'btaskee-hooks';
import {
  BTaskeeTable,
  Breadcrumbs,
  BtaskeeResponseError,
  Button,
  DATE_RANGE_PICKER_OPTIONS,
  DataTableColumnHeader,
  DropdownMenuBase,
  Grid,
  Typography,
  toast,
} from 'btaskee-ui';
import {
  DEFAULT_RANGE_DATE_CURRENT_DAY,
  convertSortString,
  getPageSizeAndPageIndex,
  getSkipAndLimit,
  momentTz,
} from 'btaskee-utils';
import { Plus } from 'lucide-react';
import { useEffect } from 'react';
import { useTranslation } from 'react-i18next';
import { hocLoader } from '~/hoc/remix';
import { getUserSession } from '~/services/helpers.server';
import { getSession } from '~/services/session.server';
import {
  getListQuizCollections,
  getTotalQuizCollections,
} from '~/services/tasker-training.server';

export function ErrorBoundary() {
  const error = useRouteError() as { message?: string };
  const { t: tCommon } = useTranslation('common');

  if (isRouteErrorResponse(error)) {
    if (error?.message) {
      toast({ description: JSON.stringify(error.message) });
    }

    return <BtaskeeResponseError t={tCommon} errorStatus={error.status} />;
  }

  return <BtaskeeResponseError t={tCommon} errorStatus={500} />;
}

export const loader = hocLoader(
  async ({ request }) => {
    const session = await getSession(request.headers.get('cookie'));
    const url = new URL(request.url);
    const { isoCode } = await getUserSession({
      headers: request.headers,
    });
    const flashMessage = await session.get('flashMessage');

    const searchText = url.searchParams.get('searchText') || '';
    const sort = url.searchParams.get('sort') || '';
    const updatedAt = url.searchParams.get('updatedAt') || '';
    const filters = url.searchParams.get('filters') || '';

    const filter = {
      name: searchText,
      code: searchText,
      filters,
      ...(updatedAt
        ? { updatedAt: DEFAULT_RANGE_DATE_CURRENT_DAY(updatedAt) }
        : {}),
    };

    const total = await getTotalQuizCollections({ isoCode, filter });
    const { limit, skip } = getSkipAndLimit(
      getPageSizeAndPageIndex({
        total,
        pageSize: Number(url.searchParams.get('pageSize')) || 0,
        pageIndex: Number(url.searchParams.get('pageIndex')) || 0,
      }),
    );
    const quizzes = await getListQuizCollections({
      isoCode,
      skip,
      limit,
      filter,
      sort: convertSortString({
        sortString: sort,
        defaultValue: { updatedAt: -1 },
      }),
    });

    return json({
      data: quizzes,
      total,
      filter,
      flashMessage,
    });
  },
  [PERMISSIONS.READ_TRAINING_QUIZ_COLLECTION],
);

export default function Quizzes() {
  const { t } = useTranslation('quiz');
  const loaderData = useLoaderDataSafely<typeof loader>();
  const permissions = useGlobalStore(store => store.permissions);
  const [searchParams] = useSearchParams();
  const navigate = useNavigate();

  useEffect(() => {
    if (loaderData.flashMessage) {
      toast({
        variant: 'success',
        description: loaderData.flashMessage,
      });
    }
  }, [loaderData.flashMessage]);

  const columns: ColumnDef<
    SerializeFrom<ReturnValueIgnorePromise<typeof getListQuizCollections>[0]>
  >[] = [
    {
      accessorKey: 'code',
      header: ({ column }) => (
        <DataTableColumnHeader column={column} title={t('CODE')} />
      ),
      size: 40,
      cell: ({ row }) => (
        <Typography variant="p">{row.original?.code}</Typography>
      ),
    },
    {
      size: 400,
      accessorKey: 'name',
      header: ({ column }) => (
        <DataTableColumnHeader
          className="whitespace-nowrap"
          column={column}
          title={t('QUIZ_COLLECTION_NAME')}
        />
      ),
      cell: ({ row }) => (
        <Typography variant="p">{row.original?.title}</Typography>
      ),
      enableSorting: false,
    },
    {
      accessorKey: 'image',
      header: ({ column }) => (
        <DataTableColumnHeader column={column} title={t('IMAGE')} />
      ),
      size: 40,
      cell: ({ row }) => (
        <Typography variant="p">
          {row.original?.image ? t('YES') : t('NO')}
        </Typography>
      ),
      enableSorting: false,
    },
    {
      accessorKey: 'video',
      header: ({ column }) => (
        <DataTableColumnHeader column={column} title={t('VIDEO')} />
      ),
      size: 40,
      cell: ({ row }) => (
        <Typography variant="p">
          {row.original?.video ? t('YES') : t('NO')}
        </Typography>
      ),
      enableSorting: false,
    },
    {
      accessorKey: 'numOfQuizzes',
      size: 60,
      header: ({ column }) => (
        <DataTableColumnHeader column={column} title={t('NUM_OF_QUIZ')} />
      ),
      cell: ({ row }) => (
        <Typography variant="p">{row.original?.numOfQuizzes || 0}</Typography>
      ),
    },
    {
      accessorKey: 'updatedByUsername',
      header: ({ column }) => (
        <DataTableColumnHeader
          className="whitespace-nowrap"
          column={column}
          title={t('UPDATED_BY')}
        />
      ),
      size: 80,
      cell: ({ row }) => (
        <Typography variant="p">
          {row.original?.updatedByUsername || row.original?.createdByUsername}
        </Typography>
      ),
      enableSorting: false,
    },
    {
      accessorKey: 'updatedAt',
      header: ({ column }) => (
        <DataTableColumnHeader column={column} title={t('UPDATED_AT')} />
      ),
      size: 80,
      cell: ({ row }) => (
        <Typography
          variant="p"
          affects="removePMargin"
          className="whitespace-nowrap">
          {row.original?.updatedAt
            ? momentTz(row.original.updatedAt).format('HH:mm - DD/MM/YYYY')
            : null}
        </Typography>
      ),
    },
    {
      accessorKey: 'action',
      header: ({ column }) => (
        <DataTableColumnHeader
          column={column}
          className="whitespace-nowrap text-right"
          title={t('ACTION')}
        />
      ),
      size: 44,
      cell: ({ row }) => (
        <DropdownMenuBase
          btnClassName="px-3 w-fit ml-auto"
          align="end"
          links={[
            {
              link: `${ROUTE_NAME.QUIZ_COLLECTION}/${row.original?._id}`,
              label: t('VIEW_DETAIL'),
            },
            {
              link: `${ROUTE_NAME.QUIZ_COLLECTION}/${row.original?._id}/edit`,
              label: t('UPDATE_QUIZ_COLLECTION'),
            },
          ]}
        />
      ),
      enableSorting: false,
    },
  ];

  return (
    <>
      <div className="flex bg-secondary p-4 justify-between items-center min-h-24 rounded-md mb-6">
        <Grid className="gap-3">
          <Typography className="capitalize" variant="h2">
            {t('QUIZ_COLLECTION')}
          </Typography>
          <Breadcrumbs />
        </Grid>
        <Button
          asChild
          disabled={
            !permissions.includes(PERMISSIONS.WRITE_TRAINING_QUIZ_COLLECTION)
          }>
          <Link
            className="flex gap-2 items-center"
            to={ROUTE_NAME.CREATE_QUIZ_COLLECTION}>
            <Plus />
            {t('CREATE_QUIZ_COLLECTION')}
          </Link>
        </Button>
      </div>
      <BTaskeeTable
        isShowClearButton
        columns={columns}
        total={loaderData?.total || 0}
        data={loaderData?.data || []}
        onClickRow={quizCollection =>
          navigate(`${ROUTE_NAME.QUIZ_COLLECTION}/${quizCollection._id}`)
        }
        search={{
          defaultValue: loaderData?.filter?.name || '',
          name: 'searchText',
          placeholder: t('SEARCH_BY_CODE_OR_NAME'),
        }}
        pagination={getPageSizeAndPageIndex({
          total: loaderData?.total || 0,
          pageSize: Number(searchParams.get('pageSize') || 0),
          pageIndex: Number(searchParams.get('pageIndex') || 0),
        })}
        filters={[
          {
            name: 'filters',
            placeholder: t('SELECT_OPTION'),
            options: Object.values(FILTER_OPTION_IN_QUIZ_COLLECTION).map(
              filter => ({
                label: t(filter),
                value: filter,
              }),
            ),
            value: loaderData?.filter?.filters || '',
          },
        ]}
        localeAddress="quiz"
        // eslint-disable-next-line @typescript-eslint/ban-ts-comment
        // @ts-ignore
        filterDate={{
          name: 'updatedAt',
          defaultValue: loaderData?.filter?.updatedAt
            ? {
                from: momentTz(loaderData.filter?.updatedAt?.from).toDate(),
                to: momentTz(loaderData.filter?.updatedAt?.to).toDate(),
              }
            : undefined,
          defaultRangeDateOptions: DATE_RANGE_PICKER_OPTIONS.NONE,
        }}
      />
    </>
  );
}
