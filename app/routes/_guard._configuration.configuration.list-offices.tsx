import { Outlet } from '@remix-run/react';
import { PERMISSIONS, ROUTE_NAME } from 'btaskee-constants';
import { useLoaderDataSafely } from 'btaskee-hooks';
import { BreadcrumbsLink, toast } from 'btaskee-ui';
import { useEffect } from 'react';
import { hocLoader } from '~/hoc/remix';
import { willBecomeOfficesConfigurationLoader } from '~/hooks/useGetOfficesConfiguration';

export const handle = {
  breadcrumb: () => (
    <BreadcrumbsLink to={ROUTE_NAME.LIST_OFFICES} label="LIST_OFFICES" />
  ),
};

export const loader = hocLoader(willBecomeOfficesConfigurationLoader, [
  PERMISSIONS.READ_LIST_OFFICES,
]);

export default function ListOffices() {
  const loaderData = useLoaderDataSafely<typeof loader>();

  useEffect(() => {
    if (loaderData?.error) {
      toast({ description: loaderData.error });
    }

    if (loaderData?.flashMessage) {
      if (loaderData?.flashMessage) {
        toast({
          variant: 'success',
          description: loaderData.flashMessage,
        });
      }
    }
  }, [loaderData]);

  return <Outlet context={loaderData} />;
}
