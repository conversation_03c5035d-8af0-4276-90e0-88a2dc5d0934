import { json, redirect } from '@remix-run/node';
import type { SerializeFrom } from '@remix-run/node';
import {
  isRouteErrorResponse,
  useActionData,
  useNavigation,
  useRouteError,
  useSubmit,
} from '@remix-run/react';
import type { ColumnDef } from '@tanstack/react-table';
import type { ConfirmUpdatingProfileFormProps } from 'app/components/tasker-common';
import {
  CardProfileDescription,
  ConfirmUpdatingProfile,
  ConfirmationDescriptions,
  NoteForm,
  ProfileImageAccordion,
  TaskerProfileAction,
  TaskerProfileCard,
  TaskerProfileStatus,
  UpdateNoteDialog,
  UpdateScheduleDialog,
  WarningMissingReasonConfigurationDialog,
} from 'app/components/tasker-common';
import {
  ACTION_NAME,
  HOUSEHOLD_IMAGE_TYPE_OF_TASKER_ONBOARDING,
  IMAGE_PROFILE_TYPE,
  MESSAGE_ALL_LANGUAGE_ON_TASKER_ONBOARDING,
  PERMISSIONS,
  REASON_TYPE_IN_TASKER_ONBOARDING_SETTING,
  REASON_UPDATING_IMAGE_IN_TASKER_ONBOARDING_PROFILE,
  ROUTE_NAME,
  TASKER_ONBOARDING_IMAGE_KEY,
  TASKER_ONBOARDING_PROCESS_STATUS,
} from 'btaskee-constants';
import { useGlobalStore, useLoaderDataSafely } from 'btaskee-hooks';
import {
  Breadcrumbs,
  BreadcrumbsLink,
  BtaskeeResponseError,
  Button,
  DataTableBasic,
  DataTableColumnHeader,
  Tabs,
  TabsContent,
  TabsList,
  TabsTrigger,
  Typography,
  toast,
  useBtaskeeFormController,
  useConfirm,
} from 'btaskee-ui';
import type { MappingOverallActionOnProfileDetailProps } from 'btaskee-utils';
import {
  getAllOfTaskerOnboardingMessageLanguage,
  getIsDisableApproveAction,
  getIsDisableSendingNotification,
  getOverallStatusInProfile,
  momentTz,
} from 'btaskee-utils';
import { format } from 'date-fns';
import i18n from 'i18next';
import { ChevronsRight } from 'lucide-react';
import { useCallback, useEffect, useMemo, useState } from 'react';
import { useForm } from 'react-hook-form';
import { useTranslation } from 'react-i18next';
import { z } from 'zod';
import { hoc404, hocAction, hocLoader } from '~/hoc/remix';
import i18next from '~/i18next.server';
import { getCitiesByUserId, getUserSession } from '~/services/helpers.server';
import { verifyManager } from '~/services/role-base-access-control.server';
import {
  getAllOfOffices,
  getAllReasonFromSetting,
  getTaskerProfileDetail,
  resetImageStatusInTaskerOrSupplierProfile,
  storeNoteIntoProfile,
  updateNoteByIndex,
  updateScheduleInfoInTaskerOrSupplierProfile,
  updateStatusOnTaskerProfile,
} from '~/services/tasker-profile.server';
import { sendNotification } from '~/services/utils.server';
import { formatPhoneNumber } from '~/utils/common';

export const handle = {
  breadcrumb: ({
    data,
  }: {
    data: SerializeFrom<
      ReturnValueIgnorePromise<typeof getTaskerProfileDetail>
    >['data'];
  }) => {
    return (
      <BreadcrumbsLink
        to={`${ROUTE_NAME.TASKER_ONBOARDING}/${data?._id}`}
        label="TASKER_PROFILE_DETAIL"
      />
    );
  },
  i18n: 'tasker-profile',
};

export function ErrorBoundary() {
  const error = useRouteError() as { message?: string };
  const { t: tCommon } = useTranslation('common');

  if (isRouteErrorResponse(error)) {
    if (error?.message) {
      toast({ description: JSON.stringify(error.message) });
    }

    return <BtaskeeResponseError t={tCommon} errorStatus={error.status} />;
  }

  return <BtaskeeResponseError t={tCommon} errorStatus={500} />;
}

export const loader = hocLoader(
  async ({ request, params }) => {
    const { isoCode, username, isSuperUser, userId } = await getUserSession({
      headers: request.headers,
    });
    const isManager = await verifyManager(userId);
    const cities = await getCitiesByUserId({
      userId,
      isManager: isManager || isSuperUser,
    });

    const [taskerProfileDetail, reasonsFromSetting, offices] =
      await Promise.all([
        hoc404(() =>
          getTaskerProfileDetail({
            profileId: params.profileId || '',
            cities,
            isoCode,
          }),
        ),
        getAllReasonFromSetting({
          isoCode,
        }),
        getAllOfOffices({ isoCode }),
      ]);

    return json({
      ...taskerProfileDetail,
      reasonsFromSetting,
      offices,
      username,
    });
  },
  [
    PERMISSIONS.READ_VERIFYING_STEP_ON_TASKER_ONBOARDING,
    PERMISSIONS.READ_APPROVED_STEP_ON_TASKER_ONBOARDING,
    PERMISSIONS.READ_ELIMINATED_STEP_ON_TASKER_ONBOARDING,
    PERMISSIONS.READ_REJECTED_STEP_ON_TASKER_ONBOARDING,
  ],
);

export const action = hocAction(
  async ({ request, params }, { setInformationActionHistory }) => {
    const formData = await request.clone().formData();
    const tTaskerProfile = await i18next.getFixedT(request, 'tasker-profile');
    const { isoCode, userId, username } = await getUserSession({
      headers: request.headers,
    });

    const status = formData.get('status')?.toString() || '';
    const reason = formData.get('reason')?.toString() || '';
    const language = formData.get('language')?.toString() || 'en';
    const message = formData.get('message')?.toString() || '';
    const fieldName = formData.get('fieldName')?.toString() || '';
    const note = formData.get('note')?.toString() || '';
    const updatedNote = formData.get('updatedNote')?.toString() || '';
    const noteIndex = formData.get('noteIndex');
    const sendRemindMessage =
      formData.get('sendRemindMessage')?.toString() || '';
    const officeInfo = formData.get('officeInfo')?.toString() || '';
    const imageField = formData.get('imageField')?.toString() || '';

    if (
      reason === REASON_UPDATING_IMAGE_IN_TASKER_ONBOARDING_PROFILE.REVIEW_AGAIN
    ) {
      const responseMessage = await resetImageStatusInTaskerOrSupplierProfile({
        isoCode,
        profileId: params?.profileId || '',
        reason,
        username,
        userId,
        imageField,
      });

      setInformationActionHistory({
        action: ACTION_NAME.RESET_IMAGE_STATUS_ON_TASKER_ONBOARDING_PROFILE,
        dataRelated: {
          profileId: params.profileId || '',
          imageField,
        },
      });

      return json({ msg: tTaskerProfile(responseMessage.msg) });
    }

    if (sendRemindMessage) {
      if (
        formData.get('currentProfileStatus')?.toString() !==
        TASKER_ONBOARDING_PROCESS_STATUS.NEEDS_UPDATE
      ) {
        await updateStatusOnTaskerProfile({
          profileId: params.profileId || '',
          status: TASKER_ONBOARDING_PROCESS_STATUS.NEEDS_UPDATE,
          userId,
          username,
          isoCode,
        });
      }

      await sendNotification({
        isSendNotificationId: true,
        userIds: [formData.get('taskerId')?.toString() || ''],
        locale: language as BtaskeeLanguage,
        message: JSON.parse(sendRemindMessage),
        isoCode,
      });

      setInformationActionHistory({
        action: ACTION_NAME.SEND_NOTIFICATION_REMINDING_TASK_TO_UPDATE_PROFILE,
        dataRelated: {
          profileId: params.profileId || '',
          newStatus: TASKER_ONBOARDING_PROCESS_STATUS.NEEDS_UPDATE,
        },
      });

      return json({ msg: tTaskerProfile('SEND_NOTIFICATION_SUCCESSFULLY') });
    }

    if (officeInfo) {
      await updateScheduleInfoInTaskerOrSupplierProfile({
        isoCode,
        profileId: params.profileId || '',
        officeInfo: JSON.parse(officeInfo),
        date: momentTz(formData.get('date')?.toString() || '').toDate(),
        message: JSON.parse(
          formData.get('scheduleMessage')?.toString() || '{}',
        ),
        language: language as BtaskeeLanguage,
      });

      if (
        formData.get('currentProfileStatus')?.toString() !==
        TASKER_ONBOARDING_PROCESS_STATUS.RESCHEDULE
      ) {
        await updateStatusOnTaskerProfile({
          profileId: params.profileId || '',
          status: TASKER_ONBOARDING_PROCESS_STATUS.RESCHEDULE,
          userId,
          username,
          isoCode,
        });
      }

      setInformationActionHistory({
        action: ACTION_NAME.RESCHEDULE_PROFILE_IN_TASKER_ONBOARDING,
        dataRelated: {
          profileId: params.profileId || '',
          status: TASKER_ONBOARDING_PROCESS_STATUS.RESCHEDULE,
        },
      });

      return json({ msg: tTaskerProfile('UPDATE_SCHEDULE_SUCCESSFULLY') });
    }

    if (note) {
      const noteResponseMessage = await storeNoteIntoProfile({
        profileId: params.profileId || '',
        note,
        userId,
        username,
        isoCode,
      });

      setInformationActionHistory({
        action: ACTION_NAME.ADD_NOTE_IN_TASKER_ONBOARDING,
        dataRelated: {
          profileId: params.profileId || '',
          note,
        },
      });

      return json({ msg: tTaskerProfile(noteResponseMessage) });
    }

    if (updatedNote && noteIndex) {
      const noteResponseMessage = await updateNoteByIndex({
        profileId: params.profileId || '',
        note: updatedNote,
        userId,
        username,
        isoCode,
        noteIndex: Number(noteIndex || 0),
      });

      setInformationActionHistory({
        action: ACTION_NAME.UPDATE_NOTE_IN_TASKER_ONBOARDING,
        dataRelated: {
          profileId: params.profileId || '',
          noteIndex: Number(noteIndex || 0),
          note: updatedNote,
        },
      });

      return json({ msg: tTaskerProfile(noteResponseMessage) });
    }

    const statusFilterValue = Object.values(
      TASKER_ONBOARDING_PROCESS_STATUS,
    )?.find(profileStatus => profileStatus === status);

    if (!statusFilterValue) {
      return redirect(ROUTE_NAME.TASKER_ONBOARDING);
    }

    const result = await updateStatusOnTaskerProfile({
      profileId: params.profileId || '',
      status: statusFilterValue,
      reason,
      userId,
      fieldName: fieldName as `${TASKER_ONBOARDING_IMAGE_KEY}`,
      username,
      isoCode,
      language: language as BtaskeeLanguage,
      ...(message ? { message: JSON.parse(message) } : {}),
    });

    setInformationActionHistory({
      action: ACTION_NAME.UPDATE_TASKER_PROFILE_STATUS,
      dataRelated: {
        profileId: params.profileId || '',
        newStatus: status,
        ...(fieldName ? { image: fieldName } : {}),
      },
    });

    return json({ msg: tTaskerProfile(result.msg) });
  },
  [PERMISSIONS.WRITE_TASKER_PROFILE_ON_TASKER_ONBOARDING],
);

export default function TaskerProfileDetail() {
  const { t: tTaskerProfile } = useTranslation('tasker-profile');

  const submit = useSubmit();
  const confirm = useConfirm();
  const [openDialog, setOpenDialog] = useState<boolean>(false);
  const [openWarningMissingReasonDialog, setOpenWarningMissingReasonDialog] =
    useState<boolean>(false);

  const {
    data: taskerProfile,
    reasonsFromSetting,
    offices: officesFromSetting,
    username,
    error: loaderError,
  } = useLoaderDataSafely<typeof loader>();
  const reversedNotes = useMemo(() => {
    // Need add index then passing it to server which update note by this index
    const notesWithIndex =
      taskerProfile?.notes?.map((note, index) => ({
        index,
        ...note,
      })) || [];
    return notesWithIndex.reverse();
  }, [taskerProfile?.notes]);
  const reversedActionHistories = useMemo(
    () => taskerProfile?.actionHistories?.reverse() || [],
    [taskerProfile?.actionHistories],
  );
  const permissions = useGlobalStore(state => state.permissions);
  const navigation = useNavigation();
  const actionData = useActionData<ActionTypeWithError<typeof action>>();

  const sendingNotificationController =
    useBtaskeeFormController<FormSendingNotificationOnTaskerOnboardingDetail>({
      zodRaw: {
        language: z.string().min(1, tTaskerProfile('THIS_IS_REQUIRE_FIELD')),
        taskerId: z.string().min(1, tTaskerProfile('THIS_IS_REQUIRE_FIELD')),
        sendRemindMessage: z
          .string()
          .min(1, tTaskerProfile('THIS_IS_REQUIRE_FIELD')),
      },
      defaultValues: {
        language: '',
        taskerId: '',
        sendRemindMessage: '',
      },
      confirmParams: {
        title: tTaskerProfile('TITLE_CONFIRM_SEND_NOTIFICATION'),
        body: (
          <>
            <Typography variant="p">
              {tTaskerProfile('DESCRIPTION_CONFIRM_SEND_NOTIFICATION')}
            </Typography>
            <ConfirmationDescriptions
              contents={[
                {
                  title: tTaskerProfile('TASKER_NAME'),
                  description: taskerProfile.taskerName,
                },
                {
                  title: tTaskerProfile('TASKER_USER_NAME'),
                  description: taskerProfile.username,
                },
              ]}
            />
          </>
        ),
        actionButton: tTaskerProfile('SUBMIT'),
        cancelButton: tTaskerProfile('CANCEL'),
      },
      formDataProvided: ({ sendRemindMessage, taskerId }) => {
        const formData = new FormData();

        formData.append('language', taskerProfile?.taskerLanguage || 'en');
        formData.append('taskerId', taskerId);
        formData.append('sendRemindMessage', sendRemindMessage);
        formData.append('currentProfileStatus', taskerProfile.status);

        return formData;
      },
    });

  const {
    control: controlUpdatingProfileStatus,
    getValues: getValuesUpdatingProfileStatus,
    setValue: setValueUpdatingProfileStatus,
  } = useForm<ConfirmUpdatingProfileFormProps>({
    defaultValues: {
      reason: '',
      status: TASKER_ONBOARDING_PROCESS_STATUS.VERIFYING,
    },
  });
  const {
    register: noteFormRegister,
    control: noteFormControl,
    resetField: resetNoteFormValue,
  } = useForm<{
    note: string;
  }>({
    defaultValues: {
      note: '',
    },
  });

  useEffect(() => {
    if (loaderError) {
      toast({ description: loaderError });
    }
  }, [loaderError]);

  useEffect(() => {
    if (actionData?.msg)
      toast({ description: actionData.msg, variant: 'success' });

    if (actionData?.error) toast({ description: actionData.error });
  }, [actionData]);

  const onTriggerProfile = async (
    values: Pick<MappingOverallActionOnProfileDetailProps, 'title' | 'dialog'>,
  ) => {
    const isConfirm = await confirm({
      title: tTaskerProfile(values.dialog?.title),
      body: (
        <ConfirmUpdatingProfile
          control={controlUpdatingProfileStatus}
          description={tTaskerProfile(values.dialog?.desc)}
          reasonsFromSetting={reasonsFromSetting}
          setValue={setValueUpdatingProfileStatus}
          taskerInfo={{
            taskerName: taskerProfile.taskerName,
            username: taskerProfile.username,
          }}
        />
      ),
      cancelButton: tTaskerProfile('CANCEL'),
      actionButton: tTaskerProfile('CONFIRM'),
    });

    if (isConfirm) {
      const formData = new FormData();
      const { status, reason, message } = getValuesUpdatingProfileStatus();
      formData.append('status', status);
      formData.append('language', taskerProfile?.taskerLanguage || 'en');

      if (reason) {
        formData.append('reason', reason);
      }

      if (message) {
        const parsedMessage = JSON.parse(message);
        formData.append(
          'message',
          JSON.stringify({
            title:
              MESSAGE_ALL_LANGUAGE_ON_TASKER_ONBOARDING[
                parsedMessage.title as keyof typeof MESSAGE_ALL_LANGUAGE_ON_TASKER_ONBOARDING
              ],
            body: MESSAGE_ALL_LANGUAGE_ON_TASKER_ONBOARDING[
              parsedMessage.body as keyof typeof MESSAGE_ALL_LANGUAGE_ON_TASKER_ONBOARDING
            ],
          }),
        );
        formData.append('language', taskerProfile?.taskerLanguage || 'en');
      }

      submit(formData, { method: 'post' });
    }
  };

  const onTriggerImageProfile = async ({
    title,
    desc,
    status,
    fieldName,
  }: ApprovalImageProfileProps) => {
    const rejectImageReasonsFromSetting = reasonsFromSetting?.filter(
      reasonFromSetting =>
        reasonFromSetting?.type === REASON_TYPE_IN_TASKER_ONBOARDING_SETTING,
    );

    if (status === TASKER_ONBOARDING_PROCESS_STATUS.REJECTED) {
      setValueUpdatingProfileStatus(
        'reason',
        rejectImageReasonsFromSetting?.[0]?.name,
      );
      setValueUpdatingProfileStatus('status', status);
    }

    const isConfirm = await confirm({
      title: tTaskerProfile(title),
      body: (
        <ConfirmUpdatingProfile
          control={controlUpdatingProfileStatus}
          description={tTaskerProfile(desc)}
          setValue={setValueUpdatingProfileStatus}
          reasons={
            status === TASKER_ONBOARDING_PROCESS_STATUS.REJECTED
              ? rejectImageReasonsFromSetting.map(reason => reason?.name)
              : []
          }
        />
      ),
      cancelButton: tTaskerProfile('CANCEL'),
      actionButton: tTaskerProfile('CONFIRM'),
    });

    if (isConfirm) {
      const formData = new FormData();
      const { reason } = getValuesUpdatingProfileStatus();
      formData.append('status', status);
      formData.append('language', taskerProfile?.taskerLanguage || 'en');
      formData.append('fieldName', fieldName);

      if (reason) {
        formData.append('reason', reason);
      }

      submit(formData, { method: 'post' });
    }
  };

  const updateNote = useCallback(
    async ({
      noteIndex,
      updatedNote,
    }: {
      noteIndex: number;
      updatedNote: string;
    }) => {
      const formData = new FormData();

      formData.append('noteIndex', noteIndex?.toString());
      formData.append('updatedNote', updatedNote || '');

      submit(formData, { method: 'post' });
    },
    [submit],
  );

  const historyColumns: ColumnDef<
    NonNullable<
      SerializeFrom<
        ReturnValueIgnorePromise<typeof getTaskerProfileDetail>
      >['data']['actionHistories']
    >[0]
  >[] = useMemo(
    () => [
      {
        accessorKey: 'updatedBy',
        header: ({ column }) => (
          <DataTableColumnHeader
            className="whitespace-nowrap"
            column={column}
            title={tTaskerProfile('CONTENT_UPDATED')}
          />
        ),
        cell: ({ row }) => (
          <div className="flex gap-3 whitespace-nowrap">
            <TaskerProfileStatus status={row.original?.oldStatus} />
            <ChevronsRight className="text-primary" />
            <TaskerProfileStatus status={row.original?.newStatus} />
          </div>
        ),
        enableSorting: false,
      },
      {
        accessorKey: 'updatedByUsername',
        header: ({ column }) => (
          <DataTableColumnHeader
            className="whitespace-nowrap"
            column={column}
            title={tTaskerProfile('UPDATED_BY')}
          />
        ),
        cell: ({ row }) => (
          <Typography variant="p">{row.original?.updatedByUsername}</Typography>
        ),
        enableSorting: false,
      },
      {
        accessorKey: 'createdAt',
        header: ({ column }) => (
          <DataTableColumnHeader
            className="whitespace-nowrap"
            column={column}
            title={tTaskerProfile('DATE')}
          />
        ),
        cell: ({ row }) =>
          row.original?.createdAt ? (
            <Typography variant="p">
              {format(row.original?.createdAt, 'HH:mm - dd/MM/yyyy')}
            </Typography>
          ) : null,
      },
      {
        accessorKey: 'reason',
        header: ({ column }) => (
          <DataTableColumnHeader
            className="whitespace-nowrap"
            column={column}
            title={tTaskerProfile('REASON')}
          />
        ),
        enableSorting: false,
        cell: ({ row }) => (
          <Typography variant="p">{row.original?.reason}</Typography>
        ),
      },
    ],
    [tTaskerProfile],
  );

  const noteColumns: ColumnDef<
    NonNullable<
      SerializeFrom<
        ReturnValueIgnorePromise<typeof getTaskerProfileDetail>
      >['data']['notes']
    >[0] & { index: number }
  >[] = useMemo(
    () => [
      {
        accessorKey: 'description',
        header: ({ column }) => (
          <DataTableColumnHeader
            className="whitespace-nowrap"
            column={column}
            title={tTaskerProfile('NOTE')}
          />
        ),
        cell: ({ row }) => (
          <div className="flex gap-2 items-center">
            <div className="flex items-center">
              <Typography variant="p" affects="removePMargin">
                {row.original?.description}
              </Typography>
              {row.original?.updatedAt ? (
                <Typography
                  className="italic text-gray-500"
                  affects="removePMargin"
                  variant="p">
                  (Edited)
                </Typography>
              ) : null}
            </div>
            {row.original?.notedAt &&
            momentTz(row.original.notedAt)
              .add(24, 'hours')
              .isAfter(momentTz().toDate()) &&
            username === row.original?.notedBy ? (
              <UpdateNoteDialog
                noteInfo={row.original}
                onSubmit={async ({ updatedNote }) =>
                  await updateNote({
                    updatedNote,
                    noteIndex: row.original?.index,
                  })
                }
              />
            ) : null}
          </div>
        ),
        enableSorting: false,
      },
      {
        accessorKey: 'updatedBy',
        header: ({ column }) => (
          <DataTableColumnHeader
            className="whitespace-nowrap"
            column={column}
            title={tTaskerProfile('NOTE_BY')}
          />
        ),
        cell: ({ row }) => (
          <Typography variant="p" className="whitespace-nowrap">
            {row.original?.notedBy}
          </Typography>
        ),
        enableSorting: false,
      },
      {
        accessorKey: 'notedAt',
        header: ({ column }) => (
          <DataTableColumnHeader
            className="whitespace-nowrap"
            column={column}
            title={tTaskerProfile('NOTE_AT')}
          />
        ),
        cell: ({ row }) =>
          row.original?.notedAt ? (
            <Typography variant="p" className="whitespace-nowrap">
              {format(row.original?.notedAt, 'HH:mm - dd/MM/yyyy')}
            </Typography>
          ) : null,
      },
      {
        accessorKey: 'updatedAt',
        header: ({ column }) => (
          <DataTableColumnHeader
            className="whitespace-nowrap"
            column={column}
            title={tTaskerProfile('UPDATED_AT')}
          />
        ),
        cell: ({ row }) =>
          row.original?.updatedAt ? (
            <Typography variant="p" className="whitespace-nowrap">
              {format(row.original?.updatedAt, 'HH:mm - dd/MM/yyyy')}
            </Typography>
          ) : null,
      },
    ],
    [tTaskerProfile, updateNote, username],
  );

  const onReviewImageAgain = async ({
    imageField,
  }: {
    imageField: `${TASKER_ONBOARDING_IMAGE_KEY}`;
  }) => {
    const isConfirm = await confirm({
      title: tTaskerProfile('TITLE_CONFIRM_REVIEW_AGAIN'),
      body: tTaskerProfile('DESCRIPTION_CONFIRM_REVIEW_AGAIN'),
      actionButton: tTaskerProfile('CONFIRM'),
      cancelButton: tTaskerProfile('CANCEL'),
    });

    if (isConfirm) {
      const formData = new FormData();

      formData.append(
        'reason',
        REASON_UPDATING_IMAGE_IN_TASKER_ONBOARDING_PROFILE.REVIEW_AGAIN,
      );
      formData.append('imageField', imageField);

      submit(formData, { method: 'post' });
    }
  };

  return (
    <>
      <div className="flex bg-secondary p-4 justify-between align-middle">
        <div className="grid space-y-2 rounded-xl">
          <div className="flex items-center">
            <Typography className="capitalize mr-6" variant="h2">
              {tTaskerProfile('TASKER_PROFILE_DETAIL')}
            </Typography>
            <TaskerProfileStatus
              status={getOverallStatusInProfile({
                processStatus: taskerProfile.processStatus,
                status: taskerProfile.status,
                isHaveAppointmentInfo: !!taskerProfile.appointmentInfo,
              })}
            />
          </div>
          <Breadcrumbs />
        </div>
        <div className="flex gap-6 my-auto pr-2">
          <TaskerProfileAction
            profileInfo={{
              taskerName: taskerProfile.taskerName,
              username: taskerProfile.username,
              appointmentInfo: taskerProfile.appointmentInfo,
            }}
            setOpenWarningReasonDialog={setOpenWarningMissingReasonDialog}
            setOpenScheduleDialog={setOpenDialog}
            isSubmitLoading={navigation.state === 'submitting'}
            status={getOverallStatusInProfile({
              processStatus: taskerProfile.processStatus,
              status: taskerProfile.status,
              isHaveAppointmentInfo: !!taskerProfile.appointmentInfo,
            })}
            onTriggerProfile={onTriggerProfile}
            reasonsFromSetting={reasonsFromSetting}
            disableApproveAction={getIsDisableApproveAction({
              status: getOverallStatusInProfile({
                processStatus: taskerProfile.processStatus,
                status: taskerProfile.status,
                isHaveAppointmentInfo: !!taskerProfile.appointmentInfo,
              }),
              isHaveAppointmentInfo: !!taskerProfile.appointmentInfo,
              identityCardStatus: taskerProfile.identityCard?.status ?? '',
              passportStatus: taskerProfile.passport?.status ?? '',
              houseElectricBillStatus:
                taskerProfile.houseElectricBill?.status ?? '',
              workPermitStatus: taskerProfile.workPermit?.status ?? '',
              householdStatus:
                taskerProfile.household?.status ??
                taskerProfile.houseElectricBill?.status,
            })}
            disabled={
              !permissions.includes(
                PERMISSIONS.WRITE_TASKER_PROFILE_ON_TASKER_ONBOARDING,
              )
            }
            setValue={setValueUpdatingProfileStatus}
            control={controlUpdatingProfileStatus}
          />
        </div>
      </div>
      {taskerProfile?.status === TASKER_ONBOARDING_PROCESS_STATUS?.APPROVED &&
      taskerProfile?.appointmentInfo ? (
        <TaskerProfileCard
          title={tTaskerProfile('INFORMATION_RECEPTION_ADDRESS')}>
          <CardProfileDescription
            descriptions={[
              {
                label: tTaskerProfile('OFFICE'),
                value: taskerProfile.appointmentInfo?.name,
              },
              {
                label: tTaskerProfile('ADDRESS'),
                value: taskerProfile.appointmentInfo?.address,
              },
              {
                label: tTaskerProfile('INTERVIEW_TIME'),
                value: taskerProfile?.appointmentInfo?.date
                  ? format(
                      taskerProfile.appointmentInfo.date,
                      'HH:mm - dd/MM/yyyy',
                    )
                  : '',
              },
            ]}
          />
        </TaskerProfileCard>
      ) : null}
      <TaskerProfileCard title={tTaskerProfile('TASKER_INFORMATION')}>
        <CardProfileDescription
          descriptions={[
            {
              label: tTaskerProfile('TASKER_NAME'),
              value: taskerProfile.taskerName,
            },
            {
              label: tTaskerProfile('PHONE_NUMBER'),
              value: formatPhoneNumber(taskerProfile.taskerPhone),
            },
            {
              label: tTaskerProfile('TASKER_GENDER'),
              value: tTaskerProfile(taskerProfile.taskerGender),
            },
            {
              label: tTaskerProfile('SUBMISSION_DATE'),
              value: taskerProfile.createdAt
                ? format(taskerProfile.createdAt, 'HH:mm - dd/MM/yyyy')
                : '',
            },
            {
              label: tTaskerProfile('UPDATED_AT'),
              value: taskerProfile.updatedAt
                ? format(taskerProfile.updatedAt, 'HH:mm - dd/MM/yyyy')
                : '',
            },
            {
              label: tTaskerProfile('SERVICES'),
              tags: taskerProfile.services?.map(
                service =>
                  `${service.text?.[i18n.language || 'en'] ?? ''} ${service.isSubscription ? '(Subscription)' : ''}`,
              ),
            },
            {
              label: tTaskerProfile('CITY'),
              tags: Array.from(
                new Set(
                  taskerProfile.taskerWorkingPlaces?.map(
                    workingPlace => workingPlace.city,
                  ) || [],
                ),
              ),
            },
          ]}
        />
      </TaskerProfileCard>
      <TaskerProfileCard
        title={tTaskerProfile('UPLOADED_DOCUMENTS')}
        extraContent={
          <Button
            className={
              getIsDisableSendingNotification({ profile: taskerProfile })
                ? 'bg-gray-300 text-white'
                : ''
            }
            onClick={async () => {
              await sendingNotificationController.onSubmit({
                language: taskerProfile?.taskerLanguage || 'en',
                taskerId: taskerProfile?.taskerId,
                sendRemindMessage: JSON.stringify(
                  getAllOfTaskerOnboardingMessageLanguage<
                    SerializeFrom<
                      ReturnValueIgnorePromise<
                        typeof getTaskerProfileDetail
                      >['data']
                    >
                  >({ profile: taskerProfile }),
                ),
              });
            }}
            disabled={getIsDisableSendingNotification({
              profile: taskerProfile,
            })}>
            {tTaskerProfile('SEND_NOTIFICATION')}
          </Button>
        }>
        <ProfileImageAccordion
          profileStatus={getOverallStatusInProfile({
            processStatus: taskerProfile.processStatus,
            status: taskerProfile.status,
            isHaveAppointmentInfo: !!taskerProfile.appointmentInfo,
          })}
          reasonsFromSetting={reasonsFromSetting || []}
          setOpenWarningMissingReasonDialog={setOpenWarningMissingReasonDialog}
          disabled={
            !permissions.includes(
              PERMISSIONS.WRITE_TASKER_PROFILE_ON_TASKER_ONBOARDING,
            ) ||
            getOverallStatusInProfile({
              processStatus: taskerProfile.processStatus,
              status: taskerProfile.status,
              isHaveAppointmentInfo: !!taskerProfile.appointmentInfo,
            }) === TASKER_ONBOARDING_PROCESS_STATUS.APPROVED
          }
          isSubmitLoading={navigation.state === 'submitting'}
          onTrigger={onTriggerImageProfile}
          localeAddress="tasker-profile"
          onReviewImageAgain={onReviewImageAgain}
          profileType={IMAGE_PROFILE_TYPE.TASKER}
          items={[
            {
              label: tTaskerProfile('IDENTITY_CARD'),
              info: taskerProfile.identityCard,
              imageField: TASKER_ONBOARDING_IMAGE_KEY.IDENTITY_CARD,
              isAllowUpdateStatusInImage: true,
            },
            {
              label: tTaskerProfile('PASSPORT'),
              info: taskerProfile.passport,
              imageField: TASKER_ONBOARDING_IMAGE_KEY.PASSPORT,
              isAllowUpdateStatusInImage: true,
            },
            {
              label:
                taskerProfile.household?.type ===
                HOUSEHOLD_IMAGE_TYPE_OF_TASKER_ONBOARDING.RESIDENCE_INFORMATION
                  ? tTaskerProfile('RESIDENCE_INFORMATION')
                  : tTaskerProfile('HOUSEHOLD_BOOKLET'),
              info: taskerProfile.household,
              imageField: TASKER_ONBOARDING_IMAGE_KEY.HOUSEHOLD,
              isAllowUpdateStatusInImage: true,
            },
            {
              label: tTaskerProfile('HOUSE_ELECTRIC_BILL'),
              info: taskerProfile.houseElectricBill,
              imageField: TASKER_ONBOARDING_IMAGE_KEY.HOUSE_ELECTRIC_BILL,
              isAllowUpdateStatusInImage: true,
            },
            {
              label: tTaskerProfile('WORK_PERMIT'),
              info: taskerProfile.workPermit,
              imageField: TASKER_ONBOARDING_IMAGE_KEY.WORK_PERMIT,
              isAllowUpdateStatusInImage: true,
            },
            {
              label: tTaskerProfile('CURRICULUM_VITAE'),
              info: taskerProfile.curriculumVitae,
              imageField: TASKER_ONBOARDING_IMAGE_KEY.CURRICULUM_VITAE,
            },
            {
              label: tTaskerProfile('CONFIRMATION_CONDUCT'),
              info: taskerProfile.confirmationConduct,
              imageField: TASKER_ONBOARDING_IMAGE_KEY.CONFIRMATION_CONDUCT,
            },
            {
              label: tTaskerProfile('CRIMINAL_RECORDS'),
              info: taskerProfile.criminalRecords,
              imageField: TASKER_ONBOARDING_IMAGE_KEY.CRIMINAL_RECORD,
            },
            {
              label: tTaskerProfile('MASSAGE_PRACTICE_CERTIFICATE'),
              info: taskerProfile.massagePracticeCertificate,
              imageField:
                TASKER_ONBOARDING_IMAGE_KEY.MASSAGE_PRACTICE_CERTIFICATE,
            },
          ]}
        />
      </TaskerProfileCard>
      <TaskerProfileCard>
        <Tabs defaultValue="note" className="pt-6">
          <TabsList className="grid grid-cols-2 w-[140px]">
            <TabsTrigger value="note">{tTaskerProfile('NOTE')}</TabsTrigger>
            <TabsTrigger value="history">
              {tTaskerProfile('HISTORY')}
            </TabsTrigger>
          </TabsList>
          <TabsContent value="note" className="mt-4">
            <NoteForm
              register={noteFormRegister}
              control={noteFormControl}
              resetField={resetNoteFormValue}
            />
            <DataTableBasic
              data={reversedNotes}
              columns={noteColumns}
              manualPagination
              isDisplayPagination={false}
            />
          </TabsContent>
          <TabsContent value="history" className="mt-4">
            <DataTableBasic
              manualPagination
              isDisplayPagination={false}
              columns={historyColumns}
              data={reversedActionHistories}
            />
          </TabsContent>
        </Tabs>
      </TaskerProfileCard>
      <UpdateScheduleDialog
        openDialog={openDialog}
        setOpenDialog={setOpenDialog}
        offices={officesFromSetting || []}
        profileInfo={taskerProfile}
      />
      <WarningMissingReasonConfigurationDialog
        openDialog={openWarningMissingReasonDialog}
        taskerName={taskerProfile?.taskerName}
        setOpenDialog={setOpenWarningMissingReasonDialog}
      />
    </>
  );
}
