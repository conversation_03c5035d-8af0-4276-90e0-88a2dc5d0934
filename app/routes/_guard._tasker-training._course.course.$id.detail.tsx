import type { SerializeFrom } from '@remix-run/node';
import {
  Link,
  Outlet,
  isRouteErrorResponse,
  json,
  useNavigate,
  useParams,
  useRouteError,
} from '@remix-run/react';
import type { Column, ColumnDef, Row } from '@tanstack/react-table';
import { CONDITION, PERMISSIONS, ROUTE_NAME, TYPE } from 'btaskee-constants';
import { useGlobalStore, useLoaderDataSafely } from 'btaskee-hooks';
import {
  Badge,
  Breadcrumbs,
  BtaskeeResponseError,
  Button,
  Card,
  CardContent,
  CardHeader,
  DataTableBasic,
  DataTableColumnHeader,
  Grid,
  Label,
  Separator,
  StatusBadge,
  Switch,
  Typography,
  toast,
} from 'btaskee-ui';
import {
  convertDaysToUnits,
  formatTimeToCompleteFromSecond,
} from 'btaskee-utils';
import { format } from 'date-fns';
import i18n from 'i18next';
import { SquareArrowOutUpRight } from 'lucide-react';
import { useEffect, useMemo, useState } from 'react';
import { useTranslation } from 'react-i18next';
import { ConditionAndNode } from '~/components/tasker-training/Course';
import { hocLoader } from '~/hoc/remix';
import { useOutletTaskerTrainingCourseDetail } from '~/hooks/useLoaderTrainingCourseDetail';
import { commitSession, getSession } from '~/services/session.server';
import type {
  getCourseDetail,
  getListQuizzesDetailSortByOrder,
} from '~/services/tasker-training.server';

export const loader = hocLoader(async ({ request }) => {
  const session = await getSession(request.headers.get('Cookie'));
  const message = session.get('flashMessage');

  return json(
    { message },
    {
      headers: {
        'Set-Cookie': await commitSession(session),
      },
    },
  );
});

export function ErrorBoundary() {
  const error = useRouteError() as { message?: string };
  const { t: tCommon } = useTranslation('common');

  if (isRouteErrorResponse(error)) {
    if (error?.message) {
      toast({ description: JSON.stringify(error.message) });
    }

    return <BtaskeeResponseError t={tCommon} errorStatus={error.status} />;
  }

  return <BtaskeeResponseError t={tCommon} errorStatus={500} />;
}

export default function CourseDetail() {
  const { t } = useTranslation('course');
  const params = useParams();
  const [openDialog, setOpenDialog] = useState(true);

  const navigate = useNavigate();
  const permissions = useGlobalStore(state => state.permissions);
  const outletData = useOutletTaskerTrainingCourseDetail();

  const columns: ColumnDef<
    SerializeFrom<
      ReturnValueIgnorePromise<typeof getListQuizzesDetailSortByOrder>[0]
    >
  >[] = useMemo(
    () => [
      {
        accessorKey: 'order',
        header: ({ column }) => (
          <DataTableColumnHeader column={column} title={t('NUMBER_ORDER')} />
        ),
        cell: ({ row }) => <span>{row.index + 1}</span>,
        size: 20,
        enableSorting: false,
      },
      {
        accessorKey: 'code',
        header: ({ column }) => (
          <DataTableColumnHeader column={column} title={t('CODE')} />
        ),
        size: 100,
        cell: ({ row }) => (
          <span className="text-gray-800">{row.original.code}</span>
        ),
        enableSorting: false,
      },
      {
        accessorKey: 'title',
        header: ({ column }) => (
          <DataTableColumnHeader
            column={column}
            title={t('QUIZ_COLLECTION_NAME')}
          />
        ),
        cell: ({ row }) => (
          <span className="text-gray-800">{row.original.title}</span>
        ),
        enableSorting: false,
      },
      {
        accessorKey: 'numberOfQuiz',
        header: ({
          column,
        }: {
          column: Column<
            SerializeFrom<
              ReturnValueIgnorePromise<
                typeof getListQuizzesDetailSortByOrder
              >[0]
            >
          >;
        }) => (
          <DataTableColumnHeader column={column} title={t('NUMBER_OF_QUIZ')} />
        ),
        cell: ({
          row,
        }: {
          row: Row<
            SerializeFrom<
              ReturnValueIgnorePromise<
                typeof getListQuizzesDetailSortByOrder
              >[0]
            >
          >;
        }) => (
          <span className="text-gray-800">
            {row.original.quizzes?.length || '-'}
          </span>
        ),
        size: 120,
        enableSorting: false,
      },
      {
        accessorKey: 'action',
        header: ({ column }) => (
          <DataTableColumnHeader
            column={column}
            className="text-center"
            title={t('ACTION')}
          />
        ),
        cell: ({ row }) => {
          return (
            <div className="flex gap-5 justify-center">
              <Button
                type="button"
                variant="ghost"
                className="text-primary hover:text-primary hover:bg-primary-50 gap-2 h-6 py-0.5"
                asChild>
                <Link
                  replace={true}
                  to={`${ROUTE_NAME.TEST_AND_REVIEW}/${params.id}/detail/view-quizzes-dialog/${row.original._id}`}>
                  <SquareArrowOutUpRight className="w-4 h-4" />
                  {t('VIEW')}
                </Link>
              </Button>
            </div>
          );
        },
        size: 150,
        enableSorting: false,
      },
    ],
    [params.id, t],
  );

  function capitalizeFirstLetter(
    text: ReturnType<typeof convertDaysToUnits>['typeOfDays'],
  ) {
    return text.charAt(0).toUpperCase() + text.slice(1);
  }

  function convertDeadlineField(
    deadlineIn: NonNullable<
      SerializeFrom<ReturnValueIgnorePromise<typeof getCourseDetail>>
    >['deadlineIn'],
  ) {
    if (!deadlineIn) return '';

    const objectDeadline = convertDaysToUnits(deadlineIn);

    return `${objectDeadline.numberDays} - ${capitalizeFirstLetter(objectDeadline.typeOfDays)}`;
  }

  const loaderData = useLoaderDataSafely<typeof loader>();

  useEffect(() => {
    if (loaderData?.error) {
      toast({ description: loaderData.error });
    }

    if (loaderData?.message) {
      toast({
        variant: 'success',
        description: loaderData.message,
      });
    }
  }, [loaderData]);

  return (
    <>
      <div className="flex flex-wrap bg-secondary rounded-md p-4 justify-between items-center min-h-24 mb-6">
        <Grid className="gap-3">
          <Typography variant="h2">
            {outletData?.data?.type === TYPE.REVIEW
              ? t('REVIEW_DETAIL')
              : t('TEST_DETAIL')}
          </Typography>
          <Breadcrumbs />
        </Grid>
        <div className="flex gap-4">
          <Button
            disabled={!permissions.includes(PERMISSIONS.WRITE_TEST_AND_REVIEW)}
            className="gap-2 bg-white border-primary text-primary border hover:text-white"
            asChild>
            <Link
              to={`${ROUTE_NAME.TEST_AND_REVIEW}/create?id=${outletData?.data?._id}`}>
              {outletData?.data?.type === TYPE.REVIEW
                ? t('CREATE_TEST')
                : t('CREATE_REVIEW')}
            </Link>
          </Button>
          <Button
            disabled={!permissions.includes(PERMISSIONS.WRITE_TEST_AND_REVIEW)}
            asChild>
            <Link
              to={`${ROUTE_NAME.TEST_AND_REVIEW}/${outletData?.data?._id}${ROUTE_NAME.EDIT}`}>
              {t('UPDATE')}
            </Link>
          </Button>
        </div>
      </div>

      <Card className="bg-gray-50 mb-8">
        <CardHeader>
          <Typography variant="h4">{t('GENERAL_INFORMATION')}</Typography>
          <Separator className="w-[200px]" />
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-3 gap-6">
            <div>
              <Label className="font-normal text-gray-400">{t('CODE')}</Label>
              <Typography
                variant="p"
                affects="removePMargin"
                className="text-gray-600 font-medium text-lg">
                {outletData?.data?.code || ''}
              </Typography>
            </div>
            <div>
              <Label className="font-normal text-gray-400">
                {t('COURSE_NAME')}
              </Label>
              <Typography
                variant="p"
                affects="removePMargin"
                className="text-gray-600 font-medium text-lg">
                {outletData?.data?.title || ''}
              </Typography>
            </div>
            <div>
              <Label className="font-normal text-gray-400">{t('TYPE')}</Label>
              <Typography
                variant="p"
                affects="removePMargin"
                className="text-gray-600 font-medium text-lg">
                {t(outletData?.data?.type || '') || ''}
              </Typography>
            </div>
            <div>
              <Label className="font-normal text-gray-400">
                {t('SERVICE')}
              </Label>
              <Typography
                variant="p"
                affects="removePMargin"
                className="text-gray-600 font-medium text-lg">
                {outletData?.data?.relatedServices?.map(service => {
                  const matchedService = outletData?.services?.find(
                    sv => sv._id === service._id,
                  );

                  if (!matchedService) return null;

                  return (
                    <div
                      key={
                        matchedService._id
                      }>{`${matchedService?.text?.[i18n.language || 'en']} ${matchedService?.isSubscription ? '(Subscription)' : ''}`}</div>
                  );
                })}
              </Typography>
            </div>
            <div>
              <Label className="font-normal text-gray-400">
                {t('DISPLAY_CONDITION')}
              </Label>
              <Typography
                variant="p"
                affects="removePMargin"
                className="text-gray-600 font-medium text-lg">
                {outletData?.data?.condition ? (
                  <ConditionAndNode
                    courseNames={outletData?.courseNames || []}
                    condition={outletData?.data?.condition || {}}
                  />
                ) : (
                  t(CONDITION.NONE)
                )}
              </Typography>
            </div>
            <div>
              <Label className="font-normal text-gray-400">
                {t('CREATED_AT')}
              </Label>
              <Typography
                variant="p"
                affects="removePMargin"
                className="text-gray-600 font-medium text-lg">
                {outletData?.data?.createdAt
                  ? format(outletData?.data?.createdAt, 'HH:mm - dd/MM/yyyy')
                  : ''}
              </Typography>
            </div>
            <div>
              <Label className="font-normal text-gray-400">
                {t('CREATED_BY')}
              </Label>
              <Typography
                variant="p"
                affects="removePMargin"
                className="text-gray-600 font-medium text-lg">
                {outletData?.data?.createdByUsername || ''}
              </Typography>
            </div>
            <div>
              <Label className="font-normal text-gray-400">
                {t('UPDATED_AT')}
              </Label>
              <Typography
                variant="p"
                affects="removePMargin"
                className="text-gray-600 font-medium text-lg">
                {outletData?.data?.updatedAt
                  ? format(outletData?.data?.updatedAt, 'HH:mm - dd/MM/yyyy')
                  : ''}
              </Typography>
            </div>
            <div>
              <Label className="font-normal text-gray-400">
                {t('UPDATED_BY')}
              </Label>
              <Typography
                variant="p"
                affects="removePMargin"
                className="text-gray-600 font-medium text-lg">
                {outletData?.data?.updatedByUsername || ''}
              </Typography>
            </div>
            <div className="grid gap-3">
              <Label className="font-normal text-gray-400">{t('CITY')}</Label>
              <div className="flex flex-wrap gap-2">
                {outletData?.data?.cities?.length ? (
                  outletData?.data?.cities?.map((city, index) => (
                    <Badge
                      className="justify-center rounded-md bg-blue-50 text-sm font-normal text-blue"
                      key={index}>
                      {city}
                    </Badge>
                  ))
                ) : (
                  <Badge className="justify-center rounded-md bg-blue-50 text-sm font-normal text-blue">
                    {t('ALL_CITIES')}
                  </Badge>
                )}
              </div>
            </div>
            <div className="grid gap-3">
              <Label className="font-normal text-gray-400">{t('STATUS')}</Label>
              <Typography
                variant="p"
                affects="removePMargin"
                className="text-gray-600 font-medium text-lg">
                {outletData?.data?.status ? (
                  <StatusBadge
                    translationKey="course"
                    status={outletData?.data?.status}
                  />
                ) : (
                  ''
                )}
              </Typography>
            </div>
          </div>
        </CardContent>
      </Card>

      <Card className="bg-gray-50 mb-8">
        <CardHeader>
          <Typography variant="h4">{t('SPECIFIC')}</Typography>
          <Separator className="w-[200px]" />
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-3 gap-6">
            {outletData?.data?.type === TYPE.TEST ? (
              <>
                <div>
                  <Label className="font-normal text-gray-400">
                    {t('TOTAL_TIME_TO_COMPLETE_THE_TEST')}
                  </Label>
                  <Typography
                    variant="p"
                    affects="removePMargin"
                    className="text-gray-600 font-medium text-lg">
                    {formatTimeToCompleteFromSecond(
                      outletData?.data?.timeToCompleteByMinutes,
                    ) || '00:00'}
                  </Typography>
                </div>
                <div>
                  <Label className="font-normal text-gray-400">
                    {t('MAXIMUM_NUMBER_OF_TIMES')}
                  </Label>
                  <Typography
                    variant="p"
                    affects="removePMargin"
                    className="text-gray-600 font-medium text-lg">
                    {outletData?.data?.maximumNumberOfRetries || 0}
                  </Typography>
                </div>
                <div>
                  <Label className="font-normal text-gray-400">
                    {t('PERCENTAGE_TO_PASS')}
                  </Label>
                  <Typography
                    variant="p"
                    affects="removePMargin"
                    className="text-gray-600 font-medium text-lg">
                    {`${Math.round((outletData?.data?.percentageToPass || 0) * 100)}%`}
                  </Typography>
                </div>
                <div>
                  <Label className="font-normal text-gray-400">
                    {t('TEST_DEADLINE')}
                  </Label>
                  <Typography
                    variant="p"
                    affects="removePMargin"
                    className="text-gray-600 font-medium text-lg">
                    {convertDeadlineField(outletData.data.deadlineIn)}
                  </Typography>
                </div>
                <div>
                  <Label className="font-normal text-gray-400">
                    {t('DISPLAY_POSITION')}
                  </Label>
                  <Typography
                    variant="p"
                    affects="removePMargin"
                    className="text-gray-600 font-medium text-lg">
                    {outletData?.data?.displayPosition
                      ? t(outletData?.data?.displayPosition)
                      : ''}
                  </Typography>
                </div>
              </>
            ) : null}
            {outletData?.data?.type === TYPE.REVIEW ? (
              <>
                <div>
                  <Label className="font-normal text-gray-400">
                    {t('TEST_DEADLINE')}
                  </Label>
                  <Typography
                    variant="p"
                    affects="removePMargin"
                    className="text-gray-600 font-medium text-lg">
                    {outletData?.data?.deadlineIn
                      ? convertDeadlineField(outletData.data.deadlineIn)
                      : ''}
                  </Typography>
                </div>
                <div>
                  <Label className="font-normal text-gray-400">
                    {t('PERCENTAGE_TO_PASS')}
                  </Label>
                  <Typography
                    variant="p"
                    affects="removePMargin"
                    className="text-gray-600 font-medium text-lg">
                    {`${Math.round((outletData?.data?.percentageToPass || 0) * 100)}%`}
                  </Typography>
                </div>
                <div className="mt-2 flex items-center gap-2">
                  <Switch
                    checked={!!outletData?.data?.isDisplayAllAnswer}
                    disabled
                  />
                  <Label>{t('DISPLAY_ALL_QA')}</Label>
                </div>
              </>
            ) : null}
          </div>
        </CardContent>
      </Card>

      <Card className="bg-gray-50">
        <CardHeader>
          <Typography variant="h4">{t('QUIZ_COLLECTION_LIST')}</Typography>
          <Separator className="w-[200px]" />
        </CardHeader>
        <CardContent>
          <DataTableBasic
            manualPagination
            isDisplayPagination={false}
            columns={columns}
            data={outletData?.quizCollection || []}
          />
        </CardContent>
      </Card>

      <Outlet
        context={{
          open: openDialog,
          setOpen: setOpenDialog,
          onClose: () =>
            navigate(`${ROUTE_NAME.TEST_AND_REVIEW}/${params.id}/detail`, {
              replace: true,
            }),
        }}
      />
    </>
  );
}
