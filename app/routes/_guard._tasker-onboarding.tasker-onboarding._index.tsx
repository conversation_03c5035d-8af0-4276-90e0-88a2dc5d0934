import type { SerializeFrom } from '@remix-run/node';
import {
  isRouteErrorResponse,
  useNavigate,
  useRouteError,
  useSearchParams,
} from '@remix-run/react';
import { type ColumnDef } from '@tanstack/react-table';
import {
  TaskerProfileStatus,
  TaskerProfileTabModel,
} from 'app/components/tasker-common';
import { ROUTE_NAME, TASKER_PROFILE_TAB } from 'btaskee-constants';
import {
  BTaskeeTable,
  Badge,
  Breadcrumbs,
  BtaskeeResponseError,
  DataTableColumnHeader,
  Grid,
  Tooltip,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
  Typography,
  toast,
} from 'btaskee-ui';
import {
  createUID,
  getOverallStatusInProfile,
  getPageSizeAndPageIndex,
  momentTz,
} from 'btaskee-utils';
import { format } from 'date-fns';
import i18n from 'i18next';
import { useMemo } from 'react';
import { useTranslation } from 'react-i18next';
import { useOutletGetTaskerOnboardingProfile } from '~/hooks/useGetTaskerOnboardingProfile';
import type { getTaskerProfile } from '~/services/tasker-profile.server';
import { formatPhoneNumber } from '~/utils/common';

export function ErrorBoundary() {
  const error = useRouteError() as { message?: string };
  const { t: tCommon } = useTranslation('common');

  if (isRouteErrorResponse(error)) {
    if (error?.message) {
      toast({ description: JSON.stringify(error.message) });
    }

    return <BtaskeeResponseError t={tCommon} errorStatus={error.status} />;
  }

  return <BtaskeeResponseError t={tCommon} errorStatus={500} />;
}

export default function TaskerOnboardingContent() {
  const { t: tTaskerProfile } = useTranslation('tasker-profile');

  const [searchParams, setSearchParams] = useSearchParams();

  const {
    permissions,
    tabId,
    status,
    filteredValue,
    taskerProfiles,
    totalProfile,
    userCities,
  } = useOutletGetTaskerOnboardingProfile();

  const navigate = useNavigate();

  const taskerProfileColumns: ColumnDef<
    SerializeFrom<ReturnValueIgnorePromise<typeof getTaskerProfile>[0]>
  >[] = useMemo(
    () => [
      {
        accessorKey: 'taskerPhone',
        header: ({ column }) => (
          <DataTableColumnHeader
            column={column}
            title={tTaskerProfile('TASKER_PHONE')}
          />
        ),
        cell: ({ row }) => (
          <Typography variant="p">
            {formatPhoneNumber(row.original?.taskerPhone)}
          </Typography>
        ),
        enableSorting: false,
      },
      {
        accessorKey: 'taskerName',
        header: ({ column }) => (
          <DataTableColumnHeader
            column={column}
            title={tTaskerProfile('TASKER_NAME')}
          />
        ),
        cell: ({ row }) => (
          <Typography variant="p">{row.original?.taskerName}</Typography>
        ),
        enableSorting: false,
      },
      {
        accessorKey: 'services',
        header: ({ column }) => (
          <DataTableColumnHeader
            column={column}
            title={tTaskerProfile('SERVICES')}
          />
        ),
        cell: ({ row }) => (
          <div className="flex flex-wrap gap-1">
            {row.original?.services?.map(service => (
              <TooltipProvider key={createUID()}>
                <Tooltip key={createUID()}>
                  <TooltipTrigger asChild>
                    <div className="w-11 h-11 rounded-[11px] overflow-hidden bg-primary-50">
                      <img
                        className="object-cover w-full h-full"
                        src={service?.icon || ''}
                        alt={service.name}
                      />
                    </div>
                  </TooltipTrigger>
                  <TooltipContent>
                    <Typography variant="p">
                      {`${service?.text?.[i18n.language || 'en']} ${service?.isSubscription ? ' (Subscription)' : ''}`}
                    </Typography>
                  </TooltipContent>
                </Tooltip>
              </TooltipProvider>
            ))}
          </div>
        ),
        enableSorting: false,
      },
      {
        accessorKey: 'taskerWorkingPlaces',
        header: ({ column }) => (
          <DataTableColumnHeader
            column={column}
            title={tTaskerProfile('TASKER_WORKING_PLACES')}
          />
        ),
        cell: ({ row }) => {
          const filteredDuplicateCities = Array.from(
            new Set(
              row.original?.taskerWorkingPlaces?.map(
                workingPlace => workingPlace.city,
              ) || [],
            ),
          );

          return (
            <div className="flex flex-wrap gap-3">
              {filteredDuplicateCities?.map(city => (
                <Badge key={city} className="rounded-md bg-blue-50 text-blue">
                  {city}
                </Badge>
              ))}
            </div>
          );
        },
        enableSorting: false,
      },
      {
        accessorKey: 'taskerGender',
        header: ({ column }) => (
          <DataTableColumnHeader
            column={column}
            title={tTaskerProfile('TASKER_GENDER')}
          />
        ),
        cell: ({ row }) => (
          <Typography variant="p">
            {tTaskerProfile(row.original?.taskerGender)}
          </Typography>
        ),
        enableSorting: false,
      },
      {
        accessorKey: 'updatedAt',
        header: ({ column }) => (
          <DataTableColumnHeader
            column={column}
            title={tTaskerProfile('UPDATED_AT')}
          />
        ),
        cell: ({ row }) =>
          row.original?.updatedAt ? (
            <div className="whitespace-nowrap">
              {format(row.original.updatedAt, 'HH:mm - dd/MM/yyyy')}
            </div>
          ) : null,
      },
      {
        accessorKey: 'status',
        header: ({ column }) => (
          <DataTableColumnHeader
            className="text-center"
            column={column}
            title={tTaskerProfile('STATUS')}
          />
        ),
        cell: ({ row }) => (
          <div className="text-center">
            <TaskerProfileStatus
              status={getOverallStatusInProfile({
                status: row.original?.status,
                processStatus: row.original?.processStatus,
                isHaveAppointmentInfo: !!row.original?.appointmentInfo,
              })}
            />
          </div>
        ),
        enableSorting: false,
      },
    ],
    [tTaskerProfile],
  );

  return (
    <div className="flex flex-col gap-6">
      <Grid className="p-4 bg-secondary">
        <div className="grid rounded-xl">
          <Typography className="mb-3 capitalize" variant="h2">
            {tTaskerProfile('TASKER_PROFILE')}
          </Typography>
          <Breadcrumbs />
        </div>
      </Grid>
      <TaskerProfileTabModel
        items={TASKER_PROFILE_TAB}
        permissions={permissions || []}
        value={tabId || ''}
        onValueChange={value => {
          setSearchParams(params => {
            params.set('tabId', value);

            const tabFound = TASKER_PROFILE_TAB.find(
              tab => tab?.tabId === value,
            );

            if (tabFound) {
              params.set('status', tabFound.status.join(','));
            }

            return params;
          });
        }}
      />
      <BTaskeeTable
        total={totalProfile || 0}
        data={taskerProfiles || []}
        columns={taskerProfileColumns}
        search={{
          name: 'searchText',
          placeholder: tTaskerProfile('SEARCH_NAME_OR_PHONE'),
          defaultValue: filteredValue?.searchText || '',
        }}
        isShowClearButton
        filterDate={{
          name: 'createdAt',
          defaultValue: {
            from: momentTz(filteredValue?.createdAt?.from).toDate(),
            to: momentTz(filteredValue?.createdAt?.to).toDate(),
          },
        }}
        defaultSearchParams={{
          tabId,
        }}
        localeAddress="tasker-profile"
        onClickRow={profile =>
          navigate(`${ROUTE_NAME.TASKER_ONBOARDING}/${profile._id}`)
        }
        pagination={getPageSizeAndPageIndex({
          total: totalProfile || 0,
          pageSize: Number(searchParams.get('pageSize') || 0),
          pageIndex: Number(searchParams.get('pageIndex') || 0),
        })}
        filters={[
          ...(status?.length > 1
            ? [
                {
                  placeholder: tTaskerProfile('STATUS'),
                  name: 'status',
                  className: 'w-[129px] h-[42px]',
                  options: status?.map(status => ({
                    label: tTaskerProfile(status),
                    value: status,
                  })),
                  value: filteredValue?.status?.join(','),
                },
              ]
            : []),
          {
            placeholder: tTaskerProfile('CITIES'),
            name: 'cities',
            options:
              userCities?.map(city => ({
                label: city,
                value: city,
              })) || [],
            value: filteredValue?.cities?.join(','),
          },
        ]}
      />
    </div>
  );
}
