import { Link, Outlet, useLocation, useParams } from '@remix-run/react';
import { ROUTE_NAME } from 'btaskee-constants';
import {
  Breadcrumbs,
  Grid,
  Tabs,
  <PERSON><PERSON>List,
  TabsTrigger,
  Typography,
} from 'btaskee-ui';
import { useEffect, useMemo, useState } from 'react';
import { useTranslation } from 'react-i18next';
import { useGetGroupDetail } from '~/hooks/useGetGroupDetail';

export default function Screen() {
  const { t: tUserSettings } = useTranslation('user-settings');
  const outletData = useGetGroupDetail();
  const params = useParams();

  const navigationTabs = useMemo(
    () => [
      {
        title: tUserSettings('MEMBER_LIST'),
        href: `${ROUTE_NAME.GROUP_SETTING}/${params.id}/member-list`,
      },
      {
        title: tUserSettings('PERMISSION'),
        href: `${ROUTE_NAME.GROUP_SETTING}/${params.id}/list-permissions`,
      },
    ],
    [params.id, tUserSettings],
  );
  const { pathname } = useLocation();

  const [selectedTab, setSelectedTab] = useState(pathname);

  useEffect(() => {
    setSelectedTab(pathname);
  }, [pathname]);

  return (
    <>
      <Grid className="rounded-md bg-secondary p-4">
        <Typography variant="h3" className="line-clamp-1 break-all">
          {outletData.group?.name}
        </Typography>
        <Breadcrumbs />
      </Grid>

      <Tabs
        defaultValue={pathname}
        value={selectedTab}
        onValueChange={setSelectedTab}
        className="relative mr-auto mt-3 w-full">
        <TabsList className="w-full justify-start rounded-none border-b bg-transparent p-0">
          {navigationTabs.map(item => (
            <Link to={item.href} key={item.href}>
              <TabsTrigger
                value={item.href}
                className="relative rounded-none border-b-2 border-b-transparent bg-transparent px-4 pb-3 pt-2 font-semibold text-muted-foreground shadow-none transition-none focus-visible:ring-0 data-[state=active]:border-b-primary data-[state=active]:text-primary data-[state=active]:shadow-none">
                {tUserSettings(item.title)}
              </TabsTrigger>
            </Link>
          ))}
        </TabsList>
      </Tabs>
      <Outlet context={outletData} />
    </>
  );
}
