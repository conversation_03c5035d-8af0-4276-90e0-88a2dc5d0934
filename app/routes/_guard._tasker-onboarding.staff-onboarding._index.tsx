import type { SerializeFrom } from '@remix-run/node';
import {
  isRouteErrorResponse,
  useNavigate,
  useRouteError,
  useSearchParams,
} from '@remix-run/react';
import { type ColumnDef } from '@tanstack/react-table';
import {
  TaskerProfileStatus,
  TaskerProfileTabModel,
} from 'app/components/tasker-common';
import { ROUTE_NAME, STAFF_TAB } from 'btaskee-constants';
import {
  BTaskeeTable,
  Badge,
  Breadcrumbs,
  BtaskeeResponseError,
  DataTableColumnHeader,
  Grid,
  Typography,
  toast,
} from 'btaskee-ui';
import { getPageSizeAndPageIndex, momentTz } from 'btaskee-utils';
import { format } from 'date-fns';
import i18n from 'i18next';
import { useMemo } from 'react';
import { useTranslation } from 'react-i18next';
import { ServiceIconWithTooltip } from '~/components/common/CommonComponent';
import { useOutletGetStaffOnboardingProfile } from '~/hooks/useGetStaffOnboardingProfile';
import type { getTaskerProfile } from '~/services/tasker-profile.server';

export function ErrorBoundary() {
  const error = useRouteError() as { message?: string };
  const { t: tCommon } = useTranslation('common');

  if (isRouteErrorResponse(error)) {
    if (error?.message) {
      toast({ description: JSON.stringify(error.message) });
    }

    return <BtaskeeResponseError t={tCommon} errorStatus={error.status} />;
  }

  return <BtaskeeResponseError t={tCommon} errorStatus={500} />;
}

export default function StaffOnboardingContent() {
  const { t } = useTranslation('tasker-profile');

  const [searchParams, setSearchParams] = useSearchParams();

  const outletData = useOutletGetStaffOnboardingProfile();

  const navigate = useNavigate();

  const columns: ColumnDef<
    SerializeFrom<ReturnValueIgnorePromise<typeof getTaskerProfile>[0]>
  >[] = useMemo(
    () => [
      {
        accessorKey: 'taskerPhone',
        header: ({ column }) => (
          <DataTableColumnHeader column={column} title={t('TASKER_PHONE')} />
        ),
        cell: ({ row }) => (
          <Typography variant="p">{row.original?.taskerPhone}</Typography>
        ),
        enableSorting: false,
      },
      {
        accessorKey: 'taskerName',
        header: ({ column }) => (
          <DataTableColumnHeader column={column} title={t('TASKER_NAME')} />
        ),
        cell: ({ row }) => (
          <Typography variant="p">{row.original?.taskerName}</Typography>
        ),
        enableSorting: false,
      },
      {
        accessorKey: 'services',
        header: ({ column }) => (
          <DataTableColumnHeader column={column} title={t('SERVICES')} />
        ),
        cell: ({ row }) => (
          <div className="flex flex-wrap gap-3">
            {row.original?.services?.map(service => (
              <ServiceIconWithTooltip
                key={service?.text?.[i18n.language || 'en']}
                service={service}
              />
            ))}
          </div>
        ),
        enableSorting: false,
      },
      {
        accessorKey: 'taskerWorkingPlaces',
        header: ({ column }) => (
          <DataTableColumnHeader
            column={column}
            title={t('TASKER_WORKING_PLACES')}
          />
        ),
        cell: ({ row }) => (
          <div className="flex flex-wrap gap-3">
            {row.original?.taskerWorkingPlaces?.map(workingPlace => (
              <Badge
                key={workingPlace.city}
                className="bg-blue-50 text-blue rounded-md">
                {workingPlace.city}
              </Badge>
            ))}
          </div>
        ),
        enableSorting: false,
      },
      {
        accessorKey: 'taskerGender',
        header: ({ column }) => (
          <DataTableColumnHeader column={column} title={t('TASKER_GENDER')} />
        ),
        cell: ({ row }) => (
          <Typography variant="p">{t(row.original?.taskerGender)}</Typography>
        ),
        enableSorting: false,
      },
      {
        accessorKey: 'updatedAt',
        header: ({ column }) => (
          <DataTableColumnHeader column={column} title={t('UPDATED_AT')} />
        ),
        cell: ({ row }) =>
          row.original?.updatedAt ? (
            <div className="whitespace-nowrap">
              {format(row.original.updatedAt, 'HH:mm - dd/MM/yyyy')}
            </div>
          ) : null,
      },
      {
        accessorKey: 'status',
        header: ({ column }) => (
          <DataTableColumnHeader
            className="text-center"
            column={column}
            title={t('STATUS')}
          />
        ),
        cell: ({ row }) => (
          <div className="text-center">
            <TaskerProfileStatus status={row.original?.status} />
          </div>
        ),
        enableSorting: false,
      },
    ],
    [t],
  );

  return (
    <div className="flex flex-col gap-6">
      <Grid className="bg-secondary p-4">
        <div className="grid rounded-xl">
          <Typography className="capitalize mb-3" variant="h2">
            {t('STAFF_PROFILE')}
          </Typography>
          <Breadcrumbs />
        </div>
      </Grid>
      <TaskerProfileTabModel
        items={STAFF_TAB}
        permissions={outletData?.permissions || []}
        value={outletData?.tabId || ''}
        onValueChange={value => {
          setSearchParams(params => {
            params.set('tabId', value);

            const tabFound = STAFF_TAB.find(tab => tab.tabId === value);

            if (tabFound) {
              params.set('status', tabFound.status?.[0]);
            }

            return params;
          });
        }}
      />
      <BTaskeeTable
        total={outletData?.total || 0}
        data={outletData?.data || []}
        columns={columns}
        search={{
          name: 'searchText',
          placeholder: t('SEARCH_NAME_OR_PHONE'),
          defaultValue: outletData.filterValue?.searchText || '',
        }}
        isShowClearButton
        filterDate={{
          name: 'createdAt',
          defaultValue: {
            from: momentTz(outletData.filterValue?.createdAt.from).toDate(),
            to: momentTz(outletData.filterValue?.createdAt.to).toDate(),
          },
        }}
        localeAddress="tasker-profile"
        onClickRow={profile =>
          navigate(`${ROUTE_NAME.STAFF_ONBOARDING}/${profile?._id}`)
        }
        pagination={getPageSizeAndPageIndex({
          total: outletData?.total || 0,
          pageSize: Number(searchParams.get('pageSize') || 0),
          pageIndex: Number(searchParams.get('pageIndex') || 0),
        })}
        filters={
          outletData.status?.length > 1
            ? [
                {
                  placeholder: t('STATUS'),
                  name: 'status',
                  className: 'w-[129px] h-[42px]',
                  options: outletData.status?.map((status: string) => ({
                    label: t(status),
                    value: status,
                  })),
                  value: outletData?.filterValue?.status.join(','),
                },
              ]
            : []
        }
      />
    </div>
  );
}
