import {
  isRouteErrorResponse,
  json,
  redirect,
  useActionData,
  useRouteError,
} from '@remix-run/react';
import {
  ACTION_NAME,
  CONDITION,
  ROUTE_NAME,
  STATUS,
  TYPE,
} from 'btaskee-constants';
import { useGlobalStore, useLoaderDataSafely } from 'btaskee-hooks';
import {
  Breadcrumbs,
  BreadcrumbsLink,
  BtaskeeResponseError,
  Typography,
  toast,
} from 'btaskee-ui';
import {
  convertDaysToUnits,
  formatTimeToCompleteFromSecond,
} from 'btaskee-utils';
import { useEffect } from 'react';
import { useForm } from 'react-hook-form';
import { useTranslation } from 'react-i18next';
import CourseForm from '~/components/form/CourseForm';
import { hocAction, hocLoader } from '~/hoc/remix';
import { useOutletTaskerTrainingCourseDetail } from '~/hooks/useLoaderTrainingCourseDetail';
import i18next from '~/i18next.server';
import { getCities, getUserSession } from '~/services/helpers.server';
import { commitSession, getSession } from '~/services/session.server';
import {
  getAllCourses,
  getCourseDetail,
  updateCourse,
} from '~/services/tasker-training.server';

export const handle = {
  breadcrumb: ({ courseType }: { courseType: string }) => {
    return (
      <BreadcrumbsLink
        to=""
        label={courseType === TYPE.REVIEW ? 'UPDATE_REVIEW' : 'UPDATE_TEST'}
      />
    );
  },
  i18n: 'course',
};

export const loader = hocLoader(async ({ request, params }) => {
  const { isoCode } = await getUserSession({ headers: request.headers });
  const course = await getCourseDetail({ isoCode, _id: params?.id || '' });

  const allCourseExcludeCourseId = await getAllCourses({
    isoCode,
    projection: { title: 1, code: 1 },
    courseId: params?.id || '',
  });

  const cities = await getCities(isoCode);

  return json({
    allCourseExcludeCourseId,
    courseId: course?._id || '',
    courseType: course?.type,
    cities,
  });
});

export const action = hocAction(
  async ({ request, params }, { setInformationActionHistory }) => {
    const { isoCode, username, userId } = await getUserSession({
      headers: request.headers,
    });

    const formData = await request.clone().formData();

    const parsedData = JSON.parse(formData.get('data')?.toString() || '{}');

    await updateCourse({
      isoCode,
      _id: params?.id || '',
      params: {
        ...parsedData,
        updatedByUserId: userId,
        updatedByUsername: username,
      },
    });

    const t = await i18next.getFixedT(request, 'course');

    setInformationActionHistory({
      action: ACTION_NAME.UPDATE_COURSE,
      dataRelated: { courseId: params?.id },
    });

    const session = await getSession(request.headers.get('cookie'));
    session.flash('flashMessage', t('UPDATE_COURSE_SUCCESSFULLY'));
    const newSession = await commitSession(session);

    return redirect(`${ROUTE_NAME.TEST_AND_REVIEW}/${params?.id}/detail`, {
      headers: {
        'Set-Cookie': newSession,
      },
    });
  },
);

export function ErrorBoundary() {
  const error = useRouteError() as { message?: string };
  const { t: tCommon } = useTranslation('common');

  if (isRouteErrorResponse(error)) {
    if (error?.message) {
      toast({ description: JSON.stringify(error.message) });
    }

    return <BtaskeeResponseError t={tCommon} errorStatus={error.status} />;
  }

  return <BtaskeeResponseError t={tCommon} errorStatus={500} />;
}

export default function EditCourse() {
  const { t } = useTranslation('course');

  const actionData = useActionData<ActionTypeWithError<typeof action>>();
  const loaderData = useLoaderDataSafely<typeof loader>();
  const featureFlags = useGlobalStore(state => state.featureFlags);

  useEffect(() => {
    if (actionData?.error) {
      toast({ description: actionData.error });
    }
  }, [actionData]);

  const { quizCollection, data, services } =
    useOutletTaskerTrainingCourseDetail();

  const form = useForm<FormCreateCourse>({
    // eslint-disable-next-line @typescript-eslint/ban-ts-comment
    // @ts-ignore
    defaultValues: {
      code: data?.code || '',
      title: data?.title || '',
      status: data?.status || STATUS.ACTIVE,
      relatedServices: data?.relatedServices || undefined,
      type: data?.type || TYPE.TEST,
      condition: {
        ...(data?.condition
          ? {
              optionCondition: (() => {
                if (data.condition.coursesMustBeCompleted) {
                  return CONDITION.WHEN_COMPLETE;
                }
                if (data.condition.byTasker) {
                  return CONDITION.TASKER_STAR;
                }
                if (data.condition.manuallyUnblock) {
                  return CONDITION.MANUALLY_UNBLOCK;
                }
                return CONDITION.NONE;
              })(),
              ...(data?.condition?.coursesMustBeCompleted && {
                coursesMustBeCompleted: {
                  courseIds:
                    data.condition.coursesMustBeCompleted?.courseIds[0] || '',
                },
              }),
              ...(data.condition.byTasker && {
                byTasker: data.condition.byTasker,
              }),
              ...(data.condition.manuallyUnblock && {
                manuallyUnblock: data.condition.manuallyUnblock,
              }),
            }
          : { optionCondition: CONDITION.NONE }),
      },
      selectedQuizzes: [],
      ...(data?.type === TYPE.TEST
        ? {
            timeToCompleteByMinutes:
              formatTimeToCompleteFromSecond(data?.timeToCompleteByMinutes) ||
              '00:00',
            maximumNumberOfRetries: data?.maximumNumberOfRetries || 0,
            // percentageToPass is a number from 50 to 100
            percentageToPass: data?.percentageToPass * 100 || 50,
            ...(data?.deadlineIn
              ? convertDaysToUnits(data.deadlineIn)
              : { numberDays: '', typeOfDays: '' }),
            displayPosition: data?.displayPosition,
          }
        : {
            ...(featureFlags.Course_deadlineIn_Review && {
              ...(data?.deadlineIn
                ? convertDaysToUnits(data.deadlineIn)
                : { numberDays: '', typeOfDays: '' }),
            }),
            isDisplayAllAnswer: data?.isDisplayAllAnswer,
            // percentageToPass is a number from 50 to 100
            percentageToPass: data?.percentageToPass * 100 || 50,
          }),
      ...{
        cities: data.cities,
      },
    },
  });

  return (
    <>
      <div className="grid space-y-2 rounded-xl bg-secondary p-4">
        <Typography variant="h2">
          {data?.type === TYPE.TEST ? t('UPDATE_TEST') : t('UPDATE_REVIEW')}
        </Typography>
        <Breadcrumbs />
      </div>

      <CourseForm
        form={form}
        services={services}
        allCourseExcludeCourseId={loaderData?.allCourseExcludeCourseId}
        quizCollection={quizCollection}
        isEdit={true}
        cities={loaderData?.cities || []}
      />
    </>
  );
}
