import { DotsHorizontalIcon } from '@radix-ui/react-icons';
import type { SerializeFrom } from '@remix-run/node';
import {
  isRouteErrorResponse,
  useNavigate,
  useRouteError,
  useSearchParams,
} from '@remix-run/react';
import { type ColumnDef } from '@tanstack/react-table';
import { ROUTE_NAME } from 'btaskee-constants';
import {
  BTaskeeTable,
  Breadcrumbs,
  BtaskeeResponseError,
  Button,
  DataTableColumnHeader,
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
  StatusBadge,
  Typography,
  toast,
} from 'btaskee-ui';
import { getPageSizeAndPageIndex, momentTz } from 'btaskee-utils';
import { format } from 'date-fns';
import { useMemo } from 'react';
import { useTranslation } from 'react-i18next';
import { useOutletOverallTrainingHistoryProfile } from '~/hooks/useLoaderOverallTrainingHistory';
import type { getTrainingHistoryAllTasker } from '~/services/training-history-all-tasker.server';

export function ErrorBoundary() {
  const error = useRouteError() as { message?: string };
  const { t: tCommon } = useTranslation('common');

  if (isRouteErrorResponse(error)) {
    if (error?.message) {
      toast({ description: JSON.stringify(error.message) });
    }

    return <BtaskeeResponseError t={tCommon} errorStatus={error.status} />;
  }

  return <BtaskeeResponseError t={tCommon} errorStatus={500} />;
}

export default function TrainingHistoryContent() {
  const { t } = useTranslation('training-history-all-tasker');
  const [searchParams] = useSearchParams();
  const navigate = useNavigate();
  const { filteredValue, trainingHistories, totalTrainingHistory } =
    useOutletOverallTrainingHistoryProfile();

  const columns: ColumnDef<
    SerializeFrom<
      ReturnValueIgnorePromise<typeof getTrainingHistoryAllTasker>[0]
    >
  >[] = useMemo(
    () => [
      {
        accessorKey: 'taskerTrainingCourse.code',
        header: ({ column }) => (
          <DataTableColumnHeader column={column} title={t('TEST_CODE')} />
        ),
        cell: ({ row }) => (
          <span className="text-gray-800">
            {row.original?.taskerTrainingCourse?.code}
          </span>
        ),
      },
      {
        accessorKey: 'taskerTrainingCourse.title',
        header: ({ column }) => (
          <DataTableColumnHeader column={column} title={t('COURSE_NAME')} />
        ),
        cell: ({ row }) => (
          <span className="text-gray-800">
            {row.original?.taskerTrainingCourse?.title}
          </span>
        ),
        enableSorting: false,
      },
      {
        accessorKey: 'tasker.name',
        header: ({ column }) => (
          <DataTableColumnHeader column={column} title={t('TASKER_NAME')} />
        ),
        cell: ({ row }) => (
          <span className="text-gray-800">{row.original?.tasker?.name}</span>
        ),
        enableSorting: false,
      },
      {
        accessorKey: 'tasker.phone',
        header: ({ column }) => (
          <DataTableColumnHeader column={column} title={t('PHONE_NUMBER')} />
        ),
        cell: ({ row }) => (
          <span className="text-gray-800">{row.original?.tasker?.phone}</span>
        ),
        enableSorting: false,
      },
      {
        accessorKey: 'numOfTimeOpened',
        header: ({ column }) => (
          <DataTableColumnHeader
            column={column}
            title={t('NUM_OF_TIME_OPENED')}
          />
        ),
        cell: ({ row }) => (
          <span className="text-gray-800">{row.original?.numOfTimeOpened}</span>
        ),
        enableSorting: false,
      },
      {
        accessorKey: 'numberOfSubmissions',
        header: ({ column }) => (
          <DataTableColumnHeader column={column} title={t('ATTEMPT_COUNT')} />
        ),
        cell: ({ row }) => (
          <span className="text-gray-800">
            {row.original?.numberOfSubmissions}
          </span>
        ),
        enableSorting: false,
      },
      {
        accessorKey: 'createdAt',
        header: ({ column }) => (
          <DataTableColumnHeader column={column} title={t('CREATED_AT')} />
        ),
        cell: ({ row }) => (
          <Typography variant="p">
            {row?.original?.createdAt
              ? format(row?.original?.createdAt, 'HH:mm - dd/MM/yyyy')
              : ''}
          </Typography>
        ),
      },
      {
        accessorKey: 'status',
        header: ({ column }) => (
          <DataTableColumnHeader
            column={column}
            title={t('STATUS')}
            className="w-auto"
          />
        ),
        cell: ({ row }) => (
          <StatusBadge
            statusClasses={{
              BLOCKED: 'bg-gray-50 text-gray-500 rounded-md text-center',
              PASSED: 'bg-green-50 text-green-500 rounded-md text-center',
              FAILED: 'bg-red-50 text-red-500 rounded-md text-center',
            }}
            status={row.original?.status}
          />
        ),
        enableSorting: false,
      },
      {
        accessorKey: 'action',
        header: ({ column }) => (
          <DataTableColumnHeader
            column={column}
            className="whitespace-nowrap text-right"
            title={t('ACTION')}
          />
        ),
        size: 44,
        cell: ({ row }) => (
          <DropdownMenu>
            <DropdownMenuTrigger asChild>
              <Button
                variant="ghost"
                className="flex h-8 w-8 p-0 data-[state=open]:bg-muted">
                <DotsHorizontalIcon className="h-4 w-4" />
              </Button>
            </DropdownMenuTrigger>
            <DropdownMenuContent>
              <DropdownMenuItem
                onClick={() =>
                  navigate(
                    `${ROUTE_NAME.TRAINING_HISTORY}/${row.original?.taskerTrainingCourse?._id}${ROUTE_NAME.TASKER}/${row.original?.tasker?._id}`,
                  )
                }>
                {t('VIEW_DETAIL')}
              </DropdownMenuItem>
            </DropdownMenuContent>
          </DropdownMenu>
        ),
        enableSorting: false,
      },
    ],
    [navigate, t],
  );

  return (
    <>
      <div className="grid bg-secondary p-4 justify-between items-center min-h-24 rounded-md mb-6">
        <Typography variant="h2">{t('TRAINING_HISTORY')}</Typography>
        <Breadcrumbs />
      </div>
      <BTaskeeTable
        columns={columns}
        data={trainingHistories || []}
        total={totalTrainingHistory || 0}
        pagination={getPageSizeAndPageIndex({
          total: totalTrainingHistory || 0,
          pageSize: Number(searchParams.get('pageSize') || 0),
          pageIndex: Number(searchParams.get('pageIndex') || 0),
        })}
        search={{
          defaultValue: searchParams.get('search') || '',
          placeholder: t('SEARCH_BY_PHONE_OR_NAME'),
          name: 'search',
        }}
        filterDate={{
          name: 'createdAt',
          defaultValue: {
            from: momentTz(filteredValue?.rangeDate?.from).toDate(),
            to: momentTz(filteredValue?.rangeDate?.to).toDate(),
          },
        }}
        localeAddress="training-history-all-tasker"
        filters={[
          {
            placeholder: t('STATUS'),
            name: 'status',
            options: [
              { label: t('PASSED'), value: 'PASSED' },
              { label: t('FAILED'), value: 'FAILED' },
              { label: t('BLOCKED'), value: 'BLOCKED' },
            ],
            value: filteredValue?.status,
          },
        ]}
        onClickRow={trainingHistory =>
          navigate(
            `${ROUTE_NAME.TRAINING_HISTORY}/${trainingHistory?.taskerTrainingCourse?._id}${ROUTE_NAME.TASKER}/${trainingHistory?.tasker?._id}`,
          )
        }
        isShowClearButton
      />
    </>
  );
}
