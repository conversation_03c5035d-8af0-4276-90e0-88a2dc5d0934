import { json, redirect } from '@remix-run/node';
import type { SerializeFrom } from '@remix-run/node';
import {
  isRouteErrorResponse,
  useActionData,
  useNavigation,
  useRouteError,
  useSubmit,
} from '@remix-run/react';
import type { ConfirmUpdatingProfileFormProps } from 'app/components/tasker-common';
import {
  CardProfileDescription,
  ConfirmUpdatingProfile,
  TaskerProfileStatus,
  WarningMissingReasonConfigurationDialog,
} from 'app/components/tasker-common';
import {
  PERMISSIONS,
  ROUTE_NAME,
  TASKER_ONBOARDING_IMAGE_KEY,
  TASKER_ONBOARDING_PROCESS_STATUS,
} from 'btaskee-constants';
import { useLoaderDataSafely } from 'btaskee-hooks';
import {
  Breadcrumbs,
  BreadcrumbsLink,
  BtaskeeResponseError,
  Button,
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON>ontent,
  Ta<PERSON>List,
  TabsTrigger,
  Typography,
  toast,
  useConfirm,
} from 'btaskee-ui';
import { format } from 'date-fns';
import { useCallback, useEffect, useMemo, useState } from 'react';
import { useForm } from 'react-hook-form';
import { useTranslation } from 'react-i18next';
import { hoc404, hocAction, hocLoader } from '~/hoc/remix';
import i18next from '~/i18next.server';
import { getCitiesByUserId, getUserSession } from '~/services/helpers.server';
import { verifyManager } from '~/services/role-base-access-control.server';
import {
  getAllOfOffices,
  getAllReasonFromSetting,
  getTaskerProfileDetail,
  storeNoteIntoProfile,
  updateNoteByIndex,
  updateStatusOnTaskerProfile,
} from '~/services/tasker-profile.server';
import { formatPhoneNumber } from '~/utils/common';

// Type for extended identity card with additional fields
type ExtendedIdentityCard = {
  status?: string;
  uploadTimestamp?: string;
  detectedPhone?: string;
  reason?: string;
  images?: string[];
};

export const handle = {
  breadcrumb: ({
    data,
  }: {
    data: SerializeFrom<
      ReturnValueIgnorePromise<typeof getTaskerProfileDetail>
    >['data'];
  }) => {
    return (
      <BreadcrumbsLink
        to={`${ROUTE_NAME.IDENTITY_INFORMATION_FOR_TASKER}/${data?._id}`}
        label="IDENTITY_INFORMATION_DETAIL"
      />
    );
  },
  i18n: 'identity-information',
};

export function ErrorBoundary() {
  const error = useRouteError() as { message?: string };
  const { t: tCommon } = useTranslation('common');

  if (isRouteErrorResponse(error)) {
    if (error?.message) {
      toast({ description: JSON.stringify(error.message) });
    }

    return <BtaskeeResponseError t={tCommon} errorStatus={error.status} />;
  }

  return <BtaskeeResponseError t={tCommon} errorStatus={500} />;
}

export const loader = hocLoader(
  async ({ request, params }) => {
    const { isoCode, username, isSuperUser, userId } = await getUserSession({
      headers: request.headers,
    });
    const isManager = await verifyManager(userId);
    const cities = await getCitiesByUserId({
      userId,
      isManager: isManager || isSuperUser,
    });

    const [taskerProfileDetail, reasonsFromSetting, offices] =
      await Promise.all([
        hoc404(() =>
          getTaskerProfileDetail({
            profileId: params.id || '',
            cities,
            isoCode,
          }),
        ),
        getAllReasonFromSetting({
          isoCode,
        }),
        getAllOfOffices({ isoCode }),
      ]);

    return json({
      ...taskerProfileDetail,
      reasonsFromSetting,
      offices,
      username,
    });
  },
  [
    PERMISSIONS.READ_VERIFY_IDENTITY_INFORMATION_FOR_TASKER,
    PERMISSIONS.READ_APPROVED_IDENTITY_INFORMATION_FOR_TASKER,
  ],
);

export const action = hocAction(
  async ({ request, params }) => {
    const { isoCode, userId, username } = await getUserSession({
      headers: request.headers,
    });
    const language = await i18next.getLocale(request);
    const tTaskerProfile = await i18next.getFixedT(language, 'tasker-profile');

    const formData = await request.formData();
    const status = formData.get('status')?.toString();
    const reason = formData.get('reason')?.toString();
    const fieldName = formData.get('fieldName')?.toString();
    const message = formData.get('message')?.toString();
    const note = formData.get('note')?.toString();
    const updatedNote = formData.get('updatedNote')?.toString();
    const noteIndex = formData.get('noteIndex')?.toString();

    if (note) {
      const noteResponseMessage = await storeNoteIntoProfile({
        profileId: params.id || '',
        note,
        userId,
        username,
        isoCode,
      });

      return json({ msg: tTaskerProfile(noteResponseMessage) });
    }

    if (updatedNote && noteIndex) {
      const noteResponseMessage = await updateNoteByIndex({
        profileId: params.id || '',
        note: updatedNote,
        userId,
        username,
        isoCode,
        noteIndex: Number(noteIndex || 0),
      });

      return json({ msg: tTaskerProfile(noteResponseMessage) });
    }

    const statusFilterValue = Object.values(
      TASKER_ONBOARDING_PROCESS_STATUS,
    )?.find(profileStatus => profileStatus === status);

    if (!statusFilterValue) {
      return redirect(ROUTE_NAME.IDENTITY_INFORMATION_FOR_TASKER);
    }

    const result = await updateStatusOnTaskerProfile({
      profileId: params.id || '',
      status: statusFilterValue,
      reason,
      userId,
      fieldName: fieldName as `${TASKER_ONBOARDING_IMAGE_KEY}`,
      username,
      isoCode,
      language: language as BtaskeeLanguage,
      ...(message ? { message: JSON.parse(message) } : {}),
    });

    return json({ msg: tTaskerProfile(result.msg) });
  },
  [
    PERMISSIONS.WRITE_TASKER_PROFILE_ON_TASKER_ONBOARDING,
    PERMISSIONS.READ_VERIFY_IDENTITY_INFORMATION_FOR_TASKER,
    PERMISSIONS.READ_APPROVED_IDENTITY_INFORMATION_FOR_TASKER,
  ],
);

export default function IdentityInformationDetail() {
  const { t: tTaskerProfile } = useTranslation('tasker-profile');
  const { t: tIdentityInfo } = useTranslation('identity-information');

  const submit = useSubmit();
  const confirm = useConfirm();
  const [openWarningMissingReasonDialog, setOpenWarningMissingReasonDialog] =
    useState<boolean>(false);

  // Image viewing states
  const [selectedImage, setSelectedImage] = useState<string | null>(null);
  const [imageLoading, setImageLoading] = useState<{ [key: string]: boolean }>(
    {},
  );
  const [imageErrors, setImageErrors] = useState<{ [key: string]: boolean }>(
    {},
  );

  const {
    data: taskerProfile,
    reasonsFromSetting,
    error: loaderError,
  } = useLoaderDataSafely<typeof loader>();

  const reversedNotes = useMemo(() => {
    const notesWithIndex =
      taskerProfile?.notes?.map((note, index) => ({
        index,
        ...note,
      })) || [];
    return notesWithIndex.reverse();
  }, [taskerProfile?.notes]);

  const reversedActionHistories = useMemo(
    () => taskerProfile?.actionHistories?.reverse() || [],
    [taskerProfile?.actionHistories],
  );

  const navigation = useNavigation();
  const actionData = useActionData<ActionTypeWithError<typeof action>>();

  const {
    control: controlUpdatingProfileStatus,
    getValues: getValuesUpdatingProfileStatus,
    setValue: setValueUpdatingProfileStatus,
  } = useForm<ConfirmUpdatingProfileFormProps>({
    defaultValues: {
      reason: '',
      status: TASKER_ONBOARDING_PROCESS_STATUS.VERIFYING,
    },
  });

  const {
    register: noteFormRegister,
    resetField: resetNoteFormValue,
    getValues: getNoteFormValues,
  } = useForm<{
    note: string;
  }>({
    defaultValues: {
      note: '',
    },
  });

  useEffect(() => {
    if (loaderError) {
      toast({ description: loaderError });
    }
  }, [loaderError]);

  useEffect(() => {
    if (actionData?.msg)
      toast({ description: actionData.msg, variant: 'success' });

    if (actionData?.error) toast({ description: actionData.error });
  }, [actionData]);

  // Image handling functions
  const handleImageLoad = useCallback((imageUrl: string) => {
    setImageLoading(prev => ({ ...prev, [imageUrl]: false }));
    setImageErrors(prev => ({ ...prev, [imageUrl]: false }));
  }, []);

  const handleImageError = useCallback((imageUrl: string) => {
    setImageLoading(prev => ({ ...prev, [imageUrl]: false }));
    setImageErrors(prev => ({ ...prev, [imageUrl]: true }));
  }, []);

  const handleImageClick = useCallback((imageUrl: string) => {
    setSelectedImage(imageUrl);
  }, []);

  const closeImageModal = useCallback(() => {
    setSelectedImage(null);
  }, []);

  // Enhanced Image Component
  const EnhancedImageCard = useCallback(
    ({ imageUrl, index }: { imageUrl: string; index: number }) => {
      const isLoading = imageLoading[imageUrl];
      const hasError = imageErrors[imageUrl];

      return (
        <div className="relative overflow-hidden transition-all duration-300 bg-white border-2 border-gray-200 shadow-sm group rounded-xl hover:shadow-lg hover:border-blue-300">
          {/* Image Container */}
          <div className="relative aspect-[4/3] bg-gray-50">
            {isLoading && (
              <div className="absolute inset-0 flex items-center justify-center bg-gray-100">
                <div className="w-8 h-8 border-b-2 border-blue-600 rounded-full animate-spin"></div>
              </div>
            )}

            {hasError ? (
              <div className="absolute inset-0 flex flex-col items-center justify-center text-gray-500 bg-gray-100">
                <svg
                  className="w-12 h-12 mb-2"
                  fill="none"
                  stroke="currentColor"
                  viewBox="0 0 24 24">
                  <path
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    strokeWidth={2}
                    d="M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z"
                  />
                </svg>
                <Typography variant="p" className="text-sm">
                  {tTaskerProfile('IMAGE_LOAD_ERROR')}
                </Typography>
              </div>
            ) : (
              <button
                type="button"
                className="w-full h-full p-0 bg-transparent border-0 cursor-pointer"
                onClick={() => handleImageClick(imageUrl)}
                onKeyDown={e => {
                  if (e.key === 'Enter' || e.key === ' ') {
                    e.preventDefault();
                    handleImageClick(imageUrl);
                  }
                }}
                aria-label={`View full size identity card ${index + 1}`}>
                <img
                  src={imageUrl}
                  alt={`Identity Card ${index + 1}`}
                  className="object-cover w-full h-full transition-transform duration-300 group-hover:scale-105"
                  onLoad={() => handleImageLoad(imageUrl)}
                  onError={() => handleImageError(imageUrl)}
                  loading="lazy"
                />
              </button>
            )}

            {/* Overlay with zoom icon */}
            {!hasError && !isLoading && (
              <div className="absolute inset-0 flex items-center justify-center transition-all duration-300 bg-black bg-opacity-0 group-hover:bg-opacity-20">
                <div className="p-2 transition-opacity duration-300 bg-white rounded-full shadow-lg opacity-0 group-hover:opacity-100">
                  <svg
                    className="w-6 h-6 text-gray-700"
                    fill="none"
                    stroke="currentColor"
                    viewBox="0 0 24 24">
                    <path
                      strokeLinecap="round"
                      strokeLinejoin="round"
                      strokeWidth={2}
                      d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0zM10 7v3m0 0v3m0-3h3m-3 0H7"
                    />
                  </svg>
                </div>
              </div>
            )}
          </div>

          {/* Image Info */}
          <div className="p-4">
            <div className="flex items-center justify-between">
              <Typography variant="h4" className="font-medium text-gray-900">
                {tTaskerProfile('IDENTITY_CARD')} {index + 1}
              </Typography>
              <Button
                variant="outline"
                size="sm"
                onClick={() => handleImageClick(imageUrl)}
                disabled={hasError}
                className="text-blue-600 border-blue-200 hover:bg-blue-50">
                <svg
                  className="w-4 h-4 mr-1"
                  fill="none"
                  stroke="currentColor"
                  viewBox="0 0 24 24">
                  <path
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    strokeWidth={2}
                    d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"
                  />
                  <path
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    strokeWidth={2}
                    d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z"
                  />
                </svg>
                {tTaskerProfile('VIEW_FULL_SIZE')}
              </Button>
            </div>
          </div>
        </div>
      );
    },
    [
      imageLoading,
      imageErrors,
      handleImageLoad,
      handleImageError,
      handleImageClick,
      tTaskerProfile,
    ],
  );

  // Handler functions for approve/reject actions
  const handleApprove = useCallback(async () => {
    setValueUpdatingProfileStatus(
      'status',
      TASKER_ONBOARDING_PROCESS_STATUS.APPROVED,
    );

    const isConfirm = await confirm({
      title: tTaskerProfile('CONFIRM_APPROVE_IDENTITY'),
      body: (
        <ConfirmUpdatingProfile
          control={controlUpdatingProfileStatus}
          description={tTaskerProfile('CONFIRM_APPROVE_IDENTITY_DESCRIPTION')}
          setValue={setValueUpdatingProfileStatus}
          taskerInfo={{
            taskerName: taskerProfile?.taskerName || '',
            username:
              taskerProfile?.username || taskerProfile?.taskerName || '',
          }}
        />
      ),
      cancelButton: tTaskerProfile('CANCEL'),
      actionButton: tTaskerProfile('CONFIRM'),
    });

    if (isConfirm) {
      const formData = new FormData();
      const values = getValuesUpdatingProfileStatus();
      formData.append('status', values.status);
      formData.append('fieldName', TASKER_ONBOARDING_IMAGE_KEY.IDENTITY_CARD);
      if (values.reason) formData.append('reason', values.reason);

      submit(formData, { method: 'post' });
    }
  }, [
    confirm,
    controlUpdatingProfileStatus,
    setValueUpdatingProfileStatus,
    tTaskerProfile,
    taskerProfile,
    getValuesUpdatingProfileStatus,
    submit,
  ]);

  const handleReject = useCallback(async () => {
    const reasonsForRejection = reasonsFromSetting?.filter(
      reason => reason.type === 'REJECTED',
    );

    if (!reasonsForRejection?.length) {
      setOpenWarningMissingReasonDialog(true);
      return;
    }

    setValueUpdatingProfileStatus(
      'status',
      TASKER_ONBOARDING_PROCESS_STATUS.NEEDS_UPDATE,
    );
    setValueUpdatingProfileStatus('reason', reasonsForRejection[0]?.name);

    const isConfirm = await confirm({
      title: tTaskerProfile('CONFIRM_REJECT_IDENTITY'),
      body: (
        <ConfirmUpdatingProfile
          control={controlUpdatingProfileStatus}
          description={tTaskerProfile('CONFIRM_REJECT_IDENTITY_DESCRIPTION')}
          setValue={setValueUpdatingProfileStatus}
          taskerInfo={{
            taskerName: taskerProfile?.taskerName || '',
            username:
              taskerProfile?.username || taskerProfile?.taskerName || '',
          }}
          reasons={reasonsForRejection.map(reason => reason.name)}
        />
      ),
      cancelButton: tTaskerProfile('CANCEL'),
      actionButton: tTaskerProfile('CONFIRM'),
    });

    if (isConfirm) {
      const formData = new FormData();
      const values = getValuesUpdatingProfileStatus();
      formData.append('status', values.status);
      formData.append('fieldName', TASKER_ONBOARDING_IMAGE_KEY.IDENTITY_CARD);
      if (values.reason) formData.append('reason', values.reason);

      submit(formData, { method: 'post' });
    }
  }, [
    confirm,
    controlUpdatingProfileStatus,
    setValueUpdatingProfileStatus,
    tTaskerProfile,
    taskerProfile,
    getValuesUpdatingProfileStatus,
    submit,
    reasonsFromSetting,
  ]);

  return (
    <>
      <div className="flex justify-between p-4 align-middle bg-secondary">
        <div className="grid space-y-2 rounded-xl">
          <div className="flex items-center">
            <Typography className="mr-6 capitalize" variant="h2">
              {tIdentityInfo('IDENTITY_INFORMATION_FOR_TASKER')} -{' '}
              {tTaskerProfile('DETAIL')}
            </Typography>
            <TaskerProfileStatus
              status={
                (taskerProfile?.identityCard
                  ?.status as keyof typeof TASKER_ONBOARDING_PROCESS_STATUS) ||
                TASKER_ONBOARDING_PROCESS_STATUS.VERIFYING
              }
            />
          </div>
          <Breadcrumbs />
        </div>

        {/* Action Buttons */}
        <div className="flex items-center gap-2">
          <Button
            variant="outline"
            onClick={handleReject}
            disabled={navigation.state === 'submitting'}
            className="text-red-500 border-red-500 hover:bg-red-50">
            {tTaskerProfile('REJECT')}
          </Button>
          <Button
            onClick={handleApprove}
            disabled={navigation.state === 'submitting'}
            className="bg-green-600 hover:bg-green-700">
            {tTaskerProfile('APPROVE')}
          </Button>
        </div>
      </div>

      <div className="p-6 space-y-6">
        {/* Tasker Profile Card */}
        <CardProfileDescription
          descriptions={[
            {
              label: tTaskerProfile('TASKER_NAME'),
              value: taskerProfile?.taskerName || '-',
            },
            {
              label: tTaskerProfile('TASKER_PHONE'),
              value: formatPhoneNumber(taskerProfile?.taskerPhone) || '-',
            },
            {
              label: tTaskerProfile('TASKER_GENDER'),
              value: taskerProfile?.taskerGender
                ? tTaskerProfile(taskerProfile.taskerGender)
                : '-',
            },
            {
              label: 'ID',
              value: taskerProfile?._id || '-',
            },
          ]}
        />

        {/* Tabs for organized content */}
        <Tabs defaultValue="identity-info" className="w-full">
          <TabsList className="grid w-full grid-cols-4">
            <TabsTrigger value="identity-info">
              {tIdentityInfo('IDENTITY_INFORMATION_FOR_TASKER')}
            </TabsTrigger>
            <TabsTrigger value="images">{tTaskerProfile('IMAGES')}</TabsTrigger>
            <TabsTrigger value="notes">{tTaskerProfile('NOTES')}</TabsTrigger>
            <TabsTrigger value="history">
              {tTaskerProfile('ACTION_HISTORY')}
            </TabsTrigger>
          </TabsList>

          {/* Identity Information Tab */}
          <TabsContent value="identity-info" className="space-y-6">
            <div className="p-6 bg-white border rounded-lg">
              <Typography variant="h3" className="mb-4">
                {tIdentityInfo('IDENTITY_INFORMATION_FOR_TASKER')}
              </Typography>

              <div className="grid grid-cols-1 gap-6 md:grid-cols-2">
                {/* Identity Card Status */}
                <div className="space-y-2">
                  <Typography variant="h4" className="font-medium">
                    {tTaskerProfile('IDENTITY_CARD_STATUS')}
                  </Typography>
                  <TaskerProfileStatus
                    status={
                      (taskerProfile?.identityCard
                        ?.status as keyof typeof TASKER_ONBOARDING_PROCESS_STATUS) ||
                      TASKER_ONBOARDING_PROCESS_STATUS.VERIFYING
                    }
                  />
                </div>

                {/* Upload Timestamp */}
                <div className="space-y-2">
                  <Typography variant="h4" className="font-medium">
                    {tTaskerProfile('UPLOAD_TIMESTAMP')}
                  </Typography>
                  <Typography variant="p">
                    {(() => {
                      const timestamp =
                        (taskerProfile?.identityCard as ExtendedIdentityCard)
                          ?.uploadTimestamp || taskerProfile?.updatedAt;
                      return timestamp
                        ? format(new Date(timestamp), 'HH:mm - dd/MM/yyyy')
                        : '-';
                    })()}
                  </Typography>
                </div>

                {/* Detected Phone */}
                <div className="space-y-2">
                  <Typography variant="h4" className="font-medium">
                    {tTaskerProfile('DETECTED_PHONE')}
                  </Typography>
                  <Typography variant="p">
                    {(taskerProfile?.identityCard as ExtendedIdentityCard)
                      ?.detectedPhone ||
                      formatPhoneNumber(taskerProfile?.taskerPhone) ||
                      '-'}
                  </Typography>
                </div>

                {/* Current Status */}
                <div className="space-y-2">
                  <Typography variant="h4" className="font-medium">
                    {tTaskerProfile('CURRENT_STATUS')}
                  </Typography>
                  <Typography variant="p">
                    {tTaskerProfile(
                      (taskerProfile?.identityCard?.status as string) ||
                        'NOT_UPLOADED',
                    )}
                  </Typography>
                </div>

                {/* Rejection Reason (if applicable) */}
                {(taskerProfile?.identityCard as ExtendedIdentityCard)
                  ?.reason && (
                  <div className="col-span-2 space-y-2">
                    <Typography
                      variant="h4"
                      className="font-medium text-red-600">
                      {tTaskerProfile('REJECTION_REASON')}
                    </Typography>
                    <Typography variant="p" className="text-red-600">
                      {tTaskerProfile(
                        (taskerProfile.identityCard as ExtendedIdentityCard)
                          .reason || '',
                      )}
                    </Typography>
                  </div>
                )}
              </div>
            </div>
          </TabsContent>

          {/* Images Tab */}
          <TabsContent value="images" className="space-y-6">
            <div className="p-6 bg-white border rounded-lg">
              <div className="flex items-center justify-between mb-6">
                <Typography variant="h3">
                  {tTaskerProfile('IDENTITY_CARD_IMAGES')}
                </Typography>
                <div className="flex items-center gap-2 text-sm text-gray-600">
                  <svg
                    className="w-4 h-4"
                    fill="none"
                    stroke="currentColor"
                    viewBox="0 0 24 24">
                    <path
                      strokeLinecap="round"
                      strokeLinejoin="round"
                      strokeWidth={2}
                      d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"
                    />
                  </svg>
                  {tTaskerProfile('CLICK_TO_ENLARGE')}
                </div>
              </div>

              {/* Enhanced Identity Card Images */}
              <div className="space-y-6">
                {taskerProfile?.identityCard?.images?.length ? (
                  <>
                    <div className="grid grid-cols-1 gap-6 lg:grid-cols-2">
                      {taskerProfile.identityCard.images.map(
                        (imageUrl, index) => {
                          const finalImageUrl =
                            typeof imageUrl === 'string'
                              ? imageUrl
                              : (imageUrl as { url?: string })?.url || '';
                          return (
                            <EnhancedImageCard
                              key={index}
                              imageUrl={finalImageUrl}
                              index={index}
                            />
                          );
                        },
                      )}
                    </div>

                    {/* Image Statistics */}
                    <div className="p-4 mt-6 border border-blue-200 rounded-lg bg-blue-50">
                      <div className="flex items-center gap-4 text-sm">
                        <div className="flex items-center gap-2">
                          <svg
                            className="w-4 h-4 text-blue-600"
                            fill="none"
                            stroke="currentColor"
                            viewBox="0 0 24 24">
                            <path
                              strokeLinecap="round"
                              strokeLinejoin="round"
                              strokeWidth={2}
                              d="M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z"
                            />
                          </svg>
                          <span className="font-medium text-blue-900">
                            {taskerProfile.identityCard.images.length}{' '}
                            {tTaskerProfile('IMAGES_UPLOADED')}
                          </span>
                        </div>
                        <div className="flex items-center gap-2">
                          <svg
                            className="w-4 h-4 text-green-600"
                            fill="none"
                            stroke="currentColor"
                            viewBox="0 0 24 24">
                            <path
                              strokeLinecap="round"
                              strokeLinejoin="round"
                              strokeWidth={2}
                              d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"
                            />
                          </svg>
                          <span className="text-green-800">
                            {tTaskerProfile('HIGH_RESOLUTION_SUPPORTED')}
                          </span>
                        </div>
                      </div>
                    </div>
                  </>
                ) : (
                  <div className="py-12 text-center">
                    <svg
                      className="w-16 h-16 mx-auto mb-4 text-gray-400"
                      fill="none"
                      stroke="currentColor"
                      viewBox="0 0 24 24">
                      <path
                        strokeLinecap="round"
                        strokeLinejoin="round"
                        strokeWidth={2}
                        d="M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z"
                      />
                    </svg>
                    <Typography variant="h4" className="mb-2 text-gray-500">
                      {tTaskerProfile('NO_IMAGES_UPLOADED')}
                    </Typography>
                    <Typography variant="p" className="text-gray-400">
                      {tTaskerProfile('IDENTITY_CARD_IMAGES_WILL_APPEAR_HERE')}
                    </Typography>
                  </div>
                )}
              </div>
            </div>
          </TabsContent>

          {/* Notes Tab */}
          <TabsContent value="notes" className="space-y-6">
            <div className="p-6 bg-white border rounded-lg">
              <Typography variant="h3" className="mb-4">
                {tTaskerProfile('NOTES')}
              </Typography>

              {/* Note Form - Simplified */}
              <div className="p-4 border rounded-lg bg-gray-50">
                <Typography variant="p" className="mb-2 text-gray-600">
                  {tTaskerProfile('ADD_NOTE_PLACEHOLDER')}
                </Typography>
                <textarea
                  {...noteFormRegister('note')}
                  className="w-full p-2 border rounded-lg resize-none"
                  rows={3}
                  placeholder={tTaskerProfile('ENTER_NOTE')}
                />
                <Button
                  type="button"
                  className="mt-2"
                  onClick={() => {
                    const formValues = getNoteFormValues();
                    const noteValue = formValues.note;
                    if (noteValue?.trim()) {
                      const formData = new FormData();
                      formData.append('note', noteValue);
                      submit(formData, { method: 'post' });
                      resetNoteFormValue('note');
                    }
                  }}
                  disabled={navigation.state === 'submitting'}>
                  {tTaskerProfile('ADD_NOTE')}
                </Button>
              </div>

              {reversedNotes.length > 0 && (
                <div className="mt-6 space-y-4">
                  {reversedNotes.map(note => (
                    <div
                      key={note.index}
                      className="p-4 border rounded-lg bg-gray-50">
                      <div className="flex items-start justify-between mb-2">
                        <Typography
                          variant="p"
                          className="text-sm text-gray-600">
                          {note.notedBy} -{' '}
                          {format(new Date(note.notedAt), 'HH:mm dd/MM/yyyy')}
                        </Typography>
                      </div>
                      <Typography variant="p">{note.description}</Typography>
                    </div>
                  ))}
                </div>
              )}
            </div>
          </TabsContent>

          {/* Action History Tab */}
          <TabsContent value="history" className="space-y-6">
            <div className="p-6 bg-white border rounded-lg">
              <Typography variant="h3" className="mb-4">
                {tTaskerProfile('ACTION_HISTORY')}
              </Typography>

              {reversedActionHistories.length > 0 ? (
                <div className="space-y-4">
                  {reversedActionHistories.map((history, index) => (
                    <div
                      key={index}
                      className="p-4 border rounded-lg bg-gray-50">
                      <div className="flex items-center gap-2 mb-2">
                        <TaskerProfileStatus
                          status={
                            history.newStatus as keyof typeof TASKER_ONBOARDING_PROCESS_STATUS
                          }
                        />
                        <Typography
                          variant="p"
                          className="text-sm text-gray-600">
                          {history.updatedByUsername} -{' '}
                          {format(
                            new Date(history.createdAt),
                            'HH:mm dd/MM/yyyy',
                          )}
                        </Typography>
                      </div>
                      {history.reason && (
                        <Typography variant="p" className="text-red-600">
                          {tTaskerProfile('REASON')}:{' '}
                          {tTaskerProfile(history.reason)}
                        </Typography>
                      )}
                    </div>
                  ))}
                </div>
              ) : (
                <Typography variant="p" className="text-gray-500">
                  {tTaskerProfile('NO_ACTION_HISTORY')}
                </Typography>
              )}
            </div>
          </TabsContent>
        </Tabs>
      </div>

      {/* Warning Dialog for Missing Reasons */}
      <WarningMissingReasonConfigurationDialog
        openDialog={openWarningMissingReasonDialog}
        setOpenDialog={setOpenWarningMissingReasonDialog}
        taskerName={taskerProfile?.taskerName || ''}
      />

      {/* Image Lightbox Modal */}
      <Dialog open={!!selectedImage} onOpenChange={closeImageModal}>
        <DialogContent className="max-w-4xl max-h-[90vh] p-0">
          <DialogHeader className="p-6 pb-0">
            <DialogTitle className="flex items-center justify-between">
              <span>{tTaskerProfile('IDENTITY_CARD_IMAGE_PREVIEW')}</span>
              <Button
                variant="ghost"
                size="sm"
                onClick={closeImageModal}
                className="w-8 h-8 p-0">
                <svg
                  className="w-4 h-4"
                  fill="none"
                  stroke="currentColor"
                  viewBox="0 0 24 24">
                  <path
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    strokeWidth={2}
                    d="M6 18L18 6M6 6l12 12"
                  />
                </svg>
              </Button>
            </DialogTitle>
          </DialogHeader>

          {selectedImage && (
            <div className="p-6 pt-0">
              <div className="relative overflow-hidden rounded-lg bg-gray-50">
                <img
                  src={selectedImage}
                  alt="Identity Card Full Size"
                  className="w-full h-auto max-h-[70vh] object-contain"
                  style={{ minHeight: '300px' }}
                />

                {/* Image Controls */}
                <div className="absolute px-4 py-2 transform -translate-x-1/2 bg-black bg-opacity-75 rounded-lg bottom-4 left-1/2">
                  <div className="flex items-center gap-4 text-sm text-white">
                    <button
                      onClick={() => window.open(selectedImage, '_blank')}
                      className="flex items-center gap-2 transition-colors hover:text-blue-300">
                      <svg
                        className="w-4 h-4"
                        fill="none"
                        stroke="currentColor"
                        viewBox="0 0 24 24">
                        <path
                          strokeLinecap="round"
                          strokeLinejoin="round"
                          strokeWidth={2}
                          d="M10 6H6a2 2 0 00-2 2v10a2 2 0 002 2h10a2 2 0 002-2v-4M14 4h6m0 0v6m0-6L10 14"
                        />
                      </svg>
                      {tTaskerProfile('OPEN_IN_NEW_TAB')}
                    </button>
                    <span className="text-gray-300">|</span>
                    <span>{tTaskerProfile('CLICK_AND_DRAG_TO_PAN')}</span>
                  </div>
                </div>
              </div>

              {/* Image Information */}
              <div className="p-4 mt-4 rounded-lg bg-gray-50">
                <div className="grid grid-cols-1 gap-4 text-sm md:grid-cols-3">
                  <div>
                    <span className="font-medium text-gray-700">
                      {tTaskerProfile('IMAGE_TYPE')}:
                    </span>
                    <span className="ml-2 text-gray-600">
                      {tTaskerProfile('IDENTITY_CARD')}
                    </span>
                  </div>
                  <div>
                    <span className="font-medium text-gray-700">
                      {tTaskerProfile('STATUS')}:
                    </span>
                    <span className="ml-2">
                      <TaskerProfileStatus
                        status={
                          (taskerProfile?.identityCard
                            ?.status as keyof typeof TASKER_ONBOARDING_PROCESS_STATUS) ||
                          TASKER_ONBOARDING_PROCESS_STATUS.VERIFYING
                        }
                      />
                    </span>
                  </div>
                  <div>
                    <span className="font-medium text-gray-700">
                      {tTaskerProfile('TASKER_NAME')}:
                    </span>
                    <span className="ml-2 text-gray-600">
                      {taskerProfile?.taskerName || '-'}
                    </span>
                  </div>
                </div>
              </div>
            </div>
          )}
        </DialogContent>
      </Dialog>
    </>
  );
}
