import { json } from '@remix-run/node';
import type { SerializeFrom } from '@remix-run/node';
import {
  isRouteErrorResponse,
  useActionData,
  useNavigation,
  useRouteError,
  useSubmit,
} from '@remix-run/react';
import type { ColumnDef } from '@tanstack/react-table';
import {
  CardProfileDescription,
  ConfirmUpdatingProfile,
  NoteForm,
  ProfileImageAccordion,
  TaskerProfileCard,
  TaskerProfileStatus,
  UpdateNoteDialog,
  WarningMissingReasonConfigurationDialog,
} from 'app/components/tasker-common';
import type { ConfirmUpdatingProfileFormProps } from 'app/components/tasker-common';
import {
  ACTION_NAME,
  IMAGE_PROFILE_TYPE,
  PERMISSIONS,
  REASON_TYPE_IN_TASKER_ONBOARDING_SETTING,
  ROUTE_NAME,
  TASKER_ONBOARDING_IMAGE_KEY,
  TASKER_ONBOARDING_PROCESS_STATUS,
} from 'btaskee-constants';
import { useGlobalStore, useLoaderDataSafely } from 'btaskee-hooks';
import {
  Breadcrumbs,
  BreadcrumbsLink,
  BtaskeeResponseError,
  Button,
  Card,
  CardContent,
  CardHeader,
  DataTableBasic,
  DataTableColumnHeader,
  LoadingSpinner,
  Separator,
  Tabs,
  TabsContent,
  TabsList,
  TabsTrigger,
  Typography,
  toast,
  useConfirm,
} from 'btaskee-ui';
import { momentTz } from 'btaskee-utils';
import { format } from 'date-fns';
import i18n from 'i18next';
import { ChevronsRight } from 'lucide-react';
import { useCallback, useEffect, useMemo, useState } from 'react';
import { useForm } from 'react-hook-form';
import { useTranslation } from 'react-i18next';
import { hoc404, hocAction, hocLoader } from '~/hoc/remix';
import i18next from '~/i18next.server';
import { getUserSession } from '~/services/helpers.server';
import {
  getAllReasonFromSetting,
  getEmployeeProfileDetail,
  rejectEmployeeProfile,
  storeNoteInStaffProfile,
  updateNoteInStaffProfileByIndex,
  updateStatusOnEmployeeProfileByAPI,
} from '~/services/tasker-profile.server';
import { sendNotification } from '~/services/utils.server';

export const handle = {
  breadcrumb: ({
    data,
  }: SerializeFrom<
    ReturnValueIgnorePromise<typeof getEmployeeProfileDetail>
  >) => {
    return (
      <BreadcrumbsLink
        to={`${ROUTE_NAME.STAFF_ONBOARDING}/${data?._id}`}
        label="STAFF_PROFILE_DETAIL"
      />
    );
  },
  i18n: 'staff-profile',
};

export function ErrorBoundary() {
  const error = useRouteError() as { message?: string };
  const { t: tCommon } = useTranslation('common');

  if (isRouteErrorResponse(error)) {
    if (error?.message) {
      toast({ description: JSON.stringify(error.message) });
    }

    return <BtaskeeResponseError t={tCommon} errorStatus={error.status} />;
  }

  return <BtaskeeResponseError t={tCommon} errorStatus={500} />;
}

export const loader = hocLoader(
  async ({ request, params }) => {
    const { isoCode } = await getUserSession({
      headers: request.headers,
    });

    const [staffProfileDetail, reasonsFromSetting] = await Promise.all([
      hoc404(() =>
        getEmployeeProfileDetail({
          employeeProfileId: params?.id || '',
          isoCode,
        }),
      ),
      getAllReasonFromSetting({
        isoCode,
      }),
    ]);

    return json({ ...staffProfileDetail, reasonsFromSetting });
  },
  [
    PERMISSIONS.READ_VERIFYING_STEP_ON_SUPPLIER_AND_STAFF_ONBOARDING,
    PERMISSIONS.READ_APPROVED_STEP_ON_SUPPLIER_AND_STAFF_ONBOARDING,
    PERMISSIONS.READ_ELIMINATED_STEP_ON_SUPPLIER_AND_STAFF_ONBOARDING,
    PERMISSIONS.READ_REJECTED_STEP_ON_SUPPLIER_AND_STAFF_ONBOARDING,
  ],
);

export const action = hocAction(
  async ({ request, params }, { setInformationActionHistory }) => {
    const formData = await request.clone().formData();
    const tStaffProfile = await i18next.getFixedT(request, 'staff-profile');
    const { isoCode, userId, language, username } = await getUserSession({
      headers: request.headers,
    });

    const status = formData.get('status')?.toString() || '';
    const fieldName = formData.get('fieldName')?.toString() || '';
    const reason = formData.get('reason')?.toString() || '';
    const note = formData.get('note')?.toString() || '';
    const updatedNote = formData.get('updatedNote')?.toString() || '';
    const noteIndex = formData.get('noteIndex');
    const sendRemindMessage =
      formData.get('sendRemindMessage')?.toString() || '';

    if (note) {
      const noteResponseMessage = await storeNoteInStaffProfile({
        profileId: params.id || '',
        note,
        userId,
        username,
        isoCode,
      });

      setInformationActionHistory({
        action: ACTION_NAME.ADD_NOTE_IN_STAFF_ONBOARDING,
        dataRelated: {
          staffProfileId: params.id || '',
          note,
        },
      });

      return json({ msg: tStaffProfile(noteResponseMessage) });
    }

    if (updatedNote && noteIndex) {
      const noteResponseMessage = await updateNoteInStaffProfileByIndex({
        profileId: params.id || '',
        note: updatedNote,
        userId,
        username,
        isoCode,
        noteIndex: Number(noteIndex || 0),
      });

      setInformationActionHistory({
        action: ACTION_NAME.UPDATE_NOTE_IN_STAFF_ONBOARDING,
        dataRelated: {
          profileId: params.profileId || '',
          noteIndex: Number(noteIndex || 0),
          note: updatedNote,
        },
      });

      return json({ msg: tStaffProfile(noteResponseMessage) });
    }

    if (status === TASKER_ONBOARDING_PROCESS_STATUS.REJECTED && !fieldName) {
      const result = await rejectEmployeeProfile({
        profileId: params?.id || '',
        username,
        userId,
        isoCode,
        reason,
      });

      return json({
        msg: tStaffProfile(result.msg),
      });
    }

    if (sendRemindMessage) {
      await sendNotification({
        isSendNotificationId: true,
        userIds: [formData.get('taskerId')?.toString() || ''],
        locale: language as BtaskeeLanguage,
        message: JSON.parse(sendRemindMessage),
        isoCode,
      });

      setInformationActionHistory({
        action: ACTION_NAME.SEND_NOTIFICATION_ON_STAFF_ONBOARDING_PROFILE,
        dataRelated: {
          profileId: params.id || '',
          message: JSON.parse(sendRemindMessage),
        },
      });

      return json({ msg: tStaffProfile('SEND_NOTIFICATION_SUCCESSFULLY') });
    }

    const result = await updateStatusOnEmployeeProfileByAPI({
      profileId: params.id || '',
      status: status as `${TASKER_ONBOARDING_PROCESS_STATUS}`,
      userId,
      fieldName: fieldName as `${TASKER_ONBOARDING_IMAGE_KEY}`,
      username,
      isoCode,
      reason,
      language,
    });

    return json({
      msg: tStaffProfile(result.msg),
      employeeProfileId: params.id,
    });
  },
  PERMISSIONS.WRITE_PARTNER_AND_PARTNER_EMPLOYEE_PROFILE_ON_TASKER_ONBOARDING,
);

export default function StaffProfileDetail() {
  const { t: tStaffProfile } = useTranslation('staff-profile');

  const {
    data: staffProfile,
    reasonsFromSetting,
    error: loaderError,
  } = useLoaderDataSafely<typeof loader>();

  const navigation = useNavigation();
  const actionData = useActionData<ActionTypeWithError<typeof action>>();
  const permissions = useGlobalStore(store => store.permissions);
  const reversedActionHistories = useMemo(
    () => staffProfile?.actionHistories?.reverse() || [],
    [staffProfile?.actionHistories],
  );
  const [openWarningMissingReasonDialog, setOpenWarningMissingReasonDialog] =
    useState<boolean>(false);
  const reversedNotes = useMemo(() => {
    // Need add index then passing it to server which update note by this index
    const notesWithIndex =
      staffProfile?.notes?.map((note, index) => ({
        index,
        ...note,
      })) || [];
    return notesWithIndex.reverse();
  }, [staffProfile?.notes]);

  const confirm = useConfirm();
  const submit = useSubmit();

  const { control, getValues, setValue } =
    useForm<ConfirmUpdatingProfileFormProps>({
      defaultValues: {
        reason: '',
        status: TASKER_ONBOARDING_PROCESS_STATUS.VERIFYING,
      },
    });

  const {
    register: noteFormRegister,
    control: noteFormControl,
    resetField: resetNoteFormValue,
  } = useForm<{
    note: string;
  }>({
    defaultValues: {
      note: '',
    },
  });

  useEffect(() => {
    if (loaderError) {
      toast({ description: loaderError });
    }
  }, [loaderError]);

  useEffect(() => {
    if (actionData?.msg)
      toast({ description: actionData.msg, variant: 'success' });

    if (actionData?.error) toast({ description: actionData.error });
  }, [actionData]);

  const onChangeProfileStatus = async ({
    newStatus,
    confirmDialog,
  }: {
    newStatus: `${TASKER_ONBOARDING_PROCESS_STATUS}`;
    confirmDialog: {
      title: string;
      description: string;
    };
    message?: {
      title: TextLang;
      body: TextLang;
    };
  }) => {
    setValue('status', newStatus);

    const isConfirmUpdatingStatus = await confirm({
      title: confirmDialog.title,
      body: (
        <ConfirmUpdatingProfile
          control={control}
          setValue={setValue}
          taskerInfo={{
            taskerName: staffProfile?.taskerName,
            username: staffProfile?.username,
          }}
          reasonsFromSetting={reasonsFromSetting}
          description={tStaffProfile(confirmDialog.description)}
        />
      ),
      cancelButton: tStaffProfile('CANCEL'),
      actionButton: tStaffProfile('CONFIRM'),
    });

    if (isConfirmUpdatingStatus) {
      const formData = new FormData();
      formData.append('status', newStatus);

      submit(formData, { method: 'post' });
    }
  };

  const onChangeImageStatus = async (values: ApprovalImageProfileProps) => {
    const rejectImageReasonsFromSetting = reasonsFromSetting?.filter(
      reasonFromSetting =>
        reasonFromSetting?.type === REASON_TYPE_IN_TASKER_ONBOARDING_SETTING,
    );

    if (values.status === TASKER_ONBOARDING_PROCESS_STATUS.REJECTED) {
      setValue('reason', rejectImageReasonsFromSetting?.[0]?.name);
    }

    const isConfirmUpdatingImageStatus = await confirm({
      title: tStaffProfile(values.title),
      body: (
        <ConfirmUpdatingProfile
          control={control}
          description={tStaffProfile(values.desc)}
          setValue={setValue}
          reasons={
            values.status === TASKER_ONBOARDING_PROCESS_STATUS.REJECTED
              ? rejectImageReasonsFromSetting.map(reason => reason.name)
              : []
          }
        />
      ),
      cancelButton: tStaffProfile('CANCEL'),
      actionButton: tStaffProfile('CONFIRM'),
    });

    if (isConfirmUpdatingImageStatus) {
      const formDataUpdatingImageStatus = new FormData();

      const { reason } = getValues();

      formDataUpdatingImageStatus.append('status', values.status);
      formDataUpdatingImageStatus.append('fieldName', values.fieldName);

      if (reason) {
        formDataUpdatingImageStatus.append('reason', reason);
      }

      submit(formDataUpdatingImageStatus, { method: 'post' });
    }
  };

  const updateNote = useCallback(
    async ({
      noteIndex,
      updatedNote,
    }: {
      noteIndex: number;
      updatedNote: string;
    }) => {
      const formDataUpdatingNote = new FormData();

      formDataUpdatingNote.append('noteIndex', noteIndex?.toString());
      formDataUpdatingNote.append('updatedNote', updatedNote || '');

      submit(formDataUpdatingNote, { method: 'post' });
    },
    [submit],
  );

  const noteColumns: ColumnDef<
    NonNullable<
      SerializeFrom<
        ReturnValueIgnorePromise<typeof getEmployeeProfileDetail>
      >['data']['notes']
    >[0] & { index: number }
  >[] = useMemo(
    () => [
      {
        accessorKey: 'description',
        header: ({ column }) => (
          <DataTableColumnHeader
            className="whitespace-nowrap"
            column={column}
            title={tStaffProfile('NOTE')}
          />
        ),
        cell: ({ row }) => (
          <div className="flex gap-2 items-center">
            <Typography variant="p" affects="removePMargin">
              {row.original?.description}
            </Typography>
            {row.original?.notedAt &&
            momentTz(row.original.notedAt)
              .add(24, 'hours')
              .isAfter(momentTz().toDate()) ? (
              <UpdateNoteDialog
                // TODO: currently, we don need care employee profile, this type will handle to next task
                noteInfo={row.original as MustBeAny}
                onSubmit={async ({ updatedNote }) =>
                  await updateNote({
                    updatedNote,
                    noteIndex: row.original?.index,
                  })
                }
              />
            ) : null}
          </div>
        ),
        enableSorting: false,
      },
      {
        accessorKey: 'updatedBy',
        header: ({ column }) => (
          <DataTableColumnHeader
            className="whitespace-nowrap"
            column={column}
            title={tStaffProfile('NOTE_BY')}
          />
        ),
        cell: ({ row }) => (
          <Typography variant="p">{row.original?.notedBy}</Typography>
        ),
        enableSorting: false,
      },
      {
        accessorKey: 'notedAt',
        header: ({ column }) => (
          <DataTableColumnHeader
            className="whitespace-nowrap"
            column={column}
            title={tStaffProfile('NOTE_AT')}
          />
        ),
        cell: ({ row }) =>
          row.original?.notedAt ? (
            <Typography variant="p">
              {format(row.original?.notedAt, 'HH:mm - dd/MM/yyyy')}
            </Typography>
          ) : null,
      },
    ],
    [tStaffProfile, updateNote],
  );

  const historyColumns: ColumnDef<
    NonNullable<
      SerializeFrom<
        ReturnValueIgnorePromise<typeof getEmployeeProfileDetail>
      >['data']['actionHistories']
    >[0]
  >[] = useMemo(
    () => [
      {
        accessorKey: 'updatedBy',
        header: ({ column }) => (
          <DataTableColumnHeader
            className="whitespace-nowrap"
            column={column}
            title={tStaffProfile('CONTENT_UPDATED')}
          />
        ),
        cell: ({ row }) => (
          <div className="flex gap-3 whitespace-nowrap">
            <TaskerProfileStatus status={row.original?.oldStatus} />
            <ChevronsRight className="text-primary" />
            <TaskerProfileStatus status={row.original?.newStatus} />
          </div>
        ),
        enableSorting: false,
      },
      {
        accessorKey: 'updatedByUsername',
        header: ({ column }) => (
          <DataTableColumnHeader
            className="whitespace-nowrap"
            column={column}
            title={tStaffProfile('UPDATED_BY')}
          />
        ),
        cell: ({ row }) => (
          <Typography variant="p">{row.original?.updatedByUsername}</Typography>
        ),
        enableSorting: false,
      },
      {
        accessorKey: 'createdAt',
        header: ({ column }) => (
          <DataTableColumnHeader
            className="whitespace-nowrap"
            column={column}
            title={tStaffProfile('DATE')}
          />
        ),
        cell: ({ row }) =>
          row.original?.createdAt ? (
            <Typography variant="p">
              {format(row.original?.createdAt, 'HH:mm - dd/MM/yyyy')}
            </Typography>
          ) : null,
      },
      {
        accessorKey: 'reason',
        header: ({ column }) => (
          <DataTableColumnHeader
            className="whitespace-nowrap"
            column={column}
            title={tStaffProfile('REASON')}
          />
        ),
        enableSorting: false,
        cell: ({ row }) => (
          <Typography variant="p">{row.original?.reason}</Typography>
        ),
      },
    ],
    [tStaffProfile],
  );

  return (
    <>
      <div className="flex bg-secondary p-4 justify-between align-middle">
        <div className="grid space-y-2 rounded-xl">
          <div className="flex">
            <Typography className="capitalize mr-6" variant="h2">
              {tStaffProfile('STAFF_PROFILE_DETAIL')}
            </Typography>
            <TaskerProfileStatus status={staffProfile.status} />
          </div>
          <Breadcrumbs />
        </div>
        {staffProfile.status === TASKER_ONBOARDING_PROCESS_STATUS.VERIFYING ? (
          <div className="flex justify-end gap-6 my-auto pr-2">
            <Button
              onClick={() => {
                onChangeProfileStatus({
                  newStatus: TASKER_ONBOARDING_PROCESS_STATUS.ELIMINATED,
                  confirmDialog: {
                    title: tStaffProfile('ELIMINATE_PROFILE_TITLE'),
                    description: tStaffProfile('ELIMINATE_PROFILE_DESC'),
                  },
                });
              }}
              disabled={
                navigation.state === 'submitting' ||
                !permissions.includes(
                  PERMISSIONS.WRITE_PARTNER_AND_PARTNER_EMPLOYEE_PROFILE_ON_TASKER_ONBOARDING,
                )
              }>
              {navigation.state === 'submitting' ? (
                <LoadingSpinner />
              ) : (
                tStaffProfile('ELIMINATE_PROFILE')
              )}
            </Button>
            <Button
              variant="outline"
              className="border-primary text-primary hover:text-primary"
              disabled={
                navigation.state === 'submitting' ||
                !permissions.includes(
                  PERMISSIONS.WRITE_PARTNER_AND_PARTNER_EMPLOYEE_PROFILE_ON_TASKER_ONBOARDING,
                )
              }
              onClick={() => {
                onChangeProfileStatus({
                  newStatus: TASKER_ONBOARDING_PROCESS_STATUS.REJECTED,
                  confirmDialog: {
                    title: tStaffProfile('REJECTED_EMPLOYEE_PROFILE_TITLE'),
                    description: tStaffProfile(
                      'REJECTED_EMPLOYEE_PROFILE_DESC',
                    ),
                  },
                });
              }}>
              {navigation.state === 'submitting' ? (
                <LoadingSpinner />
              ) : (
                tStaffProfile('REJECT_PROFILE')
              )}
            </Button>
          </div>
        ) : null}
      </div>
      <Card className="bg-gray-50 mt-6">
        <CardHeader>
          <Typography variant="h4">
            {tStaffProfile('STAFF_INFORMATION')}
          </Typography>
          <Separator className="w-[200px]" />
        </CardHeader>
        <CardContent>
          <CardProfileDescription
            descriptions={[
              {
                label: tStaffProfile('TASKER_NAME'),
                value: staffProfile?.taskerName,
              },
              {
                label: tStaffProfile('TASKER_GENDER'),
                value: tStaffProfile(staffProfile?.taskerGender),
              },
              {
                label: tStaffProfile('PHONE_NUMBER'),
                value: staffProfile?.taskerPhone,
              },
              {
                label: tStaffProfile('SUPPLIER_NAME'),
                value: staffProfile?.companyInformation?.name,
              },
              {
                label: tStaffProfile('DATE_OF_BIRTH'),
                value: staffProfile?.dob
                  ? format(staffProfile.dob, 'HH:mm - dd/MM/yyyy')
                  : null,
              },
              {
                label: tStaffProfile('UPDATED_DATE'),
                value: staffProfile?.updatedAt
                  ? format(staffProfile.updatedAt, 'HH:mm - dd/MM/yyyy')
                  : null,
              },
              {
                label: tStaffProfile('SERVICES'),
                tags: staffProfile?.services?.map(
                  (service: { text: TextLang }) =>
                    service?.text?.[i18n.language || 'en'] || '',
                ),
              },
            ]}
          />
        </CardContent>
      </Card>
      <TaskerProfileCard title={tStaffProfile('UPLOADED_DOCUMENTS')}>
        <ProfileImageAccordion
          setOpenWarningMissingReasonDialog={setOpenWarningMissingReasonDialog}
          profileStatus={staffProfile.status}
          isSubmitLoading={navigation.state === 'submitting'}
          reasonsFromSetting={reasonsFromSetting || []}
          profileType={IMAGE_PROFILE_TYPE.STAFF}
          localeAddress="staff-profile"
          disabled={
            !permissions.includes(
              PERMISSIONS.WRITE_PARTNER_AND_PARTNER_EMPLOYEE_PROFILE_ON_TASKER_ONBOARDING,
            ) ||
            staffProfile.status !== TASKER_ONBOARDING_PROCESS_STATUS.VERIFYING
          }
          onTrigger={onChangeImageStatus}
          items={[
            {
              label: tStaffProfile('IDENTITY_CARD'),
              info: staffProfile.identityCard,
              imageField: TASKER_ONBOARDING_IMAGE_KEY.IDENTITY_CARD,
            },
            {
              label: tStaffProfile('PORTRAIT'),
              info: staffProfile.portrait,
              imageField: TASKER_ONBOARDING_IMAGE_KEY.PORTRAIT,
            },
          ]}
        />
      </TaskerProfileCard>
      <TaskerProfileCard>
        <Tabs defaultValue="note" className="pt-6">
          <TabsList className="grid grid-cols-2 w-[140px]">
            <TabsTrigger value="note">{tStaffProfile('NOTE')}</TabsTrigger>
            <TabsTrigger value="history">
              {tStaffProfile('HISTORY')}
            </TabsTrigger>
          </TabsList>
          <TabsContent value="note" className="mt-4">
            <NoteForm
              register={noteFormRegister}
              control={noteFormControl}
              resetField={resetNoteFormValue}
            />
            <DataTableBasic
              data={reversedNotes}
              columns={noteColumns}
              manualPagination
              isDisplayPagination={false}
            />
          </TabsContent>
          <TabsContent value="history" className="mt-4">
            <DataTableBasic
              manualPagination
              isDisplayPagination={false}
              columns={historyColumns}
              data={reversedActionHistories}
            />
          </TabsContent>
        </Tabs>
      </TaskerProfileCard>
      <WarningMissingReasonConfigurationDialog
        openDialog={openWarningMissingReasonDialog}
        taskerName={staffProfile?.taskerName}
        setOpenDialog={setOpenWarningMissingReasonDialog}
      />
    </>
  );
}
