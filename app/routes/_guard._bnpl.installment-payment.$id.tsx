import type { SerializeFrom } from '@remix-run/node';
import { defer } from '@remix-run/node';
import {
  Await,
  Link,
  Outlet,
  isRouteErrorResponse,
  useActionData,
  useLoaderData,
  useRouteError,
} from '@remix-run/react';
import type { ColumnDef } from '@tanstack/react-table';
import {
  ACTION_NAME,
  BNPL_TRANSACTION_SOURCE,
  BTASKEE_LANGUAGE,
  CHARGE_MAIN_ACCOUNT_SINCE_DUE_DATE_REASON_IN_BNPL,
  DEDUCT_INSTALLMENT_PAYMENT_REASON_IN_TASKER_BNPL,
  PERMISSIONS,
  ROUTE_NAME,
} from 'btaskee-constants';
import { useGlobalStore } from 'btaskee-hooks';
import {
  Breadcrumbs,
  BreadcrumbsLink,
  BtaskeeResponseError,
  <PERSON><PERSON>,
  Card,
  CardContent,
  CardHeader,
  DataTableBasic,
  Separator,
  StatusBadge,
  Typography,
  toast,
} from 'btaskee-ui';
import { formatNumberWithCommas, getFormattedPhoneNumber } from 'btaskee-utils';
import { format } from 'date-fns';
import i18n from 'i18next';
import { Suspense, useCallback, useEffect, useMemo, useState } from 'react';
import { useTranslation } from 'react-i18next';
import { CardProfileDescription } from '~/components/tasker-common';
import { hocAction, hocLoader } from '~/hoc/remix';
import {
  deductInstallmentPaymentInTaskerBNPL,
  getBNPLTransactionByTaskerId,
  getInstallmentPaymentDetail,
} from '~/services/bnpl.server';
import {
  getSettingCountryByIsoCode,
  getUserSession,
} from '~/services/helpers.server';
import { getServices } from '~/services/service.server';
import { getSession } from '~/services/session.server';
import { finalizeAction } from '~/utils/common';

export const handle = {
  breadcrumb: ({
    installmentDetail,
  }: {
    installmentDetail: InstallmentPaymentDetail;
  }) => {
    return (
      <BreadcrumbsLink
        to={`${ROUTE_NAME.INSTALLMENT_PAYMENT}/${installmentDetail?._id}`}
        label="INSTALLMENT_PAYMENT_DETAIL"
      />
    );
  },
};

export function ErrorBoundary() {
  const error = useRouteError() as { message?: string };
  const { t: tCommon } = useTranslation('common');

  if (isRouteErrorResponse(error)) {
    if (error?.message) {
      toast({ description: JSON.stringify(error.message) });
    }

    return <BtaskeeResponseError t={tCommon} errorStatus={error.status} />;
  }

  return <BtaskeeResponseError t={tCommon} errorStatus={500} />;
}

export const loader = hocLoader(async ({ params, request }) => {
  const { isoCode } = await getUserSession({
    headers: request.headers,
  });

  const installmentDetail = await getInstallmentPaymentDetail({
    isoCode,
    id: params?.id || '',
  });

  const services = await getServices({
    isoCode,
    projection: { name: 1, text: 1, icon: 1, isSubscription: 1 },
  });
  const installmentTransaction = await getBNPLTransactionByTaskerId({
    isoCode,
    taskerId: installmentDetail?.tasker?._id || '',
  });

  const settingCountry = await getSettingCountryByIsoCode({
    isoCode,
    projection: { currency: 1 },
  });

  const session = await getSession(request.headers.get('cookie'));
  const flashMessage = await session.get('flashMessage');

  return defer({
    installmentDetail,
    services,
    installmentTransaction,
    settingCountry,
    flashMessage,
  });
}, PERMISSIONS.READ_INSTALLMENT_PAYMENT);

export const action = hocAction(
  async ({ request, params }, { setInformationActionHistory }) => {
    const formData = await request.clone().formData();
    const { isoCode } = await getUserSession({
      headers: request.headers,
    });

    const deductedInfo = await deductInstallmentPaymentInTaskerBNPL({
      isoCode,
      reason: formData.get('reason')?.toString() ?? '',
      bnplId: params?.id ?? '',
    });

    setInformationActionHistory({
      action: ACTION_NAME.DEDUCT_INSTALLMENT_PAYMENT_IN_TASKER_BNPL,
    });

    return finalizeAction({
      request,
      flashMessage: {
        message: deductedInfo?.msg,
        translationKey: 'installment-payment',
      },
      destinationUrl: `${ROUTE_NAME.INSTALLMENT_PAYMENT}/${params?.id}`,
    });
  },
  PERMISSIONS.WRITE_INSTALLMENT_PAYMENT,
);

export default function InstallmentPaymentDetailScreen() {
  const { t: tPayment } = useTranslation('installment-payment-detail');
  const loaderData = useLoaderData<typeof loader>();
  const permissions = useGlobalStore(store => store.permissions);
  const actionData = useActionData<ActionTypeWithError<typeof action>>();
  const [openDeductPaymentDialog, setOpenDeductPaymentDialog] = useState(true);

  const currency = useMemo(
    () => loaderData?.settingCountry?.currency?.sign || '',
    [loaderData?.settingCountry?.currency?.sign],
  );

  const service = useMemo(
    () =>
      loaderData?.installmentDetail?.tasker?.registeredServices?.map(
        registeredService =>
          loaderData?.services.find(
            service => service._id === registeredService,
          ),
      ),
    [loaderData],
  );

  useEffect(() => {
    if (loaderData?.flashMessage) {
      toast({
        variant: 'success',
        description: loaderData.flashMessage,
      });
    }

    if (actionData?.error) {
      toast({
        description: loaderData.flashMessage,
      });
    }
  }, [loaderData?.flashMessage, actionData?.error]);

  const getBNPLTransactionType = useCallback(
    ({ reason, taskId, name }: TaskerBNPLTransactionSchema['source']) => {
      if (taskId && name === BNPL_TRANSACTION_SOURCE.CHARGE_BNPL)
        return tPayment(BNPL_TRANSACTION_SOURCE.CHARGE_BNPL);

      if (!taskId && name === BNPL_TRANSACTION_SOURCE.CHARGE_BNPL) {
        if (
          reason ===
            DEDUCT_INSTALLMENT_PAYMENT_REASON_IN_TASKER_BNPL.MAKE_AN_EARLY_PAYMENT ||
          reason ===
            DEDUCT_INSTALLMENT_PAYMENT_REASON_IN_TASKER_BNPL.TERMINATE_THE_INSTALLMENT_PLAN
        )
          return tPayment('DEDUCT_INSTALLMENT_PAYMENT');

        if (reason === CHARGE_MAIN_ACCOUNT_SINCE_DUE_DATE_REASON_IN_BNPL)
          return tPayment(
            BNPL_TRANSACTION_SOURCE.CHARGE_MAIN_ACCOUNT_SINCE_DUE_DATE,
          );
      }

      return tPayment('INVALID_TRANSACTION_TYPE');
    },
    [tPayment],
  );

  const columns: ColumnDef<
    SerializeFrom<
      ReturnValueIgnorePromise<typeof getBNPLTransactionByTaskerId>[0]
    >
  >[] = useMemo(
    () => [
      {
        accessorKey: 'taskId',
        header: tPayment('TASK_ID'),
        cell: ({ row }) => <div>{row.original?.task?._id}</div>,
      },
      {
        accessorKey: 'moneyBNPLOnTask',
        header: tPayment('AMOUNT_OF_TASK'),
        cell: ({ row }) => (
          <div>{`${formatNumberWithCommas(row.original?.task?.costDetail?.totalCost ?? row.original?.task?.newCostDetail?.totalCost ?? 0)}${currency}`}</div>
        ),
      },
      {
        accessorKey: 'amount',
        header: tPayment('AMOUNT'),
        cell: ({ row }) => (
          <div>{`${formatNumberWithCommas(row.original?.amount)}${currency}`}</div>
        ),
      },
      {
        accessorKey: 'type',
        header: tPayment('TYPE'),
        cell: ({ row }) => (
          <Typography variant="p">
            {getBNPLTransactionType(row.original?.source)}
          </Typography>
        ),
      },
      {
        accessorKey: 'createdAt',
        header: tPayment('PAYMENT_DATE'),
        cell: ({ row }) =>
          row.original?.createdAt ? (
            <Typography variant="p" className="whitespace-nowrap">
              {format(row.original.createdAt, 'HH:mm - dd/MM/yyyy')}
            </Typography>
          ) : null,
      },
      {
        accessorKey: 'reason',
        header: tPayment('REASON'),
        cell: ({ row }) => (
          <Typography variant="p" className="max-w-72">
            {row.original?.source?.reason ?? ''}
          </Typography>
        ),
      },
    ],
    [currency, getBNPLTransactionType, tPayment],
  );

  return (
    <>
      <div className="mb-6 flex justify-between items-center rounded-xl bg-secondary py-4 px-6">
        <div>
          <Typography variant="h2">
            {tPayment('INSTALLMENT_PAYMENT_DETAIL')}
          </Typography>
          <Breadcrumbs />
        </div>
        {loaderData?.installmentDetail?.remainingAmount ? (
          <Button
            asChild={permissions.includes(
              PERMISSIONS.WRITE_INSTALLMENT_PAYMENT,
            )}
            disabled={
              !permissions.includes(PERMISSIONS.WRITE_INSTALLMENT_PAYMENT)
            }>
            <Link
              to={`${ROUTE_NAME.INSTALLMENT_PAYMENT}/${loaderData?.installmentDetail?._id}${ROUTE_NAME.DEDUCT_INSTALLMENT_PAYMENT_IN_TASKER_BNPL}`}>
              {tPayment('DEDUCT_INSTALLMENT_PAYMENT')}
            </Link>
          </Button>
        ) : null}
      </div>
      <div className="flex flex-col gap-6">
        <Card className="bg-gray-50">
          <CardHeader>
            <Typography variant="h4">{tPayment('GENERAL')}</Typography>
            <Separator className="w-[200px]" />
          </CardHeader>
          <CardContent>
            <Suspense fallback={<div>Loading...</div>}>
              <Await
                resolve={loaderData?.installmentDetail}
                errorElement={<div>Error, please try again later</div>}>
                {installmentDetail => (
                  <CardProfileDescription
                    descriptions={[
                      {
                        label: tPayment('TASKER_NAME'),
                        value: installmentDetail?.tasker?.name || '',
                      },
                      {
                        label: tPayment('TASKER_PHONE'),
                        value: getFormattedPhoneNumber(
                          installmentDetail?.tasker?.phone ?? '',
                        ),
                      },
                      {
                        label: tPayment('SERVICE'),
                        value: (
                          <div className="flex flex-wrap gap-1">
                            {service?.map((taskerService, index) => (
                              <span key={index} className="whitespace-nowrap">
                                {
                                  taskerService?.text?.[
                                    i18n.language ?? BTASKEE_LANGUAGE.EN
                                  ]
                                }
                                {taskerService?.isSubscription
                                  ? ' (Subscription)'
                                  : ''}
                                {index < service.length - 1 ? ',' : ''}
                              </span>
                            ))}
                          </div>
                        ),
                      },
                      {
                        label: tPayment('REGION'),
                        value:
                          loaderData?.installmentDetail?.tasker?.city ?? '',
                      },
                      {
                        label: tPayment('CREATED_DATE'),
                        value: installmentDetail?.createdAt ? (
                          <Typography variant="p">
                            {format(
                              installmentDetail.createdAt,
                              'HH:mm - dd/MM/yyyy',
                            ) || ''}
                          </Typography>
                        ) : null,
                      },
                      {
                        label: tPayment('DUE_DATE'),
                        value: installmentDetail?.expiredAt ? (
                          <Typography variant="p">
                            {format(
                              installmentDetail.expiredAt,
                              'HH:mm - dd/MM/yyyy',
                            ) || ''}
                          </Typography>
                        ) : null,
                      },
                      {
                        label: tPayment('INSTALLMENT_AMOUNT'),
                        value: `${formatNumberWithCommas(
                          installmentDetail?.amount ?? 0,
                        )}${loaderData?.settingCountry?.currency?.sign ?? ''}`,
                      },
                      {
                        label: tPayment('PAID_AMOUNT'),
                        value: `${formatNumberWithCommas(
                          (installmentDetail?.amount ?? 0) -
                            (installmentDetail?.remainingAmount ?? 0),
                        )}${loaderData?.settingCountry?.currency?.sign ?? ''}`,
                      },
                      {
                        label: tPayment('REMAINING_AMOUNT'),
                        value: `${formatNumberWithCommas(
                          installmentDetail?.remainingAmount ?? 0,
                        )}${loaderData?.settingCountry?.currency?.sign ?? ''}`,
                      },
                      {
                        label: tPayment('STATUS'),
                        value: (
                          <StatusBadge
                            statusClasses={{
                              PAYING:
                                'bg-blue-50 text-blue-500 rounded-md text-center',
                              DONE: 'bg-green-50 text-green-500 rounded-md text-center',
                              OVER_DUE:
                                'bg-yellow-50 text-yellow-500 rounded-md text-center',
                              STOPPED:
                                'bg-red-50 text-red-500 rounded-md text-center',
                            }}
                            status={installmentDetail?.status ?? ''}
                          />
                        ),
                      },
                    ]}
                  />
                )}
              </Await>
            </Suspense>
          </CardContent>
        </Card>
        <Card className="bg-gray-50">
          <CardHeader>
            <Typography variant="h4">{tPayment('PAYMENT_DETAIL')}</Typography>
            <Separator className="w-[200px]" />
          </CardHeader>
          <CardContent>
            <Suspense fallback={<div>Loading...</div>}>
              <Await
                resolve={loaderData?.installmentTransaction}
                errorElement={<div>Error, please try again later</div>}>
                {installmentProcess => (
                  <DataTableBasic columns={columns} data={installmentProcess} />
                )}
              </Await>
            </Suspense>
          </CardContent>
        </Card>
      </div>
      <Outlet
        context={{
          open: openDeductPaymentDialog,
          currency: loaderData?.settingCountry?.currency ?? {},
          onOpenChange: setOpenDeductPaymentDialog,
          installmentDetail: loaderData?.installmentDetail ?? {},
        }}
      />
    </>
  );
}
