/* eslint-disable react-hooks/exhaustive-deps */
import { zodResolver } from '@hookform/resolvers/zod';
import type {
  LoaderFunctionArgs,
  SerializeFrom,
  UploadHandler,
} from '@remix-run/node';
import {
  json,
  redirect,
  unstable_composeUploadHandlers,
  unstable_createMemoryUploadHandler,
  unstable_parseMultipartFormData,
} from '@remix-run/node';
import {
  isRouteErrorResponse,
  useActionData,
  useLoaderData,
  useNavigate,
  useRouteError,
  useSubmit,
} from '@remix-run/react';
import type { ColumnDef } from '@tanstack/react-table';
import {
  ACTION_NAME,
  MAXIMUM_IMAGE_FILE_LENGTH_TOOLKIT,
  PERMISSIONS,
  ROUTE_NAME,
} from 'btaskee-constants';
import {
  AspectRatio,
  Breadcrumbs,
  BreadcrumbsLink,
  BtaskeeResponseError,
  Button,
  DataTableBasic,
  DataTableColumnHeader,
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
  Grid,
  Input,
  MultiLanguageSection,
  MultiLanguageText,
  MultiSelectAdvance,
  Separator,
  Typography,
  toast,
  useConfirm,
} from 'btaskee-ui';
import { formatNumberWithCommas, momentTz } from 'btaskee-utils';
import i18n from 'i18next';
import { Plus, Trash2 } from 'lucide-react';
import { useEffect, useMemo, useState } from 'react';
import { useForm } from 'react-hook-form';
import { useTranslation } from 'react-i18next';
import { z } from 'zod';
import AddToolkitItemDialog from '~/components/ToolkitComponent';
import { CalculateFakeInput } from '~/components/common/CommonComponent';
import { DashedBorderButton } from '~/components/form/DashedBorderButton';
import { MediaUploaderCard } from '~/components/form/MediaUploaderCard';
import { SimpleImageUpload } from '~/components/form/SimpleImageUpload';
import { hocAction, hocLoader } from '~/hoc/remix';
import i18next from '~/i18next.server';
import {
  getSettingCountryByIsoCode,
  getUserSession,
} from '~/services/helpers.server';
import { getServices } from '~/services/service.server';
import { commitSession, getSession } from '~/services/session.server';
import {
  getListToolkitItems,
  getToolkitSettingDetail,
  updateToolkitSetting,
} from '~/services/toolkit.server';
import { s3UploadHandler } from '~/third-party/s3.server';

export const handle = {
  breadcrumb: (data: { toolkitSettingId: string }) => {
    return (
      <BreadcrumbsLink
        to={`${ROUTE_NAME.TOOLKIT_SETTING}/${data.toolkitSettingId}/edit`}
        label="UPDATE_TOOLKIT"
      />
    );
  },
  i18n: 'toolkit',
};

export function ErrorBoundary() {
  const error = useRouteError() as { message?: string };
  const { t: tCommon } = useTranslation('common');

  if (isRouteErrorResponse(error)) {
    if (error?.message) {
      toast({ description: JSON.stringify(error.message) });
    }

    return <BtaskeeResponseError t={tCommon} errorStatus={error.status} />;
  }

  return <BtaskeeResponseError t={tCommon} errorStatus={500} />;
}

export const loader = hocLoader(
  async ({ request, params }: LoaderFunctionArgs) => {
    const { isoCode } = await getUserSession({
      headers: request.headers,
    });

    const [toolkitSetting, tableData, settingCountry, listAllServices] =
      await Promise.all([
        getToolkitSettingDetail({ isoCode, toolkitSettingId: params.id || '' }),
        getListToolkitItems({ isoCode }),
        getSettingCountryByIsoCode({ isoCode, projection: { currency: 1 } }),
        getServices({
          isoCode,
          projection: {
            _id: 1,
            name: 1,
            text: 1,
            isSubscription: 1,
          },
        }),
      ]);

    return json({
      tableData: tableData || [],
      listAllServices: listAllServices || [],
      settingCountry: settingCountry,
      toolkitSetting,
      toolkitSettingId: params.id || '',
    });
  },
  PERMISSIONS.WRITE_TOOLKIT_SETTING,
);

export const action = hocAction(
  async ({ request, params }, { setInformationActionHistory }) => {
    //TODO: Move upload handler to a common place
    const uploadHandler: UploadHandler = unstable_composeUploadHandlers(
      async formField => {
        if (formField.name === 'image') {
          return await s3UploadHandler(formField);
        }

        return undefined;
      },
      unstable_createMemoryUploadHandler(),
    );
    const formData = await unstable_parseMultipartFormData(
      request.clone(),
      uploadHandler,
    );
    const parsedData = JSON.parse(formData.get('data')?.toString() || '{}');
    const uploadedImageUrl = formData.get('image')?.toString() || '';

    const { isoCode } = await getUserSession({ headers: request.headers });
    await updateToolkitSetting({
      isoCode,
      toolkitSettingId: params.id || '',
      updateInfos: {
        ...parsedData,
        ...(uploadedImageUrl ? { image: uploadedImageUrl } : {}),
        updatedAt: momentTz().toDate(),
      },
    });
    setInformationActionHistory({
      action: ACTION_NAME.UPDATE_TOOLKIT,
    });

    const session = await getSession(request.headers.get('cookie'));
    const t = await i18next.getFixedT(request, 'toolkit');
    session.flash('flashMessage', t('UPDATE_TOOLKIT_SUCCESS'));
    const newSession = await commitSession(session);

    return redirect(`${ROUTE_NAME.TOOLKIT_SETTING}/${params.id}`, {
      headers: {
        'Set-Cookie': newSession,
      },
    });
  },
  PERMISSIONS.WRITE_TOOLKIT_SETTING,
);

export default function EditToolkitSetting() {
  const { t } = useTranslation('toolkit');

  const confirm = useConfirm();
  const submit = useSubmit();
  const navigate = useNavigate();

  const loaderData = useLoaderData<LoaderTypeWithError<typeof loader>>();
  const actionData = useActionData<ActionTypeWithError<typeof action>>();

  useEffect(() => {
    if (actionData?.error) {
      toast({
        description: actionData.error,
      });
    }
  }, [actionData?.error]);

  const [openDialog, setOpenDialog] = useState(false);
  //TODO: Refactor state initialization
  const [draftSelectedToolkitItems, setDraftSelectedToolkitItems] = useState<
    SerializeFrom<ReturnValueIgnorePromise<typeof getListToolkitItems>[0]>[]
  >(
    loaderData.toolkitSetting?.toolKitItems?.map(item => ({
      ...item,
      image: loaderData.tableData?.find(i => i._id === item._id)?.image || '',
      price: loaderData.tableData?.find(i => i._id === item._id)?.price || 0,
      text: loaderData.tableData?.find(i => i._id === item._id)?.text || {},
      createdAt:
        loaderData.tableData?.find(i => i._id === item._id)?.createdAt || '',
    })) || [],
  );
  const [selectedToolkitItems, setSelectedToolkitItems] = useState<
    SerializeFrom<ReturnValueIgnorePromise<typeof getListToolkitItems>[0]>[]
  >(
    loaderData.toolkitSetting?.toolKitItems?.map(item => ({
      ...item,
      image: loaderData.tableData?.find(i => i._id === item._id)?.image || '',
      price: loaderData.tableData?.find(i => i._id === item._id)?.price || 0,
      text: loaderData.tableData?.find(i => i._id === item._id)?.text || {},
      createdAt:
        loaderData.tableData?.find(i => i._id === item._id)?.createdAt || '',
    })) || [],
  );
  const [isErrorListToolkitItem, setIsErrorListToolkitItem] = useState(false);
  const [totalPrice, setTotalPrice] = useState(0);
  const [discountAmount, setDiscountAmount] = useState(0);
  const [firstPayMoney, setFirstPayMoney] = useState(0);
  const [moneyBNPLOnTask, setMoneyBNPLOnTask] = useState(0);

  const schema = z.object({
    image: z
      .any()
      .refine(val => val !== null, { message: 'Image is required' }),
    applicableServices: z
      .array(z.any())
      .nonempty({ message: 'At least one applicable service is required' }),
    toolkitName: z.record(
      z.string().min(1, { message: 'Toolkit name is required' }),
    ),
    discountForOncePay: z.coerce.number().min(0, {
      message: 'Discount for once pay must be a non-negative number',
    }),
    percentForFirstPay: z.coerce.number().min(0, {
      message: 'Percent for first pay must be a non-negative number',
    }),
    percentBNPLOnTask: z.coerce.number().min(0, {
      message: 'Percent BNPL on task must be a non-negative number',
    }),
    period: z.coerce
      .number()
      .int()
      .positive({ message: 'Pay later period must be at least 1' }),
  });

  const form = useForm({
    resolver: zodResolver(schema),
    defaultValues: {
      image: loaderData.toolkitSetting?.image || null,
      applicableServices:
        loaderData.toolkitSetting?.serviceIds?.map(service => ({
          _id: service,
          name:
            loaderData.listAllServices?.find(s => s._id === service)?.name ||
            '',
        })) || [],
      toolkitName: loaderData.toolkitSetting?.text || {},
      //NOTE: Data schema is decimal but the UI is integer, so we need to multiply by 100 when getting to show on UI
      discountForOncePay:
        (loaderData.toolkitSetting?.discountForOncePay || 0) * 100,
      percentForFirstPay:
        (loaderData.toolkitSetting?.BNPLSetting?.firstPayPercent || 0) * 100,
      percentBNPLOnTask:
        (loaderData.toolkitSetting?.BNPLSetting?.percentBNPLOnTask || 0) * 100,
      period: loaderData.toolkitSetting.BNPLSetting?.period || 0,
    },
  });

  const { control, handleSubmit } = form;
  const discountForOncePayValue = form.watch('discountForOncePay') || 0;
  const percentForFirstPay = form.watch('percentForFirstPay') || 0;
  const percentBNPLOnTask = form.watch('percentBNPLOnTask') || 0;

  useEffect(() => {
    // Calculate total price when selected items change
    const newTotalPrice = selectedToolkitItems.reduce(
      (sum: number, item: MustBeAny) => sum + (Number(item.price) || 0),
      0,
    );
    setTotalPrice(newTotalPrice);
  }, [selectedToolkitItems]);

  useEffect(() => {
    // Calculate discount amount based on discountForOncePay value from form and totalPrice
    const calculatedDiscount = (discountForOncePayValue / 100) * totalPrice;
    setDiscountAmount(calculatedDiscount);
  }, [discountForOncePayValue, totalPrice]);

  useEffect(() => {
    // Calculate firstPayMoney and moneyBNPLOnTask
    const calculatedFirstPayMoney = (percentForFirstPay / 100) * totalPrice;
    const calculatedMoneyBNPLOnTask =
      (percentBNPLOnTask / 100) * (totalPrice - calculatedFirstPayMoney);

    setFirstPayMoney(calculatedFirstPayMoney);
    setMoneyBNPLOnTask(calculatedMoneyBNPLOnTask);
  }, [percentForFirstPay, percentBNPLOnTask, totalPrice]);

  //Update selected toolkit items error
  useEffect(() => {
    if (form.formState.isSubmitted) {
      if (selectedToolkitItems.length === 0) {
        setIsErrorListToolkitItem(true);
      } else {
        setIsErrorListToolkitItem(false);
      }
    }
  }, [selectedToolkitItems, form.formState.isSubmitted]);

  const columns: ColumnDef<ToolKitItem>[] = useMemo(
    () => [
      {
        accessorKey: 'order',
        header: ({ column }) => (
          <DataTableColumnHeader column={column} title={t('NO')} />
        ),
        cell: ({ row }) => <span>{row?.index + 1}</span>,
        size: 20,
        enableSorting: false,
      },
      {
        accessorKey: 'imageCover',
        header: ({ column }) => (
          <DataTableColumnHeader column={column} title={t('IMAGE_COVER')} />
        ),
        cell: ({ row }) => (
          <div className="w-[54px]">
            <AspectRatio ratio={1 / 1}>
              <img
                src={row?.original?.image || ''}
                alt="Toolkit"
                className="rounded-sm object-cover w-full h-full"
              />
            </AspectRatio>
          </div>
        ),
        size: 20,
        enableSorting: false,
      },
      {
        accessorKey: 'toolkitName',
        header: ({ column }) => (
          <DataTableColumnHeader column={column} title={t('TOOLKIT_NAME')} />
        ),
        cell: ({ row }) => (
          <MultiLanguageText text={row?.original?.text} layout="row" />
        ),
        size: 20,
        enableSorting: false,
      },
      {
        accessorKey: 'price',
        header: ({ column }) => (
          <DataTableColumnHeader column={column} title={t('PRICE')} />
        ),
        cell: ({ row }) => (
          <span>
            {formatNumberWithCommas(row?.original?.price || 0)}
            {loaderData.settingCountry?.currency?.sign}
          </span>
        ),
        size: 20,
        enableSorting: false,
      },
      {
        accessorKey: 'action',
        header: ({ column }) => (
          <DataTableColumnHeader column={column} title={t('ACTION')} />
        ),
        cell: ({ row }) => (
          <Button
            type="button"
            variant="ghost"
            className="text-gray-500 hover:text-gray-600 hover:bg-gray-200 gap-2 h-6 py-0.5"
            onClick={() => {
              const updatedSelectedToolkitItems = selectedToolkitItems.filter(
                (item: MustBeAny) => item._id !== row.original._id,
              );
              const updatedDraftSelectedToolkitItems =
                draftSelectedToolkitItems.filter(
                  (item: MustBeAny) => item._id !== row.original._id,
                );

              setSelectedToolkitItems(updatedSelectedToolkitItems);
              setDraftSelectedToolkitItems(updatedDraftSelectedToolkitItems);
            }}>
            <Trash2 className="w-4 h-4" />
            {t('REMOVE')}
          </Button>
        ),
        size: 20,
        enableSorting: false,
      },
    ],
    [
      draftSelectedToolkitItems,
      loaderData.settingCountry?.currency?.sign,
      selectedToolkitItems,
      t,
    ],
  );

  const onSubmit = async (data: MustBeAny) => {
    if (selectedToolkitItems.length === 0) return;
    if (
      await confirm({
        title: t('CONFIRM_UPDATE_TOOLKIT'),
        body: t('CONFIRM_UPDATE_TOOLKIT_DESCRIPTION'),
      })
    ) {
      const formData = new FormData();
      if (data.image instanceof Blob) {
        formData.append('image', data.image);
      }
      const submitData = {
        text: data.toolkitName,
        serviceIds: data.applicableServices.map(
          (service: { _id: string }) => service._id,
        ),
        toolKitItems: selectedToolkitItems.reduce<ToolkitItemWithQuantity[]>(
          (acc, item: MustBeAny) => {
            const existingItem = acc.find(accItem => accItem._id === item._id);
            if (existingItem) {
              existingItem.quantity += 1;
            } else {
              acc.push({ _id: item._id, quantity: 1 });
            }
            return acc;
          },
          [],
        ),
        discountForOncePay: Number(data.discountForOncePay) / 100,
        BNPLSetting: {
          firstPayPercent: Number(data.percentForFirstPay) / 100,
          percentBNPLOnTask: Number(data.percentBNPLOnTask) / 100,
          period: Number(data.period),
        },
      };

      formData.append('data', JSON.stringify(submitData));

      submit(formData, { method: 'post', encType: 'multipart/form-data' });
    }
  };

  return (
    <>
      <div className="flex bg-secondary p-4 justify-between items-center min-h-24 rounded-md mb-6">
        <Grid className="gap-3">
          <Typography className="capitalize" variant="h2">
            {t('UPDATE_TOOLKIT')}
          </Typography>
          <Breadcrumbs />
        </Grid>
      </div>
      <Form {...form}>
        <form
          onSubmit={handleSubmit(onSubmit)}
          encType={'multipart/form-data'}
          className={'flex flex-col gap-8 !mt-6'}>
          <section className={'flex flex-col gap-4'}>
            <Typography variant="h4" className="mb-4 mt-6">
              {t('GENERAL')}
            </Typography>
            <div className="grid grid-cols-3 gap-6 items-start">
              <MediaUploaderCard isShowCloseButton={false}>
                <FormField
                  control={control}
                  name="image"
                  render={({ field: { onChange, ref }, fieldState }) => (
                    <FormItem
                      className={`flex flex-col items-center h-fit ${fieldState.error ? 'border-red-500' : 'border-gray-200'}`}>
                      <FormControl>
                        <SimpleImageUpload
                          fieldRef={ref}
                          cardTitle={t('UPLOAD_IMAGE_COVER')}
                          onFileChange={file => onChange(file)}
                          maxContentLength={
                            MAXIMUM_IMAGE_FILE_LENGTH_TOOLKIT.VALUE
                          }
                          avatarUrl={loaderData.toolkitSetting?.image}
                          description={t('IMAGE_MAX_SIZE', {
                            size: MAXIMUM_IMAGE_FILE_LENGTH_TOOLKIT.DISPLAY_TEXT,
                          })}
                          subDescription={t('TOOLKIT_IMAGE_ASPECT_RATIO')}
                          formValue={form.watch('image')}
                        />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
              </MediaUploaderCard>
              <div className="flex flex-col gap-4">
                <FormField
                  name="applicableServices"
                  control={control}
                  render={({ field: { onChange, value } }) => (
                    <FormItem>
                      <FormLabel className="text-gray-700">
                        {t('APPLICABLE_SERVICE')}
                      </FormLabel>
                      <FormControl>
                        <MultiSelectAdvance
                          placeholder={t('SELECT_APPLICABLE_SERVICE')}
                          onValueChange={selectedValues => {
                            const formattedValues = selectedValues
                              ?.map(value => {
                                const service =
                                  loaderData.listAllServices?.find(
                                    service => service._id === value,
                                  );
                                return service
                                  ? {
                                      _id: service._id,
                                      name: `${service?.text?.[i18n.language || 'en']}${
                                        service?.isSubscription
                                          ? ' (Subscription)'
                                          : ''
                                      }`,
                                    }
                                  : null;
                              })
                              .filter(Boolean);
                            onChange(formattedValues);
                          }}
                          defaultValue={
                            value && value.length
                              ? value.map(service => service._id)
                              : []
                          }
                          options={loaderData.listAllServices?.map(service => ({
                            label: `${service?.text?.[i18n.language || 'en']}${
                              service?.isSubscription ? ' (Subscription)' : ''
                            }`,
                            value: service._id,
                          }))}
                          variant="blue"
                          maxCount={2}
                        />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <FormItem>
                  <FormLabel>{t('PRICE')}</FormLabel>
                  <CalculateFakeInput
                    value={formatNumberWithCommas(
                      Number(totalPrice.toFixed(0)),
                    )}
                    currency={loaderData.settingCountry?.currency?.code || ''}
                  />
                </FormItem>
              </div>

              <MultiLanguageSection
                form={form}
                childrenProps={[
                  {
                    name: 'toolkitName',
                    label: t('TOOLKIT_NAME'),
                    required: t('REQUIRED'),
                  },
                ]}>
                {[<Input key="toolkitName" name="toolkitName" />]}
              </MultiLanguageSection>
            </div>
          </section>

          <Separator />

          <section className="flex flex-col gap-4">
            <div className="flex justify-between items-center">
              <Typography variant="h4">{t('TOOLKIT_ITEM')}</Typography>
              {selectedToolkitItems.length > 0 && (
                <Button
                  variant="outline"
                  type="button"
                  className="flex items-center gap-2 border border-primary text-primary hover:text-primary"
                  onClick={() => setOpenDialog(true)}>
                  <Plus /> {t('ADD_TOOLKIT')}
                </Button>
              )}
            </div>

            {selectedToolkitItems.length === 0 ? (
              <DashedBorderButton
                className={`${isErrorListToolkitItem ? 'border-red-500 bg-red-50' : ''}`}
                label={t('ADD_TOOLKIT')}
                onClick={() => setOpenDialog(true)}
              />
            ) : (
              <DataTableBasic
                manualPagination
                isDisplayPagination={false}
                columns={columns as MustBeAny}
                data={selectedToolkitItems}
              />
            )}
            {isErrorListToolkitItem && (
              <FormMessage className="text-red-500 text-right">
                {t('REQUIRED_TOOLKIT_ITEM')}
              </FormMessage>
            )}
          </section>

          <Separator />

          <section className="flex flex-col gap-6">
            <Typography variant="h4">{t('PAYMENT_DETAIL')}</Typography>

            <div className="grid grid-cols-2 gap-6">
              {/* Pay Now Card */}
              <div className="border rounded-md h-fit overflow-hidden">
                <Typography
                  variant="p"
                  affects={'removePMargin'}
                  className="p-4 text-gray-600 bg-gray-100 font-medium text-base leading-tight">
                  {t('PAY_NOW')}
                </Typography>
                <div className="grid grid-cols-2 gap-6 pt-4 pb-6 px-6">
                  <FormField
                    control={control}
                    name="discountForOncePay"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>{t('DISCOUNT_FOR_ONCE_PAY')}</FormLabel>
                        <FormControl>
                          <Input
                            {...field}
                            type="number"
                            endAdornment={
                              <span className="text-gray-600">%</span>
                            }
                            onChange={field.onChange}
                          />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                  <FormItem>
                    <FormLabel>{t('MONEY_DISCOUNT_FOR_ONCE_PAY')}</FormLabel>
                    <CalculateFakeInput
                      value={formatNumberWithCommas(
                        Number(discountAmount.toFixed(0)),
                      )}
                      currency={loaderData.settingCountry?.currency?.code || ''}
                    />
                  </FormItem>
                </div>
              </div>

              {/* Pay Later Card */}
              <div className="border rounded-md h-fit overflow-hidden">
                <Typography
                  variant="p"
                  affects={'removePMargin'}
                  className="p-4 text-gray-600 bg-gray-100 font-medium text-base leading-tight">
                  {t('PAY_LATER')}
                </Typography>
                <div className="grid grid-cols-2 gap-6 pt-4 pb-6 px-6">
                  <FormField
                    control={control}
                    name="percentForFirstPay"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>{t('PERCENT_FOR_FIRST_PAY')}</FormLabel>
                        <FormControl>
                          <Input
                            {...field}
                            type="number"
                            endAdornment={
                              <span className="text-gray-600">%</span>
                            }
                          />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                  <FormItem>
                    <FormLabel>{t('FIRST_PAY_MONEY')}</FormLabel>
                    <CalculateFakeInput
                      value={formatNumberWithCommas(
                        Number(firstPayMoney.toFixed(0)),
                      )}
                      currency={loaderData.settingCountry?.currency?.code || ''}
                    />
                  </FormItem>
                  <FormField
                    control={control}
                    name="percentBNPLOnTask"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>{t('PERCENT_BNPL_ON_TASK')}</FormLabel>
                        <FormControl>
                          <Input
                            {...field}
                            type="number"
                            endAdornment={
                              <span className="text-gray-600">%</span>
                            }
                          />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                  <FormItem>
                    <FormLabel>{t('MONEY_BNPL_ON_TASK')}</FormLabel>
                    <FormControl>
                      <CalculateFakeInput
                        value={formatNumberWithCommas(
                          Number(moneyBNPLOnTask.toFixed(0)),
                        )}
                        currency={
                          loaderData.settingCountry?.currency?.code || ''
                        }
                      />
                    </FormControl>
                  </FormItem>
                  <FormField
                    control={control}
                    name="period"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>{t('PERIOD')}</FormLabel>
                        <FormControl>
                          <Input {...field} type="number" />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                </div>
              </div>
            </div>
          </section>

          <div className="flex justify-end gap-4">
            <Button
              type="button"
              variant="outline"
              className="border-primary text-primary hover:text-primary"
              onClick={() => navigate(-1)}>
              {t('CANCEL')}
            </Button>
            <Button
              type="submit"
              className="bg-primary hover:bg-primary-600"
              onClick={() => {
                setIsErrorListToolkitItem(selectedToolkitItems.length === 0);
              }}>
              {t('UPDATE')}
            </Button>
          </div>
        </form>
      </Form>

      <AddToolkitItemDialog
        t={t}
        data={loaderData.tableData}
        open={openDialog}
        setOpen={setOpenDialog}
        selectedToolkitItems={selectedToolkitItems}
        setSelectedToolkitItems={setSelectedToolkitItems}
        draftSelectedToolkitItems={draftSelectedToolkitItems}
        setDraftSelectedToolkitItems={setDraftSelectedToolkitItems}
        currencySign={loaderData.settingCountry?.currency?.sign || ''}
      />
    </>
  );
}
