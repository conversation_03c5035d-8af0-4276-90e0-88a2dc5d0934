import { json } from '@remix-run/node';
import {
  isRouteErrorResponse,
  useActionData,
  useRouteError,
} from '@remix-run/react';
import { PERMISSIONS, ROUTE_NAME } from 'btaskee-constants';
import {
  Breadcrumbs,
  BreadcrumbsLink,
  BtaskeeResponseError,
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
  Input,
  Separator,
  Switch,
  Typography,
  toast,
  useBtaskeeFormController,
} from 'btaskee-ui';
import { createUID } from 'btaskee-utils';
import { useEffect, useState } from 'react';
import { useFieldArray } from 'react-hook-form';
import { useTranslation } from 'react-i18next';
import { z } from 'zod';
import { DashedBorderButton } from '~/components/form/DashedBorderButton';
import { FormFooterQuiz } from '~/components/form/TrainingComponents';
import { AnswerCard } from '~/components/form/cards/AnswerCard';
import ImageUploaderCard from '~/components/form/cards/ImageUploaderCard';
import { hocAction, hocLoader } from '~/hoc/remix';
import {
  zodAnswersSchema,
  zodCodeSchema,
  zodImageWithDescriptionSchema,
} from '~/schemas/zodSchema';
import { handleQuizAction } from '~/utils/tasker-training/quizAction';

export const handle = {
  breadcrumb: () => {
    return (
      <BreadcrumbsLink
        to={ROUTE_NAME.CREATE_QUIZ}
        label="CREATE_TRAINING_QUIZ"
      />
    );
  },
};

export function ErrorBoundary() {
  const error = useRouteError() as { message?: string };
  const { t: tCommon } = useTranslation('common');

  if (isRouteErrorResponse(error)) {
    if (error?.message) {
      toast({ description: JSON.stringify(error.message) });
    }

    return <BtaskeeResponseError t={tCommon} errorStatus={error.status} />;
  }

  return <BtaskeeResponseError t={tCommon} errorStatus={500} />;
}

export const loader = hocLoader(
  async () => json({}),
  PERMISSIONS.WRITE_TRAINING_QUIZ,
);

export const action = hocAction(
  async ({ request }, { setInformationActionHistory }) => {
    return handleQuizAction({
      request,
      isEdit: false,
      setInformationActionHistory,
    });
  },
  PERMISSIONS.WRITE_TRAINING_QUIZ,
);

export default function CreateQuiz() {
  const { t } = useTranslation('quiz');

  const actionData = useActionData<ActionTypeWithError<typeof action>>();
  useEffect(() => {
    if (actionData?.error) {
      toast({
        description: actionData.error,
      });
    }
  }, [actionData]);

  const [isShowImage, setIsShowImage] = useState(false);
  const [selectedCorrectAnswer, setSelectedCorrectAnswer] =
    useState<string>('');

  const { form, onSubmit } = useBtaskeeFormController<FormQuiz>({
    zodRaw: {
      code: zodCodeSchema(t),
      name: z.string().min(3, t('MINIMUM_CHARACTERS', { number: 3 })),
      isRandomAnswer: z.boolean(),
      answers: zodAnswersSchema(t),
      image: zodImageWithDescriptionSchema(t).newImage,
    },
    defaultValues: {
      code: '',
      name: '',
      isRandomAnswer: false,
      answers: [
        {
          _id: createUID(),
          content: '',
          isCorrect: false,
        },
        {
          _id: createUID(),
          content: '',
          isCorrect: false,
        },
      ],
      image: null,
    },
    confirmParams: {
      title: t('CONFIRM'),
      body: t('ARE_YOU_SURE_YOUR_INFORMATION_IS_CORRECT'),
      actionButton: t('SUBMIT'),
      cancelButton: t('CANCEL'),
    },
    formDataProvided: data => {
      const formData = new FormData();

      if (data.image?.value) {
        formData.append('image', data.image.value);
      }

      formData.append('data', JSON.stringify(data));

      return formData;
    },
  });
  const { control, setValue, handleSubmit, clearErrors } = form;
  const { fields, append, remove } = useFieldArray({
    control,
    name: 'answers',
  });

  useEffect(() => {
    if (selectedCorrectAnswer) {
      clearErrors('answers');

      fields.forEach((field, index) => {
        if (
          field._id === selectedCorrectAnswer ||
          field.id === selectedCorrectAnswer
        ) {
          setValue(`answers.${index}.isCorrect`, true);
        } else {
          setValue(`answers.${index}.isCorrect`, false);
        }
      });
    }
  }, [clearErrors, fields, selectedCorrectAnswer, setValue]);

  return (
    <>
      <div className="flex bg-secondary p-4 justify-between align-middle rounded-md">
        <div className="grid space-y-2 rounded-xl">
          <div className="flex">
            <Typography
              className="capitalize"
              variant="h2"
              affects="removePMargin">
              {t('CREATE_QUIZ')}
            </Typography>
          </div>
          <Breadcrumbs className={'text-sm'} />
        </div>
      </div>

      <Form {...form}>
        <form
          onSubmit={handleSubmit(onSubmit)}
          encType={'multipart/form-data'}
          className={'flex flex-col gap-8 !mt-6'}>
          <section className={'flex flex-col gap-4'}>
            <Typography
              variant={'h4'}
              className={'text-xl tracking-tighter font-semibold'}>
              {t('QUESTION')}
            </Typography>
            <div className="grid grid-cols-2 gap-6">
              <FormField
                name={'code'}
                control={control}
                render={({ field }) => (
                  <FormItem>
                    <FormLabel className={'text-gray-700'}>
                      {t('QUIZ_CODE')}
                    </FormLabel>
                    <FormControl>
                      <Input {...field} placeholder={t('ENTER_QUIZ_CODE')} />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
              <FormField
                name={'name'}
                control={control}
                render={({ field }) => (
                  <FormItem>
                    <FormLabel className={'text-gray-700'}>
                      {t('QUESTION')}
                    </FormLabel>
                    <FormControl>
                      <Input {...field} placeholder={t('ENTER_QUESTION')} />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <div className="space-y-2 mt-2">
                <div className="flex justify-between items-center h-6">
                  <FormLabel className={'text-gray-700'}>
                    {t('IMAGE')}
                  </FormLabel>
                  <Typography
                    variant="p"
                    affects="removePMargin"
                    className="text-sm text-gray-400 leading-tight">
                    {t('OPTIONAL')}
                  </Typography>
                </div>

                {!isShowImage && (
                  <DashedBorderButton
                    className={'py-[23px]'}
                    label={t('ADD_IMAGE')}
                    onClick={() => {
                      setIsShowImage(true);
                      setValue('image', {
                        value: null,
                        description: '',
                      });
                    }}
                  />
                )}

                {isShowImage && (
                  <ImageUploaderCard
                    onClose={() => {
                      setIsShowImage(false);
                      setValue('image', null);
                    }}
                    form={form}
                  />
                )}
              </div>
            </div>

            <Separator className="my-2" />

            <div className="flex flex-col items-start gap-6 mb-2">
              <Typography
                variant={'h4'}
                className={'text-xl tracking-tighter font-semibold'}>
                {t('ANSWER')}
              </Typography>

              <FormField
                name={'isRandomAnswer'}
                control={control}
                render={({ field }) => (
                  <FormItem className="flex items-center gap-4">
                    <FormControl>
                      <Switch
                        checked={!!field.value}
                        onCheckedChange={field.onChange}
                      />
                    </FormControl>
                    <FormLabel className={'text-gray-700 !mt-0'}>
                      {t('RANDOM_ANSWER')}
                    </FormLabel>
                    <FormMessage />
                  </FormItem>
                )}
              />
            </div>

            <div className="max-h-[380px] overflow-y-auto">
              <div className="grid grid-cols-2 gap-6 items-start">
                {fields.map((field, index) => (
                  <AnswerCard
                    key={field.id}
                    fieldId={field.id}
                    control={control}
                    index={index}
                    remove={remove}
                    selectedCorrectAnswer={selectedCorrectAnswer}
                    setSelectedCorrectAnswer={setSelectedCorrectAnswer}
                  />
                ))}

                <DashedBorderButton
                  label={t('ADD_ANSWER')}
                  className="mt-0 py-[23px]"
                  onClick={() =>
                    append({
                      content: '',
                      isCorrect: false,
                      _id: createUID(),
                    })
                  }
                />
              </div>
            </div>

            <Separator className={'my-2'} />

            <FormFooterQuiz form={form} t={t} isEdit={false} />
          </section>
        </form>
      </Form>
    </>
  );
}
