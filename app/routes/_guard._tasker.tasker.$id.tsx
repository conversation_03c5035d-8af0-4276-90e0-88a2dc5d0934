import {
  Link,
  Outlet,
  isRouteErrorResponse,
  json,
  useLoaderData,
  useLocation,
  useRouteError,
} from '@remix-run/react';
import { PERMISSIONS, ROUTE_NAME } from 'btaskee-constants';
import { useCopyToClipboard, useGlobalStore } from 'btaskee-hooks';
import {
  Avatar,
  AvatarFallback,
  AvatarImage,
  Badge,
  BreadcrumbsLink,
  BtaskeeResponseError,
  Button,
  DropdownMenu,
  DropdownMenuTrigger,
  Label,
  Separator,
  StatusBadge,
  Typography,
  toast,
} from 'btaskee-ui';
import { CircleUserRound, Copy, CopyCheck, Phone } from 'lucide-react';
import { useMemo } from 'react';
import { useTranslation } from 'react-i18next';
import { CommentRatings } from '~/components/AGVRating';
import { InfoBlock, InfoCardWrapper } from '~/components/InformationCard';
import { ServiceIconWithTooltip } from '~/components/common/CommonComponent';
import { EditIcon } from '~/components/common/FigmaIcon';
import { hocLoader } from '~/hoc/remix';
import {
  getSettingCountryByIsoCode,
  getUserSession,
} from '~/services/helpers.server';
import { getTaskerDetail } from '~/services/tasker.server';

export const handle = {
  breadcrumb: ({ tasker }: { tasker: UserApp }) => {
    return (
      <BreadcrumbsLink
        to={`${ROUTE_NAME.TASKER}/${tasker._id}`}
        label="TASKER_DETAIL"
      />
    );
  },
  i18n: 'tasker',
};

export function ErrorBoundary() {
  const error = useRouteError() as { message?: string };
  const { t: tCommon } = useTranslation('common');

  if (isRouteErrorResponse(error)) {
    if (error?.message) {
      toast({ description: JSON.stringify(error.message) });
    }

    return <BtaskeeResponseError t={tCommon} errorStatus={error.status} />;
  }

  return <BtaskeeResponseError t={tCommon} errorStatus={500} />;
}

export const loader = hocLoader(async ({ request, params }) => {
  const { isoCode } = await getUserSession({ headers: request.headers });
  const taskerId = params.id || '';

  const [tasker, settingCountry] = await Promise.all([
    getTaskerDetail({
      isoCode,
      taskerId,
    }),
    getSettingCountryByIsoCode({
      isoCode,
      projection: {
        currency: 1,
      },
    }),
  ]);

  return json({
    tasker,
    settingCountry,
  });
}, PERMISSIONS.READ_TASKER_DETAIL);

export default function TaskerDetailScreen() {
  const { t } = useTranslation('tasker');
  const permissions = useGlobalStore(state => state.permissions);

  const location = useLocation();
  const isDetailPage = location.pathname.endsWith('/detail');

  const { isCopied: isPhoneCopied, copyToClipboard: copyPhoneToClipboard } =
    useCopyToClipboard();

  const loaderData = useLoaderData<LoaderTypeWithError<typeof loader>>();

  const NAV_TABS = useMemo(
    () => [
      {
        label: t('TRAINING_HISTORY'),
        link: ROUTE_NAME.TRAINING_HISTORY,
        isAccessible: permissions.includes(PERMISSIONS.READ_TRAINING_HISTORY),
      },
    ],
    [t, permissions],
  );

  return (
    <div className="flex flex-col gap-6">
      {!isDetailPage && (
        <>
          <div className="grid grid-cols-3 gap-6">
            <InfoCardWrapper>
              <div className="flex flex-col items-center justify-center">
                <Avatar className="w-[136px] h-[136px] mx-auto">
                  <AvatarImage
                    src={loaderData.tasker?.avatar || ''}
                    alt="tasker-avatar"
                  />
                  <AvatarFallback>
                    <CircleUserRound className="h-32 w-32" />
                  </AvatarFallback>
                </Avatar>

                <Typography
                  className="text-center text-gray-600 mt-4 mb-2"
                  variant="h4">
                  {loaderData.tasker?.name}
                </Typography>
                <StatusBadge
                  statusClasses={{
                    ACTIVE:
                      'text-secondary-foreground bg-secondary rounded-md text-center',
                    DISABLED:
                      'text-gray-500 bg-gray-100 rounded-md text-center',
                    INACTIVE:
                      'text-yellow-500 bg-yellow-50 rounded-md text-center',
                    IN_PROBATION:
                      'text-primary bg-primary-50 rounded-md text-center',
                    LOCKED: 'text-red-500 bg-red-50 rounded-md text-center',
                    UNVERIFIED:
                      'text-yellow-500 bg-yellow-50 rounded-md text-center',
                    UNLOCKED: 'text-blue-500 bg-blue-50 rounded-md text-center',
                    PASSED_INTERVIEW:
                      'text-blue-500 bg-blue-50 rounded-md text-center',
                  }}
                  status={loaderData.tasker?.status || 'ACTIVE'}
                />
                <div
                  className={`flex rounded-[8px] bg-white py-3 px-4 w-full my-3 ${loaderData?.tasker?.isPremiumTasker || loaderData?.tasker?.isEco ? 'justify-between' : 'justify-center'}`}>
                  <CommentRatings
                    rating={loaderData?.tasker?.avgRating || 0}
                    totalStars={5}
                    size={24}
                    variant="default"
                    disabled={true}
                  />
                  {loaderData?.tasker?.isEco ? (
                    <div className="py-1.5 px-3 bg-secondary text-secondary-foreground rounded-md text-sm">
                      {t('ECO_ASKER')}
                    </div>
                  ) : null}

                  {loaderData?.tasker?.isPremiumTasker ? (
                    <div className="py-1.5 px-3 bg-secondary text-secondary-foreground rounded-md text-sm">
                      {t('PREMIUM_TASKER')}
                    </div>
                  ) : null}
                </div>
                <Typography
                  className="text-center text-gray"
                  variant="p"
                  affects="removePMargin">
                  {t('TASK_DONE')}:{' '}
                  <span className="text-primary font-semibold text-sm">
                    {loaderData?.tasker?.taskDone}
                  </span>
                </Typography>
              </div>
            </InfoCardWrapper>

            <InfoCardWrapper className="col-span-2">
              <div className="flex flex-col gap-4">
                <div className="flex justify-between">
                  <div className="w-fit">
                    <Typography
                      className="pr-20 mb-3"
                      affects="large"
                      variant="h4">
                      {t('INFORMATION')}
                    </Typography>
                    <Separator />
                  </div>
                  <div className="flex gap-4 items-stretch">
                    <DropdownMenu>
                      <DropdownMenuTrigger asChild>
                        <Button
                          className="text-primary h-[unset] border-primary"
                          variant="outline"
                          type="button">
                          {t('OTHERS')}
                        </Button>
                        {/* TODO: Handle open dropdown */}
                      </DropdownMenuTrigger>
                    </DropdownMenu>
                    <Button className="h-[unset]" type="button">
                      {t('VIEW_DETAIL')}
                    </Button>
                  </div>
                </div>
              </div>
              <div className="grid grid-cols-2 gap-6">
                <InfoBlock
                  label={t('PHONE')}
                  value={
                    <div className="flex items-center gap-2">
                      <Typography className="text-lg font-semibold" variant="p">
                        {loaderData?.tasker?.phone}
                      </Typography>
                      <Button variant="ghost" size="icon">
                        <Phone color="green" />
                      </Button>
                      <Button
                        variant="ghost"
                        size="icon"
                        className={
                          isPhoneCopied ? 'text-green-500' : 'text-orange-500'
                        }
                        onClick={() =>
                          copyPhoneToClipboard(loaderData?.tasker?.phone || '')
                        }>
                        {isPhoneCopied ? <CopyCheck /> : <Copy />}
                      </Button>
                    </div>
                  }
                />
                <InfoBlock
                  label={t('ADDRESS')}
                  value={loaderData?.tasker?.address}
                />
                <InfoBlock
                  label={t('MAIN_ACCOUNT')}
                  value={`${loaderData?.tasker?.fMainAccountMoney?.toLocaleString()}${loaderData?.settingCountry?.currency?.sign}`}
                />
                {/* 3 country same response */}
                <InfoBlock
                  label={t('AVAILABLE_BALANCE')}
                  value={`${loaderData?.tasker?.taskerMoney?.FMainAccount?.toLocaleString()}${loaderData?.settingCountry?.currency?.sign}`}
                />
                <div className="grid gap-3">
                  <div className="flex items-center gap-2">
                    <Label className="text-sm font-normal text-gray-400">
                      {t('SERVICES')}
                    </Label>
                    <div className="flex items-center gap-1">
                      <EditIcon />
                      <Label className="text-sm font-normal text-primary">
                        {t('EDIT')}
                      </Label>
                    </div>
                  </div>
                  <div className="flex flex-wrap gap-2">
                    {loaderData?.tasker?.servicesOfTasker?.map(
                      (service: Service) => (
                        <ServiceIconWithTooltip
                          key={service._id}
                          service={service}
                        />
                      ),
                    )}
                  </div>
                </div>

                <div className="grid gap-3">
                  <div className="flex items-center gap-2">
                    <Label className="text-sm font-normal text-gray-400">
                      {t('WORKING_PLACES')}
                    </Label>
                    <div className="flex items-center gap-1">
                      <EditIcon />
                      <Label className="text-sm font-normal text-primary">
                        {t('EDIT')}
                      </Label>
                    </div>
                  </div>
                  <div className="flex flex-wrap gap-2">
                    {/* TODO: Fix correct type */}
                    {loaderData?.tasker?.workingPlaces?.map(
                      (place: MustBeAny, index: number) => (
                        <Badge
                          key={index}
                          className="text-sm text-blue bg-blue-50 rounded-[6px] font-normal">
                          {place?.district}
                        </Badge>
                      ),
                    )}
                  </div>
                </div>
              </div>
            </InfoCardWrapper>
          </div>

          <ul className="w-fit flex items-center gap-0.5 p-[5px] bg-gray-100 rounded-md">
            {NAV_TABS.filter(tab => tab.isAccessible).map((navTab, index) => (
              <li key={index}>
                <Link
                  className={`py-1.5 px-3 rounded-sm font-medium leading-tight text-sm ${location.pathname.includes(navTab.link) ? ' bg-white text-gray-800 shadow-md' : 'text-gray-400 hover:bg-white hover:text-gray-800'}`}
                  to={`${ROUTE_NAME.TASKER}/${loaderData?.tasker?._id}${navTab.link}`}>
                  {navTab.label}
                </Link>
              </li>
            ))}
          </ul>
        </>
      )}

      <Outlet context={loaderData} />
    </div>
  );
}
