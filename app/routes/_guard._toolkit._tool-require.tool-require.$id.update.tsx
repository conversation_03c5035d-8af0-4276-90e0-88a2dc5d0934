import type { UploadHandler } from '@remix-run/node';
import {
  json,
  redirect,
  unstable_composeUploadHandlers,
  unstable_createMemoryUploadHandler,
  unstable_parseMultipartFormData,
} from '@remix-run/node';
import {
  isRouteErrorResponse,
  useActionData,
  useLoaderData,
  useNavigate,
  useRouteError,
  useSubmit,
} from '@remix-run/react';
import { ACTION_NAME, PERMISSIONS, ROUTE_NAME } from 'btaskee-constants';
import {
  Breadcrumbs,
  BreadcrumbsLink,
  BtaskeeResponseError,
  Button,
  Form,
  Grid,
  Typography,
  toast,
  useConfirm,
} from 'btaskee-ui';
import { useEffect, useMemo } from 'react';
import { useTranslation } from 'react-i18next';
import { ToolRequireForm } from '~/components/tool-require/ToolRequireForm';
import { hocAction, hocLoader } from '~/hoc/remix';
import { useToolRequireForm } from '~/hooks/useToolRequireForm';
import i18next from '~/i18next.server';
import {
  getSettingCountryByIsoCode,
  getUserSession,
} from '~/services/helpers.server';
import { commitSession, getSession } from '~/services/session.server';
import {
  getToolItemDetail,
  updateToolItem,
} from '~/services/tool-require.server';
import { s3UploadHandler } from '~/third-party/s3.server';

export const handle = {
  breadcrumb: (data: { toolId: string }) => {
    return (
      <BreadcrumbsLink
        to={`${ROUTE_NAME.TOOL_REQUIRE}/${data.toolId}/update`}
        label="UPDATE_TOOL"
      />
    );
  },
};

export const loader = hocLoader(
  async ({ request, params }) => {
    const { isoCode } = await getUserSession({
      headers: request.headers,
    });

    const [toolItemDetail, settingCountry] = await Promise.all([
      getToolItemDetail({
        isoCode,
        toolkitItemId: params?.id || '',
      }),
      getSettingCountryByIsoCode({
        isoCode,
        projection: { currency: 1 },
      }),
    ]);

    return json({ toolItemDetail, settingCountry, toolId: params.id || '' });
  },
  [PERMISSIONS.WRITE_TOOL_REQUIRE],
);

export const action = hocAction(
  async ({ request, params }, { setInformationActionHistory }) => {
    // TODO: create a common function to handle upload image
    const uploadHandler: UploadHandler = unstable_composeUploadHandlers(
      async formField => {
        if (formField.name === 'toolImage') {
          return await s3UploadHandler(formField);
        }

        return undefined;
      },
      unstable_createMemoryUploadHandler(),
    );

    const formData = await unstable_parseMultipartFormData(
      request.clone(),
      uploadHandler,
    );

    const { isoCode } = await getUserSession({
      headers: request.headers,
    });

    updateToolItem({
      isoCode,
      toolkitItemId: params?.id || '',
      updateInfo: {
        ...JSON.parse(formData.get('toolkitItem')?.toString() || '{}'),
        image: formData.get('toolImage')?.toString(),
      },
    });

    setInformationActionHistory({
      action: ACTION_NAME.UPDATE_TOOL,
    });

    const session = await getSession(request.headers.get('cookie'));
    const t = await i18next.getFixedT(request, 'tool-require-update');
    session.flash('flashMessage', t('UPDATE_TOOL_SUCCESS'));
    const newSession = await commitSession(session);

    return redirect(ROUTE_NAME.TOOL_REQUIRE, {
      headers: {
        'Set-Cookie': newSession,
      },
    });
  },
  PERMISSIONS.WRITE_TOOL_REQUIRE,
);

export function ErrorBoundary() {
  const error = useRouteError() as { message?: string };
  const { t: tCommon } = useTranslation('common');

  if (isRouteErrorResponse(error)) {
    if (error?.message) {
      toast({ description: JSON.stringify(error.message) });
    }

    return <BtaskeeResponseError t={tCommon} errorStatus={error.status} />;
  }

  return <BtaskeeResponseError t={tCommon} errorStatus={500} />;
}

export default function UpdateTool() {
  const { t } = useTranslation('tool-require-update');

  const navigate = useNavigate();
  const confirm = useConfirm();
  const submit = useSubmit();

  const loaderData = useLoaderData<LoaderTypeWithError<typeof loader>>();
  const actionData = useActionData<ActionTypeWithError<typeof action>>();

  useEffect(() => {
    if (actionData?.error) {
      toast({
        description: actionData.error,
      });
    }
  }, [actionData, t]);

  const form = useToolRequireForm(loaderData?.toolItemDetail);

  const onSubmit = async (data: ToolRequireFormValue) => {
    const isConfirmSubmit = await confirm({
      title: t('CONFIRM'),
      body: t('ARE_YOU_SURE_INPUT_INFORMATION_IS_CORRECT'),
    });

    if (isConfirmSubmit) {
      const formData = new FormData();

      const submitData = {
        text: data.text,
        price: Number(data.priceChar.replaceAll(',', '')),
        image: data.image.value,
      };

      formData.append('toolkitItem', JSON.stringify(submitData));
      if (typeof data.image.value !== 'string') {
        formData.append('toolImage', data.image.value);
      }
      submit(formData, { method: 'put', encType: 'multipart/form-data' });
    }
  };

  const formEditValues = form.watch();

  const isFormChange = useMemo(() => {
    const listLanguageOfToolName = Object.keys(formEditValues.text) as Array<
      keyof typeof formEditValues.text
    >;

    const isLanguageUpdated = listLanguageOfToolName.some(
      language =>
        formEditValues.text[language] !==
        loaderData?.toolItemDetail?.text[language],
    );

    return (
      formEditValues.image.value !== loaderData?.toolItemDetail?.image ||
      Number(formEditValues.priceChar.replaceAll(',', '')) !==
        loaderData?.toolItemDetail?.price ||
      isLanguageUpdated
    );
  }, [
    loaderData?.toolItemDetail?.image,
    loaderData?.toolItemDetail?.price,
    loaderData?.toolItemDetail?.text,
    formEditValues,
  ]);

  return (
    <>
      <div className="flex bg-secondary p-4 justify-between items-center min-h-24 rounded-md mb-6">
        <Grid className="gap-3">
          <Typography variant="h2">{t('UPDATE_TOOL')}</Typography>
          <Breadcrumbs />
        </Grid>
      </div>
      <div>
        <Typography variant="h4">{t('GENERAL')}</Typography>
        <Form {...form}>
          <form
            onSubmit={form.handleSubmit(onSubmit)}
            className={'flex flex-col gap-8 !mt-6'}>
            <ToolRequireForm
              form={form}
              currencyCode={loaderData?.settingCountry?.currency?.code || 'VND'}
            />
            <div className="flex justify-end gap-4">
              <Button
                variant="outline"
                className="border-primary text-primary hover:text-primary"
                type="button"
                onClick={() => navigate(ROUTE_NAME.TOOL_REQUIRE)}>
                {t('CANCEL')}
              </Button>
              <Button disabled={!isFormChange}>{t('SAVE_CHANGE')}</Button>
            </div>
          </form>
        </Form>
      </div>
    </>
  );
}
