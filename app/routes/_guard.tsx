import { type LoaderFunctionArgs, json } from '@remix-run/node';
import {
  Form,
  Link,
  Outlet,
  useLoaderData,
  useLocation,
  useSubmit,
} from '@remix-run/react';
import {
  PERMISSIONS,
  ROUTE_NAME,
  TASKER_PROFILE_REJECT_STATUS,
} from 'btaskee-constants';
import { getEnvFeatureFlag } from 'btaskee-dotenv';
import {
  GlobalContext,
  type GlobalStore,
  createGlobalStore,
} from 'btaskee-hooks';
import {
  Avatar,
  AvatarFallback,
  AvatarImage,
  Button,
  Drawer,
  DrawerClose,
  DrawerContent,
  DrawerDescription,
  DrawerHeader,
  DrawerTitle,
  DrawerTrigger,
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
  Input,
  NavigatorDropdownCommon,
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
  Separator,
  TaskerOperationLogo,
} from 'btaskee-ui';
import { AlignJustify, UserCircle, XIcon } from 'lucide-react';
import { useMemo, useRef } from 'react';
import { useTranslation } from 'react-i18next';
import { authenticator } from '~/services/passports.server';
import { getUserPermissionIdsGlobal } from '~/services/role-base-access-control.server';
import { commitSession, getSession } from '~/services/session.server';
import { getUserProfile } from '~/services/settings.server';

export const loader = async ({ request }: LoaderFunctionArgs) => {
  const featureFlags = getEnvFeatureFlag();
  const user = await authenticator.isAuthenticated(request, {
    failureRedirect: ROUTE_NAME.SIGN_IN,
  });
  const [session, userPermissions, userProfile] = await Promise.all([
    getSession(request.headers.get('cookie')),
    getUserPermissionIdsGlobal(user.userId),
    getUserProfile(user.userId),
  ]);

  // get flash session
  session.get('flashMessage');

  return json(
    {
      user: { ...user, permissions: userPermissions },
      userProfile,
      featureFlags,
    },
    {
      headers: {
        'Set-Cookie': await commitSession(session), // You must commit the session whenever you read a flash
      },
    },
  );
};

export const handle = { i18n: 'common' };

export default function Screen() {
  const { user, userProfile, featureFlags } = useLoaderData<typeof loader>();

  const storeRef = useRef<GlobalStore>();
  if (!storeRef.current) {
    storeRef.current = createGlobalStore({ ...user, featureFlags });
  }
  const { t } = useTranslation('common');

  const { i18n } = useTranslation();
  const submit = useSubmit();

  const location = useLocation();

  const onSubmit = (language: string) => {
    const formData = new FormData();
    formData.append('language', language);
    formData.append('name', 'changeLanguage');
    formData.append('redirect', location.pathname);

    i18n.changeLanguage(language);
    submit(formData, {
      method: 'post',
      action: '/',
    });
  };

  const navigation = useMemo(
    () => [
      {
        title: 'REPORT',
        routes: [
          {
            permissions: [PERMISSIONS.READ_HISTORY_BREWARD],
            route: ROUTE_NAME.REPORT_HISTORY_BREWARD,
            title: 'HISTORY_BREWARD',
          },
        ],
      },
      {
        title: 'TASKER',
        routes: [
          {
            permissions: [PERMISSIONS.READ_TASKER_LIST],
            route: ROUTE_NAME.ALL_TASKER,
            title: 'ALL_TASKER',
          },
          {
            permissions: [PERMISSIONS.READ_TASKER_LOCATION_DETECTED],
            route: ROUTE_NAME.TASKER_LOCATION_DETECTED,
            title: 'TASKER_LOCATION_DETECTED',
          },
          {
            title: 'MARKETING',
            routes: [
              {
                permissions: [PERMISSIONS.READ_SPECIAL_CAMPAIGN],
                route: ROUTE_NAME.SPECIAL_CAMPAIGN,
                title: 'SPECIAL_CAMPAIGN',
              },
              {
                permissions: [PERMISSIONS.READ_SPECIAL_CAMPAIGN_TRANSACTION],
                route: ROUTE_NAME.SPECIAL_CAMPAIGN_TRANSACTION,
                title: 'SPECIAL_CAMPAIGN_TRANSACTION',
              },
            ],
          },
          {
            title: 'TRAINING',
            routes: [
              {
                permissions: [PERMISSIONS.READ_TRAINING_QUIZ],
                route: ROUTE_NAME.QUIZ,
                title: 'TRAINING_QUIZ',
              },
              {
                permissions: [PERMISSIONS.READ_TRAINING_QUIZ_COLLECTION],
                route: ROUTE_NAME.QUIZ_COLLECTION,
                title: 'TRAINING_QUIZ_COLLECTION',
              },
              {
                permissions: [PERMISSIONS.READ_TEST_AND_REVIEW],
                route: ROUTE_NAME.TEST_AND_REVIEW,
                title: 'TEST_AND_REVIEW',
              },
              {
                permissions: [PERMISSIONS.READ_TRAINING_HISTORY_OF_ALL_TASKER],
                route: ROUTE_NAME.TRAINING_HISTORY,
                title: 'TRAINING_HISTORY',
              },
            ],
          },
          {
            title: 'ONBOARDING',
            routes: [
              {
                permissions: [
                  PERMISSIONS.READ_VERIFY_IDENTITY_INFORMATION_FOR_TASKER,
                  PERMISSIONS.READ_APPROVED_IDENTITY_INFORMATION_FOR_TASKER,
                ],
                route: ROUTE_NAME.IDENTITY_INFORMATION_FOR_TASKER,
                title: 'IDENTITY_INFORMATION_FOR_TASKER',
              },
              {
                permissions: [
                  PERMISSIONS.READ_VERIFYING_STEP_ON_TASKER_ONBOARDING,
                  PERMISSIONS.READ_APPROVED_STEP_ON_TASKER_ONBOARDING,
                  PERMISSIONS.READ_ELIMINATED_STEP_ON_TASKER_ONBOARDING,
                  PERMISSIONS.READ_REJECTED_STEP_ON_TASKER_ONBOARDING,
                ],
                route: ROUTE_NAME.TASKER_ONBOARDING,
                title: 'TASKER_PROFILE',
              },
              {
                permissions: [
                  PERMISSIONS.READ_VERIFYING_STEP_ON_SUPPLIER_AND_STAFF_ONBOARDING,
                  PERMISSIONS.READ_APPROVED_STEP_ON_SUPPLIER_AND_STAFF_ONBOARDING,
                  PERMISSIONS.READ_ELIMINATED_STEP_ON_SUPPLIER_AND_STAFF_ONBOARDING,
                  PERMISSIONS.READ_REJECTED_STEP_ON_SUPPLIER_AND_STAFF_ONBOARDING,
                ],
                route: ROUTE_NAME.SUPPLIER_ONBOARDING,
                title: 'SUPPLIER_PROFILE',
              },
              {
                permissions: [
                  PERMISSIONS.READ_VERIFYING_STEP_ON_SUPPLIER_AND_STAFF_ONBOARDING,
                  PERMISSIONS.READ_APPROVED_STEP_ON_SUPPLIER_AND_STAFF_ONBOARDING,
                  PERMISSIONS.READ_ELIMINATED_STEP_ON_SUPPLIER_AND_STAFF_ONBOARDING,
                  PERMISSIONS.READ_REJECTED_STEP_ON_SUPPLIER_AND_STAFF_ONBOARDING,
                ],
                route: ROUTE_NAME.STAFF_ONBOARDING,
                title: 'STAFF_PROFILE',
              },
            ],
          },
          {
            title: 'TOOLKIT',
            routes: [
              {
                permissions: [PERMISSIONS.READ_ORDER_MANAGEMENT],
                route: ROUTE_NAME.ORDER_MANAGEMENT,
                title: 'ORDER_MANAGEMENT',
              },
              {
                permissions: [PERMISSIONS.READ_INSTALLMENT_PAYMENT],
                route: ROUTE_NAME.INSTALLMENT_PAYMENT,
                title: 'INSTALLMENT_PAYMENT',
              },
              {
                permissions: [PERMISSIONS.READ_FULL_PAYMENT],
                route: ROUTE_NAME.FULL_PAYMENT,
                title: 'FULL_PAYMENT',
              },
              {
                permissions: [PERMISSIONS.READ_TOOLKIT_SETTING],
                route: ROUTE_NAME.TOOLKIT_SETTING,
                title: 'TOOLKIT_MANAGEMENT',
              },
              {
                permissions: [PERMISSIONS.READ_TOOL_REQUIRE],
                route: ROUTE_NAME.TOOL_REQUIRE,
                title: 'TOOL_MANAGEMENT',
              },
            ],
          },
        ],
      },
      {
        title: 'CONFIGURATION',
        routes: [
          {
            permissions: [PERMISSIONS.READ_LIST_OFFICES],
            route: ROUTE_NAME.LIST_OFFICES,
            title: 'LIST_OFFICE',
          },
          {
            permissions: [PERMISSIONS.READ_LIST_REASONS],
            route: `${ROUTE_NAME.LIST_REASONS}/${TASKER_PROFILE_REJECT_STATUS.NEEDS_UPDATE}`,
            title: 'LIST_REASONS',
          },
          {
            permissions: [PERMISSIONS.READ_LIST_HOLIDAYS],
            route: ROUTE_NAME.LIST_HOLIDAYS,
            title: 'LIST_HOLIDAYS',
          },
        ],
      },
    ],
    [],
  );

  return (
    <>
      <div className="flex h-16 items-center m-auto max-w-[1392px]">
        <Drawer direction="left">
          <DrawerTrigger className="lg:hidden">
            <AlignJustify className="hover:text-primary" />
          </DrawerTrigger>
          <DrawerContent className="h-full w-[334px] rounded-br-lg rounded-tr-lg">
            <DrawerTitle className="flex justify-between w-full px-4 align-middle">
              <Link
                to="/"
                className="text-sm font-medium transition-colors text-muted-foreground">
                <DrawerClose>
                  <TaskerOperationLogo />
                </DrawerClose>
              </Link>
              <DrawerClose>
                <XIcon className="transition-transform hover:rotate-180 hover:text-primary" />
              </DrawerClose>
            </DrawerTitle>
            <DrawerHeader>
              <DrawerDescription className="flex flex-col gap-4">
                <div className="flex flex-col flex-wrap max-w-[200px]">
                  <Input
                    type="search"
                    placeholder={t('SEARCH')}
                    className="w-full my-2"
                  />
                  <NavigatorDropdownCommon
                    navigation={navigation}
                    userPermissions={user.permissions}
                  />
                </div>
              </DrawerDescription>
            </DrawerHeader>
          </DrawerContent>
        </Drawer>
        <nav className=" hidden items-center gap-2 space-x-0.5 lg:flex lg:space-x-2">
          <Link
            to="/"
            className="text-sm font-medium transition-colors text-muted-foreground">
            <TaskerOperationLogo />
          </Link>
          <NavigatorDropdownCommon
            navigation={navigation}
            userPermissions={user.permissions}
          />
        </nav>
        <div className="flex items-center ml-auto space-x-4">
          <Input
            type="search"
            placeholder={t('SEARCH')}
            className="hidden md:w-[100px] lg:inline-block lg:w-[270px]"
          />
          <Select
            defaultValue={userProfile?.language ?? 'en'}
            onValueChange={onSubmit}>
            <SelectTrigger className="h-10 w-[180px]">
              <SelectValue />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="vi">Vietnamese</SelectItem>
              <SelectItem value="en">English</SelectItem>
            </SelectContent>
          </Select>
          <DropdownMenu>
            <DropdownMenuTrigger asChild>
              <Button className="relative w-8 h-8 rounded-full">
                <Avatar className="w-10 h-10">
                  <AvatarImage src={userProfile?.avatarUrl} />
                  <AvatarFallback>
                    <UserCircle />
                  </AvatarFallback>
                </Avatar>
              </Button>
            </DropdownMenuTrigger>
            <DropdownMenuContent className="w-56 mt-2" align="end" forceMount>
              <DropdownMenuLabel className="font-normal">
                <div className="flex flex-col space-y-1">
                  <p className="text-sm font-medium leading-none">
                    {userProfile?.username}
                  </p>
                  <p className="text-xs leading-none text-muted-foreground">
                    {userProfile?.email}
                  </p>
                </div>
              </DropdownMenuLabel>
              <DropdownMenuSeparator />
              <DropdownMenuItem asChild>
                <Link className="w-full" to={ROUTE_NAME.PROFILE_SETTING}>
                  {t('SETTINGS')}
                </Link>
              </DropdownMenuItem>
              <DropdownMenuItem asChild>
                <Form className="w-full" method="post" action="/logout">
                  <button className="w-full text-start" type="submit">
                    {t('LOGOUT')}
                  </button>
                </Form>
              </DropdownMenuItem>
            </DropdownMenuContent>
          </DropdownMenu>
        </div>
      </div>
      <Separator />
      <div className="flex-1 mt-6 m-auto max-w-[1392px] mb-6">
        <GlobalContext.Provider value={storeRef.current}>
          <Outlet />
        </GlobalContext.Provider>
      </div>
    </>
  );
}
