import type { SerializeFrom } from '@remix-run/node';
import {
  isRouteErrorResponse,
  json,
  useRouteError,
  useSearchParams,
} from '@remix-run/react';
import type { ColumnDef } from '@tanstack/react-table';
import {
  PERMISSIONS,
  STATUS_SPECIAL_CAMPAIGN_TRANSACTION,
} from 'btaskee-constants';
import { useLoaderDataSafely } from 'btaskee-hooks';
import {
  BTaskeeTable,
  Breadcrumbs,
  BtaskeeResponseError,
  DATE_RANGE_PICKER_OPTIONS,
  DataTableColumnHeader,
  StatusBadge,
  Typography,
  toast,
} from 'btaskee-ui';
import {
  convertSortString,
  getPageSizeAndPageIndex,
  getSkipAndLimit,
  getValuesFromSearchParams,
  momentTz,
} from 'btaskee-utils';
import { format } from 'date-fns';
import { useEffect, useMemo } from 'react';
import { useTranslation } from 'react-i18next';
import { hocLoader } from '~/hoc/remix';
import { getUserSession } from '~/services/helpers.server';
import {
  getListSpecialCampaignTransaction,
  getTotalSpecialCampaignTransaction,
} from '~/services/special-campaign-transaction.server';

export function ErrorBoundary() {
  const error = useRouteError() as { message?: string };
  const { t: tCommon } = useTranslation('common');

  if (isRouteErrorResponse(error)) {
    if (error?.message) {
      toast({ description: JSON.stringify(error.message) });
    }

    return <BtaskeeResponseError t={tCommon} errorStatus={error.status} />;
  }

  return <BtaskeeResponseError t={tCommon} errorStatus={500} />;
}

export const loader = hocLoader(async ({ request }) => {
  const { isoCode } = await getUserSession({
    headers: request.headers,
  });
  const url = new URL(request.url);

  const [
    { sort: sortString, status, completedDate: rangeDate, search },
    { pageSize, pageIndex },
  ] = getValuesFromSearchParams(url.searchParams, {
    keysString: ['sort', 'status', 'completedDate', 'search'],
    keysNumber: ['pageSize', 'pageIndex'],
  });

  const parsedRangeDate = rangeDate ? JSON.parse(rangeDate) : null;

  const filterValue = {
    search,
    status,
    rangeDate:
      parsedRangeDate?.from && parsedRangeDate?.to
        ? {
            from: momentTz(parsedRangeDate.from).startOf('day').toDate(),
            to: momentTz(parsedRangeDate.to).endOf('day').toDate(),
          }
        : {
            from: momentTz().subtract(7, 'days').startOf('day').toDate(),
            to: momentTz().endOf('day').toDate(),
          },
  };

  const total = await getTotalSpecialCampaignTransaction({
    isoCode,
    filter: filterValue,
  });

  const { limit, skip } = getSkipAndLimit(
    getPageSizeAndPageIndex({
      total,
      pageSize,
      pageIndex,
    }),
  );

  const specialCampaignTransactions = await getListSpecialCampaignTransaction({
    isoCode,
    filter: filterValue,
    skip,
    limit,
    sort: convertSortString({
      sortString,
      defaultValue: { completedDate: -1 },
    }),
  });

  return json({
    total,
    filterValue,
    specialCampaignTransactions,
  });
}, PERMISSIONS.READ_SPECIAL_CAMPAIGN_TRANSACTION);

export default function SpecialCampaignTransactionIndex() {
  const { t: tSpecialCampaignTransaction } = useTranslation(
    'special-campaign-transaction',
  );
  const [searchParams] = useSearchParams();
  const {
    error: loaderError,
    total,
    specialCampaignTransactions,
    filterValue,
  } = useLoaderDataSafely<typeof loader>();

  useEffect(() => {
    if (loaderError) toast({ description: loaderError });
  }, [loaderError]);

  const columns: ColumnDef<
    SerializeFrom<
      ReturnValueIgnorePromise<typeof getListSpecialCampaignTransaction>[0]
    >
  >[] = useMemo(
    () => [
      {
        accessorKey: 'taskerId',
        header: ({ column }) => (
          <DataTableColumnHeader
            column={column}
            title={tSpecialCampaignTransaction('TASKER_ID')}
          />
        ),
        cell: ({ row }) => (
          <Typography
            variant="p"
            affects="removePMargin"
            className="text-gray-800 font-normal text-sm">
            {row.original.taskerId}
          </Typography>
        ),
        enableSorting: false,
      },
      {
        accessorKey: 'phone',
        header: ({ column }) => (
          <DataTableColumnHeader
            column={column}
            title={tSpecialCampaignTransaction('PHONE')}
          />
        ),
        cell: ({ row }) => (
          <Typography
            variant="p"
            affects="removePMargin"
            className="text-gray-800 font-normal text-sm">
            {row.original.phone}
          </Typography>
        ),
        enableSorting: false,
      },
      {
        accessorKey: 'campaignId',
        header: ({ column }) => (
          <DataTableColumnHeader
            column={column}
            title={tSpecialCampaignTransaction('CAMPAIGN_ID')}
          />
        ),
        cell: ({ row }) => (
          <Typography
            variant="p"
            affects="removePMargin"
            className="text-gray-800 font-normal text-sm">
            {row.original.campaignId}
          </Typography>
        ),
        enableSorting: false,
      },
      {
        accessorKey: 'campaignName',
        header: ({ column }) => (
          <DataTableColumnHeader
            column={column}
            title={tSpecialCampaignTransaction('CAMPAIGN_NAME')}
          />
        ),
        cell: ({ row }) => (
          <Typography
            variant="p"
            affects="removePMargin"
            className="text-gray-800 font-normal text-sm">
            {row.original.campaignName}
          </Typography>
        ),
        enableSorting: false,
      },
      {
        accessorKey: 'completedDate',
        header: ({ column }) => (
          <DataTableColumnHeader
            column={column}
            title={tSpecialCampaignTransaction('COMPLETED_DATE')}
          />
        ),
        cell: ({ row }) => {
          return (
            <Typography
              variant="p"
              affects="removePMargin"
              className="text-gray-800 font-normal text-sm">
              {row?.original?.completedDate
                ? format(row?.original?.completedDate, 'HH:mm - dd/MM/yyyy')
                : '-'}
            </Typography>
          );
        },
      },
      {
        accessorKey: 'rewardedDate',
        header: ({ column }) => (
          <DataTableColumnHeader
            column={column}
            title={tSpecialCampaignTransaction('REWARDED_DATE')}
          />
        ),
        cell: ({ row }) => {
          return (
            <Typography
              variant="p"
              affects="removePMargin"
              className="text-gray-800 font-normal text-sm">
              {row?.original?.rewardedDate
                ? format(row?.original?.rewardedDate, 'HH:mm - dd/MM/yyyy')
                : '-'}
            </Typography>
          );
        },
      },
      {
        accessorKey: 'createdAt',
        header: ({ column }) => (
          <DataTableColumnHeader
            column={column}
            title={tSpecialCampaignTransaction('CREATED_AT')}
          />
        ),
        cell: ({ row }) => {
          return (
            <Typography
              variant="p"
              affects="removePMargin"
              className="text-gray-800 font-normal text-sm">
              {row?.original?.createdAt
                ? format(row?.original?.createdAt, 'HH:mm - dd/MM/yyyy')
                : '-'}
            </Typography>
          );
        },
      },
      {
        accessorKey: 'status',
        header: ({ column }) => (
          <DataTableColumnHeader
            column={column}
            title={tSpecialCampaignTransaction('STATUS')}
          />
        ),
        cell: ({ row }) => (
          <StatusBadge
            statusClasses={{
              'IN-PROCESS':
                'bg-yellow-50 text-yellow-500 rounded-md text-center',
              COMPLETED: 'bg-green-50 text-green-500 rounded-md text-center',
              REWARDED: 'bg-blue-50 text-blue-500 rounded-md text-center',
            }}
            translationKey="special-campaign-transaction"
            status={row.original?.status}
          />
        ),
        enableSorting: false,
      },
    ],
    [tSpecialCampaignTransaction],
  );

  return (
    <>
      <div className="mb-6 flex items-center justify-between rounded-xl bg-secondary p-4 font-sans">
        <div className="grid space-y-2">
          <Typography variant="h2">
            {tSpecialCampaignTransaction('SPECIAL_CAMPAIGN_TRANSACTION')}
          </Typography>
          <Breadcrumbs />
        </div>
      </div>
      <BTaskeeTable
        isShowClearButton
        data={specialCampaignTransactions}
        columns={columns}
        total={total}
        pagination={getPageSizeAndPageIndex({
          total,
          pageSize: Number(searchParams.get('pageSize') || 0),
          pageIndex: Number(searchParams.get('pageIndex') || 0),
        })}
        localeAddress="special-campaign-transaction"
        filterDate={{
          name: 'completedDate',
          defaultValue: {
            from: momentTz(filterValue?.rangeDate?.from).toDate(),
            to: momentTz(filterValue?.rangeDate?.to).toDate(),
          },
          defaultRangeDateOptions: DATE_RANGE_PICKER_OPTIONS.LAST_7_DAYS,
        }}
        search={{
          placeholder: tSpecialCampaignTransaction(
            'SEARCH_BY_PHONE_CAMPAIGN_ID',
          ),
          defaultValue: filterValue.search || '',
          name: 'search',
        }}
        filters={[
          {
            placeholder: tSpecialCampaignTransaction('STATUS'),
            name: 'status',
            options: Object.values(STATUS_SPECIAL_CAMPAIGN_TRANSACTION).map(
              status => ({
                label: tSpecialCampaignTransaction(status),
                value: status,
              }),
            ),
            value: filterValue.status,
          },
        ]}
      />
    </>
  );
}
