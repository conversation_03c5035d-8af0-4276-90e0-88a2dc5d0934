import { Link, Outlet, useLocation } from '@remix-run/react';
import { PERMISSIONS, ROUTE_NAME } from 'btaskee-constants';
import { useGlobalStore } from 'btaskee-hooks';
import { Grid, Tabs, TabsList, TabsTrigger, Typography, cn } from 'btaskee-ui';
import { useMemo, useState } from 'react';
import { useTranslation } from 'react-i18next';

const navigation = [
  {
    title: 'HISTORY_BREWARD',
    href: ROUTE_NAME.REPORT_HISTORY_BREWARD,
    permission: PERMISSIONS.READ_HISTORY_BREWARD,
  },
  {
    title: 'TASKER_OPERATION',
    href: ROUTE_NAME.REPORT_TASKER_OPERATION,
    permission: PERMISSIONS.READ_REPORT_TASKER_OPERATION,
  },
];

export default function Screen() {
  const { t } = useTranslation('common');
  const { pathname } = useLocation();
  const globalData = useGlobalStore(state => state);
  const [selectedTab, setSelectedTab] = useState(pathname);

  const currentNav = useMemo(() => {
    return navigation.find(
      nav =>
        globalData.permissions.includes(nav.permission) &&
        nav.href === pathname,
    );
  }, [globalData.permissions, pathname]);

  const handleTabChange = (newTab: string) => {
    setSelectedTab(newTab);
  };

  return (
    <div className="md:block">
      <Grid
        className={cn(
          'bg-secondary p-4',
          pathname !== currentNav?.href ? 'hidden' : null,
        )}>
        <div className="flex items-center justify-between">
          <Grid className="gap-3">
            <Typography variant="h2">{t('REPORT')}</Typography>
            <Tabs
              defaultValue={selectedTab}
              onValueChange={handleTabChange}
              className="space-y-4">
              <TabsList>
                {navigation.map(item => (
                  <Link to={item.href} key={item.href}>
                    {globalData.permissions?.includes(item.permission) ? (
                      <TabsTrigger value={item.href}>
                        {t(item.title)}
                      </TabsTrigger>
                    ) : null}
                  </Link>
                ))}
              </TabsList>
            </Tabs>
          </Grid>
        </div>
      </Grid>
      <Outlet />
    </div>
  );
}
