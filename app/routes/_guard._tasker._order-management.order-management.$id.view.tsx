import {
  isRouteErrorResponse,
  json,
  useLoaderData,
  useNavigate,
  useRouteError,
} from '@remix-run/react';
import { FORM_TYPE, PERMISSIONS } from 'btaskee-constants';
import {
  AlertDialog,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
  BtaskeeResponseError,
  Button,
  toast,
} from 'btaskee-ui';
import { useTranslation } from 'react-i18next';
import { OrderInfo } from '~/components/order-management/OrderInfo';
import { hocLoader } from '~/hoc/remix';
import {
  getSettingCountryByIsoCode,
  getUserSession,
} from '~/services/helpers.server';
import { getDetailPaymentToolKitTransaction } from '~/services/payment-and-delivery.server';

export const loader = hocLoader(
  async ({ request, params }) => {
    const { isoCode } = await getUserSession({
      headers: request.headers,
    });

    const [order, settingCountry] = await Promise.all([
      getDetailPaymentToolKitTransaction({
        isoCode,
        paymentToolKitTransactionId: params?.id || '',
      }),
      getSettingCountryByIsoCode({ isoCode, projection: { currency: 1 } }),
    ]);

    return json({ order, settingCountry });
  },
  [PERMISSIONS.READ_ORDER_MANAGEMENT],
);

export function ErrorBoundary() {
  const error = useRouteError() as { message?: string };
  const { t: tCommon } = useTranslation('common');

  if (isRouteErrorResponse(error)) {
    if (error?.message) {
      toast({ description: JSON.stringify(error.message) });
    }

    return <BtaskeeResponseError t={tCommon} errorStatus={error.status} />;
  }

  return <BtaskeeResponseError t={tCommon} errorStatus={500} />;
}

export default function OrderDelivery() {
  const { t } = useTranslation('order-management');
  const loaderData = useLoaderData<LoaderTypeWithError<typeof loader>>();
  const navigate = useNavigate();

  return (
    <AlertDialog defaultOpen={true}>
      <AlertDialogContent>
        <AlertDialogHeader>
          <AlertDialogTitle>{t('DELIVERY_METHOD')}</AlertDialogTitle>
          <AlertDialogDescription />
        </AlertDialogHeader>
        <div className="p-4 flex flex-col gap-3 rounded-md bg-gray-50 border border-gray-200 overflow-hidden">
          <OrderInfo
            order={loaderData?.order}
            typeOfForm={FORM_TYPE.VIEW}
            currency={loaderData?.settingCountry?.currency?.sign || ''}
          />
        </div>
        <AlertDialogFooter>
          <Button type="button" onClick={() => navigate(-1)}>
            {t('CLOSE')}
          </Button>
        </AlertDialogFooter>
      </AlertDialogContent>
    </AlertDialog>
  );
}
