import type { SerializeFrom } from '@remix-run/node';
import { Outlet, isRouteErrorResponse, useRouteError } from '@remix-run/react';
import { PERMISSIONS, ROUTE_NAME } from 'btaskee-constants';
import { useLoaderDataSafely } from 'btaskee-hooks';
import { BreadcrumbsLink, BtaskeeResponseError, toast } from 'btaskee-ui';
import { useEffect } from 'react';
import { useTranslation } from 'react-i18next';
import { hocLoader } from '~/hoc/remix';
import { willBecomeTaskerTrainingHistoryLoader } from '~/hooks/useLoaderTaskerTrainingHistory';

export function ErrorBoundary() {
  const error = useRouteError() as { message?: string };
  const { t: tCommon } = useTranslation('common');

  if (isRouteErrorResponse(error)) {
    if (error?.message) {
      toast({ description: JSON.stringify(error.message) });
    }

    return <BtaskeeResponseError t={tCommon} errorStatus={error.status} />;
  }

  return <BtaskeeResponseError t={tCommon} errorStatus={500} />;
}

export const handle = {
  breadcrumb: ({
    taskerId,
  }: SerializeFrom<typeof willBecomeTaskerTrainingHistoryLoader>) => (
    <BreadcrumbsLink
      to={`${ROUTE_NAME.TASKER}/${taskerId}${ROUTE_NAME.TRAINING_HISTORY}`}
      label="TRAINING_HISTORY"
    />
  ),
  i18n: 'course',
};

export const loader = hocLoader(
  willBecomeTaskerTrainingHistoryLoader,
  PERMISSIONS.READ_TRAINING_HISTORY,
);

export default function TrainingHistory() {
  const { error: loaderError, ...restLoaderData } =
    useLoaderDataSafely<typeof loader>();

  useEffect(() => {
    if (loaderError) {
      toast({ description: loaderError });
    }
  }, [loaderError]);

  return <Outlet context={restLoaderData} />;
}
