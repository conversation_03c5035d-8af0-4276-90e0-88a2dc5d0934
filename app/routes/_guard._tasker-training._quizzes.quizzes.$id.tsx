import { json } from '@remix-run/node';
import { PERMISSIONS, ROUTE_NAME } from 'btaskee-constants';
import { BreadcrumbsLink } from 'btaskee-ui';
import { hocLoader } from '~/hoc/remix';

export const handle = {
  breadcrumb: (data: { quizzesId: string }) => {
    return (
      <BreadcrumbsLink
        to={`${ROUTE_NAME.QUIZ_COLLECTION}/${data.quizzesId}`}
        label="TRAINING_QUIZ_COLLECTION_DETAIL"
      />
    );
  },
};

export const loader = hocLoader(async ({ params }) => {
  return json({ quizzesId: params.id || '' });
}, PERMISSIONS.READ_TRAINING_QUIZ_COLLECTION);
