import { json } from '@remix-run/node';
import { PERMISSIONS } from 'btaskee-constants';
import { hocLoader } from '~/hoc/remix';
import { getUserSession } from '~/services/helpers.server';
import { verifyListQuizCode } from '~/services/tasker-training.server';

export const loader = hocLoader(async ({ request }) => {
  const url = new URL(request.url);
  const codes = url.searchParams.get('codes') || '';

  const { isoCode } = await getUserSession({
    headers: request.headers,
  });

  const listExist = await verifyListQuizCode({
    isoCode,
    codes: codes.split(','),
  });

  return json({ listExist: listExist || [] });
}, PERMISSIONS.WRITE_TRAINING_QUIZ_COLLECTION);
