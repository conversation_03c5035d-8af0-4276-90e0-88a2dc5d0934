import {
  Link,
  isRouteErrorResponse,
  json,
  useRouteError,
} from '@remix-run/react';
import {
  PERMISSIONS,
  ROUTE_NAME,
  TYPE_REWARD_ACCOUNT_SPECIAL_CAMPAIGN,
  TYPE_SPECIAL_CAMPAIGN,
} from 'btaskee-constants';
import { useGlobalStore, useLoaderDataSafely } from 'btaskee-hooks';
import {
  Badge,
  Breadcrumbs,
  BtaskeeResponseError,
  Button,
  Card,
  CardContent,
  CardTitle,
  Grid,
  GridItem,
  Label,
  MultiLanguageSectionView,
  Separator,
  StatusBadge,
  Typography,
  toast,
} from 'btaskee-ui';
import { formatNumberWithCommas } from 'btaskee-utils';
import { format } from 'date-fns';
import i18n from 'i18next';
import { MoveRight } from 'lucide-react';
import { useEffect } from 'react';
import { useTranslation } from 'react-i18next';
import { hoc404, hocLoader } from '~/hoc/remix';
import {
  getAllLevelsOfJourney,
  getSettingCountryByIsoCode,
  getUserSession,
} from '~/services/helpers.server';
import { getServices } from '~/services/service.server';
import { getSession } from '~/services/session.server';
import { getSpecialCampaignDetail } from '~/services/special-campaign.server';

export const loader = hocLoader(async ({ request, params }) => {
  const { isoCode } = await getUserSession({
    headers: request.headers,
  });

  const [
    settingCountry,
    specialCampaignDetail,
    session,
    services,
    levelsOfJourney,
  ] = await Promise.all([
    getSettingCountryByIsoCode({
      isoCode,
      projection: { currency: 1 },
    }),
    hoc404(() =>
      getSpecialCampaignDetail({
        isoCode,
        specialCampaignId: params.id || '',
      }),
    ),
    getSession(request.headers.get('Cookie')),
    getServices({
      isoCode,
      projection: { name: 1, text: 1, isSubscription: 1 },
    }),
    getAllLevelsOfJourney({ isoCode }),
  ]);

  const message = session.get('flashMessage');

  return json({
    specialCampaignId: params.id || '',
    specialCampaignDetail,
    currency: settingCountry?.currency,
    services,
    levelsOfJourney,
    message,
  });
}, PERMISSIONS.READ_SPECIAL_CAMPAIGN);

export function ErrorBoundary() {
  const error = useRouteError() as { message?: string };
  const { t: tCommon } = useTranslation('common');

  if (isRouteErrorResponse(error)) {
    if (error?.message) {
      toast({ description: JSON.stringify(error.message) });
    }

    return <BtaskeeResponseError t={tCommon} errorStatus={error.status} />;
  }

  return <BtaskeeResponseError t={tCommon} errorStatus={500} />;
}

export default function SpecialCampaignDetail() {
  const { t: tSpecialCampaign } = useTranslation('special-campaign');
  const {
    error: loaderError,
    specialCampaignId,
    specialCampaignDetail,
    currency,
    services,
    levelsOfJourney,
    message,
  } = useLoaderDataSafely<typeof loader>();
  const permissions = useGlobalStore(state => state.permissions);

  const getLevelOfJourneyByLevelOfJourneyName = (
    levelOfJourneyName: string,
  ) => {
    const foundedLevelOfJourney = levelsOfJourney.find(
      level => level.name === levelOfJourneyName,
    );

    return foundedLevelOfJourney?.text?.[i18n.language || 'en'] || '';
  };

  useEffect(() => {
    if (loaderError) toast({ description: loaderError });
  }, [loaderError]);

  useEffect(() => {
    if (message) {
      toast({ variant: 'success', description: message });
    }
  }, [message, tSpecialCampaign]);

  return (
    <Grid className="gap-6">
      <div className="flex items-center justify-between bg-secondary p-4">
        <Grid className="gap-3">
          <Typography variant="h2">
            {tSpecialCampaign('SPECIAL_CAMPAIGN_DETAIL')}
          </Typography>
          <Breadcrumbs />
        </Grid>
        <Button
          asChild={permissions?.includes(PERMISSIONS.WRITE_SPECIAL_CAMPAIGN)}
          disabled={!permissions?.includes(PERMISSIONS.WRITE_SPECIAL_CAMPAIGN)}>
          <Link to={`${ROUTE_NAME.SPECIAL_CAMPAIGN}/${specialCampaignId}/edit`}>
            {tSpecialCampaign('UPDATE')}
          </Link>
        </Button>
      </div>

      <Card className="bg-gray-50 p-6">
        <Typography variant="h4">
          {tSpecialCampaign('GENERAL_INFORMATION_TITLE')}
        </Typography>
        <Separator className="mb-6 mt-3 w-52" />
        <div className="grid grid-cols-3 gap-6">
          <div className="col-span-1 flex flex-col gap-6">
            <Card className="flex justify-center rounded-md border border-dashed bg-gray-50 p-6">
              <img
                src={specialCampaignDetail?.image?.imageUrl}
                alt="special-campaign-img"
              />
            </Card>

            <MultiLanguageSectionView
              data={{
                text: specialCampaignDetail?.text,
              }}
              fields={{
                name: tSpecialCampaign('DISPLAY_NAME'),
                description: tSpecialCampaign('DESCRIPTION'),
              }}
              parentField="text"
              useNestedStructure={true}
            />
          </div>
          <div className="col-span-2">
            <Grid className="grid-cols-2 gap-6">
              <GridItem className="space-y-1">
                <Label className="text-sm font-normal text-gray-400">
                  {tSpecialCampaign('CAMPAIGN_NAME')}
                </Label>
                <Typography
                  affects="large"
                  variant="h4"
                  className="text-gray-600">
                  {specialCampaignDetail?.name}
                </Typography>
              </GridItem>
              <GridItem className="space-y-1">
                <Label className="text-sm font-normal text-gray-400">
                  {tSpecialCampaign('VALIDITY_PERIOD')}
                </Label>
                <Typography
                  className="flex items-center gap-2 text-gray-600"
                  affects="large"
                  variant="h4">
                  {format(
                    specialCampaignDetail?.startDate,
                    'HH:mm - dd/MM/yyyy',
                  )}
                  <MoveRight className="h-4 w-4" />
                  {format(specialCampaignDetail?.endDate, 'HH:mm - dd/MM/yyyy')}
                </Typography>
              </GridItem>
              <GridItem className="space-y-1">
                <Label className="text-sm font-normal text-gray-400">
                  {tSpecialCampaign('TYPE')}
                </Label>
                <Typography
                  affects="large"
                  variant="h4"
                  className="text-gray-600">
                  {tSpecialCampaign(specialCampaignDetail?.type)}
                </Typography>
              </GridItem>
              <GridItem className="space-y-1">
                <Label className="text-sm font-normal text-gray-400">
                  {specialCampaignDetail?.type ===
                  TYPE_SPECIAL_CAMPAIGN.REFERRAL_CAMPAIGN
                    ? tSpecialCampaign('NO_OF_REFEREE')
                    : tSpecialCampaign('NO_OF_TASK')}
                </Label>
                <Typography
                  affects="large"
                  variant="h4"
                  className="text-gray-600">
                  {formatNumberWithCommas(specialCampaignDetail?.value)}
                </Typography>
              </GridItem>
              <GridItem className="space-y-1">
                <Label className="text-sm font-normal text-gray-400">
                  {tSpecialCampaign('CREATED_BY')}
                </Label>
                <Typography
                  affects="large"
                  variant="h4"
                  className="text-gray-600">
                  {specialCampaignDetail?.createdBy
                    ? specialCampaignDetail.createdBy
                    : '-'}
                </Typography>
              </GridItem>
              <GridItem className="space-y-1">
                <Label className="text-sm font-normal text-gray-400">
                  {tSpecialCampaign('CREATED_AT')}
                </Label>
                <Typography
                  affects="large"
                  variant="h4"
                  className="text-gray-600">
                  {specialCampaignDetail?.createdAt
                    ? format(
                        specialCampaignDetail.createdAt,
                        'HH:mm - dd/MM/yyyy',
                      )
                    : '-'}
                </Typography>
              </GridItem>
              <GridItem className="space-y-1">
                <Label className="text-sm font-normal text-gray-400">
                  {tSpecialCampaign('UPDATED_BY')}
                </Label>
                <Typography
                  affects="large"
                  variant="h4"
                  className="text-gray-600">
                  {specialCampaignDetail?.updatedBy
                    ? specialCampaignDetail.updatedBy
                    : '-'}
                </Typography>
              </GridItem>
              <GridItem className="space-y-1">
                <Label className="text-sm font-normal text-gray-400">
                  {tSpecialCampaign('UPDATED_AT')}
                </Label>
                <Typography
                  affects="large"
                  variant="h4"
                  className="text-gray-600">
                  {specialCampaignDetail?.updatedAt
                    ? format(
                        specialCampaignDetail.updatedAt,
                        'HH:mm - dd/MM/yyyy',
                      )
                    : '-'}
                </Typography>
              </GridItem>
              <GridItem className="space-y-1">
                <Label className="text-sm font-normal text-gray-400">
                  {tSpecialCampaign('CITY')}
                </Label>
                <div className="flex flex-wrap gap-2">
                  {specialCampaignDetail?.city?.length ? (
                    specialCampaignDetail.city.map((city, index) => (
                      <Badge
                        className="justify-center rounded-md bg-blue-50 text-sm font-normal text-blue"
                        key={index}>
                        {city}
                      </Badge>
                    ))
                  ) : (
                    <Badge className="justify-center rounded-md bg-blue-50 text-sm font-normal text-blue">
                      {tSpecialCampaign('ALL_CITIES')}
                    </Badge>
                  )}
                </div>
              </GridItem>
              <GridItem className="space-y-1">
                <Label className="text-sm font-normal text-gray-400">
                  {tSpecialCampaign('STATUS')}
                </Label>
                <StatusBadge
                  statusClasses={{
                    ACTIVE: 'bg-green-50 text-green-500 rounded-md text-center',
                    INACTIVE:
                      'bg-gray-100 text-gray-500 rounded-md text-center',
                  }}
                  status={specialCampaignDetail?.status}
                />
              </GridItem>
            </Grid>
          </div>
        </div>
      </Card>

      <Card className="bg-gray-50 p-6">
        <Typography variant="h4">{tSpecialCampaign('REWARDS')}</Typography>
        <Separator className="mb-6 mt-3 w-52" />
        <Grid className="grid-cols-2 gap-6">
          {specialCampaignDetail?.rewards?.map((reward, index) => (
            <GridItem key={index}>
              <Card>
                <CardTitle className="bg-gray-100 px-4 py-2 rounded-t-lg">
                  <Label className="text-base font-medium text-gray-700">
                    {tSpecialCampaign('REWARDS_INFORMATION', {
                      value: index + 1,
                    })}
                  </Label>
                </CardTitle>
                <CardContent className="px-6 pb-6 pt-4">
                  <Grid className="grid-cols-2 gap-6">
                    <GridItem className="space-y-1">
                      <Label className="text-sm font-normal text-gray-400">
                        {tSpecialCampaign('TYPE')}
                      </Label>
                      <Typography
                        affects="large"
                        variant="h4"
                        className="text-gray-600">
                        {tSpecialCampaign(reward.type)}
                      </Typography>
                    </GridItem>
                    <GridItem className="space-y-1">
                      <Label className="text-sm font-normal text-gray-400">
                        {tSpecialCampaign('AMOUNT')}
                      </Label>
                      <Typography
                        affects="large"
                        variant="h4"
                        className="text-gray-600">
                        {`${formatNumberWithCommas(reward.amount)}${currency?.sign}`}
                      </Typography>
                    </GridItem>
                    <GridItem className="space-y-1">
                      <Label className="text-sm font-normal text-gray-400">
                        {tSpecialCampaign('ACCOUNT_TYPE')}
                      </Label>
                      <Typography
                        affects="large"
                        variant="h4"
                        className="text-gray-600">
                        {reward.applyAccountType ===
                        TYPE_REWARD_ACCOUNT_SPECIAL_CAMPAIGN.MAIN_ACCOUNT
                          ? tSpecialCampaign('MAIN_ACCOUNT')
                          : reward.applyAccountType ===
                              TYPE_REWARD_ACCOUNT_SPECIAL_CAMPAIGN.PROMOTION_ACCOUNT
                            ? tSpecialCampaign('PROMOTION_ACCOUNT')
                            : reward.applyAccountType}
                      </Typography>
                    </GridItem>
                    <GridItem className="space-y-1">
                      <Label className="text-sm font-normal text-gray-400">
                        {tSpecialCampaign('TASKER_JOURNEY_LEVELS')}
                      </Label>
                      <Typography
                        affects="large"
                        variant="h4"
                        className="text-gray-600">
                        {reward.taskerJourneyLevels?.length
                          ? reward.taskerJourneyLevels
                              .map(level =>
                                getLevelOfJourneyByLevelOfJourneyName(level),
                              )
                              .join(', ')
                          : '-'}
                      </Typography>
                    </GridItem>
                    {specialCampaignDetail?.type ===
                      TYPE_SPECIAL_CAMPAIGN.TASK_CAMPAIGN && (
                      <GridItem className="space-y-1">
                        <Label className="text-sm font-normal text-gray-400">
                          {tSpecialCampaign('MIN_RATE_TASK')}
                        </Label>
                        <Typography
                          affects="large"
                          variant="h4"
                          className="text-gray-600">
                          {reward?.minRateTask ? reward.minRateTask : '-'}
                        </Typography>
                      </GridItem>
                    )}
                    <GridItem className="space-y-1">
                      <Label className="text-sm font-normal text-gray-400">
                        {tSpecialCampaign('SERVICE')}
                      </Label>
                      <div className="flex flex-wrap gap-2">
                        {reward?.applyForServices?.length ? (
                          <>
                            {services?.map(service => {
                              if (
                                reward.applyForServices?.includes(service._id)
                              ) {
                                return (
                                  <Badge
                                    className="justify-center rounded-md bg-blue-50 text-sm font-normal text-blue"
                                    key={service._id}>
                                    {`${service?.text?.[i18n?.language || 'en']} ${service.isSubscription ? tSpecialCampaign('SUBSCRIPTION') : ''}`}
                                  </Badge>
                                );
                              }
                              return null;
                            })}
                          </>
                        ) : (
                          <Badge className="justify-center rounded-md bg-blue-50 text-sm font-normal text-blue">
                            {tSpecialCampaign('ALL_SERVICE')}
                          </Badge>
                        )}
                      </div>
                    </GridItem>
                  </Grid>
                </CardContent>
              </Card>
            </GridItem>
          ))}
        </Grid>
      </Card>
    </Grid>
  );
}
