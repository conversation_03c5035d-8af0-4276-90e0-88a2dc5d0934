import { DotsHorizontalIcon } from '@radix-ui/react-icons';
import {
  isRouteErrorResponse,
  useOutletContext,
  useRouteError,
  useSearchParams,
} from '@remix-run/react';
import type { ColumnDef } from '@tanstack/react-table';
import {
  BTaskeeTable,
  Badge,
  Breadcrumbs,
  BtaskeeResponseError,
  Button,
  Card,
  DataTableColumnHeader,
  Dialog,
  DialogClose,
  DialogContent,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
  Label,
  Separator,
  Tooltip,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
  Typography,
  toast,
} from 'btaskee-ui';
import { formatNumberWithCommas, getPageSizeAndPageIndex } from 'btaskee-utils';
import { format } from 'date-fns';
import i18n from 'i18next';
import { useMemo } from 'react';
import { useTranslation } from 'react-i18next';

type OutletContextType = {
  fullPayment: {
    data: FullPayment[];
    totalAmount: number;
  };
  total: number;
  settingCountry: SettingCountry;
  services: Service[];
  cities: string[];
  filterValue: {
    status: string;
    service: string;
    city: string;
    rangeDate: {
      from: Date;
      to: Date;
    };
  };
};

export function ErrorBoundary() {
  const error = useRouteError() as { message?: string };
  const { t: tCommon } = useTranslation('common');

  if (isRouteErrorResponse(error)) {
    if (error?.message) {
      toast({ description: JSON.stringify(error.message) });
    }

    return <BtaskeeResponseError t={tCommon} errorStatus={error.status} />;
  }

  return <BtaskeeResponseError t={tCommon} errorStatus={500} />;
}

export default function FullPaymentIndex() {
  const { t } = useTranslation('full-payment');

  const [searchParams] = useSearchParams();

  const outletData = useOutletContext<OutletContextType>();
  const currency = outletData?.settingCountry?.currency?.sign || '';

  const columns: ColumnDef<FullPayment>[] = useMemo(
    () => [
      {
        accessorKey: 'tasker.phone',
        header: ({ column }) => (
          <DataTableColumnHeader column={column} title={t('TASKER_PHONE')} />
        ),
        cell: ({ row }) => <div>{row.original?.tasker?.phone}</div>,
        enableSorting: false,
      },
      {
        accessorKey: 'tasker.name',
        header: ({ column }) => (
          <DataTableColumnHeader column={column} title={t('TASKER_NAME')} />
        ),
        cell: ({ row }) => <div>{row.original?.tasker?.name}</div>,
        enableSorting: false,
      },
      {
        accessorKey: 'tasker.registeredServices',
        header: ({ column }) => (
          <DataTableColumnHeader column={column} title={t('SERVICE')} />
        ),
        cell: ({ row }) => (
          <div className="flex flex-wrap gap-3">
            {row.original?.tasker?.registeredServices?.map(
              registeredService => {
                const serviceFoundById = outletData?.services?.find(
                  service => registeredService === service._id,
                );

                return (
                  <TooltipProvider key={registeredService}>
                    <Tooltip key={registeredService}>
                      <TooltipTrigger asChild>
                        <div className="w-11 h-11 rounded-[11px] overflow-hidden bg-primary-50">
                          <img
                            className="w-full h-full object-cover"
                            src={serviceFoundById?.icon || ''}
                            alt={registeredService}
                          />
                        </div>
                      </TooltipTrigger>
                      <TooltipContent>
                        <Typography variant="p">
                          {`${serviceFoundById?.text?.[i18n.language || 'en']} ${serviceFoundById?.isSubscription ? ' (Subscription)' : ''}`}
                        </Typography>
                      </TooltipContent>
                    </Tooltip>
                  </TooltipProvider>
                );
              },
            )}
          </div>
        ),
        size: 300,
        enableSorting: false,
      },
      {
        accessorKey: 'tasker.city',
        header: ({ column }) => (
          <DataTableColumnHeader column={column} title={t('CITY')} />
        ),
        cell: ({ row }) => <div>{row.original?.tasker?.city}</div>,
        enableSorting: false,
      },
      {
        accessorKey: 'amount',
        header: ({ column }) => (
          <DataTableColumnHeader column={column} title={t('AMOUNT')} />
        ),
        cell: ({ row }) => (
          <div>{`${formatNumberWithCommas(row.original?.amount)}${currency}`}</div>
        ),
        enableSorting: false,
      },
      {
        accessorKey: 'createdAt',
        header: ({ column }) => (
          <DataTableColumnHeader column={column} title={t('CREATED_AT')} />
        ),
        cell: ({ row }) =>
          row?.getValue('createdAt') ? (
            <div>{format(row.getValue('createdAt'), 'HH:mm - dd/MM/yyyy')}</div>
          ) : (
            ''
          ),
        enableSorting: false,
      },
      {
        accessorKey: 'action',
        header: ({ column }) => (
          <DataTableColumnHeader
            column={column}
            className="text-center"
            title={t('ACTION')}
          />
        ),
        cell: ({ row }) => (
          <DropdownMenu>
            <DropdownMenuTrigger asChild>
              <Button
                variant="ghost"
                className="flex h-8 w-8 p-0 data-[state=open]:bg-muted">
                <DotsHorizontalIcon className="h-4 w-4" />
              </Button>
            </DropdownMenuTrigger>
            <DropdownMenuContent>
              <Dialog>
                <DialogTrigger asChild>
                  <DropdownMenuItem onSelect={event => event.preventDefault()}>
                    {t('VIEW')}
                  </DropdownMenuItem>
                </DialogTrigger>
                <DialogContent>
                  <DialogHeader>
                    <DialogTitle>
                      <Typography variant="h3">
                        {t('PAYMENT_DETAIL')}
                      </Typography>
                    </DialogTitle>
                  </DialogHeader>
                  <div className="flex flex-col gap-4">
                    <div className="flex flex-col gap-2">
                      <Label className="text-gray-400">{t('LIST_ITEMS')}</Label>
                      <Card className="p-4 bg-gray-50">
                        {row?.original?.listTool?.map((tool, index, array) => (
                          <>
                            <div
                              className="flex items-center justify-between"
                              key={tool.text}>
                              <div className="flex gap-2">
                                <Label className="text-base text-gray-600">
                                  {tool.text}
                                </Label>
                                <Label className="text-base text-green-500">{`x${tool?.quantity}`}</Label>
                              </div>
                              <Label className="text-base text-primary">
                                {`${formatNumberWithCommas(tool?.price)}${currency}`}
                              </Label>
                            </div>
                            {index < array.length - 1 && (
                              <Separator className="my-2" />
                            )}
                          </>
                        ))}
                      </Card>
                    </div>
                    <div className="flex flex-col gap-2">
                      <Label className="text-gray-400">{t('GENERAL')}</Label>
                      <Card className="p-4 bg-gray-50 flex flex-col gap-3">
                        <div className="flex flex-col gap-1">
                          <Label className="text-gray-400">
                            {t('TASKER_PHONE_NO')}
                          </Label>
                          <Label className="text-base text-gray-600">
                            {row.original?.tasker?.phone}
                          </Label>
                        </div>
                        <Separator />
                        <div className="flex flex-col gap-1">
                          <Label className="text-gray-400">
                            {t('TOTAL_AMOUNT')}
                          </Label>
                          <Label className="text-base text-gray-600">
                            {`${formatNumberWithCommas(row.original?.amount)}${currency}`}
                          </Label>
                        </div>
                        <Separator />
                        <div className="flex flex-col gap-1">
                          <Label className="text-gray-400">
                            {t('PAYMENT_DATE')}
                          </Label>
                          <Label className="text-base text-gray-600">
                            {row.original?.createdAt
                              ? format(
                                  row.original?.createdAt,
                                  'HH:mm - dd/MM/yyyy',
                                )
                              : ''}
                          </Label>
                        </div>
                      </Card>
                    </div>
                  </div>
                  <DialogFooter>
                    <DialogClose asChild>
                      <Button type="button">{t('CLOSE')}</Button>
                    </DialogClose>
                  </DialogFooter>
                </DialogContent>
              </Dialog>
            </DropdownMenuContent>
          </DropdownMenu>
        ),
        enableSorting: false,
      },
    ],
    [currency, outletData?.services, t],
  );

  return (
    <>
      <div className="mb-6 grid gap-2 rounded-xl bg-secondary p-4 font-sans">
        <Typography variant="h2">{t('FULL_PAYMENT')}</Typography>
        <Breadcrumbs />
      </div>
      <BTaskeeTable
        isShowClearButton
        columns={columns}
        data={outletData?.fullPayment?.data}
        total={outletData?.total}
        pagination={getPageSizeAndPageIndex({
          total: outletData?.total || 0,
          pageSize: Number(searchParams.get('pageSize') || 0),
          pageIndex: Number(searchParams.get('pageIndex') || 0),
        })}
        search={{
          defaultValue: searchParams.get('search') || '',
          name: 'search',
          placeholder: t('SEARCH_NAME_PHONE'),
        }}
        filterDate={{
          name: 'createdAt',
          defaultValue: outletData?.filterValue?.rangeDate,
        }}
        filters={[
          {
            placeholder: t('SERVICE'),
            name: 'service',
            options: outletData?.services.map(service => ({
              label: `${service?.text?.[i18n.language || 'en']}${service.isSubscription ? ' (Subscription)' : ''}`,
              value: service._id,
            })),
            value: outletData?.filterValue?.service,
          },
          {
            placeholder: t('CITY'),
            name: 'city',
            options: outletData?.cities.map(city => ({
              label: city,
              value: city,
            })),
            value: outletData?.filterValue?.city,
          },
        ]}
        extraContent={
          <div className="flex gap-6 justify-end">
            <div className="inline-flex flex-col items-center gap-1">
              <Label className="text-sm font-normal text-gray-400">
                {t('TOTAL_AMOUNT_ON_TASK')}
              </Label>
              <Badge className="bg-blue-50 text-blue-500 rounded-md flex items-center justify-center py-[11px] w-full text-sm">
                {`${formatNumberWithCommas(
                  outletData?.fullPayment?.totalAmount || 0,
                )}${currency}`}
              </Badge>
            </div>
          </div>
        }
      />
    </>
  );
}
