import { type SerializeFrom } from '@remix-run/node';
import {
  Link,
  isRouteErrorResponse,
  useNavigate,
  useRouteError,
  useSearchParams,
} from '@remix-run/react';
import type { ColumnDef } from '@tanstack/react-table';
import {
  PERMISSIONS,
  ROUTE_NAME,
  STATUS,
  TYPE_SPECIAL_CAMPAIGN,
} from 'btaskee-constants';
import { useGlobalStore, useLoaderDataSafely } from 'btaskee-hooks';
import {
  BTaskeeTable,
  Breadcrumbs,
  BtaskeeResponseError,
  Button,
  DataTableColumnHeader,
  DropdownMenuBase,
  Grid,
  Label,
  StatusBadge,
  Typography,
  toast,
} from 'btaskee-ui';
import { getPageSizeAndPageIndex, momentTz } from 'btaskee-utils';
import { format } from 'date-fns';
import { Plus } from 'lucide-react';
import { useEffect, useMemo } from 'react';
import { useTranslation } from 'react-i18next';
import { hocLoader } from '~/hoc/remix';
import { willBecomeSpecialCampaignLoader } from '~/hooks/useLoaderSpecialCampaign';
import { type getListSpecialCampaign } from '~/services/special-campaign.server';

export const loader = hocLoader(
  willBecomeSpecialCampaignLoader,
  PERMISSIONS.READ_SPECIAL_CAMPAIGN,
);

export function ErrorBoundary() {
  const error = useRouteError() as { message?: string };
  const { t: tCommon } = useTranslation('common');

  if (isRouteErrorResponse(error)) {
    if (error?.message) {
      toast({ description: JSON.stringify(error.message) });
    }

    return <BtaskeeResponseError t={tCommon} errorStatus={error.status} />;
  }

  return <BtaskeeResponseError t={tCommon} errorStatus={500} />;
}

export default function SpecialCampaignIndex() {
  const { t: tSpecialCampaign } = useTranslation('special-campaign');
  const navigate = useNavigate();
  const permissions = useGlobalStore(store => store.permissions);
  const [searchParams] = useSearchParams();

  const {
    error: loaderError,
    total,
    specialCampaigns,
    filterValue,
  } = useLoaderDataSafely<typeof loader>();

  useEffect(() => {
    if (loaderError) toast({ description: loaderError });
  }, [loaderError]);

  const columns: ColumnDef<
    SerializeFrom<ReturnValueIgnorePromise<typeof getListSpecialCampaign>[0]>
  >[] = useMemo(
    () => [
      {
        accessorKey: 'campaignName',
        header: ({ column }) => (
          <DataTableColumnHeader
            column={column}
            title={tSpecialCampaign('CAMPAIGN_NAME')}
          />
        ),
        cell: ({ row }) => (
          <Typography
            variant="p"
            affects="removePMargin"
            className="line-clamp-2 whitespace-normal break-all font-normal text-gray-800">
            {row.original.name}
          </Typography>
        ),
        size: 316,
        enableSorting: false,
      },
      {
        accessorKey: 'validity_period',
        header: ({ column }) => (
          <DataTableColumnHeader
            column={column}
            title={tSpecialCampaign('VALIDITY_PERIOD')}
          />
        ),
        cell: ({ row }) => (
          <div className="flex items-center gap-2">
            <svg
              width="16"
              height="40"
              viewBox="0 0 16 40"
              fill="none"
              xmlns="http://www.w3.org/2000/svg">
              <g clipPath="url(#clip0_403_10428)">
                <path
                  d="M8.00001 38.6663C11.6819 38.6663 14.6667 35.6816 14.6667 31.9997C14.6667 28.3178 11.6819 25.333 8.00001 25.333C4.31811 25.333 1.33334 28.3178 1.33334 31.9997C1.33334 35.6816 4.31811 38.6663 8.00001 38.6663Z"
                  stroke="#EF4444"
                  strokeWidth="1.33333"
                  strokeLinecap="round"
                  strokeLinejoin="round"
                />
                <path
                  d="M8 28V32L10.6667 33.3333"
                  stroke="#EF4444"
                  strokeWidth="1.33333"
                  strokeLinecap="round"
                  strokeLinejoin="round"
                />
              </g>
              <path
                d="M8 12C10.2091 12 12 10.2091 12 8C12 5.79086 10.2091 4 8 4C5.79086 4 4 5.79086 4 8C4 10.2091 5.79086 12 8 12Z"
                stroke="#22C55E"
                strokeWidth="2"
                strokeLinecap="round"
                strokeLinejoin="round"
              />
              <line
                x1="8"
                y1="24"
                x2="8"
                y2="14"
                stroke="#A3A3A3"
                strokeWidth="2"
              />
              <defs>
                <clipPath id="clip0_403_10428">
                  <rect
                    width="16"
                    height="16"
                    fill="white"
                    transform="translate(0 24)"
                  />
                </clipPath>
              </defs>
            </svg>

            <Grid className="gap-1">
              <Label className="whitespace-nowrap text-sm font-normal text-gray-800">
                {format(row.original.startDate, 'HH:mm - dd/MM/yyyy')}
              </Label>
              <Label className="whitespace-nowrap text-sm font-normal text-gray-800">
                {format(row.original.endDate, 'HH:mm - dd/MM/yyyy')}
              </Label>
            </Grid>
          </div>
        ),
        size: 200,
        enableSorting: false,
      },
      {
        accessorKey: 'type',
        header: ({ column }) => (
          <DataTableColumnHeader
            column={column}
            title={tSpecialCampaign('TYPE')}
          />
        ),
        cell: ({ row }) => (
          <Label className="text-sm font-normal text-gray-800">
            {tSpecialCampaign(row.original.type)}
          </Label>
        ),
        size: 260,
        enableSorting: false,
      },
      {
        accessorKey: 'updatedAt',
        header: ({ column }) => (
          <DataTableColumnHeader
            column={column}
            title={tSpecialCampaign('UPDATED_AT')}
          />
        ),
        cell: ({ row }) => {
          return (
            <Typography
              variant="p"
              affects="removePMargin"
              className="text-gray-800">
              {row?.original?.updatedAt
                ? format(row?.original?.updatedAt, 'HH:mm - dd/MM/yyyy')
                : null}
            </Typography>
          );
        },
        size: 200,
      },
      {
        accessorKey: 'updatedBy',
        header: ({ column }) => (
          <DataTableColumnHeader
            column={column}
            title={tSpecialCampaign('UPDATED_BY')}
          />
        ),
        cell: ({ row }) => (
          <Typography
            variant="p"
            affects="removePMargin"
            className="text-gray-800">
            {row.original.updatedBy}
          </Typography>
        ),
        size: 180,
        enableSorting: false,
      },
      {
        accessorKey: 'status',
        header: ({ column }) => (
          <DataTableColumnHeader
            column={column}
            title={tSpecialCampaign('STATUS')}
          />
        ),
        cell: ({ row }) => (
          <div className="flex text-start">
            <StatusBadge status={row.original.status} />
          </div>
        ),
        size: 100,
        enableSorting: false,
      },
      {
        id: 'actions',
        header: ({ column }) => (
          <DataTableColumnHeader
            column={column}
            title={tSpecialCampaign('ACTION')}
          />
        ),
        cell: ({ row }) => (
          <DropdownMenuBase
            links={[
              {
                link: `${ROUTE_NAME.SPECIAL_CAMPAIGN}/${row.original._id}`,
                label: tSpecialCampaign('VIEW'),
              },
              ...(permissions?.includes(PERMISSIONS.WRITE_SPECIAL_CAMPAIGN)
                ? [
                    {
                      link: `${ROUTE_NAME.SPECIAL_CAMPAIGN}/${row.original._id}/edit`,
                      label: tSpecialCampaign('UPDATE'),
                    },
                  ]
                : []),
            ]}
          />
        ),
        size: 44,
        enableSorting: false,
      },
    ],
    [permissions, tSpecialCampaign],
  );

  return (
    <>
      <div className="mb-6 flex items-center justify-between rounded-xl bg-secondary p-4 font-sans">
        <div className="grid space-y-2">
          <Typography variant="h2">
            {tSpecialCampaign('SPECIAL_CAMPAIGN')}
          </Typography>
          <Breadcrumbs />
        </div>
        <Button
          asChild={permissions?.includes(PERMISSIONS.WRITE_SPECIAL_CAMPAIGN)}
          disabled={!permissions?.includes(PERMISSIONS.WRITE_SPECIAL_CAMPAIGN)}>
          <Link
            className="flex items-center gap-2"
            to={`${ROUTE_NAME.SPECIAL_CAMPAIGN}/create`}>
            <Plus />
            {tSpecialCampaign('CREATE')}
          </Link>
        </Button>
      </div>
      <BTaskeeTable
        isShowClearButton
        data={specialCampaigns}
        columns={columns}
        total={total}
        pagination={getPageSizeAndPageIndex({
          total,
          pageSize: Number(searchParams.get('pageSize') || 0),
          pageIndex: Number(searchParams.get('pageIndex') || 0),
        })}
        localeAddress="special-campaign"
        onClickRow={specialCampaign => {
          navigate(`${ROUTE_NAME.SPECIAL_CAMPAIGN}/${specialCampaign._id}`);
        }}
        filterDate={{
          name: 'updatedAt',
          defaultValue: {
            from: momentTz(filterValue?.rangeDate?.from).toDate(),
            to: momentTz(filterValue?.rangeDate?.to).toDate(),
          },
        }}
        search={{
          placeholder: tSpecialCampaign('SEARCH_BY_NAME'),
          defaultValue: filterValue.search || '',
          name: 'search',
        }}
        filters={[
          {
            placeholder: tSpecialCampaign('TYPE'),
            name: 'type',
            options: Object.values(TYPE_SPECIAL_CAMPAIGN).map(type => ({
              label: tSpecialCampaign(type),
              value: type,
            })),
            value: filterValue?.type,
          },
          {
            placeholder: tSpecialCampaign('STATUS'),
            name: 'status',
            options: Object.values(STATUS).map(status => ({
              label: tSpecialCampaign(status),
              value: status,
            })),
            value: filterValue.status,
          },
        ]}
      />
    </>
  );
}
