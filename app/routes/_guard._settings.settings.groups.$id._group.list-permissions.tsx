import { json } from '@remix-run/node';
import {
  useActionData,
  useNavigate,
  useParams,
  useSubmit,
} from '@remix-run/react';
import { GROUP_STATUS, PERMISSIONS, ROUTE_NAME } from 'btaskee-constants';
import { useLoaderDataSafely } from 'btaskee-hooks';
import {
  Button,
  Checkbox,
  Label,
  Separator,
  VectorEmptyDataTable,
  toast,
  useConfirm,
} from 'btaskee-ui';
import { groupPermissionsByModule } from 'btaskee-utils';
import { ChevronDown, ChevronUp } from 'lucide-react';
import { useEffect, useState } from 'react';
import { useForm } from 'react-hook-form';
import { useTranslation } from 'react-i18next';
import { hocAction, hocLoader } from '~/hoc/remix';
import { getUserSession } from '~/services/helpers.server';
import {
  getGroupPermissions,
  getGroupStatus,
  getPermissionsCreatedByGroupId,
  updatePermissionsByGroupId,
} from '~/services/role-base-access-control.server';

export const loader = hocLoader(async ({ params, request }) => {
  const groupId = params.id || '';
  const { isSuperUser, isManager } = await getUserSession({
    headers: request.headers,
  });
  const [permissionsInGroups, allPermission, groupStatus] = await Promise.all([
    getPermissionsCreatedByGroupId({ groupId }),
    getGroupPermissions({ groupId, isSuperUser, isManager }),
    getGroupStatus({ groupId }),
  ]);

  const mappedPermissions = allPermission.filter(permission =>
    permissionsInGroups.includes(permission._id),
  );

  return json({
    isSuperUser,
    allPermissions: groupPermissionsByModule(allPermission),
    permissionsByModule: groupPermissionsByModule(mappedPermissions),
    groupStatus,
  });
}, PERMISSIONS.WRITE_ROLE);

export const action = hocAction(async ({ request, params }) => {
  const formData = await request.clone().formData();
  const permissions = JSON.parse(formData.get('data')?.toString() || '[]');

  await updatePermissionsByGroupId({ groupId: params.id || '', permissions });

  return json({ isSent: true });
}, PERMISSIONS.WRITE_ROLE);

export default function ContentSelector() {
  const { t: tGroupPermissions } = useTranslation('group-permissions');
  const navigate = useNavigate();
  const { permissionsByModule, allPermissions, groupStatus } =
    useLoaderDataSafely<typeof loader>();

  const actionData = useActionData<ActionTypeWithError<typeof action>>();
  useEffect(() => {
    if (actionData?.isSent) {
      toast({
        variant: 'success',
        description: tGroupPermissions('UPDATE_PERMISSIONS_SUCCESSFULLY'),
      });
    }
    if (actionData?.error) {
      toast({ description: actionData?.error });
    }
  }, [actionData?.error, actionData?.isSent, tGroupPermissions]);

  const params = useParams();
  const submit = useSubmit();
  const confirm = useConfirm();

  const [expandedSections, setExpandedSections] = useState<string[]>([]);
  const [selectedItems, setSelectedItems] = useState<string[]>([]);

  useEffect(() => {
    const initialSelectedItems = permissionsByModule.flatMap(section =>
      section.actions.map(item => item._id),
    );
    setSelectedItems(initialSelectedItems);
  }, [permissionsByModule]);

  const toggleSection = (sectionId: string) => {
    setExpandedSections(prev =>
      prev.includes(sectionId)
        ? prev.filter(id => id !== sectionId)
        : [...prev, sectionId],
    );
  };

  const toggleItem = (itemId: string) => {
    setSelectedItems(prev =>
      prev.includes(itemId)
        ? prev.filter(id => id !== itemId)
        : [...prev, itemId],
    );
  };

  const toggleAll = () => {
    const allItemIds = allPermissions.flatMap(section =>
      section.actions.map(item => item._id),
    );
    setSelectedItems(
      selectedItems.length === allItemIds.length ? [] : allItemIds,
    );
  };

  const isAllSelected = allPermissions
    .flatMap(section => section.actions)
    .every(item => selectedItems.includes(item._id));

  const { handleSubmit } = useForm<MustBeAny>();

  async function onSubmit() {
    const formData = new FormData();

    formData.append('data', JSON.stringify(selectedItems));

    const isConfirm = await confirm({
      title: tGroupPermissions('EDIT'),
      body: tGroupPermissions('ARE_YOU_SURE_EDIT_THIS_RECORD'),
      cancelButton: tGroupPermissions('CANCEL'),
      actionButton: tGroupPermissions('CONFIRM'),
    });

    if (isConfirm) submit(formData, { method: 'post' });
  }

  return (
    <div className="mx-auto grid w-full gap-2">
      {allPermissions?.length ? (
        <>
          <div className="mb-4 mt-6 flex items-center space-x-2">
            <Checkbox
              id="selectAll"
              checked={isAllSelected}
              onCheckedChange={toggleAll}
              disabled={groupStatus === GROUP_STATUS.INACTIVE}
            />
            <Label
              htmlFor="selectAll"
              className="text-base font-semibold leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70">
              {tGroupPermissions('SELECT_ALL')}
            </Label>
          </div>
          {allPermissions.map(section => (
            <div
              key={section.module}
              className="rounded-[6px] border border-gray-200 bg-gray-50">
              <button
                className="flex w-full items-center justify-between p-4 text-left"
                onClick={() => toggleSection(section.module)}>
                <div className="flex items-center space-x-2">
                  <Checkbox
                    id={section.module}
                    checked={section.actions.every(item =>
                      selectedItems.includes(item._id),
                    )}
                    onCheckedChange={() => {
                      const sectionItemIds = section.actions.map(
                        item => item._id,
                      );
                      const allSelected = sectionItemIds.every(id =>
                        selectedItems.includes(id),
                      );
                      setSelectedItems(
                        allSelected
                          ? selectedItems.filter(
                              id => !sectionItemIds.includes(id),
                            )
                          : [...new Set([...selectedItems, ...sectionItemIds])],
                      );
                    }}
                    onClick={event => event.stopPropagation()} // Prevent event bubbling
                    disabled={groupStatus === GROUP_STATUS.INACTIVE}
                  />
                  <label
                    htmlFor={section.module}
                    className="text-base font-semibold leading-none text-primary peer-disabled:cursor-not-allowed peer-disabled:opacity-70">
                    {section.module}
                  </label>
                </div>
                {expandedSections.includes(section.module) ? (
                  <ChevronUp className="h-4 w-4" />
                ) : (
                  <ChevronDown className="h-4 w-4" />
                )}
              </button>
              {expandedSections.includes(section.module) && (
                <>
                  <Separator className="!mx-4 mb-4 w-[calc(100%-32px)]" />
                  <div className="grid grid-cols-3 gap-x-[28px] gap-y-4 p-4 pt-0">
                    {section.actions.map(item => (
                      <div
                        key={item._id}
                        className="flex items-start space-x-2">
                        <Checkbox
                          className="mt-1.5"
                          id={item._id}
                          checked={selectedItems.includes(item._id)}
                          onCheckedChange={() => toggleItem(item._id)}
                          disabled={groupStatus === GROUP_STATUS.INACTIVE}
                        />
                        <div>
                          <label
                            htmlFor={item._id}
                            className="text-sm font-normal leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70">
                            {item.name}
                          </label>
                          <p className="mt-1 line-clamp-2 text-sm font-normal text-gray-400">
                            {item.description}
                          </p>
                        </div>
                      </div>
                    ))}
                  </div>
                </>
              )}
            </div>
          ))}
          <Separator className="!mb-6 !mt-4" />
          <div className="flex justify-end gap-4">
            <Button
              type="button"
              className="border border-gray-200"
              variant="ghost"
              onClick={() =>
                navigate(`${ROUTE_NAME.GROUP_SETTING}/${params.id}/member-list`)
              }>
              {tGroupPermissions('CANCEL')}
            </Button>
            <form onSubmit={handleSubmit(onSubmit)}>
              <Button
                disabled={groupStatus === GROUP_STATUS.INACTIVE}
                className="text-white"
                variant="default"
                type="submit"
                color="primary">
                {tGroupPermissions('SAVE_CHANGES')}
              </Button>
            </form>
          </div>
        </>
      ) : (
        <div className="mt-[58px] flex justify-center">
          <VectorEmptyDataTable width="272px" height="244px" />
        </div>
      )}
    </div>
  );
}
