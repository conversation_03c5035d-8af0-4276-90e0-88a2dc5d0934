import { zodResolver } from '@hookform/resolvers/zod';
import {
  isRouteErrorResponse,
  useNavigate,
  useRouteError,
  useSubmit,
} from '@remix-run/react';
import {
  ACTION_NAME,
  DAYS_OF_WEEK,
  OFFICE_STATUS_IN_CONFIGURATION,
  PERMISSIONS,
  ROUTE_NAME,
} from 'btaskee-constants';
import {
  Breadcrumbs,
  BreadcrumbsLink,
  BtaskeeResponseError,
  Button,
  DateTimePicker,
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
  HourRangePicker,
  Input,
  MultiSelectAdvance,
  SelectBase,
  Separator,
  Typography,
  toast,
  useConfirm,
} from 'btaskee-ui';
import { momentTz } from 'btaskee-utils';
import { Plus, Trash2 } from 'lucide-react';
import { useFieldArray, useForm } from 'react-hook-form';
import { useTranslation } from 'react-i18next';
import { z } from 'zod';
import { hocAction } from '~/hoc/remix';
import { useOutletOfficesConfiguration } from '~/hooks/useGetOfficesConfiguration';
import { getUserSession } from '~/services/helpers.server';
import { getHolidaysFromSettingSystem } from '~/services/holiday-configuration.server';
import {
  createOffice,
  getTotalOfficesFromSettingSystem,
} from '~/services/offices-configuration.server';
import { finalizeAction } from '~/utils/common';

export const handle = {
  breadcrumb: () => (
    <BreadcrumbsLink
      to={`${ROUTE_NAME.LIST_OFFICES}${ROUTE_NAME.CREATE}`}
      label="CREATE_OFFICE"
    />
  ),
};

export function ErrorBoundary() {
  const error = useRouteError() as { message?: string };
  const { t: tCommon } = useTranslation('common');

  if (isRouteErrorResponse(error)) {
    if (error?.message) {
      toast({ description: JSON.stringify(error.message) });
    }

    return <BtaskeeResponseError t={tCommon} errorStatus={error.status} />;
  }

  return <BtaskeeResponseError t={tCommon} errorStatus={500} />;
}

export const action = hocAction(
  async ({ request }, { setInformationActionHistory }) => {
    const { isoCode, username } = await getUserSession({
      headers: request.headers,
    });
    const formData = await request.clone().formData();
    const creationData = JSON.parse(
      formData.get('creationData')?.toString() || '{}',
    );

    const holidays = await getHolidaysFromSettingSystem({
      isoCode,
    });

    const activeHolidays =
      Array.isArray(creationData?.holidays) && creationData?.holidays.length > 0
        ? holidays.map(holiday => ({
            ...holiday,
            isActive: creationData.holidays.includes(holiday.reason),
          }))
        : [];

    const offDaysForOffice = [
      ...(Array.isArray(creationData?.offDays) &&
      creationData?.offDays.length > 0
        ? creationData.offDays.map(
            (
              offDay: OffDay & {
                date: { from: OffDay['from']; to: OffDay['to'] };
              },
            ) => ({
              ...offDay,
              from: momentTz(offDay.date.from).toDate(),
              to: momentTz(offDay.date.to).toDate(),
              isActive: true,
              isHoliday: false,
            }),
          )
        : []),
    ];
    const responseCreatingOffice = await createOffice({
      isoCode,
      username,
      name: creationData?.name,
      status: creationData?.status,
      phoneNumber: creationData?.phoneNumber,
      city: creationData?.city,
      address: creationData?.address,
      workingStartDate: creationData?.workingTime?.from,
      workingEndDate: creationData?.workingTime?.to,
      workingDays: creationData?.workingDays,
      offDays: activeHolidays,
      offDaysForOffice,
    });

    setInformationActionHistory({
      action: ACTION_NAME.CREATE_OFFICE_IN_CONFIGURATION,
      dataRelated: { name: creationData.name },
    });

    const total = await getTotalOfficesFromSettingSystem({ isoCode });

    return finalizeAction({
      request,
      flashMessage: {
        message: responseCreatingOffice.msg,
        translationKey: 'offices-configuration',
      },
      destinationUrl: `${ROUTE_NAME.LIST_OFFICES}/${total - 1}`,
    });
  },
  PERMISSIONS.WRITE_LIST_OFFICES,
);

export default function CreationOffice() {
  const { t } = useTranslation('offices-configuration');
  const navigate = useNavigate();
  const submit = useSubmit();
  const confirm = useConfirm();

  const { userCities, holidays } = useOutletOfficesConfiguration();

  const formSchema = z.object({
    name: z.string().min(1, t('THIS_FIELD_IS_REQUIRED')),
    address: z.string().min(1, t('THIS_FIELD_IS_REQUIRED')),
    status: z.string().min(1, t('THIS_FIELD_IS_REQUIRED')),
    phoneNumber: z.string().min(1, t('THIS_FIELD_IS_REQUIRED')),
    city: z.string().min(1, t('THIS_FIELD_IS_REQUIRED')),
    workingTime: z.object({
      from: z.date(),
      to: z.date(),
    }),
    workingDays: z.array(z.number()).min(1, t('THIS_FIELD_IS_REQUIRED')),
    holidays: z.array(z.string()),
    offDays: z.array(
      z.object({
        reason: z
          .string()
          .min(1, {
            message: t('DAY_OFF_REASON_MIN_LENGTH', { min: 3 }),
          })
          .max(100, {
            message: t('DAY_OFF_REASON_MAX_LENGTH', { max: 100 }),
          }),
        date: z
          .object({
            from: z.date(),
            to: z.date(),
          })
          .refine(
            date => {
              const tomorrow = momentTz().add(1, 'day').startOf('day').toDate();
              return date.from >= tomorrow && date.to >= tomorrow;
            },
            {
              message: t('DAY_OFF_DATE_MUST_BE_AT_LEAST_TOMORROW'),
            },
          ),
      }),
    ),
  });

  const form = useForm<z.infer<typeof formSchema>>({
    resolver: zodResolver(formSchema),
    defaultValues: {
      address: '',
      name: '',
      city: '',
      phoneNumber: '',
      status: '',
      workingTime: {
        from: momentTz().toDate(),
        to: momentTz().toDate(),
      },
      workingDays: [],
      holidays: [],
      offDays: [],
    },
  });

  const { fields, append, remove } = useFieldArray({
    control: form.control,
    name: 'offDays',
  });

  const onSubmit = async (creationData: z.infer<typeof formSchema>) => {
    const isConfirm = await confirm({
      title: t('TITLE_CONFIRM_OFFICE_CREATION'),
      body: t('DESCRIPTION_CONFIRM_OFFICE_CREATION'),
      cancelButton: t('CANCEL'),
      actionButton: t('CONFIRM'),
    });

    if (isConfirm) {
      const formData = new FormData();
      formData.append('creationData', JSON.stringify(creationData));

      submit(formData, { method: 'post' });
    }
  };

  return (
    <>
      <div className="mb-6 grid space-y-2 rounded-2xl bg-secondary p-4 min-h-[90px]">
        <Typography variant="h3">{t('CREATE_OFFICE')}</Typography>
        <Breadcrumbs />
      </div>
      <Form {...form}>
        <form onSubmit={form.handleSubmit(onSubmit)}>
          <div className="grid grid-cols-2 gap-6">
            <FormField
              name="name"
              control={form.control}
              render={({ field }) => (
                <FormItem>
                  <FormLabel className={'text-gray-700'}>
                    {t('OFFICE_NAME')}
                  </FormLabel>
                  <FormControl>
                    <Input {...field} placeholder={t('ENTER_OFFICE_NAME')} />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />
            <FormField
              name="address"
              control={form.control}
              render={({ field }) => (
                <FormItem>
                  <FormLabel className={'text-gray-700'}>
                    {t('ADDRESS')}
                  </FormLabel>
                  <FormControl>
                    <Input {...field} placeholder={t('ENTER_ADDRESS')} />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />
            <FormField
              name="city"
              control={form.control}
              render={({ field: { onChange, value } }) => (
                <FormItem>
                  <FormLabel className={'text-gray-700'}>{t('CITY')}</FormLabel>
                  <FormControl>
                    <SelectBase
                      placeholder={t('SELECT_CITY')}
                      defaultValue={value?.toString() || ''}
                      onValueChange={onChange}
                      options={userCities?.map(city => ({
                        label: city,
                        value: city,
                      }))}
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />
            <FormField
              name="phoneNumber"
              control={form.control}
              render={({ field }) => (
                <FormItem>
                  <FormLabel className={'text-gray-700'}>
                    {t('PHONE_NUMBER')}
                  </FormLabel>
                  <FormControl>
                    <Input {...field} placeholder={t('ENTER_PHONE_NUMBER')} />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />
            <FormField
              name="workingTime"
              control={form.control}
              render={({ field }) => (
                <FormItem>
                  <FormLabel className={'text-gray-700'}>
                    {t('OPERATION_HOURS')}
                  </FormLabel>
                  <FormControl>
                    <HourRangePicker onValueChange={field.onChange} />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />
            <FormField
              name="workingDays"
              control={form.control}
              render={({ field: { onChange, value } }) => (
                <FormItem>
                  <FormLabel className={'text-gray-700'}>
                    {t('WORKING_DAY')}
                  </FormLabel>
                  <FormControl>
                    <MultiSelectAdvance
                      placeholder={t('SELECT_WORKING_DAY')}
                      onValueChange={selectedValues => {
                        const formattedValues = selectedValues?.map(value =>
                          Number(value),
                        );
                        onChange(formattedValues);
                      }}
                      defaultValue={
                        value ? value?.map((day: number) => day.toString()) : []
                      }
                      options={DAYS_OF_WEEK?.map(day => ({
                        label: t(day.label),
                        value: day.value.toString(),
                      }))}
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />
            <FormField
              name="holidays"
              control={form.control}
              render={({ field: { onChange, value } }) => (
                <FormItem>
                  <FormLabel className={'text-gray-700'}>
                    {t('HOLIDAY')}
                  </FormLabel>
                  <FormControl>
                    <MultiSelectAdvance
                      placeholder={t('SELECT_HOLIDAY')}
                      onValueChange={selectedValues => {
                        const formattedValues = selectedValues?.map(
                          value => value,
                        );
                        onChange(formattedValues);
                      }}
                      defaultValue={
                        value ? value?.map((holiday: string) => holiday) : []
                      }
                      options={holidays?.map(holiday => ({
                        label: holiday.reason,
                        value: holiday.reason,
                      }))}
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />
            <FormField
              name="status"
              control={form.control}
              render={({ field: { onChange, value } }) => (
                <FormItem>
                  <FormLabel className={'text-gray-700'}>
                    {t('STATUS')}
                  </FormLabel>
                  <FormControl>
                    <SelectBase
                      placeholder={t('SELECT_STATUS')}
                      defaultValue={value?.toString() || ''}
                      onValueChange={onChange}
                      options={Object.values(
                        OFFICE_STATUS_IN_CONFIGURATION,
                      )?.map(status => ({
                        label: t(status),
                        value: status,
                      }))}
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            {fields.map((field, index) => (
              <div
                className="flex flex-col rounded-md border border-gray-200"
                key={field.id}>
                <div className="flex justify-between items-center text-gray-700 bg-gray-100 p-4">
                  <Typography
                    variant="p"
                    affects={'removePMargin'}
                    className="text-sm leading-tight font-medium">
                    {t('OFF_DAY', { index: index + 1 })}
                  </Typography>
                  <Button
                    type="button"
                    variant="ghost"
                    className="text-sm leading-none flex items-center gap-2 p-1 h-fit"
                    onClick={() => remove(index)}>
                    <Trash2 className="min-w-4 min-h-4 w-4 h-4" />
                    {t('REMOVE')}
                  </Button>
                </div>

                <div className="flex flex-col gap-2 p-4">
                  <FormField
                    name={`offDays.${index}.reason`}
                    control={form.control}
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>{t('REASON')}</FormLabel>
                        <FormControl>
                          <Input {...field} placeholder={t('ENTER_REASON')} />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                  <DateTimePicker
                    form={form}
                    name={`offDays.${index}.date`}
                    label={t('DATE')}
                    showTime={false}
                    formatString="dd/MM/yyyy"
                  />
                </div>
              </div>
            ))}

            <Button
              type="button"
              variant="ghost"
              className="border-dashed border border-primary text-primary hover:text-primary flex items-center gap-2 p-6 h-fit hover:bg-primary-50"
              onClick={() =>
                append({
                  reason: '',
                  date: {
                    from: momentTz().add(1, 'day').startOf('day').toDate(),
                    to: momentTz().add(1, 'day').startOf('day').toDate(),
                  },
                })
              }>
              <Plus className="w-5 h-5" />
              {t('ADD_OFF_DAY')}
            </Button>
          </div>
          <Separator className="my-6" />
          <div className="flex justify-end gap-4">
            <Button
              type="button"
              variant="outline"
              className="border-primary text-primary hover:text-primary"
              onClick={() => navigate(ROUTE_NAME.LIST_OFFICES)}>
              {t('CANCEL')}
            </Button>
            <Button type="submit" className="bg-primary hover:bg-primary-600">
              {t('SUBMIT')}
            </Button>
          </div>
        </form>
      </Form>
    </>
  );
}
