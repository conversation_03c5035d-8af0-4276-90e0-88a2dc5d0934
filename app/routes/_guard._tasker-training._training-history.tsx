import { Outlet } from '@remix-run/react';
import { PERMISSIONS, ROUTE_NAME } from 'btaskee-constants';
import { useLoaderDataSafely } from 'btaskee-hooks';
import { BreadcrumbsLink, toast } from 'btaskee-ui';
import { useEffect } from 'react';
import { hocLoader } from '~/hoc/remix';
import { willBecomeOverallTrainingHistoryLoader } from '~/hooks/useLoaderOverallTrainingHistory';

export const handle = {
  breadcrumb: () => (
    <BreadcrumbsLink
      to={ROUTE_NAME.TRAINING_HISTORY}
      label="TRAINING_HISTORY"
    />
  ),
};

export const loader = hocLoader(
  willBecomeOverallTrainingHistoryLoader,
  PERMISSIONS.READ_TRAINING_HISTORY_OF_ALL_TASKER,
);

export default function TrainingHistoryRoot() {
  const { error: loaderError, ...restLoaderData } =
    useLoaderDataSafely<typeof loader>();

  useEffect(() => {
    if (loaderError) {
      toast({ description: loaderError });
    }
  }, [loaderError]);

  return <Outlet context={restLoaderData} />;
}
