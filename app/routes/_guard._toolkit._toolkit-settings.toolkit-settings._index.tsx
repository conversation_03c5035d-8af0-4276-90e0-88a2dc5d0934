import type { LoaderFunctionArgs, SerializeFrom } from '@remix-run/node';
import { json } from '@remix-run/node';
import {
  Link,
  useLoaderData,
  useNavigate,
  useSearchParams,
} from '@remix-run/react';
import type { ColumnDef } from '@tanstack/react-table';
import { PERMISSIONS, ROUTE_NAME } from 'btaskee-constants';
import { useGlobalStore } from 'btaskee-hooks';
import {
  AspectRatio,
  BTaskeeTable,
  Breadcrumbs,
  Button,
  DataTableColumnHeader,
  DropdownMenuBase,
  Grid,
  MultiLanguageText,
  Typography,
} from 'btaskee-ui';
import {
  DEFAULT_RANGE_DATE_CURRENT_DAY,
  convertSortString,
  formatNumberWithCommas,
  getOrderNumber,
  getPageSizeAndPageIndex,
  getSkipAndLimit,
  getTotalPriceToolkitSetting,
  momentTz,
} from 'btaskee-utils';
import i18n from 'i18next';
import { Plus } from 'lucide-react';
import { useMemo } from 'react';
import { useTranslation } from 'react-i18next';
import { ServiceIconWithTooltip } from '~/components/common/CommonComponent';
import { hocLoader } from '~/hoc/remix';
import {
  getSettingCountryByIsoCode,
  getUserSession,
} from '~/services/helpers.server';
import { getServices } from '~/services/service.server';
import {
  getListToolkitSetting,
  getTotalToolkitSetting,
} from '~/services/toolkit.server';

export const loader = hocLoader(async ({ request }: LoaderFunctionArgs) => {
  const url = new URL(request.url);
  const { isoCode } = await getUserSession({
    headers: request.headers,
  });
  const sort = url.searchParams.get('sort') || '';
  const search = url.searchParams.get('name') || '';
  const services = url.searchParams.get('services') || '';
  const createdAt = url.searchParams.get('createdAt') || '';
  const rangeDate = DEFAULT_RANGE_DATE_CURRENT_DAY(createdAt);

  const total = await getTotalToolkitSetting({
    isoCode,
    services,
    search,
    rangeDate,
  });

  const { limit, skip } = getSkipAndLimit(
    getPageSizeAndPageIndex({
      total,
      pageSize: Number(url.searchParams.get('pageSize')) || 0,
      pageIndex: Number(url.searchParams.get('pageIndex')) || 0,
    }),
  );

  const [settingCountry, listAllServices, tableData] = await Promise.all([
    getSettingCountryByIsoCode({ isoCode, projection: { currency: 1 } }),
    getServices({
      isoCode,
      projection: {
        _id: 1,
        name: 1,
        text: 1,
        isSubscription: 1,
      },
    }),
    getListToolkitSetting({
      isoCode,
      search,
      services,
      rangeDate,
      skip,
      limit,
      sort: convertSortString({
        sortString: sort,
        defaultValue: { updatedAt: -1 },
      }),
    }),
  ]);

  return json({
    total: total || 0,
    tableData: tableData || [],
    filter: {
      name: search,
      services,
      rangeDate,
    },
    listAllServices: listAllServices || [],
    settingCountry: settingCountry,
  });
}, PERMISSIONS.READ_TOOLKIT_SETTING);

export default function ToolkitSetting() {
  const { t } = useTranslation('toolkit');
  const loaderData = useLoaderData<LoaderTypeWithError<typeof loader>>();

  const permissions = useGlobalStore(store => store.permissions);
  const [searchParams] = useSearchParams();
  const navigate = useNavigate();

  const columns: ColumnDef<
    SerializeFrom<ReturnValueIgnorePromise<typeof getListToolkitSetting>[0]>
  >[] = useMemo(
    () => [
      {
        accessorKey: 'No',
        header: ({ column }) => (
          <DataTableColumnHeader column={column} title={t('NO')} />
        ),
        maxSize: 20,
        cell: ({ row }) => (
          <Typography variant="p">
            {getOrderNumber({
              pageSize: Number(searchParams.get('pageSize') || 10),
              pageIndex: Number(searchParams.get('pageIndex') || 0),
              orderColumn: row.index || 0,
            })}
          </Typography>
        ),
        enableSorting: false,
      },
      {
        size: 150,
        accessorKey: 'imageCover',
        header: ({ column }) => (
          <DataTableColumnHeader
            className="whitespace-nowrap"
            column={column}
            title={t('IMAGE_COVER')}
          />
        ),
        cell: ({ row }) => (
          <div className="w-[108px]">
            <AspectRatio ratio={2 / 1}>
              <img
                src={row?.original?.image || ''}
                alt="Toolkit"
                className="rounded-sm object-cover w-full h-full"
              />
            </AspectRatio>
          </div>
        ),
        enableSorting: false,
      },
      {
        accessorKey: 'toolkitName',
        header: ({ column }) => (
          <DataTableColumnHeader
            className="whitespace-nowrap"
            column={column}
            title={t('TOOLKIT_NAME')}
          />
        ),
        cell: ({ row }) => <MultiLanguageText text={row.original?.text} />,
        enableSorting: false,
        size: 220,
      },
      {
        accessorKey: 'price',
        header: ({ column }) => (
          <DataTableColumnHeader column={column} title={t('PRICE')} />
        ),
        maxSize: 40,
        cell: ({ row }) => (
          <Typography variant="p" affects="removePMargin">
            {formatNumberWithCommas(
              getTotalPriceToolkitSetting(
                row.original?.toolKitItems || [],
                // TODO: fix any type after move utils func to utils repos by the comment owner
                (row.original?.toolkitItemsInfos || []) as MustBeAny,
              ),
            )}
            {loaderData.settingCountry?.currency?.sign}
          </Typography>
        ),
        enableSorting: true,
      },
      {
        accessorKey: 'services',
        header: ({ column }) => (
          <DataTableColumnHeader
            className="whitespace-nowrap"
            column={column}
            title={t('APPLICABLE_SERVICE')}
          />
        ),
        size: 80,
        cell: ({ row }) => (
          <div className="flex flex-wrap gap-3">
            {row.original?.serviceInfos?.map((service: Service) => (
              <ServiceIconWithTooltip key={service._id} service={service} />
            ))}
          </div>
        ),
        enableSorting: false,
      },
      {
        accessorKey: 'createdAt',
        header: ({ column }) => (
          <DataTableColumnHeader
            className="whitespace-nowrap"
            column={column}
            title={t('CREATED_AT')}
          />
        ),
        size: 80,
        cell: ({ row }) => (
          <Typography
            variant="p"
            className="whitespace-nowrap"
            affects="removePMargin">
            {row.original?.createdAt
              ? momentTz(row.original.createdAt).format('HH:mm - DD/MM/YYYY')
              : null}
          </Typography>
        ),
        enableSorting: true,
      },
      {
        accessorKey: 'updatedAt',
        header: ({ column }) => (
          <DataTableColumnHeader
            className="whitespace-nowrap"
            column={column}
            title={t('UPDATED_AT')}
          />
        ),
        size: 40,
        cell: ({ row }) => (
          <Typography
            variant="p"
            className="whitespace-nowrap"
            affects="removePMargin">
            {row.original?.updatedAt
              ? momentTz(row.original.updatedAt).format('HH:mm - DD/MM/YYYY')
              : row.original?.createdAt
                ? momentTz(row.original.createdAt).format('HH:mm - DD/MM/YYYY')
                : null}
          </Typography>
        ),
        enableSorting: true,
      },
      {
        accessorKey: 'action',
        header: ({ column }) => {
          return (
            <DataTableColumnHeader
              column={column}
              className="whitespace-nowrap text-right"
              title={t('ACTION')}
            />
          );
        },
        size: 40,
        cell: ({ row }) => (
          <DropdownMenuBase
            btnClassName="px-3 w-fit ml-auto"
            align="end"
            links={[
              {
                link: `${ROUTE_NAME.TOOLKIT_SETTING}/${row.original?._id}`,
                label: t('VIEW_DETAIL'),
              },
              {
                link: `${ROUTE_NAME.TOOLKIT_SETTING}/${row.original?._id}/edit`,
                label: t('UPDATE_TOOLKIT'),
              },
            ]}
          />
        ),
        enableSorting: false,
      },
    ],
    [loaderData.settingCountry?.currency?.sign, searchParams, t],
  );

  return (
    <>
      <div className="flex bg-secondary p-4 justify-between items-center min-h-24 rounded-md mb-6">
        <Grid className="gap-3">
          <Typography className="capitalize" variant="h2">
            {t('TOOLKIT_MANAGEMENT')}
          </Typography>
          <Breadcrumbs />
        </Grid>
        <Button
          asChild
          disabled={!permissions.includes(PERMISSIONS.WRITE_TOOLKIT_SETTING)}>
          <Link
            className="flex gap-2 items-center"
            to={ROUTE_NAME.CREATE_TOOLKIT_SETTING}>
            <Plus />
            {t('CREATE_TOOLKIT')}
          </Link>
        </Button>
      </div>
      <BTaskeeTable
        localeAddress="toolkit"
        isShowClearButton
        columns={columns}
        total={loaderData.total || 0}
        data={loaderData.tableData || []}
        onClickRow={row => navigate(`${ROUTE_NAME.TOOLKIT_SETTING}/${row._id}`)}
        search={{
          defaultValue: loaderData.filter?.name || '',
          name: 'name',
          placeholder: t('SEARCH_BY_NAME'),
        }}
        pagination={getPageSizeAndPageIndex({
          total: loaderData.total || 0,
          pageSize: Number(searchParams.get('pageSize') || 0),
          pageIndex: Number(searchParams.get('pageIndex') || 0),
        })}
        filters={[
          {
            name: 'services',
            placeholder: t('SERVICES'),
            options:
              loaderData.listAllServices?.map(service => ({
                label: service?.text?.[i18n.language || 'en']
                  ? `${service.text[i18n.language || 'en']}${
                      service.isSubscription ? ' (Subscription)' : ''
                    }`
                  : `${service.name}${service.isSubscription ? ' (Subscription)' : ''}`,
                value: service._id,
              })) || [],
            value: loaderData.filter?.services || '',
          },
        ]}
        filterDate={{
          name: 'createdAt',
          defaultValue: {
            from: momentTz(loaderData.filter?.rangeDate?.from).toDate(),
            to: momentTz(loaderData.filter?.rangeDate?.to).toDate(),
          },
        }}
      />
    </>
  );
}
