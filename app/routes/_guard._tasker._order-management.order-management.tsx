import { type SerializeFrom, json } from '@remix-run/node';
import {
  Link,
  Outlet,
  isRouteErrorResponse,
  useLoaderData,
  useLocation,
  useRouteError,
  useSearchParams,
} from '@remix-run/react';
import { type ColumnDef } from '@tanstack/react-table';
import {
  ORDER_STATUS,
  PAYMENT_TYPE,
  PERMISSIONS,
  ROUTE_NAME,
} from 'btaskee-constants';
import { useGlobalStore } from 'btaskee-hooks';
import {
  BTaskeeTable,
  Badge,
  Breadcrumbs,
  BtaskeeResponseError,
  DataTableColumnHeader,
  Typography,
  cn,
  toast,
} from 'btaskee-ui';
import {
  DEFAULT_RANGE_DATE_CURRENT_DAY,
  convertSortString,
  createUID,
  formatNumberWithCommas,
  getPageSizeAndPageIndex,
  getSkipAndLimit,
  momentTz,
} from 'btaskee-utils';
import i18n from 'i18next';
import { useCallback, useEffect, useMemo } from 'react';
import { useTranslation } from 'react-i18next';
import { hocLoader } from '~/hoc/remix';
import {
  getSettingCountryByIsoCode,
  getUserSession,
} from '~/services/helpers.server';
import {
  getPaymentToolKitTransaction,
  getTotalPaymentToolKitTransaction,
} from '~/services/payment-and-delivery.server';
import { getServices } from '~/services/service.server';
import { getSession } from '~/services/session.server';

export const loader = hocLoader(
  async ({ request }) => {
    const session = await getSession(request.headers.get('cookie'));
    const url = new URL(request.url);
    const { isoCode } = await getUserSession({
      headers: request.headers,
    });
    const flashMessage = await session.get('flashMessage');

    const sort = url.searchParams.get('sort') || '';
    const createdAt = url.searchParams.get('createdAt') || '';
    const service = url.searchParams.get('service') || '';
    const typeOfPayment = url.searchParams.get('type_of_payment') || '';
    const status = url.searchParams.get('status') || '';
    const search = url.searchParams.get('search') || '';

    const filter = {
      search,
      service,
      typeOfPayment,
      status,
      rangeDate: DEFAULT_RANGE_DATE_CURRENT_DAY(createdAt),
    };

    const [total, services, settingCountry] = await Promise.all([
      getTotalPaymentToolKitTransaction({ isoCode, filter }),
      getServices({
        isoCode,
        projection: { icon: 1, text: 1, isSubscription: 1 },
      }),
      getSettingCountryByIsoCode({ isoCode, projection: { currency: 1 } }),
    ]);

    const { limit, skip } = getSkipAndLimit(
      getPageSizeAndPageIndex({
        total,
        pageSize: Number(url.searchParams.get('pageSize')) || 0,
        pageIndex: Number(url.searchParams.get('pageIndex')) || 0,
      }),
    );

    const orderServicesData = await getPaymentToolKitTransaction({
      isoCode,
      skip,
      limit,
      filter,
      sort: convertSortString({
        sortString: sort,
        defaultValue: { createdAt: -1 },
      }),
    });

    return json({
      orderServicesData,
      services,
      flashMessage,
      total,
      filter,
      settingCountry,
    });
  },
  [PERMISSIONS.READ_ORDER_MANAGEMENT],
);

export function ErrorBoundary() {
  const error = useRouteError() as { message?: string };
  const { t: tCommon } = useTranslation('common');

  if (isRouteErrorResponse(error)) {
    if (error?.message) {
      toast({ description: JSON.stringify(error.message) });
    }

    return <BtaskeeResponseError t={tCommon} errorStatus={error.status} />;
  }

  return <BtaskeeResponseError t={tCommon} errorStatus={500} />;
}

export default function OrderManagement() {
  const { t } = useTranslation('order-management');
  const loaderData = useLoaderData<LoaderTypeWithError<typeof loader>>();
  const permissions = useGlobalStore(store => store.permissions);

  const [searchParams] = useSearchParams();
  const { pathname, search } = useLocation();

  const styledStatusBadge = useCallback((status: OrderStatus) => {
    const statusStyles = {
      PAID: 'bg-blue-50 text-blue-500',
      DELIVERED: 'bg-primary-50 text-primary',
      RECEIVED: 'bg-secondary text-secondary-foreground',
    };
    return statusStyles[status] || '';
  }, []);

  const formattedData = useMemo(
    () =>
      loaderData?.orderServicesData?.data?.map(order => {
        return {
          ...order,
          registeredServices: order.registeredServices.map(
            (registered: string) => {
              return {
                id: registered,
                icon:
                  loaderData?.services?.find(
                    service => service._id === registered,
                  )?.icon || '',
              };
            },
          ),
        };
      }) || [],
    [loaderData?.orderServicesData?.data, loaderData?.services],
  );

  const writePermission = permissions.includes(
    PERMISSIONS.WRITE_ORDER_MANAGEMENT,
  );

  const readPermission = permissions.includes(
    PERMISSIONS.READ_ORDER_MANAGEMENT,
  );

  const columns: ColumnDef<SerializeFrom<PaymentTransaction>>[] = [
    {
      accessorKey: 'phoneNumber',
      header: ({ column }) => (
        <DataTableColumnHeader column={column} title={t('PHONE_NUMBER')} />
      ),
      cell: ({ row }) => (
        <Typography variant="p">{row.original?.tasker?.phone || ''}</Typography>
      ),
      enableSorting: false,
      size: 100,
    },
    {
      accessorKey: 'taskerName',
      header: ({ column }) => (
        <DataTableColumnHeader column={column} title={t('TASKER_NAME')} />
      ),
      cell: ({ row }) => (
        <Typography variant="p" className="whitespace-nowrap">
          {row.original?.tasker?.name || ''}
        </Typography>
      ),
      enableSorting: false,
      size: 160,
    },
    {
      accessorKey: 'service',
      header: ({ column }) => (
        <DataTableColumnHeader column={column} title={t('SERVICE')} />
      ),
      cell: ({ row }) => (
        <div className="flex items-center">
          {row.original?.registeredServices?.map(service => (
            <img
              src={service?.icon || ''}
              alt="service icon gap-3"
              className="object-cover rounded-md w-11 h-11"
              key={createUID()}
            />
          ))}
        </div>
      ),
      enableSorting: false,
      size: 330,
    },
    {
      accessorKey: 'amount',
      header: ({ column }) => (
        <DataTableColumnHeader column={column} title={t('AMOUNT')} />
      ),
      cell: ({ row }) => (
        <Typography variant="p" className="whitespace-nowrap">
          {(row.original?.amount || 0).toLocaleString() +
            loaderData?.settingCountry?.currency?.sign || ''}
        </Typography>
      ),
      size: 60,
    },
    {
      accessorKey: 'typeOfPayment',
      header: ({ column }) => (
        <DataTableColumnHeader column={column} title={t('TYPE_OF_PAYMENT')} />
      ),
      cell: ({ row }) => (
        <Typography variant="p" className="whitespace-nowrap">
          {t(PAYMENT_TYPE[row.original?.type as keyof typeof PAYMENT_TYPE])}
        </Typography>
      ),
      enableSorting: false,
      size: 150,
    },
    {
      accessorKey: 'status',
      header: ({ column }) => (
        <DataTableColumnHeader column={column} title={t('STATUS')} />
      ),
      cell: ({ row }) => (
        <Badge
          className={cn(
            'text-xs font-medium whitespace-nowrap',
            styledStatusBadge(row.original?.status as OrderStatus),
          )}>
          {t(row.original?.status)}
        </Badge>
      ),
      enableSorting: false,
      size: 60,
    },
    {
      accessorKey: 'action',
      header: ({ column }) => (
        <DataTableColumnHeader column={column} title={t('ACTION')} />
      ),
      cell: ({ row }) => {
        return (
          <Link
            state={{ from: pathname + search }}
            to={`${ROUTE_NAME.ORDER_MANAGEMENT}/${row.original._id}/${row.original?.status === 'PAID' ? 'delivery' : 'view'}`}
            className={cn(
              'font-normal text-sm text-primary underline hover:cursor-pointer hover:no-underline',
              `${row.original?.status === 'PAID' && !writePermission ? 'pointer-events-none opacity-50' : ''}`,
              `${row.original?.status === 'DELIVERED' && !readPermission ? 'pointer-events-none opacity-50' : ''}`,
            )}>
            {t(row.original?.status === 'PAID' ? 'DELIVERY' : 'VIEW')}
          </Link>
        );
      },
      enableSorting: false,
      size: 60,
    },
  ];

  useEffect(() => {
    if (loaderData?.flashMessage) {
      toast({ description: loaderData.flashMessage, variant: 'success' });
    }
  }, [loaderData?.flashMessage]);

  return (
    <>
      <div className="flex flex-col bg-secondary gap-3 p-4 justify-between min-h-24 rounded-md mb-6">
        <Typography variant="h2">{t('ORDER_MANAGEMENT')}</Typography>
        <Breadcrumbs />
      </div>
      <BTaskeeTable
        isShowClearButton
        columns={columns}
        total={loaderData?.total || 0}
        data={formattedData || []}
        search={{
          defaultValue: searchParams.get('search') || '',
          name: 'search',
          placeholder: t('SEARCH'),
        }}
        pagination={getPageSizeAndPageIndex({
          total: loaderData?.total || 0,
          pageSize: Number(searchParams.get('pageSize') || 0),
          pageIndex: Number(searchParams.get('pageIndex') || 0),
        })}
        localeAddress="order-management"
        filterDate={{
          name: 'createdAt',
          defaultValue: {
            from: momentTz(loaderData?.filter?.rangeDate?.from).toDate(),
            to: momentTz(loaderData?.filter?.rangeDate?.to).toDate(),
          },
        }}
        filters={[
          {
            name: 'service',
            placeholder: t('SERVICE'),
            options:
              loaderData?.services?.map(service => {
                return {
                  label: `${service?.text?.[i18n.language || 'en']} ${service?.isSubscription ? '(Subscription)' : ''}`,
                  value: service._id,
                };
              }) || [],
            value: loaderData?.filter?.service || '',
          },
          {
            name: 'type_of_payment',
            placeholder: t('TYPE_OF_PAYMENT'),
            options: Object.keys(PAYMENT_TYPE).map(type => {
              return {
                label: t(PAYMENT_TYPE[type as keyof typeof PAYMENT_TYPE]),
                value: type,
              };
            }),
            value: loaderData?.filter?.typeOfPayment || '',
          },
          {
            name: 'status',
            placeholder: t('STATUS'),
            options: ORDER_STATUS.map(status => {
              return {
                label: t(status),
                value: status,
              };
            }),
            value: loaderData?.filter?.status || '',
          },
        ]}
        extraContent={
          <div className="flex justify-end">
            <div className="flex flex-col gap-1 w-fit text-center">
              <Typography className="text-sm font-normal text-gray-400">
                {t('TOTAL_PAID_AMOUNT')}
              </Typography>
              <Badge className="text-lg font-semibold px-6 py-3 rounded-md bg-blue-50 text-blue-500">
                {formatNumberWithCommas(
                  loaderData?.orderServicesData?.totalAmount,
                ) + (loaderData?.settingCountry?.currency?.sign || '')}
              </Badge>
            </div>
          </div>
        }
      />
      <Outlet />
    </>
  );
}
