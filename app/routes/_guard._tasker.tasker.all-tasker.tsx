import { DotsHorizontalIcon } from '@radix-ui/react-icons';
import type { SerializeFrom } from '@remix-run/node';
import {
  Link,
  isRouteErrorResponse,
  json,
  useLoaderData,
  useNavigate,
  useRouteError,
  useSearchParams,
} from '@remix-run/react';
import type { ColumnDef } from '@tanstack/react-table';
import { GENDER, PERMISSIONS, ROUTE_NAME, STATUSES } from 'btaskee-constants';
import { useGlobalStore } from 'btaskee-hooks';
import {
  Avatar,
  AvatarFallback,
  AvatarImage,
  BTaskeeTable,
  Badge,
  Breadcrumbs,
  BreadcrumbsLink,
  BtaskeeResponseError,
  Button,
  DataTableColumnHeader,
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
  Grid,
  Typography,
  cn,
} from 'btaskee-ui';
import {
  convertSortString,
  createUID,
  formatNumberWithCommas,
  getPageSizeAndPageIndex,
  getSkipAndLimit,
  getValuesFromSearchParams,
} from 'btaskee-utils';
import { format } from 'date-fns';
import i18n from 'i18next';
import { UserCircle } from 'lucide-react';
import { useCallback, useMemo } from 'react';
import { useTranslation } from 'react-i18next';
import { AccountMulti, AccountMultiCheck } from '~/components/common/FigmaIcon';
import { hocLoader } from '~/hoc/remix';
import { USER_STATUS, USER_TYPE } from '~/services/constants.server';
import { getCitiesByUserId, getUserSession } from '~/services/helpers.server';
import { verifyManager } from '~/services/role-base-access-control.server';
import { getServices } from '~/services/service.server';
import { getSession } from '~/services/session.server';
import {
  getListTaskerOnAllTaskerPage,
  getTotalTaskerOnAllTaskerPage,
  getTotalUsers,
} from '~/services/tasker-list.server';
import { formatPhoneNumber } from '~/utils/common';

export const handle = {
  breadcrumb: () => {
    return <BreadcrumbsLink to={ROUTE_NAME.ALL_TASKER} label="ALL_TASKER" />;
  },
};

export const loader = hocLoader(
  async ({ request }) => {
    const url = new URL(request.url);
    const [
      { sort: sortString, services, status, search },
      { pageSize, pageIndex },
    ] = getValuesFromSearchParams(url.searchParams, {
      keysString: ['sort', 'services', 'status', 'search'],
      keysNumber: ['pageSize', 'pageIndex'],
    });

    const { isoCode, userId, isSuperUser } = await getUserSession({
      headers: request.headers,
    });

    const filterValue = {
      search,
      status,
      services,
    };

    const isManager = await verifyManager(userId);

    const userCities = await getCitiesByUserId({
      userId,
      isManager: isSuperUser || isManager,
    });

    const totalTasker = await getTotalTaskerOnAllTaskerPage({
      filterValue,
      isoCode,
      city: userCities,
    });

    const { limit, skip } = getSkipAndLimit(
      getPageSizeAndPageIndex({
        total: totalTasker,
        pageSize,
        pageIndex,
      }),
    );

    const [taskerList, totalUsers, totalActiveUsers, serviceList, session] =
      await Promise.all([
        getListTaskerOnAllTaskerPage({
          isoCode,
          filterValue,
          sort: convertSortString({
            sortString,
            defaultValue: { createdAt: -1 },
          }),
          skip,
          limit,
          city: userCities,
        }),
        getTotalUsers({
          isoCode,
          userType: USER_TYPE.TASKER,
          cities: userCities,
        }),
        getTotalUsers({
          isoCode,
          userType: USER_TYPE.TASKER,
          userStatus: USER_STATUS.ACTIVE,
          cities: userCities,
        }),
        getServices({
          isoCode,
          projection: { name: 1, text: 1, icon: 1, isSubscription: 1 },
        }),
        getSession(request.headers.get('cookie')),
      ]);

    const flashMessage = await session.get('flashMessage');

    return json({
      filterValue,
      total: totalTasker,
      taskerList,
      totalUsers,
      totalActiveUsers,
      serviceList,
      flashMessage,
    });
  },
  [PERMISSIONS.READ_TASKER_LIST],
);

export function ErrorBoundary() {
  const error = useRouteError();
  const { t: tCommon } = useTranslation('common');

  if (isRouteErrorResponse(error)) {
    return <BtaskeeResponseError t={tCommon} errorStatus={error.status} />;
  }

  return <BtaskeeResponseError t={tCommon} errorStatus={500} />;
}

export default function AllTaskerScreen() {
  const { t } = useTranslation('all-tasker');
  const loaderData = useLoaderData<LoaderTypeWithError<typeof loader>>();

  const permissions = useGlobalStore(store => store.permissions);
  const navigate = useNavigate();
  const [searchParams] = useSearchParams();

  const serviceIconMap = useMemo(() => {
    const services: { [key: string]: string } = {};

    loaderData?.serviceList?.forEach(
      (service: { _id: string; icon: string }) => {
        services[service._id || ''] = service.icon || '';
      },
    );

    return services;
  }, [loaderData?.serviceList]);

  const styleStatusBadge = useCallback((status: keyof typeof STATUSES) => {
    switch (status) {
      case 'ACTIVE':
        return 'text-secondary-foreground bg-secondary';
      case 'DISABLED':
        return 'text-gray-500 bg-gray-100';
      case 'INACTIVE':
        return 'text-yellow-500 bg-yellow-50';
      case 'IN_PROBATION':
        return 'text-primary bg-primary-50';
      case 'LOCKED':
        return 'text-red-500 bg-red-50';
      case 'UNVERIFIED':
        return 'text-yellow-500 bg-yellow-50';
      case 'UNLOCKED':
        return 'text-blue-500 bg-blue-50';
      case 'PASSED_INTERVIEW':
        return 'text-blue-500 bg-blue-50';
      default:
        return '';
    }
  }, []);

  const columns: ColumnDef<
    SerializeFrom<
      ReturnValueIgnorePromise<typeof getListTaskerOnAllTaskerPage>[0]
    >
  >[] = [
    {
      accessorKey: 'name',
      header: ({ column }) => (
        <DataTableColumnHeader
          column={column}
          className="whitespace-nowrap"
          title={t('NAME')}
        />
      ),
      cell: ({ row }) => (
        <div className="flex items-center gap-3">
          <Avatar className="h-10 w-10">
            <AvatarImage src={row.original?.avatar} />
            <AvatarFallback>
              <UserCircle />
            </AvatarFallback>
          </Avatar>
          <Typography variant="p" affects="removePMargin">
            {row.original?.name || '-'}
          </Typography>
        </div>
      ),
      enableSorting: false,
      size: 112,
    },
    {
      accessorKey: 'phone',
      header: ({ column }) => (
        <DataTableColumnHeader column={column} title={t('PHONE')} />
      ),
      cell: ({ row }) => (
        <Typography variant="p">
          {row.original?.phone ? formatPhoneNumber(row.original.phone) : '-'}
        </Typography>
      ),
      enableSorting: false,
      size: 72,
    },
    {
      accessorKey: 'gender',
      header: ({ column }) => (
        <DataTableColumnHeader column={column} title={t('GENDER')} />
      ),
      cell: ({ row }) => (
        <Typography variant="p">
          {GENDER[row.original?.gender as keyof typeof GENDER] || '-'}
        </Typography>
      ),
      enableSorting: false,
      size: 82,
    },
    {
      accessorKey: 'avgRating',
      header: ({ column }) => (
        <DataTableColumnHeader column={column} title={t('AVG_RATING')} />
      ),
      cell: ({ row }) => (
        <Typography variant="p">{row.original?.avgRating || '-'}</Typography>
      ),
      size: 12,
    },
    {
      accessorKey: 'service',
      header: ({ column }) => (
        <DataTableColumnHeader
          column={column}
          title={t('SERVICE')}
          className="whitespace-nowrap"
        />
      ),
      cell: ({ row }) => (
        <div className="flex items-center flex-wrap">
          {row.original?.registeredServices?.map((service: string) => {
            return serviceIconMap[service] ? (
              <img
                src={serviceIconMap[service]}
                alt="service icon"
                className="object-cover rounded-md w-11 h-11"
                key={createUID()}
              />
            ) : null;
          })}
        </div>
      ),
      enableSorting: false,
      size: 422,
    },
    {
      accessorKey: 'createdAt',
      header: ({ column }) => (
        <DataTableColumnHeader column={column} title={t('CREATED_AT')} />
      ),
      cell: ({ row }) => (
        <Typography variant="p" className="whitespace-nowrap">
          {row.original?.createdAt
            ? format(row.original.createdAt, 'HH:mm - dd/MM/yyyy')
            : '-'}
        </Typography>
      ),
      size: 22,
    },
    {
      accessorKey: 'taskDone',
      header: ({ column }) => (
        <DataTableColumnHeader column={column} title={t('TASK_DONE')} />
      ),
      cell: ({ row }) => (
        <Typography variant="p">
          {row.original?.taskDone
            ? formatNumberWithCommas(row.original?.taskDone)
            : '-'}
        </Typography>
      ),
      size: 12,
    },
    {
      accessorKey: 'lastDoneTask',
      header: ({ column }) => (
        <DataTableColumnHeader column={column} title={t('LAST_DONE_TASK')} />
      ),
      cell: ({ row }) => (
        <Typography variant="p" className="whitespace-nowrap">
          {row.original?.lastDoneTask
            ? format(row.original.lastDoneTask, 'HH:mm - dd/MM/yyyy')
            : '-'}
        </Typography>
      ),
      size: 22,
    },
    {
      accessorKey: 'status',
      header: ({ column }) => (
        <DataTableColumnHeader
          column={column}
          className="whitespace-nowrap"
          title={t('STATUS')}
        />
      ),
      cell: ({ row }) =>
        row.original?.status ? (
          <Badge
            className={cn(
              'font-medium text-xs',
              styleStatusBadge(row.original?.status),
            )}>
            {t(row.original?.status)}
          </Badge>
        ) : (
          '-'
        ),
      enableSorting: false,
      size: 92,
    },
    {
      accessorKey: 'action',
      header: ({ column }) => (
        <DataTableColumnHeader column={column} title={t('ACTION')} />
      ),
      cell: ({ row }) => (
        <DropdownMenu>
          <DropdownMenuTrigger asChild>
            <Button
              variant="ghost"
              className="flex h-8 w-8 p-0 data-[state=open]:bg-muted">
              <DotsHorizontalIcon className="h-4 w-4" />
            </Button>
          </DropdownMenuTrigger>
          <DropdownMenuContent>
            {permissions.includes(PERMISSIONS.READ_TASKER_DETAIL) ? (
              <Link to={`${ROUTE_NAME.TASKER}/${row.original._id}`}>
                <DropdownMenuItem>{t('DETAIL')}</DropdownMenuItem>
              </Link>
            ) : (
              <DropdownMenuItem disabled>{t('DETAIL')}</DropdownMenuItem>
            )}
          </DropdownMenuContent>
        </DropdownMenu>
      ),
      enableSorting: false,
      size: 72,
    },
  ];

  return (
    <>
      <Grid className="bg-secondary p-4 min-h-24 rounded-md gap-3">
        <Typography variant="h2">{t('ALL_TASKER')}</Typography>
        <Breadcrumbs />
      </Grid>
      <div className="flex gap-6 my-6">
        <div className="flex items-center gap-4 py-4 px-6 border rounded-[15px] w-fit">
          <AccountMulti />
          <div>
            <Typography className="font-normal text-sm text-gray-800">
              {t('TOTAL_TASKER')}
            </Typography>
            <Typography className="font-semibold text-2xl -tracking-[0.6%] text-primary">
              {formatNumberWithCommas(loaderData?.totalUsers || 0)}
            </Typography>
          </div>
        </div>
        <div className="flex items-center gap-4 py-4 px-6 border rounded-[15px] w-fit">
          <AccountMultiCheck />
          <div>
            <Typography className="font-normal text-sm text-gray-800">
              {t('TOTAL_TASKER_ACTIVE')}
            </Typography>
            <Typography className="font-semibold text-2xl -tracking-[0.6%] text-secondary-foreground">
              {formatNumberWithCommas(loaderData?.totalActiveUsers || 0)}
            </Typography>
          </div>
        </div>
      </div>
      <BTaskeeTable
        isShowClearButton
        columns={columns}
        total={loaderData?.total || 0}
        data={loaderData?.taskerList || []}
        onClickRow={row => {
          if (permissions.includes(PERMISSIONS.READ_TASKER_DETAIL)) {
            navigate(`${ROUTE_NAME.TASKER}/${row._id}`);
          }
        }}
        search={{
          defaultValue: searchParams.get('search') || '',
          name: 'search',
          placeholder: t('SEARCH'),
          searchByEnter: true,
        }}
        pagination={getPageSizeAndPageIndex({
          total: loaderData?.total || 0,
          pageSize: Number(searchParams.get('pageSize') || 0),
          pageIndex: Number(searchParams.get('pageIndex') || 0),
        })}
        localeAddress="all-tasker"
        filters={[
          {
            name: 'services',
            placeholder: t('SERVICE'),
            options:
              loaderData?.serviceList?.map(service => {
                return {
                  label: `${service?.text?.[i18n.language || 'en']}${service.isSubscription ? ' (Subscription)' : ''}`,
                  value: service._id,
                };
              }) || [],
            value: loaderData?.filterValue?.services || '',
          },
          {
            name: 'status',
            placeholder: t('STATUS'),
            options:
              Object.keys(STATUSES).map(status => {
                return {
                  label: t(status),
                  value: status,
                };
              }) || [],
            value: loaderData?.filterValue?.status || '',
          },
        ]}
      />
    </>
  );
}
