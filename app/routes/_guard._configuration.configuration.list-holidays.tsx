import { DotsHorizontalIcon } from '@radix-ui/react-icons';
import type { SerializeFrom } from '@remix-run/node';
import { json } from '@remix-run/node';
import {
  isRouteErrorResponse,
  useActionData,
  useRouteError,
  useSubmit,
} from '@remix-run/react';
import type { ColumnDef } from '@tanstack/react-table';
import {
  ACTION_NAME,
  EFormActionType,
  PERMISSIONS,
  ROUTE_NAME,
} from 'btaskee-constants';
import { useGlobalStore, useLoaderDataSafely } from 'btaskee-hooks';
import {
  BTaskeeTable,
  Breadcrumbs,
  BreadcrumbsLink,
  BtaskeeResponseError,
  Button,
  CardInformation,
  DataTableColumnHeader,
  DateTimePicker,
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
  Input,
  Typography,
  toast,
  useBtaskeeFormController,
  useConfirm,
} from 'btaskee-ui';
import { momentTz } from 'btaskee-utils';
import { useEffect, useMemo, useState } from 'react';
import { useTranslation } from 'react-i18next';
import { z } from 'zod';
import { hocAction, hocLoader } from '~/hoc/remix';
import { getUserSession } from '~/services/helpers.server';
import {
  createHoliday,
  deleteHoliday,
  getHolidaysFromSettingSystem,
  updateHoliday,
} from '~/services/holiday-configuration.server';

export const handle = {
  breadcrumb: () => (
    <BreadcrumbsLink to={ROUTE_NAME.LIST_HOLIDAYS} label="LIST_HOLIDAYS" />
  ),
};

export const loader = hocLoader(
  async ({ request }) => {
    const url = new URL(request.url);
    const search = url.searchParams.get('search') || '';

    const { isoCode } = await getUserSession(request);

    const holidays = await getHolidaysFromSettingSystem({
      isoCode,
      search,
    });

    return json({
      holidays,
      filters: {
        search,
      },
    });
  },
  [PERMISSIONS.READ_LIST_HOLIDAYS],
);

export const action = hocAction(
  async ({ request }, { setInformationActionHistory }) => {
    const formData = await request.clone().formData();
    const action = formData.get('action')?.toString() || '';
    const holidayName = formData.get('holidayName')?.toString() || '';
    const holidayDateRange = JSON.parse(
      formData.get('holidayDateRange')?.toString() || '{}',
    );

    const { isoCode, username } = await getUserSession(request);

    if (action === EFormActionType.CREATE) {
      const result = await createHoliday({
        isoCode,
        username,
        holidayName,
        holidayDateRange,
      });
      setInformationActionHistory({
        action: ACTION_NAME.CREATE_HOLIDAY_IN_CONFIGURATION,
        dataRelated: { holidayName },
      });

      return json(result);
    }

    if (action === EFormActionType.EDIT) {
      const previousHolidayName =
        formData.get('previousHolidayName')?.toString() || '';
      const result = await updateHoliday({
        isoCode,
        previousHolidayName,
        holidayName,
        holidayDateRange,
      });
      setInformationActionHistory({
        action: ACTION_NAME.UPDATE_HOLIDAY_IN_CONFIGURATION,
        dataRelated: { holidayName },
      });

      return json(result);
    }

    if (action === EFormActionType.DELETE) {
      const result = await deleteHoliday({
        isoCode,
        holidayName,
      });
      setInformationActionHistory({
        action: ACTION_NAME.DELETE_HOLIDAY_IN_CONFIGURATION,
        dataRelated: { holidayName },
      });

      return json(result);
    }

    return json({
      success: false,
      message: 'Invalid Action',
    });
  },
  PERMISSIONS.WRITE_LIST_HOLIDAYS,
);

export default function ListHolidays() {
  const { t: tHoliday } = useTranslation('holidays-configuration');

  const { holidays, filters } = useLoaderDataSafely<typeof loader>();
  const actionData = useActionData<ActionTypeWithError<typeof action>>();

  const permissions = useGlobalStore(store => store.permissions);
  const submitDeleteHolidayAction = useSubmit();
  const isConfirmDeleteHoliday = useConfirm();

  const [isOpenDialog, setIsOpenDialog] = useState(false);
  const [actionTypeDialog, setActionTypeDialog] = useState<'edit' | 'create'>(
    'create',
  );
  const [previousHolidayName, setPreviousHolidayName] =
    useState<OffDay['reason']>('');

  useEffect(() => {
    if (actionData?.error) {
      toast({
        variant: 'error',
        description: tHoliday(actionData.error),
      });
    }

    if (actionData?.success) {
      toast({
        variant: 'success',
        description: tHoliday(actionData.message),
      });
    }
  }, [actionData, tHoliday]);

  const { form, onSubmit } = useBtaskeeFormController<FormHoliday>({
    zodRaw: {
      holidayName: z
        .string()
        .min(3, { message: tHoliday('HOLIDAY_NAME_MIN_LENGTH', { min: 3 }) })
        .max(100, {
          message: tHoliday('HOLIDAY_NAME_MAX_LENGTH', { max: 100 }),
        }),
      holidayDateRange: z.object({
        from: z.date(),
        to: z.date(),
      }),
    },
    defaultValues: {
      holidayName: '',
      holidayDateRange: {
        from: momentTz().startOf('day').toDate(),
        to: momentTz().endOf('day').toDate(),
      },
    },
    confirmParams: {
      title: tHoliday(
        actionTypeDialog === 'create'
          ? 'TITLE_CREATE_HOLIDAY'
          : 'TITLE_EDIT_HOLIDAY',
      ),
      body: tHoliday(
        actionTypeDialog === 'create'
          ? 'DESCRIPTION_CREATE_HOLIDAY'
          : 'DESCRIPTION_EDIT_HOLIDAY',
      ),
      actionButton: tHoliday(
        actionTypeDialog === 'create' ? 'SUBMIT' : 'SAVE_CHANGES',
      ),
      cancelButton: tHoliday('CANCEL'),
    },
    formDataProvided: ({ holidayName, holidayDateRange }) => {
      setIsOpenDialog(false);
      form.reset();

      const formData = new FormData();

      formData.append('action', actionTypeDialog);
      if (actionTypeDialog === EFormActionType.EDIT) {
        formData.append('previousHolidayName', previousHolidayName);
      }
      formData.append('holidayName', holidayName);
      formData.append('holidayDateRange', JSON.stringify(holidayDateRange));

      return formData;
    },
  });

  const columns: ColumnDef<
    SerializeFrom<
      ReturnValueIgnorePromise<typeof getHolidaysFromSettingSystem>[0]
    >
  >[] = useMemo(() => {
    return [
      {
        accessorKey: 'holidayName',
        header: ({ column }) => (
          <DataTableColumnHeader
            column={column}
            className="whitespace-nowrap"
            title={tHoliday('HOLIDAY_NAME')}
          />
        ),
        enableSorting: false,
        cell: ({ row }) => (
          <Typography className="break-words whitespace-normal" variant="p">
            {row.original?.reason}
          </Typography>
        ),
      },
      {
        accessorKey: 'holidayDateRange',
        header: ({ column }) => (
          <DataTableColumnHeader
            column={column}
            className="whitespace-nowrap"
            title={tHoliday('HOLIDAY_DATE_RANGE')}
          />
        ),
        cell: ({ row }) => (
          <time title="DD/MM" className="whitespace-nowrap">
            {momentTz(row.original?.from).format('DD/MM')} -{' '}
            {momentTz(row.original?.to).format('DD/MM')}
          </time>
        ),
        enableSorting: false,
      },
      {
        accessorKey: 'createdBy',
        header: ({ column }) => (
          <DataTableColumnHeader
            column={column}
            className="whitespace-nowrap"
            title={tHoliday('CREATED_BY')}
          />
        ),
        cell: ({ row }) => (
          <Typography className="break-words whitespace-normal" variant="p">
            {row.original?.createdBy}
          </Typography>
        ),
        enableSorting: false,
      },
      {
        accessorKey: 'createdAt',
        header: ({ column }) => (
          <DataTableColumnHeader
            column={column}
            className="whitespace-nowrap"
            title={tHoliday('CREATED_AT')}
          />
        ),
        cell: ({ row }) => (
          <Typography className="whitespace-nowrap" variant="p">
            {momentTz(row.original?.createdAt).format('HH:mm - DD/MM/YYYY')}
          </Typography>
        ),
        enableSorting: false,
      },
      {
        accessorKey: 'action',
        header: ({ column }) => (
          <DataTableColumnHeader
            column={column}
            className="whitespace-nowrap text-center"
            title={tHoliday('ACTION')}
          />
        ),
        size: 50,
        cell: ({ row }) => (
          <DropdownMenu>
            <DropdownMenuTrigger asChild>
              <Button
                variant="ghost"
                className="mx-auto flex h-8 w-8 p-0 data-[state=open]:bg-muted">
                <DotsHorizontalIcon className="h-4 w-4" />
                <Typography className="sr-only">
                  {tHoliday('OPEN_MENU')}
                </Typography>
              </Button>
            </DropdownMenuTrigger>
            <DropdownMenuContent>
              <DropdownMenuItem
                onSelect={event => {
                  event.stopPropagation();
                }}>
                <Button
                  variant="ghost"
                  className="h-4"
                  disabled={
                    !permissions.includes(PERMISSIONS.WRITE_LIST_HOLIDAYS)
                  }
                  onClick={() => {
                    setIsOpenDialog(true);
                    setPreviousHolidayName(row.original?.reason);
                    form.setValue('holidayName', row.original?.reason);
                    form.setValue('holidayDateRange', {
                      from: (() => {
                        /**
                         * Adjusts the 'from' date for the holiday range.
                         * @param {moment.Moment} fromDate - The original 'from' date.
                         * @returns {Date} The adjusted date.
                         */
                        const adjustFromDate = (
                          fromDate: moment.Moment,
                        ): Date => {
                          const currentYear = momentTz().year();
                          const originalYear = fromDate.year();
                          const isOriginalLeapYear = momentTz([
                            originalYear,
                          ]).isLeapYear();

                          const newDate = fromDate.clone();

                          if (
                            isOriginalLeapYear &&
                            newDate.month() === 1 &&
                            newDate.date() === 29
                          ) {
                            // Keep the original year for leap year February 29th
                            return newDate.toDate();
                          } else {
                            newDate.year(currentYear);

                            // Adjust for non-leap years
                            if (
                              newDate.month() === 1 &&
                              newDate.date() === 29 &&
                              !momentTz([currentYear]).isLeapYear()
                            ) {
                              newDate.date(28);
                            }

                            return newDate.toDate();
                          }
                        };

                        return adjustFromDate(momentTz(row.original?.from));
                      })(),
                      to: (() => {
                        /**
                         * Adjusts the 'to' date for the holiday range.
                         * @param {moment.Moment} toDate - The original 'to' date.
                         * @returns {Date} The adjusted date.
                         */
                        const adjustToDate = (toDate: moment.Moment): Date => {
                          const currentYear = momentTz().year();
                          const originalYear = toDate.year();
                          const isOriginalLeapYear = momentTz([
                            originalYear,
                          ]).isLeapYear();

                          const newDate = toDate.clone();

                          if (
                            isOriginalLeapYear &&
                            newDate.month() === 1 &&
                            newDate.date() === 29
                          ) {
                            // Keep the original year for leap year February 29th
                            return newDate.toDate();
                          } else {
                            newDate.year(currentYear);

                            // Adjust for non-leap years
                            if (
                              newDate.month() === 1 &&
                              newDate.date() === 29 &&
                              !momentTz([currentYear]).isLeapYear()
                            ) {
                              newDate.date(28);
                            }

                            return newDate.toDate();
                          }
                        };

                        return adjustToDate(momentTz(row.original?.to));
                      })(),
                    });
                    setActionTypeDialog('edit');
                  }}>
                  {tHoliday('EDIT_HOLIDAY')}
                </Button>
              </DropdownMenuItem>
              <DropdownMenuItem>
                <Button
                  onClick={async () => {
                    if (
                      await isConfirmDeleteHoliday({
                        title: tHoliday('CONFIRM_DELETE_HOLIDAY'),
                        body: (
                          <div className="flex flex-col gap-4">
                            <Typography variant="p">
                              {tHoliday('CONFIRM_DELETE_HOLIDAY_DESCRIPTION')}
                            </Typography>
                            <CardInformation
                              className="w-full p-4 flex flex-col gap-3 bg-gray-50 rounded-md border border-gray-200"
                              classNameBlockDescription="[&:not(:last-child)]:pb-3 [&:not(:last-child)]:border-b [&:not(:last-child)]:border-gray-200"
                              descriptions={[
                                {
                                  label: tHoliday('HOLIDAY_NAME'),
                                  value: row.original?.reason || '',
                                },
                                {
                                  label: tHoliday('HOLIDAY_DATE_RANGE_OFF'),
                                  value: `${momentTz(row.original?.from).format('DD/MM')} - ${momentTz(row.original?.to).format('DD/MM')}`,
                                },
                                {
                                  label: tHoliday('CREATED_BY'),
                                  value: row.original?.createdBy || '',
                                },
                              ]}
                              variant="sm"
                            />
                          </div>
                        ),
                        cancelButton: tHoliday('CANCEL'),
                        actionButton: tHoliday('CONFIRM'),
                      })
                    ) {
                      submitDeleteHolidayAction(
                        {
                          action: EFormActionType.DELETE,
                          holidayName: row.original?.reason,
                        },
                        { method: 'post' },
                      );
                    }
                  }}
                  type="button"
                  variant="ghost"
                  disabled={
                    !permissions.includes(PERMISSIONS.WRITE_LIST_HOLIDAYS)
                  }
                  className="text-red-500 h-4">
                  {tHoliday('DELETE_HOLIDAY')}
                </Button>
              </DropdownMenuItem>
            </DropdownMenuContent>
          </DropdownMenu>
        ),
        enableSorting: false,
      },
    ];
  }, [
    form,
    isConfirmDeleteHoliday,
    permissions,
    submitDeleteHolidayAction,
    tHoliday,
  ]);

  return (
    <div className="overflow-auto">
      <div className="mb-6 flex justify-between items-center rounded-xl bg-secondary py-4 px-6">
        <div className="grid gap-2">
          <Typography variant="h2">{tHoliday('LIST_HOLIDAYS')}</Typography>
          <Breadcrumbs />
        </div>
        <Button
          disabled={!permissions.includes(PERMISSIONS.WRITE_LIST_HOLIDAYS)}
          onClick={() => {
            setIsOpenDialog(true);
            setActionTypeDialog('create');
          }}>
          {tHoliday('CREATE_HOLIDAY')}
        </Button>
      </div>

      <BTaskeeTable
        columns={columns}
        data={holidays}
        total={holidays.length}
        isShowClearButton
        localeAddress="holidays-configuration"
        search={{
          name: 'search',
          placeholder: tHoliday('SEARCH'),
          defaultValue: filters.search || '',
        }}
      />

      <Dialog
        open={isOpenDialog}
        onOpenChange={isOpen => {
          setIsOpenDialog(isOpen);
          form.reset();
        }}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>
              {actionTypeDialog === 'create'
                ? tHoliday('CREATE_HOLIDAY')
                : tHoliday('EDIT_HOLIDAY')}
            </DialogTitle>
          </DialogHeader>
          <Form {...form}>
            <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-4">
              <div className="grid gap-4">
                <FormField
                  control={form.control}
                  name="holidayName"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel className="text-gray-700">
                        {tHoliday('HOLIDAY_NAME')}
                      </FormLabel>
                      <FormControl>
                        <Input
                          {...field}
                          placeholder={tHoliday('ENTER_HOLIDAY_NAME')}
                        />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
                <DateTimePicker
                  form={form}
                  name="holidayDateRange"
                  label={tHoliday('HOLIDAY_DATE_RANGE_OFF')}
                  showTime={false}
                  mode="dayMonth"
                />
              </div>

              <div className="flex justify-end gap-2">
                <Button
                  type="button"
                  variant="outline"
                  onClick={() => {
                    setIsOpenDialog(false);
                    form.reset();
                  }}>
                  {tHoliday('CANCEL')}
                </Button>
                <Button type="submit" disabled={!form.formState.isDirty}>
                  {actionTypeDialog === 'create'
                    ? tHoliday('SUBMIT')
                    : tHoliday('SAVE_CHANGES')}
                </Button>
              </div>
            </form>
          </Form>
        </DialogContent>
      </Dialog>
    </div>
  );
}

export function ErrorBoundary() {
  const error = useRouteError() as { message?: string };
  const { t: tCommon } = useTranslation('common');

  if (isRouteErrorResponse(error)) {
    if (error?.message) {
      toast({ description: JSON.stringify(error.message) });
    }

    return <BtaskeeResponseError t={tCommon} errorStatus={error.status} />;
  }

  return <BtaskeeResponseError t={tCommon} errorStatus={500} />;
}
