import {
  isRouteErrorResponse,
  json,
  useActionData,
  useNavigate,
  useRouteError,
} from '@remix-run/react';
import {
  MIN_RATE_STAR_OF_TASK_SPECIAL_CAMPAIGN,
  PERMISSIONS,
  ROUTE_NAME,
  STATUS,
  TYPE_REWARD_ACCOUNT_SPECIAL_CAMPAIGN,
  TYPE_REWARD_SPECIAL_CAMPAIGN,
  TYPE_SPECIAL_CAMPAIGN,
} from 'btaskee-constants';
import { useLoaderDataSafely } from 'btaskee-hooks';
import {
  Breadcrumbs,
  BreadcrumbsLink,
  BtaskeeResponseError,
  Button,
  Card,
  CardContent,
  CardTitle,
  DateTimePicker,
  Form,
  FormControl,
  FormDescription,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
  FormSubmissionButton,
  Grid,
  ImageUpload,
  Input,
  Label,
  MoneyInput,
  MultiLanguageSection,
  MultiSelectAdvance,
  SelectBase,
  Separator,
  Switch,
  Textarea,
  Typography,
  toast,
  useBtaskeeFormController,
} from 'btaskee-ui';
import { momentTz } from 'btaskee-utils';
import i18n from 'i18next';
import { Plus, Trash2 } from 'lucide-react';
import { useEffect } from 'react';
import { Controller, useFieldArray } from 'react-hook-form';
import { useTranslation } from 'react-i18next';
import { hocAction, hocLoader } from '~/hoc/remix';
import { zodSpecialCampaignFormSchema } from '~/schemas/zodSchema';
import {
  getAllLevelsOfJourney,
  getCities,
  getSettingCountryByIsoCode,
  getUserSession,
} from '~/services/helpers.server';
import { getServices } from '~/services/service.server';
import { handleSpecialCampaignAction } from '~/utils/marketing/special-campaign';

export const loader = hocLoader(async ({ request }) => {
  const { isoCode } = await getUserSession({ headers: request.headers });
  const [settingCountry, cities, levelsOfJourney, services] = await Promise.all(
    [
      getSettingCountryByIsoCode({
        isoCode,
        projection: { currency: 1 },
      }),
      getCities(isoCode),
      getAllLevelsOfJourney({ isoCode }),
      getServices({
        isoCode,
        projection: { name: 1, text: 1, isSubscription: 1 },
      }),
    ],
  );

  return json({
    currency: settingCountry?.currency?.code || '',
    cities,
    levelsOfJourney,
    services,
  });
}, PERMISSIONS.WRITE_SPECIAL_CAMPAIGN);

export const handle = {
  breadcrumb: () => (
    <BreadcrumbsLink
      to={`${ROUTE_NAME.SPECIAL_CAMPAIGN}/create`}
      label="CREATE_SPECIAL_CAMPAIGN"
    />
  ),
};

export const action = hocAction(
  async ({ request }, { setInformationActionHistory }) => {
    return handleSpecialCampaignAction({
      request,
      isEdit: false,
      setInformationActionHistory,
    });
  },
  PERMISSIONS.WRITE_SPECIAL_CAMPAIGN,
);

export function ErrorBoundary() {
  const error = useRouteError() as { message?: string };
  const { t: tCommon } = useTranslation('common');

  if (isRouteErrorResponse(error)) {
    if (error?.message) {
      toast({ description: JSON.stringify(error.message) });
    }

    return <BtaskeeResponseError t={tCommon} errorStatus={error.status} />;
  }

  return <BtaskeeResponseError t={tCommon} errorStatus={500} />;
}

export default function CreateSpecialCampaign() {
  const { t: tSpecialCampaign } = useTranslation('special-campaign');
  const {
    error: loaderError,
    currency,
    cities,
    levelsOfJourney,
    services,
  } = useLoaderDataSafely<typeof loader>();
  useEffect(() => {
    if (loaderError) toast({ description: loaderError });
  }, [loaderError]);

  const actionData = useActionData<ActionTypeWithError<typeof action>>();
  useEffect(() => {
    if (actionData?.error) {
      toast({
        description: actionData.error,
      });
    }
  }, [actionData]);

  const { form: formCreateSpecialCampaign, onSubmit } =
    useBtaskeeFormController<FormSpecialCampaign>({
      zodRaw: zodSpecialCampaignFormSchema(tSpecialCampaign),
      defaultValues: {
        rangeDate: {
          from: momentTz().startOf('day').toDate(),
          to: momentTz().endOf('day').toDate(),
        },
        type: TYPE_SPECIAL_CAMPAIGN.REFERRAL_CAMPAIGN,
        value: 0,
        status: STATUS.ACTIVE,
        rewards: [
          {
            type: TYPE_REWARD_SPECIAL_CAMPAIGN.BPOINT,
            amount: 0,
            applyAccountType: TYPE_REWARD_ACCOUNT_SPECIAL_CAMPAIGN.MAIN_ACCOUNT,
          },
        ],
      },
      confirmParams: {
        title: tSpecialCampaign('CONFIRM'),
        body: tSpecialCampaign('ARE_YOU_SURE_YOUR_INFORMATION_IS_CORRECT'),
        actionButton: tSpecialCampaign('SUBMIT'),
        cancelButton: tSpecialCampaign('CANCEL'),
      },
      formDataProvided: data => {
        const formData = new FormData();

        if (data?.image?.imageUrl) {
          formData.append('image', data.image.imageUrl);
        }

        formData.append('data', JSON.stringify(data));

        return formData;
      },
    });

  const navigate = useNavigate();

  const { control, handleSubmit, watch } = formCreateSpecialCampaign;
  const { fields, append, remove } = useFieldArray({
    control,
    name: 'rewards',
  });
  const watchType = watch('type');

  return (
    <>
      <div className="grid space-y-2 rounded-xl bg-secondary p-4">
        <Typography variant="h2">
          {tSpecialCampaign('CREATE_SPECIAL_CAMPAIGN')}
        </Typography>
        <Breadcrumbs />
      </div>

      <Form {...formCreateSpecialCampaign}>
        <form onSubmit={handleSubmit(onSubmit)} encType="multipart/form-data">
          <Typography className="mb-4 mt-6" variant="h4">
            {tSpecialCampaign('GENERAL')}
          </Typography>

          <Grid className="grid-cols-3 gap-6">
            <div className="col-span-1 flex flex-col gap-6">
              <FormField
                control={control}
                name="image.imageUrl"
                render={({ field, fieldState }) => (
                  <FormItem>
                    <ImageUpload
                      title={tSpecialCampaign('IMAGE_URL_SPECIAL_CAMPAIGN')}
                      onFileChange={file => field.onChange(file)}
                      ratio={2}
                    />
                    <FormMessage />
                  </FormItem>
                )}
              />
              <MultiLanguageSection
                form={formCreateSpecialCampaign}
                childrenProps={[
                  {
                    name: 'name',
                    label: tSpecialCampaign('DISPLAY_NAME'),
                    required: tSpecialCampaign('REQUIRED'),
                  },
                  {
                    name: 'description',
                    label: tSpecialCampaign('DESCRIPTION'),
                    required: tSpecialCampaign('REQUIRED'),
                  },
                ]}
                useNestedStructure={true}
                parentField="text">
                <Input placeholder={tSpecialCampaign('ENTER_DISPLAY_NAME')} />
                <Textarea
                  className="min-h-10"
                  rows={1}
                  placeholder={tSpecialCampaign('ENTER_DESCRIPTION')}
                />
              </MultiLanguageSection>
            </div>

            <Grid className="col-span-2">
              <div>
                <Grid className="grid-cols-2 gap-6">
                  <FormField
                    name="name"
                    control={control}
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel className="text-gray-700">
                          {tSpecialCampaign('CAMPAIGN_NAME')}
                        </FormLabel>
                        <FormControl>
                          <Input
                            {...field}
                            placeholder={tSpecialCampaign(
                              'ENTER_CAMPAIGN_NAME',
                            )}
                          />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                  <DateTimePicker
                    form={formCreateSpecialCampaign}
                    name="rangeDate"
                    label={tSpecialCampaign('VALIDITY_PERIOD')}
                    showTime
                  />
                  <FormField
                    name="type"
                    control={control}
                    render={({ field: { value, onChange, ref } }) => (
                      <FormItem>
                        <FormLabel className="text-gray-700">
                          {tSpecialCampaign('TYPE')}
                        </FormLabel>
                        <FormControl>
                          <SelectBase
                            selectTriggerRef={ref}
                            defaultValue={value}
                            onValueChange={onChange}
                            options={Object.values(TYPE_SPECIAL_CAMPAIGN).map(
                              status => ({
                                label: tSpecialCampaign(status),
                                value: status,
                              }),
                            )}
                          />
                        </FormControl>
                      </FormItem>
                    )}
                  />
                  <FormField
                    name="value"
                    control={control}
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel className="text-gray-700">
                          {watchType === TYPE_SPECIAL_CAMPAIGN.REFERRAL_CAMPAIGN
                            ? tSpecialCampaign('NO_OF_REFEREE')
                            : tSpecialCampaign('NO_OF_TASK')}
                        </FormLabel>
                        <FormControl>
                          <Input
                            {...field}
                            type="number"
                            placeholder={tSpecialCampaign('CHOOSE_VALUE')}
                          />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                  <FormField
                    name="city"
                    control={control}
                    render={({ field: { onChange, value } }) => (
                      <FormItem>
                        <div className="flex justify-between items-center">
                          <FormLabel className="text-gray-700">
                            {tSpecialCampaign('CITY')}
                          </FormLabel>
                          <Typography
                            variant="p"
                            affects="removePMargin"
                            className="text-sm text-gray-400 leading-tight">
                            {tSpecialCampaign('OPTIONAL')}
                          </Typography>
                        </div>
                        <FormControl>
                          <MultiSelectAdvance
                            options={cities?.map(city => ({
                              label: city,
                              value: city,
                            }))}
                            onValueChange={onChange}
                            defaultValue={value || []}
                            placeholder={tSpecialCampaign('CHOOSE_CITY')}
                            variant="blue"
                            maxCount={1}
                          />
                        </FormControl>
                        <FormDescription>
                          {tSpecialCampaign('TOOL_TIP_APPLY_FOR_ALL_CITIES')}
                        </FormDescription>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                  <div className="flex items-center gap-4">
                    <Controller
                      control={control}
                      name="status"
                      render={({ field: { onChange, value } }) => {
                        return (
                          <Switch
                            checked={value === STATUS.ACTIVE}
                            onCheckedChange={val => {
                              onChange(
                                val === true ? STATUS.ACTIVE : STATUS.INACTIVE,
                              );
                            }}
                          />
                        );
                      }}
                    />
                    <Label className="text-gray-700">
                      {tSpecialCampaign('ACTIVE')}
                    </Label>
                  </div>
                </Grid>
                <Separator className="my-4" />
                <Typography variant="h4">
                  {tSpecialCampaign('REWARDS')}
                </Typography>
                {fields.map((field, index) => (
                  <Card key={field.id} className="mt-4">
                    <CardTitle className="flex items-center justify-between rounded-t-lg bg-gray-100 px-4 py-2">
                      <Label className="text-sm font-medium text-gray-700">
                        {tSpecialCampaign('REWARDS_INFORMATION', {
                          value: index + 1,
                        })}
                      </Label>
                      {fields.length > 0 && (
                        <Button
                          className="gap-2 text-sm font-medium text-gray-700"
                          type="button"
                          variant="ghost"
                          onClick={() => remove(index)}>
                          <Trash2 className="h-4 w-4" />
                        </Button>
                      )}
                    </CardTitle>
                    <CardContent className="px-6 pb-6 pt-4">
                      <Grid className="grid-cols-2 gap-6">
                        <FormField
                          name={`rewards.${index}.type`}
                          control={control}
                          render={({ field: { value, onChange, ref } }) => (
                            <FormItem>
                              <FormLabel className="text-gray-700">
                                {tSpecialCampaign('TYPE')}
                              </FormLabel>
                              <FormControl>
                                <SelectBase
                                  selectTriggerRef={ref}
                                  defaultValue={value}
                                  onValueChange={onChange}
                                  options={Object.values(
                                    TYPE_REWARD_SPECIAL_CAMPAIGN,
                                  ).map(type => ({
                                    label: tSpecialCampaign(type),
                                    value: type,
                                  }))}
                                />
                              </FormControl>
                            </FormItem>
                          )}
                        />

                        <MoneyInput
                          form={formCreateSpecialCampaign}
                          currency={currency}
                          name={`rewards.${index}.amount`}
                          label={tSpecialCampaign('AMOUNT')}
                          placeholder={tSpecialCampaign('ENTER_AMOUNT')}
                        />
                        <FormField
                          name={`rewards.${index}.applyAccountType`}
                          control={control}
                          render={({ field: { value, onChange, ref } }) => (
                            <FormItem>
                              <FormLabel className="text-gray-700">
                                {tSpecialCampaign('ACCOUNT_TYPE')}
                              </FormLabel>
                              <FormControl>
                                <SelectBase
                                  selectTriggerRef={ref}
                                  defaultValue={value}
                                  onValueChange={onChange}
                                  options={Object.entries(
                                    TYPE_REWARD_ACCOUNT_SPECIAL_CAMPAIGN,
                                  ).map(([key, value]) => ({
                                    label: tSpecialCampaign(key),
                                    value: value,
                                  }))}
                                />
                              </FormControl>
                            </FormItem>
                          )}
                        />
                        <FormField
                          name={`rewards.${index}.taskerJourneyLevels`}
                          control={control}
                          render={({ field: { value, onChange } }) => (
                            <FormItem>
                              <FormLabel className="text-gray-700">
                                {tSpecialCampaign('TASKER_JOURNEY_LEVELS')}
                              </FormLabel>
                              <FormControl>
                                <MultiSelectAdvance
                                  options={levelsOfJourney?.map(level => ({
                                    label: level.text?.[i18n.language || 'en'],
                                    value: level.name,
                                  }))}
                                  onValueChange={onChange}
                                  defaultValue={value || []}
                                  placeholder={tSpecialCampaign(
                                    'SELECT_TASKER_JOURNEY_LEVELS',
                                  )}
                                  variant="blue"
                                  maxCount={1}
                                />
                              </FormControl>
                            </FormItem>
                          )}
                        />
                        {watchType === TYPE_SPECIAL_CAMPAIGN.TASK_CAMPAIGN && (
                          <FormField
                            name={`rewards.${index}.minRateTask`}
                            control={control}
                            render={({ field: { value, onChange, ref } }) => (
                              <FormItem>
                                <FormLabel className="text-gray-700">
                                  {tSpecialCampaign('MIN_RATE_TASK')}
                                </FormLabel>
                                <FormControl>
                                  <SelectBase
                                    selectTriggerRef={ref}
                                    defaultValue={value?.toString() || ''}
                                    onValueChange={onChange}
                                    placeholder={tSpecialCampaign(
                                      'CHOOSE_MIN_RATE_TASK',
                                    )}
                                    options={MIN_RATE_STAR_OF_TASK_SPECIAL_CAMPAIGN.map(
                                      star => ({
                                        label: star,
                                        value: star,
                                      }),
                                    )}
                                  />
                                </FormControl>
                              </FormItem>
                            )}
                          />
                        )}
                        <FormField
                          name={`rewards.${index}.applyForServices`}
                          control={control}
                          render={({ field: { onChange, value } }) => (
                            <FormItem>
                              <FormLabel className="text-gray-700">
                                {tSpecialCampaign('SERVICE')}
                              </FormLabel>
                              <FormControl>
                                <MultiSelectAdvance
                                  options={services?.map(service => ({
                                    label: `${service?.text?.[i18n.language || 'en']}${service.isSubscription ? ` ${tSpecialCampaign('SUBSCRIPTION')}` : ''}`,
                                    value: service._id,
                                  }))}
                                  onValueChange={onChange}
                                  defaultValue={value || []}
                                  placeholder={tSpecialCampaign(
                                    'SELECT_SERVICE',
                                  )}
                                  variant="blue"
                                  maxCount={1}
                                />
                              </FormControl>
                              <FormDescription>
                                {tSpecialCampaign(
                                  'TOOL_TIP_APPLY_FOR_ALL_SERVICES',
                                )}
                              </FormDescription>
                            </FormItem>
                          )}
                        />
                      </Grid>
                    </CardContent>
                  </Card>
                ))}
                <Card
                  onClick={() =>
                    append({
                      type: TYPE_REWARD_SPECIAL_CAMPAIGN.BPOINT,
                      amount: 0,
                      applyAccountType:
                        TYPE_REWARD_ACCOUNT_SPECIAL_CAMPAIGN.MAIN_ACCOUNT,
                    })
                  }
                  className="mt-6 flex h-[72px] cursor-pointer items-center justify-center gap-2 border-dashed border-primary p-6 text-primary">
                  <Plus />
                  {tSpecialCampaign('ADD_REWARDS')}
                </Card>
              </div>
            </Grid>
          </Grid>

          <Separator className="my-6" />

          <FormSubmissionButton
            cancelText={tSpecialCampaign('CANCEL')}
            submitText={tSpecialCampaign('SUBMIT')}
            onCancel={() => navigate(-1)}
          />
        </form>
      </Form>
    </>
  );
}
