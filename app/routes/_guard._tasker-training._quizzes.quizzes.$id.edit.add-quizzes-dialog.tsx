import { isRouteErrorResponse, useRouteError } from '@remix-run/react';
import { BtaskeeResponseError, toast } from 'btaskee-ui';
import { useTranslation } from 'react-i18next';
import {
  default as AddQuizListDialog,
  loader as loaderQuizListDialog,
} from '~/components/tasker-training/AddQuizListDialog';

export function ErrorBoundary() {
  const error = useRouteError() as { message?: string };
  const { t: tCommon } = useTranslation('common');

  if (isRouteErrorResponse(error)) {
    if (error?.message) {
      toast({ description: JSON.stringify(error.message) });
    }

    return <BtaskeeResponseError t={tCommon} errorStatus={error.status} />;
  }

  return <BtaskeeResponseError t={tCommon} errorStatus={500} />;
}

export const loader = loaderQuizListDialog;

export default AddQuizListDialog;
