import { json } from '@remix-run/node';
import { PERMISSIONS, ROUTE_NAME } from 'btaskee-constants';
import { BreadcrumbsLink } from 'btaskee-ui';
import { hocLoader } from '~/hoc/remix';

export const handle = {
  breadcrumb: (data: { quizId: string }) => {
    return (
      <BreadcrumbsLink
        to={`${ROUTE_NAME.QUIZ}/${data.quizId}`}
        label="TRAINING_QUIZ_DETAIL"
      />
    );
  },
};

export const loader = hocLoader(async ({ params }) => {
  return json({ quizId: params.id || '' });
}, PERMISSIONS.READ_TRAINING_QUIZ);
