import type { UploadHandler } from '@remix-run/node';
import {
  json,
  redirect,
  unstable_composeUploadHandlers,
  unstable_createMemoryUploadHandler,
  unstable_parseMultipartFormData,
} from '@remix-run/node';
import {
  isRouteErrorResponse,
  useActionData,
  useLoaderData,
  useNavigate,
  useRouteError,
  useSubmit,
} from '@remix-run/react';
import { ACTION_NAME, PERMISSIONS, ROUTE_NAME } from 'btaskee-constants';
import {
  Breadcrumbs,
  BreadcrumbsLink,
  BtaskeeResponseError,
  Button,
  Form,
  Typography,
  toast,
  useConfirm,
} from 'btaskee-ui';
import { useEffect } from 'react';
import { useTranslation } from 'react-i18next';
import { ToolRequireForm } from '~/components/tool-require/ToolRequireForm';
import { hocAction, hocLoader } from '~/hoc/remix';
import { useToolRequireForm } from '~/hooks/useToolRequireForm';
import i18next from '~/i18next.server';
import {
  getSettingCountryByIsoCode,
  getUserSession,
} from '~/services/helpers.server';
import { commitSession, getSession } from '~/services/session.server';
import { createToolItem } from '~/services/tool-require.server';
import { s3UploadHandler } from '~/third-party/s3.server';

export const handle = {
  breadcrumb: () => {
    return (
      <BreadcrumbsLink
        to={ROUTE_NAME.CREATE_TOOL_REQUIRE}
        label="CREATE_TOOL"
      />
    );
  },
};

export const loader = hocLoader(
  async ({ request }) => {
    const { isoCode } = await getUserSession({
      headers: request.headers,
    });

    const settingCountry = await getSettingCountryByIsoCode({
      isoCode,
      projection: { currency: 1 },
    });

    return json({
      settingCountry,
    });
  },
  [PERMISSIONS.WRITE_TOOL_REQUIRE],
);

export const action = hocAction(
  async ({ request }, { setInformationActionHistory }) => {
    // TODO: create a common function to handle upload image
    const uploadHandler: UploadHandler = unstable_composeUploadHandlers(
      async formField => {
        if (formField.name === 'toolImage') {
          return await s3UploadHandler(formField);
        }

        return undefined;
      },
      unstable_createMemoryUploadHandler(),
    );

    const formData = await unstable_parseMultipartFormData(
      request.clone(),
      uploadHandler,
    );

    const { isoCode } = await getUserSession({
      headers: request.headers,
    });

    await createToolItem({
      isoCode,
      toolkitItem: {
        ...JSON.parse(formData.get('toolkitItem')?.toString() || '{}'),
        image: formData.get('toolImage')?.toString(),
      },
    });

    setInformationActionHistory({
      action: ACTION_NAME.CREATE_TOOL,
    });

    const session = await getSession(request.headers.get('cookie'));
    const t = await i18next.getFixedT(request, 'tool-require-create');
    session.flash('flashMessage', t('CREATE_TOOL_SUCCESS'));
    const newSession = await commitSession(session);

    return redirect(ROUTE_NAME.TOOL_REQUIRE, {
      headers: {
        'Set-Cookie': newSession,
      },
    });
  },
  PERMISSIONS.WRITE_TOOL_REQUIRE,
);

export function ErrorBoundary() {
  const error = useRouteError() as { message?: string };
  const { t: tCommon } = useTranslation('common');

  if (isRouteErrorResponse(error)) {
    if (error?.message) {
      toast({ description: JSON.stringify(error.message) });
    }

    return <BtaskeeResponseError t={tCommon} errorStatus={error.status} />;
  }

  return <BtaskeeResponseError t={tCommon} errorStatus={500} />;
}

export default function ToolRequire() {
  const { t } = useTranslation('tool-require-create');
  const loaderData = useLoaderData<LoaderTypeWithError<typeof loader>>();
  const actionData = useActionData<ActionTypeWithError<typeof action>>();

  useEffect(() => {
    if (actionData?.error) {
      toast({
        description: actionData.error,
      });
    }
  }, [actionData, t]);

  const navigate = useNavigate();
  const confirm = useConfirm();
  const submit = useSubmit();
  const form = useToolRequireForm();

  const onSubmit = async (dataFormEdit: ToolRequireFormValue) => {
    const isConfirmSubmit = await confirm({
      title: t('CONFIRM'),
      body: t('ARE_YOU_SURE_INPUT_INFORMATION_IS_CORRECT'),
    });

    if (isConfirmSubmit) {
      const formData = new FormData();

      const submitData = {
        text: dataFormEdit.text,
        price: Number(dataFormEdit.priceChar.replaceAll(',', '')),
        image: dataFormEdit.image.value,
      };

      formData.append('toolkitItem', JSON.stringify(submitData));
      formData.append('toolImage', dataFormEdit.image.value);
      submit(formData, { method: 'post', encType: 'multipart/form-data' });
    }
  };

  return (
    <>
      <div className="flex flex-col gap-3 bg-secondary p-4 min-h-24 rounded-md mb-6">
        <Typography variant="h2">{t('CREATE_TOOL')}</Typography>
        <Breadcrumbs />
      </div>
      <div>
        <Typography variant="h4">{t('GENERAL')}</Typography>
        <Form {...form}>
          <form
            onSubmit={form.handleSubmit(onSubmit)}
            className={'flex flex-col gap-8 !mt-6'}>
            <ToolRequireForm
              form={form}
              currencyCode={loaderData?.settingCountry?.currency?.code || ''}
            />
            <div className="flex justify-end gap-4">
              <Button
                type="button"
                variant="outline"
                className="border-primary text-primary hover:text-primary"
                onClick={() => navigate(ROUTE_NAME.TOOL_REQUIRE)}>
                {t('CANCEL')}
              </Button>
              <Button>{t('SUBMIT')}</Button>
            </div>
          </form>
        </Form>
      </div>
    </>
  );
}
