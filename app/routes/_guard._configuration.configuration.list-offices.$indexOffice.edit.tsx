import { zodResolver } from '@hookform/resolvers/zod';
import {
  isRouteErrorResponse,
  json,
  useLoaderData,
  useNavigate,
  useRouteError,
  useSubmit,
} from '@remix-run/react';
import {
  ACTION_NAME,
  DAYS_OF_WEEK,
  OFFICE_STATUS_IN_CONFIGURATION,
  PERMISSIONS,
  ROUTE_NAME,
} from 'btaskee-constants';
import {
  Breadcrumbs,
  BreadcrumbsLink,
  BtaskeeResponseError,
  Button,
  DateTimePicker,
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
  HourRangePicker,
  Input,
  MultiSelectAdvance,
  SelectBase,
  Separator,
  Typography,
  toast,
  useConfirm,
} from 'btaskee-ui';
import { momentTz } from 'btaskee-utils';
import { format } from 'date-fns';
import { CalendarIcon, Plus, Trash2 } from 'lucide-react';
import { useMemo } from 'react';
import { useFieldArray, useForm } from 'react-hook-form';
import { useTranslation } from 'react-i18next';
import { z } from 'zod';
import { hocAction, hocLoader } from '~/hoc/remix';
import { useOutletOfficeConfigurationDetail } from '~/hooks/useGetOfficeConfigurationDetail';
import { getUserSession } from '~/services/helpers.server';
import { getHolidaysFromSettingSystem } from '~/services/holiday-configuration.server';
import {
  getOfficeDetailByIndexInSetting,
  updateOffice,
} from '~/services/offices-configuration.server';
import { finalizeAction } from '~/utils/common';

export const handle = {
  breadcrumb: ({ indexOffice }: { indexOffice: string }) => (
    <BreadcrumbsLink
      to={`${ROUTE_NAME.LIST_OFFICES}/${indexOffice}/${ROUTE_NAME.EDIT}`}
      label="UPDATE_OFFICE"
    />
  ),
};

export function ErrorBoundary() {
  const error = useRouteError() as { message?: string };
  const { t: tCommon } = useTranslation('common');

  if (isRouteErrorResponse(error)) {
    if (error?.message) {
      toast({ description: JSON.stringify(error.message) });
    }

    return <BtaskeeResponseError t={tCommon} errorStatus={error.status} />;
  }

  return <BtaskeeResponseError t={tCommon} errorStatus={500} />;
}

export const loader = hocLoader(async ({ request, params }) => {
  const { isoCode } = await getUserSession({ headers: request.headers });

  const [holidays, officeDetail] = await Promise.all([
    getHolidaysFromSettingSystem({
      isoCode,
    }),
    getOfficeDetailByIndexInSetting({
      isoCode,
      indexOffice: Number(params.indexOffice),
    }),
  ]);

  return json({
    ...officeDetail,
    indexOffice: Number(params.indexOffice),
    holidays,
  });
}, PERMISSIONS.WRITE_LIST_OFFICES);

export const action = hocAction(
  async ({ request, params }, { setInformationActionHistory }) => {
    const { isoCode, username } = await getUserSession({
      headers: request.headers,
    });
    const formData = await request.clone().formData();
    const updateData = JSON.parse(
      formData.get('updateData')?.toString() || '{}',
    );

    const holidays = await getHolidaysFromSettingSystem({
      isoCode,
    });

    const updateActiveHolidays =
      Array.isArray(updateData?.holidays) && updateData?.holidays.length > 0
        ? holidays.map(holiday => ({
            ...holiday,
            isActive: updateData.holidays.includes(holiday.reason),
          }))
        : [];

    const offDaysForOffice = [
      ...(Array.isArray(updateData?.offDays) && updateData?.offDays.length > 0
        ? updateData.offDays.map(
            (
              offDay: OffDay & {
                date: { from: OffDay['from']; to: OffDay['to'] };
              },
            ) => ({
              ...offDay,
              from: momentTz(offDay.date.from).toDate(),
              to: momentTz(offDay.date.to).toDate(),
              isActive: true,
              isHoliday: false,
            }),
          )
        : []),
    ];

    const responseUpdatingOffice = await updateOffice({
      isoCode,
      officeIndex: Number(params.indexOffice),
      updateInfo: {
        createdBy: username,
        name: updateData?.name,
        city: updateData?.city,
        phoneNumber: updateData?.phoneNumber,
        status: updateData?.status,
        address: updateData?.address,
        workingStartDate: updateData?.workingTime?.from,
        workingEndDate: updateData?.workingTime?.to,
        workingDays: updateData?.workingDays,
        offDays: updateActiveHolidays,
        offDaysForOffice,
      },
    });

    setInformationActionHistory({
      action: ACTION_NAME.UPDATE_OFFICE_IN_CONFIGURATION,
      dataRelated: { name: updateData.name },
    });

    return finalizeAction({
      request,
      flashMessage: {
        message: responseUpdatingOffice.msg,
        translationKey: 'offices-configuration',
      },
      destinationUrl: `${ROUTE_NAME.LIST_OFFICES}/${params.indexOffice}`,
    });
  },
  PERMISSIONS.WRITE_LIST_OFFICES,
);

export default function UpdateOffice() {
  const { t } = useTranslation('offices-configuration');
  const navigate = useNavigate();
  const submit = useSubmit();
  const confirm = useConfirm();
  const loaderData = useLoaderData<LoaderTypeWithError<typeof loader>>();
  const { userCities } = useOutletOfficeConfigurationDetail();

  /* 
    List off days in the past that are not editable, so we don't put them in the form
  */
  const offDaysUnEditable = useMemo(
    () =>
      loaderData?.offDays
        ?.filter(
          offDay =>
            !offDay.isHoliday &&
            offDay.isActive &&
            momentTz().isAfter(offDay.from) &&
            momentTz(offDay.from).year() >= momentTz().year(),
        )
        ?.map(offDay => ({
          ...offDay,
          date: {
            from: momentTz(offDay.from).toDate(),
            to: momentTz(offDay.to).toDate(),
          },
        })) || [],
    [loaderData?.offDays],
  );

  const formSchema = z.object({
    name: z.string().min(1, t('THIS_FIELD_IS_REQUIRED')),
    address: z.string().min(1, t('THIS_FIELD_IS_REQUIRED')),
    status: z.string().min(1, t('THIS_FIELD_IS_REQUIRED')),
    city: z.string().min(1, t('THIS_FIELD_IS_REQUIRED')),
    phoneNumber: z.string().min(1, t('THIS_FIELD_IS_REQUIRED')),
    workingTime: z.object({
      from: z.date(),
      to: z.date(),
    }),
    workingDays: z.array(z.number()).min(1, t('THIS_FIELD_IS_REQUIRED')),
    holidays: z.array(z.string()),
    offDays: z.array(
      z.object({
        reason: z
          .string()
          .min(1, {
            message: t('DAY_OFF_REASON_MIN_LENGTH', { min: 3 }),
          })
          .max(100, {
            message: t('DAY_OFF_REASON_MAX_LENGTH', { max: 100 }),
          }),
        date: z
          .object({
            from: z.date(),
            to: z.date(),
          })
          .refine(
            dateRange => {
              const minimumAllowedDate = momentTz()
                .add(1, 'day')
                .startOf('day')
                .toDate();
              return dateRange.from >= minimumAllowedDate;
            },
            {
              message: t('DAY_OFF_DATE_MUST_BE_AT_LEAST_TOMORROW'),
            },
          ),
      }),
    ),
  });

  const form = useForm<z.infer<typeof formSchema>>({
    resolver: zodResolver(formSchema),
    defaultValues: {
      address: loaderData?.address || '',
      name: loaderData?.name || '',
      status: loaderData?.status || '',
      city: loaderData?.city || '',
      phoneNumber: loaderData?.phoneNumber || '',
      // eslint-disable-next-line @typescript-eslint/ban-ts-comment
      //@ts-ignore
      workingTime: {
        from: loaderData?.workingStartDate
          ? momentTz(loaderData?.workingStartDate).toDate()
          : undefined,
        to: loaderData?.workingEndDate
          ? momentTz(loaderData?.workingEndDate).toDate()
          : undefined,
      },
      workingDays: loaderData?.workingDays || [],
      holidays:
        loaderData?.offDays
          ?.filter(offDay => offDay.isHoliday && offDay.isActive)
          .map(holiday => holiday.reason) || [],
      offDays:
        loaderData?.offDays
          ?.filter(
            offDay =>
              !offDay.isHoliday &&
              offDay.isActive &&
              !momentTz().isAfter(offDay.from),
          )
          ?.map(offDay => ({
            ...offDay,
            date: {
              from: momentTz(offDay.from).toDate(),
              to: momentTz(offDay.to).toDate(),
            },
          })) || [],
    },
  });

  const { fields, append, remove } = useFieldArray({
    control: form.control,
    name: 'offDays',
  });

  const onSubmit = async (updateData: z.infer<typeof formSchema>) => {
    const isConfirm = await confirm({
      title: t('TITLE_CONFIRM_UPDATING_OFFICE'),
      body: t('DESCRIPTION_CONFIRM_UPDATING_OFFICE'),
      cancelButton: t('CANCEL'),
      actionButton: t('CONFIRM'),
    });

    if (isConfirm) {
      const formData = new FormData();

      const updatedData = {
        ...updateData,
        /**
         * Add off days in the past that are not editable to the data,
         * because query is using set
         */
        offDays: [...offDaysUnEditable, ...updateData.offDays],
      };
      formData.append('updateData', JSON.stringify(updatedData));

      submit(formData, { method: 'post' });
    }
  };

  return (
    <>
      <div className="mb-6 grid space-y-2 rounded-2xl bg-secondary p-4 min-h-[90px]">
        <Typography variant="h3">{t('UPDATE_OFFICE')}</Typography>
        <Breadcrumbs />
      </div>
      <Form {...form}>
        <form onSubmit={form.handleSubmit(onSubmit)}>
          <div className="grid grid-cols-2 gap-6">
            <FormField
              name="name"
              control={form.control}
              render={({ field }) => (
                <FormItem>
                  <FormLabel className={'text-gray-700'}>
                    {t('OFFICE_NAME')}
                  </FormLabel>
                  <FormControl>
                    <Input {...field} placeholder={t('ENTER_OFFICE_NAME')} />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />
            <FormField
              name="address"
              control={form.control}
              render={({ field }) => (
                <FormItem>
                  <FormLabel className={'text-gray-700'}>
                    {t('ADDRESS')}
                  </FormLabel>
                  <FormControl>
                    <Input {...field} placeholder={t('ENTER_ADDRESS')} />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />
            <FormField
              name="city"
              control={form.control}
              render={({ field: { onChange, value } }) => (
                <FormItem>
                  <FormLabel className={'text-gray-700'}>{t('CITY')}</FormLabel>
                  <FormControl>
                    <SelectBase
                      placeholder={t('SELECT_CITY')}
                      defaultValue={value}
                      onValueChange={onChange}
                      options={userCities?.map(city => ({
                        label: t(city),
                        value: city,
                      }))}
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />
            <FormField
              name="phoneNumber"
              control={form.control}
              render={({ field }) => (
                <FormItem>
                  <FormLabel className={'text-gray-700'}>
                    {t('PHONE_NUMBER')}
                  </FormLabel>
                  <FormControl>
                    <Input {...field} placeholder={t('ENTER_PHONE_NUMBER')} />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />
            <div>
              <FormField
                name="workingTime"
                control={form.control}
                render={({ field: { value, onChange } }) => (
                  <FormItem>
                    <FormLabel className={'text-gray-700'}>
                      {t('OPERATION_HOURS')}
                    </FormLabel>
                    <FormControl>
                      <HourRangePicker
                        initialValue={{
                          from: value.from,
                          to: value.to,
                        }}
                        onValueChange={values => {
                          onChange(values);
                        }}
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
            </div>

            <FormField
              name="workingDays"
              control={form.control}
              render={({ field: { onChange, value } }) => (
                <FormItem>
                  <FormLabel className={'text-gray-700'}>
                    {t('WORKING_DAY')}
                  </FormLabel>
                  <FormControl>
                    <MultiSelectAdvance
                      placeholder={t('SELECT_WORKING_DAY')}
                      onValueChange={selectedValues => {
                        if (
                          Array.isArray(selectedValues) &&
                          selectedValues.length > 0
                        ) {
                          const formattedValues = selectedValues.map(value =>
                            Number(value),
                          );
                          onChange(formattedValues);
                        } else {
                          onChange(undefined);
                        }
                      }}
                      defaultValue={
                        Array.isArray(value) && value.length > 0
                          ? value.map((day: number) => day.toString())
                          : []
                      }
                      options={DAYS_OF_WEEK?.map(day => ({
                        label: t(day.label),
                        value: day.value.toString(),
                      }))}
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />
            <FormField
              name="holidays"
              control={form.control}
              render={({ field: { onChange, value } }) => (
                <FormItem>
                  <FormLabel className={'text-gray-700'}>
                    {t('HOLIDAY')}
                  </FormLabel>
                  <FormControl>
                    <MultiSelectAdvance
                      placeholder={t('SELECT_HOLIDAY')}
                      onValueChange={selectedValues =>
                        onChange(
                          Array.isArray(selectedValues) ? selectedValues : [],
                        )
                      }
                      defaultValue={
                        Array.isArray(value) && value.length > 0
                          ? value.map((holiday: string) => holiday)
                          : []
                      }
                      options={
                        Array.isArray(loaderData.holidays) &&
                        loaderData.holidays.length > 0
                          ? loaderData.holidays.map(holiday => ({
                              label: holiday.reason,
                              value: holiday.reason,
                            }))
                          : []
                      }
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />
            <FormField
              name="status"
              control={form.control}
              render={({ field: { onChange, value } }) => (
                <FormItem>
                  <FormLabel className={'text-gray-700'}>
                    {t('STATUS')}
                  </FormLabel>
                  <FormControl>
                    <SelectBase
                      placeholder={t('SELECT_STATUS')}
                      defaultValue={value}
                      onValueChange={onChange}
                      options={Object.values(
                        OFFICE_STATUS_IN_CONFIGURATION,
                      )?.map(status => ({
                        label: t(status),
                        value: status,
                      }))}
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            {/**
             *  List off days in the past that are not editable, so we don't put them in the form
             *  But we need to show them in the UI
             */}
            {offDaysUnEditable.map((offDay, index) => (
              <div
                className="flex flex-col rounded-md border border-gray-200"
                key={offDay._id}>
                <div className="flex justify-between items-center text-gray-700 bg-gray-100 p-4">
                  <Typography
                    variant="p"
                    affects={'removePMargin'}
                    className="text-sm leading-tight font-medium">
                    {t('OFF_DAY', { index: index + 1 })}
                  </Typography>
                  <Button
                    type="button"
                    variant="ghost"
                    className="text-sm leading-none flex items-center gap-2 p-1 h-fit"
                    disabled={momentTz().isAfter(offDay.date.from)}
                    onClick={() => remove(index)}>
                    <Trash2 className="min-w-4 min-h-4 w-4 h-4" />
                    {t('REMOVE')}
                  </Button>
                </div>

                <div className="flex flex-col gap-2 p-4">
                  <FormItem>
                    <FormLabel>{t('REASON')}</FormLabel>
                    <FormControl>
                      <Input
                        className="!disabled:cursor-default"
                        value={offDay.reason}
                        disabled={true}
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                  <FormItem>
                    <FormLabel className="text-gray-700">{t('DATE')}</FormLabel>
                    <Button
                      id="date"
                      variant={'outline'}
                      className={
                        'w-full justify-start text-left font-normal disabled:cursor-not-allowed text-muted-foreground'
                      }
                      disabled={true}>
                      <CalendarIcon className="mr-2 h-4 w-4" />
                      {`${format(offDay.date.from, 'dd/MM/y')} - ${format(offDay.date.to, 'LLL/dd/y')}`}
                    </Button>
                  </FormItem>
                </div>
              </div>
            ))}

            {fields.map((fieldArrayWithId, index) => (
              <div
                className="flex flex-col rounded-md border border-gray-200"
                key={fieldArrayWithId.id}>
                <div className="flex justify-between items-center text-gray-700 bg-gray-100 p-4">
                  <Typography
                    variant="p"
                    affects={'removePMargin'}
                    className="text-sm leading-tight font-medium">
                    {t('OFF_DAY', {
                      index: index + offDaysUnEditable.length + 1,
                    })}
                  </Typography>
                  <Button
                    type="button"
                    variant="ghost"
                    className="text-sm leading-none flex items-center gap-2 p-1 h-fit"
                    disabled={momentTz().isAfter(fieldArrayWithId.date.from)}
                    onClick={() => remove(index)}>
                    <Trash2 className="min-w-4 min-h-4 w-4 h-4" />
                    {t('REMOVE')}
                  </Button>
                </div>

                <div className="flex flex-col gap-2 p-4">
                  <FormField
                    name={`offDays.${index}.reason`}
                    control={form.control}
                    render={({ field: fieldInput }) => (
                      <FormItem>
                        <FormLabel>{t('REASON')}</FormLabel>
                        <FormControl>
                          <Input
                            className="!disabled:cursor-default"
                            {...fieldInput}
                            placeholder={t('ENTER_REASON')}
                            disabled={momentTz().isAfter(
                              fieldArrayWithId.date.from,
                            )}
                          />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                  <DateTimePicker
                    form={form}
                    name={`offDays.${index}.date`}
                    label={t('DATE')}
                    showTime={false}
                    formatString="dd/MM/yyyy"
                    disabled={momentTz().isAfter(fieldArrayWithId.date.from)}
                  />
                </div>
              </div>
            ))}

            <Button
              type="button"
              variant="ghost"
              className="border-dashed border border-primary text-primary hover:text-primary flex items-center gap-2 p-6 h-fit hover:bg-primary-50"
              onClick={() =>
                append({
                  reason: '',
                  date: {
                    from: momentTz().add(1, 'day').startOf('day').toDate(),
                    to: momentTz().add(1, 'day').startOf('day').toDate(),
                  },
                })
              }>
              <Plus className="w-5 h-5" />
              {t('ADD_OFF_DAY')}
            </Button>
          </div>
          <Separator className="my-6" />
          <div className="flex justify-end gap-4">
            <Button
              type="button"
              variant="outline"
              className="border-primary text-primary hover:text-primary"
              onClick={() =>
                navigate(`${ROUTE_NAME.LIST_OFFICES}/${loaderData.indexOffice}`)
              }>
              {t('CANCEL')}
            </Button>
            <Button
              type="submit"
              className="bg-primary hover:bg-primary-600"
              disabled={!form.formState.isDirty}>
              {t('SUBMIT')}
            </Button>
          </div>
        </form>
      </Form>
    </>
  );
}
