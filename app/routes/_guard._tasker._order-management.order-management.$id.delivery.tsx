import { zodResolver } from '@hookform/resolvers/zod';
import { redirect } from '@remix-run/node';
import {
  isRouteErrorResponse,
  json,
  useLoaderData,
  useLocation,
  useNavigate,
  useRouteError,
  useSubmit,
} from '@remix-run/react';
import {
  ACTION_NAME,
  FORM_TYPE,
  PERMISSIONS,
  ROUTE_NAME,
  TYPE_LADING,
} from 'btaskee-constants';
import {
  AlertDialog,
  AlertDialogContent,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
  BtaskeeResponseError,
  Button,
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
  Input,
  RadioGroupsBase,
  SelectBase,
  Separator,
  toast,
  useConfirm,
} from 'btaskee-ui';
import { useEffect } from 'react';
import { useForm } from 'react-hook-form';
import { useTranslation } from 'react-i18next';
import { z } from 'zod';
import { OrderInfo } from '~/components/order-management/OrderInfo';
import { hocAction, hocLoader } from '~/hoc/remix';
import i18next from '~/i18next.server';
import {
  getAllOffices,
  getSettingCountryByIsoCode,
  getUserSession,
} from '~/services/helpers.server';
import {
  confirmPaymentToolKitTransactionByTypeLading,
  getDetailPaymentToolKitTransaction,
} from '~/services/payment-and-delivery.server';
import { commitSession, getSession } from '~/services/session.server';

export const loader = hocLoader(
  async ({ request, params }) => {
    const { isoCode } = await getUserSession({ headers: request.headers });
    const [order, offices, settingCountry] = await Promise.all([
      getDetailPaymentToolKitTransaction({
        isoCode,
        paymentToolKitTransactionId: params?.id || '',
      }),
      getAllOffices({ isoCode }),
      getSettingCountryByIsoCode({ isoCode, projection: { currency: 1 } }),
    ]);

    return json({ order, offices, settingCountry });
  },
  [PERMISSIONS.WRITE_ORDER_MANAGEMENT],
);

export const action = hocAction(
  async ({ request, params: urlParams }, { setInformationActionHistory }) => {
    const formData = await request.clone().formData();
    const { isoCode, username } = await getUserSession({
      headers: request.headers,
    });

    const toolKitTransactionId = urlParams.id;
    const data = JSON.parse(formData.get('data')?.toString() || '');

    const redirectTo = formData.get('previousUrl')?.toString();
    const { type, taskerId, ...params } = data;

    const dataSubmit = {
      isoCode,
      type,
      taskerId,
      params: { ...params, createdBy: username },
      toolKitTransactionId: toolKitTransactionId || '',
    };

    await confirmPaymentToolKitTransactionByTypeLading(dataSubmit);

    setInformationActionHistory({
      action: ACTION_NAME.DELIVERY_TOOL,
    });

    const session = await getSession(request.headers.get('cookie'));
    const t = await i18next.getFixedT(request, 'order-management');
    session.flash('flashMessage', t('DELIVERED_SUCCESSFULLY'));
    const newSession = await commitSession(session);

    return redirect(redirectTo || ROUTE_NAME.ORDER_MANAGEMENT, {
      headers: {
        'Set-Cookie': newSession,
      },
    });
  },
  PERMISSIONS.WRITE_ORDER_MANAGEMENT,
);

export function ErrorBoundary() {
  const error = useRouteError() as { message?: string };
  const { t: tCommon } = useTranslation('common');

  if (isRouteErrorResponse(error)) {
    if (error?.message) {
      toast({ description: JSON.stringify(error.message) });
    }

    return <BtaskeeResponseError t={tCommon} errorStatus={error.status} />;
  }

  return <BtaskeeResponseError t={tCommon} errorStatus={500} />;
}

export default function OrderDelivery() {
  const { t } = useTranslation('order-management');
  const loaderData = useLoaderData<LoaderTypeWithError<typeof loader>>();
  const navigate = useNavigate();

  const location = useLocation();
  const confirm = useConfirm();
  const submit = useSubmit();

  const redirectTo = location.state?.from || '/';
  const DeliverySchema = z
    .object({
      type: z.string().optional(),
      taskerId: z.string().optional(),
      amount: z.number().optional(),
      createdBy: z.string().optional(),
      placeOfReceipt: z.string().optional(),
      billOfLading: z.string().optional(),
      domesticRouting: z.string().optional(),
    })
    .refine(
      data => {
        if (
          data.type === TYPE_LADING.LADING &&
          !z.string().url().safeParse(data.domesticRouting).success
        ) {
          return false;
        }
        return true;
      },
      {
        message: t('INVALID_URL'),
        path: ['domesticRouting'],
      },
    );

  const form = useForm<OrderDeliveryFormValues>({
    resolver: zodResolver(DeliverySchema),
    defaultValues: {
      type: TYPE_LADING.OFFICE,
      taskerId: loaderData?.order?.tasker?._id || '',
      amount: loaderData?.order?.amount || 0,
      placeOfReceipt: '',
      billOfLading: '',
      domesticRouting: '',
    },
  });

  const { watch, handleSubmit, control, setValue, getValues } = form;
  const [type, placeOfReceipt] = watch(['type', 'placeOfReceipt']);

  const onSubmit = async (dataSubmit: OrderDeliveryFormValues) => {
    const formData = new FormData();

    const shouldConfirm = await confirm({
      title: t('DELIVERY_METHOD'),
      body: t('ARE_YOU_SURE_YOUR_INFORMATION_IS_CORRECT'),
    });

    if (shouldConfirm) {
      formData.append('data', JSON.stringify(dataSubmit));
      formData.append('previousUrl', redirectTo);
      submit(formData, { method: 'post' });
    }
  };

  useEffect(() => {
    if (type === TYPE_LADING.LADING) {
      setValue('placeOfReceipt', '');
    } else if (type === TYPE_LADING.OFFICE) {
      setValue('billOfLading', '', { shouldDirty: true });
      setValue('domesticRouting', '', { shouldDirty: true });
    }
  }, [setValue, type]);

  return (
    <AlertDialog defaultOpen={true}>
      <AlertDialogContent>
        <Form {...form}>
          <form
            onSubmit={handleSubmit(onSubmit)}
            className="flex flex-col gap-4">
            <AlertDialogHeader>
              <AlertDialogTitle>{t('DELIVERY_METHOD')}</AlertDialogTitle>
            </AlertDialogHeader>

            <div className="p-4 flex flex-col gap-3 rounded-md bg-gray-50 border border-gray-200">
              <OrderInfo
                order={loaderData?.order}
                typeOfForm={FORM_TYPE.DELIVERY}
                currency={loaderData?.settingCountry?.currency?.sign || ''}
              />
            </div>
            <Separator />
            <div className="rounded-md border border-gray-200">
              <FormField
                name="type"
                control={control}
                render={({ field: { value, onChange } }) => (
                  <RadioGroupsBase
                    className="flex flex-row gap-8 bg-gray-100 p-4 rounded-t-md"
                    defaultValue={value}
                    options={[
                      { label: t('IN_THE_OFFICE'), value: TYPE_LADING.OFFICE },
                      { label: t('DELIVERY'), value: TYPE_LADING.LADING },
                    ]}
                    onValueChange={onChange}
                  />
                )}
              />
              <div className="p-4 flex flex-col gap-4">
                {type === TYPE_LADING.OFFICE ? (
                  <FormField
                    name="placeOfReceipt"
                    control={control}
                    render={({ field }) => (
                      <SelectBase
                        defaultValue={field.value}
                        placeholder={t('CHOOSE_OFFICE')}
                        onValueChange={value => {
                          field.onChange(value);
                        }}
                        options={loaderData?.offices.map(
                          (office: MustBeAny) => ({
                            label: office.name,
                            value: office.name,
                          }),
                        )}
                      />
                    )}
                  />
                ) : null}
                {type === TYPE_LADING.LADING ? (
                  <>
                    <FormField
                      name="billOfLading"
                      control={control}
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel className="text-gray-700">
                            {t('LADING_CODE_BILL')}
                          </FormLabel>
                          <FormControl>
                            <Input
                              {...field}
                              placeholder={t('ENTER_LADING_CODE_BILL')}
                            />
                          </FormControl>
                        </FormItem>
                      )}
                    />
                    <FormField
                      name="domesticRouting"
                      control={control}
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel className="text-gray-700">
                            {t('ORDER_TRACKING_URL')}
                          </FormLabel>
                          <FormControl>
                            <Input
                              {...field}
                              placeholder={t('ENTER_ORDER_TRACKING_URL')}
                            />
                          </FormControl>
                          <FormMessage />
                        </FormItem>
                      )}
                    />
                  </>
                ) : null}
              </div>
            </div>
            <AlertDialogFooter>
              <Button
                type="button"
                variant="outline"
                onClick={() => navigate(-1)}>
                {t('CANCEL')}
              </Button>
              <Button
                type="submit"
                disabled={
                  (type === TYPE_LADING.OFFICE && placeOfReceipt === '') ||
                  (type === TYPE_LADING.LADING &&
                    (getValues('billOfLading') === '' ||
                      getValues('domesticRouting') === ''))
                }>
                {t('CONFIRM')}
              </Button>
            </AlertDialogFooter>
          </form>
        </Form>
      </AlertDialogContent>
    </AlertDialog>
  );
}
