import { Outlet } from '@remix-run/react';
import { PERMISSIONS, ROUTE_NAME } from 'btaskee-constants';
import { useLoaderDataSafely } from 'btaskee-hooks';
import { BreadcrumbsLink, toast } from 'btaskee-ui';
import { useEffect } from 'react';
import { hocLoader } from '~/hoc/remix';
import { willBecomeOfficesConfigurationDetailLoader } from '~/hooks/useGetOfficeConfigurationDetail';

export const handle = {
  breadcrumb: ({ indexOffice }: { indexOffice: string }) => (
    <BreadcrumbsLink
      to={`${ROUTE_NAME.LIST_OFFICES}/${indexOffice}`}
      label="OFFICE_DETAIL"
    />
  ),
};

export const loader = hocLoader(willBecomeOfficesConfigurationDetailLoader, [
  PERMISSIONS.READ_LIST_OFFICES,
]);

export default function OfficeDetail() {
  const { error: loaderError, ...resetLoaderData } =
    useLoaderDataSafely<typeof loader>();

  useEffect(() => {
    if (loaderError) toast({ description: loaderError });
  }, [loaderError]);

  return <Outlet context={resetLoaderData} />;
}
