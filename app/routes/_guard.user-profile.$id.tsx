import DefaultImage from '@/images/default-image.svg';
import { type LoaderFunctionArgs, json } from '@remix-run/node';
import { useLoaderData } from '@remix-run/react';
import { ROUTE_NAME } from 'btaskee-constants';
import {
  Avatar,
  AvatarFallback,
  AvatarImage,
  Badge,
  BreadcrumbsLink,
  Card,
  CardContent,
  CardHeader,
  CardTitle,
  Grid,
  Label,
  Separator,
  Typography,
} from 'btaskee-ui';
import { useTranslation } from 'react-i18next';
import { hoc404 } from '~/hoc/remix';
import { getUserProfile } from '~/services/settings.server';

export const handle = {
  breadcrumb: (data: { userProfile: Users }) => {
    const { userProfile } = data;

    return (
      <BreadcrumbsLink
        to={`${ROUTE_NAME.PROFILE_SETTING}/${userProfile._id}`}
        label="PROFILE"
      />
    );
  },
  i18n: 'user-settings',
};

export const loader = async ({ params }: LoaderFunctionArgs) => {
  const userId = params.id || '';

  const userProfile = await hoc404(() => getUserProfile(userId));

  return json({ userProfile });
};

export default function Screen() {
  const { t } = useTranslation('user-settings');
  const loaderData = useLoaderData<typeof loader>();

  return (
    <div className="space-y-6">
      <div className="rounded-lg bg-secondary p-4">
        <Typography variant="h3">{t('USER_PROFILE')}</Typography>
      </div>
      <div className="grid grid-cols-2 gap-10">
        <div className="flex flex-col gap-5">
          <Card>
            <CardHeader>
              <CardTitle className="text-lg">{t('PERSONAL_DETAILS')}</CardTitle>
            </CardHeader>
            <Separator />
            <CardContent className="flex flex-col gap-5 py-4">
              <Grid>
                <Label className="text-gray-400">{t('EMAIL')}</Label>
                <Typography variant="h4" affects="large">
                  {loaderData?.userProfile?.email}
                </Typography>
              </Grid>
              <Grid>
                <Label className="text-gray-400">{t('USERNAME')}</Label>
                <Typography variant="h4" affects="large">
                  {loaderData?.userProfile?.username}
                </Typography>
              </Grid>
            </CardContent>
          </Card>
          <Card>
            <CardHeader>
              <CardTitle className="text-lg">{t('AUTHORIZATION')}</CardTitle>
            </CardHeader>
            <Separator />
            <CardContent className="mt-4 grid gap-4">
              <Label className="text-gray-400">{t('CITIES')}</Label>
              <div className="grid grid-cols-4 gap-2">
                {loaderData?.userProfile?.cities?.map((city, index) => {
                  return (
                    <Badge
                      className="block rounded-md bg-blue-50 py-2 text-center font-normal text-blue"
                      key={index}>
                      {city}
                    </Badge>
                  );
                })}
              </div>
            </CardContent>
          </Card>
        </div>
        <Card className="h-[370px]">
          <CardHeader>
            <CardTitle className="text-lg">{t('AVATAR')}</CardTitle>
          </CardHeader>
          <Separator />
          <div className="flex flex-col items-center justify-center">
            <div className="mt-10 h-40 w-40 text-center">
              <Avatar className="h-full w-full">
                <AvatarImage
                  src={loaderData?.userProfile?.avatarUrl}
                  className="object-cover"
                />
                <AvatarFallback className="bg-secondary">
                  <img
                    src={DefaultImage}
                    alt="Default avatar user"
                    className="h-16 w-16"
                  />
                </AvatarFallback>
              </Avatar>
            </div>
          </div>
        </Card>
      </div>
    </div>
  );
}
