import { json } from '@remix-run/node';
import {
  Outlet,
  isRouteErrorResponse,
  useActionData,
  useFetcher,
  useNavigate,
  useRouteError,
  useSubmit,
} from '@remix-run/react';
import { PERMISSIONS, ROUTE_NAME } from 'btaskee-constants';
import {
  Breadcrumbs,
  BreadcrumbsLink,
  BtaskeeResponseError,
  Form,
  Separator,
  Typography,
  toast,
  useConfirm,
} from 'btaskee-ui';
import { handleQuizFormSubmit } from 'btaskee-utils';
import { useEffect, useState } from 'react';
import { useForm } from 'react-hook-form';
import { useTranslation } from 'react-i18next';
import CustomFormMessage from '~/components/form/CustomFormMessage';
import {
  FormFooterQuizzes,
  QuestionDisplayConfigs,
  QuizzesGeneralInfo,
  QuizzesMediaSection,
  TableListQuizOfCollection,
  TimeToCompleteByMinutesField,
} from '~/components/form/TrainingComponents';
import { hocAction, hocLoader } from '~/hoc/remix';
import type { loader as loaderExternal } from '~/routes/_guard._tasker-training._quiz.quiz.check-unique-code';
import { zodQuizzesFormResolver } from '~/schemas/zodResolver';
import { handleQuizAction } from '~/utils/tasker-training/quizzesAction';

export const handle = {
  breadcrumb: () => {
    return (
      <BreadcrumbsLink
        to={ROUTE_NAME.CREATE_QUIZ_COLLECTION}
        label="CREATE_TRAINING_QUIZ_COLLECTION"
      />
    );
  },
};

export function ErrorBoundary() {
  const error = useRouteError() as { message?: string };
  const { t: tCommon } = useTranslation('common');

  if (isRouteErrorResponse(error)) {
    if (error?.message) {
      toast({ description: JSON.stringify(error.message) });
    }

    return <BtaskeeResponseError t={tCommon} errorStatus={error.status} />;
  }

  return <BtaskeeResponseError t={tCommon} errorStatus={500} />;
}

export const loader = hocLoader(
  async () => json({}),
  PERMISSIONS.WRITE_TRAINING_QUIZ_COLLECTION,
);

export const action = hocAction(
  async ({ request }, { setInformationActionHistory }) => {
    return handleQuizAction({
      request,
      isEdit: false,
      setInformationActionHistory,
    });
  },
  PERMISSIONS.WRITE_TRAINING_QUIZ_COLLECTION,
);

export default function CreateQuizzes() {
  const { t } = useTranslation('quiz');
  const confirm = useConfirm();
  const submit = useSubmit();
  const navigate = useNavigate();

  const loaderFetcherCheckUniqueCode =
    useFetcher<LoaderTypeWithError<typeof loaderExternal>>();

  const actionData = useActionData<ActionTypeWithError<typeof action>>();

  const [openDialog, setOpenDialog] = useState(true);
  const [existQuizList, setExistQuizList] = useState('');
  const [selectedQuizzes, setSelectedQuizzes] = useState<
    (QuizWillBeAddedToCollection & {
      isCSVQuiz?: boolean;
    })[]
  >([]);

  const resolversSchema = zodQuizzesFormResolver({
    t,
    isEdit: false,
    totalSelectedQuizzes: selectedQuizzes.length,
  });

  const form = useForm<FormCreateQuizCollection>({
    resolver: resolversSchema,
    defaultValues: {
      code: '',
      name: '',
      description: '',
      timeToCompleteByMinutes: {
        hours: 0,
        minutes: 0,
      },
      numberOfDisplayQuizzes: 0,
      isRandomQuizzes: false,
      image: null,
      video: null,
      quizzes: [],
      csvUpload: [], //TODO: replace csvUpload away, because it's better if storage in a state, easier to handle, don't need to setValue, setError,... make the from more complex than it should be
    },
  });
  const { control, handleSubmit, setValue, clearErrors } = form;

  const onSubmit = async (data: FormCreateQuizCollection) => {
    await handleQuizFormSubmit({
      data,
      t,
      confirm,
      submit,
    });
  };

  useEffect(() => {
    if (actionData?.error) {
      toast({ description: actionData.error });
    }
  }, [actionData]);

  useEffect(() => {
    setValue('quizzes', selectedQuizzes);
    setValue('numberOfDisplayQuizzes', selectedQuizzes.length);
    if (selectedQuizzes.length > 0) {
      clearErrors('quizzes');
      clearErrors('numberOfDisplayQuizzes');
    }
  }, [clearErrors, selectedQuizzes, setValue]);

  return (
    <>
      <div className="flex bg-secondary p-4 justify-between align-middle rounded-md min-h-24">
        <div className="grid space-y-3 rounded-xl">
          <Typography
            className="capitalize"
            variant="h2"
            affects="removePMargin">
            {t('CREATE_QUIZ_COLLECTION')}
          </Typography>
          <Breadcrumbs />
        </div>
      </div>
      <Form {...form}>
        <form
          onSubmit={handleSubmit(onSubmit)}
          encType={'multipart/form-data'}
          className={'flex flex-col gap-8 !mt-6'}>
          <section className={'flex flex-col gap-4'}>
            <Typography
              variant={'h4'}
              className={'text-xl tracking-tighter font-semibold'}>
              {t('GENERAL')}
            </Typography>
            <div className="grid grid-cols-2 gap-6">
              <QuizzesGeneralInfo t={t} control={control} />

              <TimeToCompleteByMinutesField form={form} t={t} />

              <QuestionDisplayConfigs
                t={t}
                control={control}
                totalSelectedQuizzes={selectedQuizzes.length}
              />

              <QuizzesMediaSection form={form} t={t} />
            </div>
          </section>

          <Separator />

          <TableListQuizOfCollection
            t={t}
            form={form}
            selectedQuizzes={selectedQuizzes}
            setSelectedQuizzes={setSelectedQuizzes}
            setExistQuizList={setExistQuizList}
            loaderFetcherCheckUniqueCode={loaderFetcherCheckUniqueCode}
          />

          <CustomFormMessage name="csvUpload" />
          {existQuizList && (
            <Typography
              variant="p"
              affects="removePMargin"
              className="text-red-500">
              {existQuizList}
            </Typography>
          )}

          <Separator />

          <FormFooterQuizzes form={form} t={t} isEdit={false} />
        </form>
      </Form>

      <Outlet
        context={{
          open: openDialog,
          setOpen: setOpenDialog,
          onClose: () =>
            navigate(ROUTE_NAME.CREATE_QUIZ_COLLECTION, {
              replace: true,
              preventScrollReset: true,
            }),
          selectedQuizzes,
          setSelectedQuizzes,
          loaderFetcherCheckUniqueCode,
          setExistQuizList,
        }}
      />
    </>
  );
}
