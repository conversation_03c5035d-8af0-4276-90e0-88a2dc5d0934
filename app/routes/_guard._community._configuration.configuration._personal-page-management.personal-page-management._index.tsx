import DefaultImage from '@/images/default-image.svg';
import {
  Link,
  isRouteErrorResponse,
  json,
  useLoaderData,
  useRouteError,
} from '@remix-run/react';
import { PERMISSIONS, ROUTE_NAME } from 'btaskee-constants';
import { useGlobalStore } from 'btaskee-hooks';
import {
  Breadcrumbs,
  BtaskeeResponseError,
  Button,
  Card,
  CardContent,
  CardTitle,
  Typography,
  toast,
} from 'btaskee-ui';
import { momentTz } from 'btaskee-utils';
import { useEffect, useMemo } from 'react';
import { useTranslation } from 'react-i18next';
import { hocLoader } from '~/hoc/remix';
import { getBtaskeeCommunityDetail } from '~/services/community-configuration.server';
import { getUserSession } from '~/services/helpers.server';
import { getSession } from '~/services/session.server';

export const loader = hocLoader(
  async ({ request }) => {
    const { isoCode } = await getUserSession({
      headers: request.headers,
    });

    const btaskeeProfileData = await getBtaskeeCommunityDetail({ isoCode });

    const session = await getSession(request.headers.get('cookie'));
    const flashMessage = await session.get('flashMessage');

    return json({
      btaskeeProfileData,
      flashMessage,
    });
  },
  [PERMISSIONS.READ_BTASKEE_PROFILE],
);

export function ErrorBoundary() {
  const error = useRouteError() as { message?: string };
  const { t: tCommon } = useTranslation('common');

  if (isRouteErrorResponse(error)) {
    if (error?.message) {
      toast({ description: JSON.stringify(error.message) });
    }

    return <BtaskeeResponseError t={tCommon} errorStatus={error.status} />;
  }

  return <BtaskeeResponseError t={tCommon} errorStatus={500} />;
}

export default function Screen() {
  const { t } = useTranslation('configuration-personal-page-management');
  const permissions = useGlobalStore(state => state.permissions);
  const loaderData = useLoaderData<LoaderTypeWithError<typeof loader>>();

  useEffect(() => {
    if (loaderData?.flashMessage) {
      toast({
        variant: 'success',
        description: loaderData.flashMessage,
      });
    }
  }, [loaderData?.flashMessage]);

  const profileData = useMemo(
    () => [
      { label: t('NAME'), value: loaderData?.btaskeeProfileData?.name || '-' },
      { label: t('BIO'), value: loaderData?.btaskeeProfileData?.bio || '-' },
      {
        label: t('UPDATED_BY'),
        value: loaderData?.btaskeeProfileData?.updatedBy || '-',
      },
      {
        label: t('UPDATED_AT'),
        value: loaderData?.btaskeeProfileData?.updatedAt
          ? momentTz(loaderData.btaskeeProfileData.updatedAt).format(
              'HH:mm - DD/MM/YYYY',
            )
          : '-',
      },
      {
        // TODO: navigate to the list of posts when user click
        label: t('NUM_OF_POSTS'),
        value: loaderData?.btaskeeProfileData?.numberOfPosts || '-',
      },
      {
        label: t('NUM_OF_LIKES'),
        value: loaderData?.btaskeeProfileData?.numberOfLikes || '-',
      },
      {
        label: t('NUM_OF_SHARES'),
        value: loaderData?.btaskeeProfileData?.numberOfShares || '-',
      },
      {
        label: t('NUM_OF_ASKER_VIEWS'),
        value: loaderData?.btaskeeProfileData?.numberOfAskerViews || '-',
      },
    ],
    [t, loaderData],
  );

  return (
    <div className="flex flex-col gap-6">
      <div className="flex items-center justify-between p-4 rounded-2xl bg-secondary">
        <div className="flex flex-col gap-2">
          <Typography variant="h3">{t('PERSONAL_PAGE_MANAGEMENT')}</Typography>
          <Breadcrumbs />
        </div>
        {permissions?.includes(PERMISSIONS.WRITE_BTASKEE_PROFILE) ? (
          <Link to={ROUTE_NAME.CONFIGURATION_PERSONAL_PAGE_MANAGEMENT_UPDATE}>
            <Button className="gap-2 text-sm font-medium" variant="default">
              {t('UPDATE_INFORMATION')}
            </Button>
          </Link>
        ) : null}
      </div>
      <Card className="bg-gray-50 border p-6 rounded-2xl">
        <CardTitle className="w-fit pb-3 border-b">
          {t('GENERAL_INFORMATION')}
        </CardTitle>
        <CardContent className="p-0 mt-6 flex gap-6">
          <div className="flex flex-col gap-4 items-center border border-dashed rounded-md w-[306px] h-fit pt-4 pb-8 border-gray-300 bg-gray-100">
            <Typography className="font-semibold text-lg">
              {t('AVATAR')}
            </Typography>
            <img
              src={loaderData?.btaskeeProfileData?.avatar || DefaultImage}
              className="object-cover rounded-md w-[120px] h-[120px]"
              alt="ImageUpload"
            />
          </div>
          <div className="grid grid-cols-2 gap-6 flex-1">
            {profileData.map((item, idx) => (
              <div key={idx}>
                <Typography className="font-normal text-sm text-gray-400">
                  {item.label}
                </Typography>
                <Typography className="font-semibold text-lg break-words">
                  {item.value}
                </Typography>
              </div>
            ))}
          </div>
        </CardContent>
      </Card>
    </div>
  );
}
