import { json } from '@remix-run/node';
import {
  Outlet,
  isRouteErrorResponse,
  useActionData,
  useFetcher,
  useNavigate,
  useRouteError,
  useSubmit,
} from '@remix-run/react';
import { PERMISSIONS, ROUTE_NAME } from 'btaskee-constants';
import { useLoaderDataSafely } from 'btaskee-hooks';
import {
  Breadcrumbs,
  BreadcrumbsLink,
  BtaskeeResponseError,
  Form,
  Separator,
  Typography,
  toast,
  useConfirm,
} from 'btaskee-ui';
import { handleQuizFormSubmit } from 'btaskee-utils';
import { useEffect, useState } from 'react';
import { useForm } from 'react-hook-form';
import { useTranslation } from 'react-i18next';
import CustomFormMessage from '~/components/form/CustomFormMessage';
import {
  FormFooterQuizzes,
  QuestionDisplayConfigs,
  QuizzesGeneralInfo,
  QuizzesMediaSection,
  TableListQuizOfCollection,
  TimeToCompleteByMinutesField,
} from '~/components/form/TrainingComponents';
import { hocAction, hocLoader } from '~/hoc/remix';
import type { loader as loaderExternal } from '~/routes/_guard._tasker-training._quiz.quiz.check-unique-code';
import { zodQuizzesFormResolver } from '~/schemas/zodResolver';
import { getUserSession } from '~/services/helpers.server';
import { getQuizCollectionDetail } from '~/services/tasker-training.server';
import { handleQuizAction } from '~/utils/tasker-training/quizzesAction';

export function ErrorBoundary() {
  const error = useRouteError() as { message?: string };
  const { t: tCommon } = useTranslation('common');

  if (isRouteErrorResponse(error)) {
    if (error?.message) {
      toast({ description: JSON.stringify(error.message) });
    }

    return <BtaskeeResponseError t={tCommon} errorStatus={error.status} />;
  }

  return <BtaskeeResponseError t={tCommon} errorStatus={500} />;
}

export const handle = {
  breadcrumb: (data: { quizzesId: QuizCollection['_id'] }) => {
    return (
      <BreadcrumbsLink
        to={`${ROUTE_NAME.QUIZ_COLLECTION}/${data.quizzesId}/edit`}
        label="EDIT_TRAINING_QUIZ_COLLECTION"
      />
    );
  },
  i18n: 'quiz',
};

export const loader = hocLoader(async ({ request, params }) => {
  const { isoCode } = await getUserSession({ headers: request.headers });

  const quizzesDetail = await getQuizCollectionDetail({
    isoCode,
    _id: params.id || '',
  });

  return json({
    quizzesId: params.id,
    quizzesDetail,
  });
}, PERMISSIONS.WRITE_TRAINING_QUIZ_COLLECTION);

export const action = hocAction(
  async ({ request }, { setInformationActionHistory }) => {
    return handleQuizAction({
      request,
      isEdit: true,
      setInformationActionHistory,
    });
  },
  PERMISSIONS.WRITE_TRAINING_QUIZ_COLLECTION,
);

export default function EditQuizzes() {
  const { t } = useTranslation('quiz');
  const confirm = useConfirm();
  const submit = useSubmit();
  const navigate = useNavigate();

  const loaderFetcherCheckUniqueCode =
    useFetcher<LoaderTypeWithError<typeof loaderExternal>>();

  const loaderData = useLoaderDataSafely<typeof loader>();
  const actionData = useActionData<ActionTypeWithError<typeof action>>();

  const [openDialog, setOpenDialog] = useState(true);
  // TODO: this type cannot fix, need refactor all of flow using this state
  const [selectedQuizzes, setSelectedQuizzes] = useState<MustBeAny>(
    loaderData.quizzesDetail?.quizzes || [],
  );
  const [existQuizList, setExistQuizList] = useState('');

  const resolversSchema = zodQuizzesFormResolver({
    t,
    isEdit: true,
    totalSelectedQuizzes: selectedQuizzes.length,
  });

  const form = useForm<FormCreateQuizCollection>({
    resolver: resolversSchema,
    defaultValues: {
      code: loaderData.quizzesDetail?.code,
      name: loaderData.quizzesDetail?.title,
      description: loaderData.quizzesDetail?.description ?? '',
      timeToCompleteByMinutes: {
        hours: Math.floor(
          loaderData.quizzesDetail?.timeToCompleteByMinutes / 60,
        ),
        minutes: loaderData.quizzesDetail?.timeToCompleteByMinutes % 60,
      },
      numberOfDisplayQuizzes: Number(
        loaderData.quizzesDetail?.numberOfDisplayQuizzes,
      ),
      isRandomQuizzes: Boolean(loaderData.quizzesDetail?.isRandomQuizzes),
      image: loaderData.quizzesDetail?.image
        ? {
            value: loaderData.quizzesDetail.image?.url || '',
            description: loaderData.quizzesDetail.image?.description || '',
          }
        : null,
      video: loaderData.quizzesDetail?.video
        ? {
            url: loaderData.quizzesDetail.video?.url || '',
            description: loaderData.quizzesDetail.video?.description || '',
          }
        : null,
      quizzes: loaderData.quizzesDetail?.quizzes || [],
      csvUpload: [], //TODO: replace csvUpload away, because it's better if storage in a state, easier to handle, don't need to setValue, setError,... make the from more complex than it should be
    },
  });
  const { control, handleSubmit, setValue, clearErrors } = form;
  const onSubmit = async (data: FormCreateQuizCollection) => {
    await handleQuizFormSubmit({
      data,
      t,
      confirm,
      submit,
      loaderData,
    });
  };

  useEffect(() => {
    if (actionData?.error) {
      toast({ description: actionData.error });
    }
  }, [actionData]);

  useEffect(() => {
    setValue('quizzes', selectedQuizzes);
    if (selectedQuizzes.length > 0) {
      clearErrors('quizzes');
      clearErrors('numberOfDisplayQuizzes');
    }
  }, [clearErrors, selectedQuizzes, setValue]);

  return (
    <>
      <div className="flex bg-secondary p-4 justify-between align-middle rounded-md">
        <div className="grid space-y-2 rounded-xl">
          <div className="flex">
            <Typography
              className="capitalize"
              variant="h2"
              affects="removePMargin">
              {t('EDIT_QUIZ_COLLECTION')}
            </Typography>
          </div>
          <Breadcrumbs />
        </div>
      </div>
      <Form {...form}>
        <form
          onSubmit={handleSubmit(onSubmit)}
          encType={'multipart/form-data'}
          className={'flex flex-col gap-8 !mt-6'}>
          <section className={'flex flex-col gap-4'}>
            <Typography
              variant={'h4'}
              className={'text-xl tracking-tighter font-semibold'}>
              {t('GENERAL')}
            </Typography>
            <div className="grid grid-cols-2 gap-6">
              <QuizzesGeneralInfo t={t} control={control} />

              <TimeToCompleteByMinutesField
                form={form}
                t={t}
                defaultValue={loaderData.quizzesDetail?.timeToCompleteByMinutes}
              />

              <QuestionDisplayConfigs
                t={t}
                control={control}
                totalSelectedQuizzes={selectedQuizzes.length}
              />

              <QuizzesMediaSection
                form={form}
                t={t}
                defaultValue={{
                  image: loaderData?.quizzesDetail?.image ?? {
                    url: '',
                    description: '',
                  },
                  video: loaderData?.quizzesDetail?.video ?? {
                    url: '',
                    description: '',
                  },
                }}
              />
            </div>
          </section>

          <Separator />

          <TableListQuizOfCollection
            t={t}
            form={form}
            selectedQuizzes={selectedQuizzes}
            setSelectedQuizzes={setSelectedQuizzes}
            setExistQuizList={setExistQuizList}
            loaderFetcherCheckUniqueCode={loaderFetcherCheckUniqueCode}
          />

          <CustomFormMessage name="csvUpload" />
          {existQuizList && (
            <Typography
              variant="p"
              affects="removePMargin"
              className="text-red-500">
              {existQuizList}
            </Typography>
          )}

          <Separator />

          <FormFooterQuizzes form={form} t={t} isEdit={true} />
        </form>
      </Form>

      <Outlet
        context={{
          open: openDialog,
          setOpen: setOpenDialog,
          onClose: () =>
            navigate(
              `${ROUTE_NAME.QUIZ_COLLECTION}/${loaderData.quizzesDetail?._id}/edit`,
              {
                replace: true,
                preventScrollReset: true,
              },
            ),
          selectedQuizzes,
          setSelectedQuizzes,
          loaderFetcherCheckUniqueCode,
          setExistQuizList,
        }}
      />
    </>
  );
}
