import { Outlet, json, useLoaderData } from '@remix-run/react';
import { PERMISSIONS, ROUTE_NAME } from 'btaskee-constants';
import { BreadcrumbsLink } from 'btaskee-ui';
import {
  DEFAULT_RANGE_DATE_CURRENT_DAY,
  convertSortString,
  getPageSizeAndPageIndex,
  getSkipAndLimit,
} from 'btaskee-utils';
import { hocLoader } from '~/hoc/remix';
import {
  getInstallmentPayment,
  getTotalInstallmentPayment,
} from '~/services/bnpl.server';
import {
  getSettingCountryByIsoCode,
  getUserSession,
} from '~/services/helpers.server';
import { getServices } from '~/services/service.server';

export const handle = {
  breadcrumb: () => (
    <BreadcrumbsLink
      to={ROUTE_NAME.INSTALLMENT_PAYMENT}
      label="INSTALLMENT_PAYMENT"
    />
  ),
};

export const loader = hocLoader(async ({ request }) => {
  const { isoCode } = await getUserSession({ headers: request.headers });
  const url = new URL(request.url);

  const search = url.searchParams.get('search') || '';
  const rangeDate = url.searchParams.get('createdAt') || '';
  const service = url.searchParams.get('service') || '';
  const status = url.searchParams.get('status') || '';
  const sort = url.searchParams.get('sort') || '';

  const filterValue = {
    search,
    service,
    status,
    rangeDate: DEFAULT_RANGE_DATE_CURRENT_DAY(rangeDate),
  };

  const total = await getTotalInstallmentPayment({
    isoCode,
    filter: filterValue,
  });

  const { limit, skip } = getSkipAndLimit(
    getPageSizeAndPageIndex({
      total,
      pageSize: Number(url.searchParams.get('pageSize')) || 0,
      pageIndex: Number(url.searchParams.get('pageIndex')) || 0,
    }),
  );

  const [installmentPayment, settingCountry, services] = await Promise.all([
    getInstallmentPayment({
      isoCode,
      filter: filterValue,
      limit,
      skip,
      sort: convertSortString({
        sortString: sort,
        defaultValue: { createdAt: -1 },
      }),
    }),
    getSettingCountryByIsoCode({ isoCode, projection: { currency: 1 } }),
    getServices({
      isoCode,
      projection: { name: 1, text: 1, icon: 1, isSubscription: 1 },
    }),
  ]);

  return json({
    installmentPayment,
    total,
    settingCountry,
    services,
    filterValue,
  });
}, PERMISSIONS.READ_INSTALLMENT_PAYMENT);

export default function PaymentManagementScreen() {
  const loaderData = useLoaderData<LoaderTypeWithError<typeof loader>>();

  return <Outlet context={loaderData} />;
}
