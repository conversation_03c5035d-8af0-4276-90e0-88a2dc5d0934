import { Outlet, isRouteErrorResponse, useRouteError } from '@remix-run/react';
import { PERMISSIONS, ROUTE_NAME, TYPE } from 'btaskee-constants';
import { useLoaderDataSafely } from 'btaskee-hooks';
import { BreadcrumbsLink, BtaskeeResponseError, toast } from 'btaskee-ui';
import { useEffect } from 'react';
import { useTranslation } from 'react-i18next';
import { hocLoader } from '~/hoc/remix';
import { willBecomeTaskerTrainingCourseDetailLoader } from '~/hooks/useLoaderTrainingCourseDetail';

export const handle = {
  breadcrumb: ({ data }: { data: Course }) => {
    return (
      <BreadcrumbsLink
        to={`${ROUTE_NAME.TEST_AND_REVIEW}/${data?._id}/detail`}
        label={data?.type === TYPE.REVIEW ? 'REVIEW_DETAIL' : 'TEST_DETAIL'}
      />
    );
  },
  i18n: 'course',
};

export const loader = hocLoader(
  willBecomeTaskerTrainingCourseDetailLoader,
  PERMISSIONS.READ_TEST_AND_REVIEW,
);

export function ErrorBoundary() {
  const error = useRouteError() as { message?: string };
  const { t: tCommon } = useTranslation('common');

  if (isRouteErrorResponse(error)) {
    if (error?.message) {
      toast({ description: JSON.stringify(error.message) });
    }

    return <BtaskeeResponseError t={tCommon} errorStatus={error.status} />;
  }

  return <BtaskeeResponseError t={tCommon} errorStatus={500} />;
}

export default function CourseDetailScreen() {
  const { error: loaderError, ...restLoaderData } =
    useLoaderDataSafely<typeof loader>();

  useEffect(() => {
    if (loaderError) {
      toast({ description: loaderError });
    }
  }, [loaderError]);

  return <Outlet context={restLoaderData} />;
}
