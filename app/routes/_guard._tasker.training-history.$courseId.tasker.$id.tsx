import { type SerializeFrom, json } from '@remix-run/node';
import {
  isRouteErrorResponse,
  useActionData,
  useRouteError,
  useSubmit,
} from '@remix-run/react';
import { type ColumnDef } from '@tanstack/react-table';
import {
  ACTION_NAME,
  COURSE_COMPLETION_IN_TRAINING,
  COURSE_CURRENT_STATUS_FROM_API,
  COURSE_VISIBILITY_IN_TRAINING,
  OPEN_TRAINING,
  PERMISSIONS,
  ROUTE_NAME,
  SUBMISSION_STATUS_IN_TRAINING,
} from 'btaskee-constants';
import { useGlobalStore, useLoaderDataSafely } from 'btaskee-hooks';
import {
  Breadcrumbs,
  BreadcrumbsLink,
  BtaskeeResponseError,
  Button,
  Card,
  CardContent,
  CardHeader,
  DataTableBasic,
  DataTableColumnHeader,
  Grid,
  Separator,
  StatusBadge,
  Tooltip,
  Tooltip<PERSON>ontent,
  TooltipProvider,
  TooltipTrigger,
  Typography,
  toast,
  useConfirm,
} from 'btaskee-ui';
import { format } from 'date-fns';
import i18n from 'i18next';
import { useCallback, useEffect, useMemo } from 'react';
import { useTranslation } from 'react-i18next';
import { CardProfileDescription } from '~/components/tasker-common';
import { ConditionAndNode } from '~/components/tasker-training/Course';
import { hocAction, hocLoader } from '~/hoc/remix';
import getRestApiByMultiRegion, {
  API_KEY,
} from '~/services/api-proxy/index.server';
import { getUserSession } from '~/services/helpers.server';
import { getServices } from '~/services/service.server';
import {
  getNameOfCourseByIds,
  getTrainingHistoryDetail,
} from '~/services/tasker-training.server';
import { fetchAPI } from '~/services/utils.server';

export const handle = {
  breadcrumb: ({
    taskerId,
    courseId,
  }: {
    taskerId: Users['_id'];
    courseId: Course['_id'];
  }) => (
    <BreadcrumbsLink
      to={`${ROUTE_NAME.TRAINING_HISTORY}/${courseId}${ROUTE_NAME.TASKER}/${taskerId}`}
      label="TRAINING_HISTORY_DETAIL"
    />
  ),
};

export const loader = hocLoader(
  async ({ request, params }) => {
    const { isoCode } = await getUserSession({
      headers: request.headers,
    });

    const historyDetail = await getTrainingHistoryDetail({
      isoCode,
      taskerId: params?.id || '',
      courseId: params?.courseId || '',
    });

    const services = await getServices({
      isoCode,
      projection: { name: 1, text: 1, icon: 1, isSubscription: 1 },
    });

    const courseNames = await getNameOfCourseByIds({
      isoCode,
      courseIds:
        historyDetail?.condition?.coursesMustBeCompleted?.courseIds || [],
    });

    return json({
      ...historyDetail,
      services,
      taskerId: params.id || '',
      courseId: params.courseId || '',
      courseNames,
    });
  },
  [PERMISSIONS.READ_TRAINING_HISTORY],
);

export function ErrorBoundary() {
  const error = useRouteError() as { message?: string };
  const { t: tCommon } = useTranslation('common');

  if (isRouteErrorResponse(error)) {
    if (error?.message) {
      toast({ description: JSON.stringify(error.message) });
    }

    return <BtaskeeResponseError t={tCommon} errorStatus={error.status} />;
  }

  return <BtaskeeResponseError t={tCommon} errorStatus={500} />;
}

export const action = hocAction(
  async ({ request }, { setInformationActionHistory }) => {
    const formData = await request.clone().formData();
    const action = formData.get('action')?.toString() || '';
    const taskerId = formData.get('taskerId')?.toString() || '';
    const courseId = formData.get('courseId')?.toString() || '';

    const { isoCode, userId } = await getUserSession({
      headers: request.headers,
    });

    if (action === OPEN_TRAINING.MANUALLY) {
      const apiUrl = getRestApiByMultiRegion({
        apiKey: API_KEY.UNBLOCK_TRAINING_HISTORY_MANUALLY,
        isoCode,
      });
      await fetchAPI(
        apiUrl,
        {
          taskerId,
          courseId,
          userId,
        },
        isoCode,
      );
      setInformationActionHistory({
        action: ACTION_NAME.UNBLOCK_MANUALLY_TRAINING_HISTORY,
      });

      return json({
        message: 'OPEN_THE_TEST_SUCCESSFULLY',
      });
    }

    if (action === OPEN_TRAINING.UNBLOCK_TEST) {
      const apiUrl = getRestApiByMultiRegion({
        apiKey: API_KEY.UNBLOCK_TRAINING,
        isoCode,
      });
      await fetchAPI(
        apiUrl,
        {
          taskerId,
          courseId,
          userId,
        },
        isoCode,
      );
      setInformationActionHistory({
        action: ACTION_NAME.UNBLOCK_TRAINING_HISTORY,
      });
      return json({
        message: 'UNBLOCK_THE_TEST_SUCCESSFULLY',
      });
    }

    if (action === OPEN_TRAINING.UNBLOCK_TEST_ONCE) {
      const apiUrl = getRestApiByMultiRegion({
        apiKey: API_KEY.UNBLOCK_TRAINING_ONCE,
        isoCode,
      });
      await fetchAPI(
        apiUrl,
        {
          taskerId,
          courseId,
          userId,
        },
        isoCode,
      );
      setInformationActionHistory({
        action: ACTION_NAME.UNBLOCK_TRAINING_HISTORY_ONCE,
      });
      return json({
        message: 'UNBLOCK_THE_TEST_ONCE_SUCCESSFULLY',
      });
    }

    return json({
      message: 'Error! Please try again.',
    });
  },
  PERMISSIONS.WRITE_TRAINING_HISTORY,
);

export default function TrainingHistoryDetail() {
  const { t: tCourse } = useTranslation('course');
  const submit = useSubmit();
  const confirm = useConfirm();
  const permissions = useGlobalStore(store => store.permissions);

  const loaderData = useLoaderDataSafely<typeof loader>();
  const actionData = useActionData<ActionTypeWithError<typeof action>>();

  useEffect(() => {
    if (loaderData?.error) {
      toast({ description: loaderData.error });
    }
  }, [loaderData?.error]);

  useEffect(() => {
    if (actionData?.error) {
      toast({ description: actionData.error });
    }
    if (actionData?.message) {
      toast({ variant: 'success', description: tCourse(actionData.message) });
    }
  }, [actionData, tCourse]);

  const handleOpenManually = useCallback(async () => {
    const isConfirm = await confirm({
      title: tCourse('TITLE_CONFIRM_OPEN_THE_TEST'),
      body: tCourse('DESCRIPTION_CONFIRM_OPEN_THE_TEST'),
      actionButton: tCourse('CONFIRM'),
      cancelButton: tCourse('CANCEL'),
    });

    if (isConfirm) {
      const formData = new FormData();

      formData.append('action', OPEN_TRAINING.MANUALLY);
      formData.append('taskerId', loaderData?.taskerId || '');
      formData.append('courseId', loaderData?.courseId || '');
      submit(formData, {
        method: 'post',
        encType: 'multipart/form-data',
      });
    }
  }, [confirm, loaderData, submit, tCourse]);

  const handleUnblockTest = useCallback(async () => {
    const isConfirm = await confirm({
      title: tCourse('TITLE_CONFIRM_UNBLOCK_THE_TEST'),
      body: tCourse('DESCRIPTION_CONFIRM_UNBLOCK_THE_TEST'),
      actionButton: tCourse('CONFIRM'),
      cancelButton: tCourse('CANCEL'),
    });

    if (isConfirm) {
      const formData = new FormData();

      formData.append('action', OPEN_TRAINING.UNBLOCK_TEST);
      formData.append('taskerId', loaderData?.taskerId || '');
      formData.append('courseId', loaderData?.courseId || '');
      submit(formData, {
        method: 'post',
        encType: 'multipart/form-data',
      });
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  const handleUnblockTestOnce = useCallback(async () => {
    const isConfirm = await confirm({
      title: tCourse('TITLE_CONFIRM_UNBLOCK_THE_TEST_ONCE'),
      body: tCourse('DESCRIPTION_CONFIRM_UNBLOCK_THE_TEST_ONCE'),
      actionButton: tCourse('CONFIRM'),
      cancelButton: tCourse('CANCEL'),
    });

    if (isConfirm) {
      const formData = new FormData();

      formData.append('action', OPEN_TRAINING.UNBLOCK_TEST_ONCE);
      formData.append('taskerId', loaderData?.taskerId || '');
      formData.append('courseId', loaderData?.courseId || '');
      submit(formData, {
        method: 'post',
        encType: 'multipart/form-data',
      });
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  const columns: ColumnDef<
    SerializeFrom<
      ReturnValueIgnorePromise<
        typeof getTrainingHistoryDetail
      >['submissionHistories'][0]
    >
  >[] = useMemo(
    () => [
      {
        accessorKey: 'order',
        header: ({ column }) => (
          <DataTableColumnHeader
            column={column}
            title={tCourse('SUBMISSION_HISTORY_ORDER')}
          />
        ),
        cell: ({ row }) => <Typography variant="p">{row.index + 1}</Typography>,
        size: 20,
        enableSorting: false,
      },
      {
        accessorKey: 'pass',
        header: ({ column }) => (
          <DataTableColumnHeader
            className="text-center"
            column={column}
            title={tCourse('PASS')}
          />
        ),
        cell: ({ row }) => (
          <div className="text-center">
            <StatusBadge
              statusClasses={{
                [SUBMISSION_STATUS_IN_TRAINING.NONE]:
                  'bg-gray-100 text-gray-500 rounded-md',
                [SUBMISSION_STATUS_IN_TRAINING.PASSED]:
                  'text-secondary-foreground bg-secondary rounded-md',
                [SUBMISSION_STATUS_IN_TRAINING.FAILED]:
                  'text-red-500 bg-red-50 rounded-md',
              }}
              status={
                row.original?.status || SUBMISSION_STATUS_IN_TRAINING.NONE
              }
            />
          </div>
        ),
        enableSorting: false,
      },
      {
        accessorKey: 'result',
        header: ({ column }) => (
          <DataTableColumnHeader
            className="text-center"
            column={column}
            title={tCourse('RESULT')}
          />
        ),
        cell: ({ row }) => (
          <Typography
            className="text-center"
            variant="p">{`${row.original?.numOfCorrect || 0}/${row.original?.total || 0}`}</Typography>
        ),
        enableSorting: false,
      },
      {
        accessorKey: 'createdAt',
        header: ({ column }) => (
          <DataTableColumnHeader
            className="text-center"
            column={column}
            title={tCourse('CREATED_AT')}
          />
        ),
        cell: ({ row }) =>
          row.original?.createdAt ? (
            <Typography className="text-center" variant="p">
              {format(row.original?.createdAt, 'HH:mm - dd/MM/yyyy')}
            </Typography>
          ) : null,
        enableSorting: false,
      },
    ],
    [tCourse],
  );

  return (
    <>
      <div className="flex flex-wrap bg-secondary rounded-md p-4 justify-between items-center min-h-24 mb-6">
        <Grid className="gap-3">
          <Typography variant="h2">
            {tCourse('TRAINING_HISTORY_DETAIL')}
          </Typography>
          <Breadcrumbs />
        </Grid>

        <div className={'flex gap-2 items-center'}>
          {loaderData?.visibility ===
            COURSE_VISIBILITY_IN_TRAINING.NOT_OPENED &&
          loaderData?.condition?.manuallyUnblock &&
          loaderData?.currentCourseStatus !==
            COURSE_CURRENT_STATUS_FROM_API.INACTIVE ? (
            <Button
              disabled={
                !permissions.includes(PERMISSIONS.WRITE_TRAINING_HISTORY)
              }
              variant="outline"
              onClick={handleOpenManually}
              className="gap-2 border-primary text-primary hover:text-primary">
              <Typography variant="p" affects="removePMargin">
                {tCourse('UNLOCK_MANUALLY')}
              </Typography>
            </Button>
          ) : null}

          {loaderData?.visibility === COURSE_VISIBILITY_IN_TRAINING.BLOCKED &&
          loaderData?.currentCourseStatus !==
            COURSE_CURRENT_STATUS_FROM_API.INACTIVE ? (
            <Button
              disabled={
                !permissions.includes(PERMISSIONS.WRITE_TRAINING_HISTORY)
              }
              variant="outline"
              onClick={handleUnblockTestOnce}
              className="gap-2 border-primary text-primary hover:text-primary">
              <Typography variant="p" affects="removePMargin">
                {tCourse('UNBLOCK_THE_TEST_ONCE')}
              </Typography>
            </Button>
          ) : null}

          {loaderData?.visibility === COURSE_VISIBILITY_IN_TRAINING.BLOCKED &&
          loaderData?.currentCourseStatus !==
            COURSE_CURRENT_STATUS_FROM_API.INACTIVE ? (
            <Button
              disabled={
                !permissions.includes(PERMISSIONS.WRITE_TRAINING_HISTORY)
              }
              variant="outline"
              onClick={handleUnblockTest}
              className="gap-2 border-primary text-primary hover:text-primary">
              <Typography variant="p" affects="removePMargin">
                {tCourse('UNBLOCK_THE_TEST')}
              </Typography>
            </Button>
          ) : null}
        </div>
      </div>
      <Card className="bg-gray-50 pt-6">
        <CardContent>
          <CardProfileDescription
            descriptions={[
              {
                label: tCourse('TEST_CODE'),
                value: loaderData?.code || '',
              },
              {
                label: tCourse('COURSE_NAME'),
                value: loaderData?.title || '',
              },
              {
                label: tCourse('DISPLAY_CONDITION'),
                value: (
                  <ConditionAndNode
                    courseNames={loaderData?.courseNames || []}
                    condition={loaderData?.condition || {}}
                  />
                ),
              },
              {
                label: tCourse('NUMBER_OF_TIME_OPENED'),
                value: loaderData?.numOfTimeOpened || 0,
              },
              {
                label: tCourse('MAXIMUM_NUM_OF_TIMES'),
                value: loaderData?.maximumNumberOfRetries || 0,
              },
              {
                label: tCourse('ATTEMPT_COUNT'),
                value: loaderData?.numberOfSubmissions || 0,
              },
              {
                label: tCourse('VISIBILITY'),
                value: (
                  <StatusBadge
                    statusClasses={{
                      [COURSE_VISIBILITY_IN_TRAINING.NOT_OPENED]:
                        'bg-gray-100 text-gray-500 rounded-md',
                      [COURSE_VISIBILITY_IN_TRAINING.OPENED]:
                        'text-secondary-foreground bg-secondary rounded-md',
                      [COURSE_VISIBILITY_IN_TRAINING.BLOCKED]:
                        'text-red-500 bg-red-50 rounded-md',
                    }}
                    status={
                      loaderData?.visibility ||
                      COURSE_VISIBILITY_IN_TRAINING.NOT_OPENED
                    }
                  />
                ),
              },
              {
                label: tCourse('COMPLETION'),
                value: (
                  <StatusBadge
                    statusClasses={{
                      [COURSE_COMPLETION_IN_TRAINING.NONE]:
                        'bg-gray-100 text-gray-500 rounded-md',
                      [COURSE_COMPLETION_IN_TRAINING.COMPLETED]:
                        'text-secondary-foreground bg-secondary rounded-md',
                      [COURSE_COMPLETION_IN_TRAINING.NOT_STARTED]:
                        'text-yellow-500 bg-yellow-50 rounded-md',
                      [COURSE_COMPLETION_IN_TRAINING.EXPIRED]:
                        'text-red-500 bg-red-50 rounded-md',
                      [COURSE_COMPLETION_IN_TRAINING.IN_PROGRESS]:
                        'text-blue-500 bg-blue-50 rounded-md',
                    }}
                    status={
                      loaderData?.completion ||
                      COURSE_COMPLETION_IN_TRAINING.NONE
                    }
                  />
                ),
              },
              {
                label: tCourse('PASS'),
                value: (
                  <StatusBadge
                    statusClasses={{
                      [SUBMISSION_STATUS_IN_TRAINING.NONE]:
                        'bg-gray-100 text-gray-500 rounded-md',
                      [SUBMISSION_STATUS_IN_TRAINING.PASSED]:
                        'text-secondary-foreground bg-secondary rounded-md',
                      [SUBMISSION_STATUS_IN_TRAINING.FAILED]:
                        'text-red-500 bg-red-50 rounded-md',
                    }}
                    status={
                      loaderData?.status || SUBMISSION_STATUS_IN_TRAINING.NONE
                    }
                  />
                ),
              },
              {
                label: tCourse('SERVICE'),
                value: (
                  <div className="flex flex-wrap gap-3">
                    {loaderData?.relatedServices?.map(relatedService => {
                      const serviceFoundById = loaderData?.services?.find(
                        service => relatedService.name === service.name,
                      );

                      return (
                        <TooltipProvider key={relatedService._id}>
                          <Tooltip key={relatedService._id}>
                            <TooltipTrigger asChild>
                              <div className="w-11 h-11 rounded-[11px] overflow-hidden bg-primary-50">
                                <img
                                  className="w-full h-full object-cover"
                                  src={serviceFoundById?.icon || ''}
                                  alt={relatedService.name}
                                />
                              </div>
                            </TooltipTrigger>
                            <TooltipContent>
                              <Typography variant="p">
                                {`${serviceFoundById?.text?.[i18n.language || 'en']} ${serviceFoundById?.isSubscription ? '(Subscription)' : ''}`}
                              </Typography>
                            </TooltipContent>
                          </Tooltip>
                        </TooltipProvider>
                      );
                    })}
                  </div>
                ),
              },
              {
                label: tCourse('UPDATED_DATE'),
                value: loaderData?.updatedAt
                  ? format(loaderData?.updatedAt, 'HH:mm - dd/MM/yyyy')
                  : '',
              },
              {
                label: tCourse('TEST_STATUS'),
                value: loaderData?.currentCourseStatus
                  ? tCourse(loaderData.currentCourseStatus)
                  : '',
              },
            ]}
          />
        </CardContent>
      </Card>
      <Card className="bg-gray-50 mt-6">
        <CardHeader>
          <Typography variant="h4">{tCourse('RESULT_LIST')}</Typography>
          <Separator className="w-[200px]" />
        </CardHeader>
        <CardContent>
          <DataTableBasic
            columns={columns}
            data={loaderData?.submissionHistories || []}
          />
        </CardContent>
      </Card>
    </>
  );
}
