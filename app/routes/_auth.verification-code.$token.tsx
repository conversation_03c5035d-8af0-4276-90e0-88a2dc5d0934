import type { ActionFunctionArgs, LoaderFunctionArgs } from '@remix-run/node';
import { json, redirect } from '@remix-run/node';
import { useActionData, useNavigation, useSubmit } from '@remix-run/react';
import { ERROR, ROUTE_NAME } from 'btaskee-constants';
import {
  Button,
  ErrorMessageBase,
  Input,
  Label,
  LoadingSpinner,
  Typography,
  toast,
} from 'btaskee-ui';
import { useEffect } from 'react';
import { useForm } from 'react-hook-form';
import { useTranslation } from 'react-i18next';
import i18next from '~/i18next.server';
import { isVerificationCodeExpired } from '~/services/auth.server';
import { authenticator, verifyCode } from '~/services/passports.server';
import { commitSession, getSession } from '~/services/session.server';

export async function action({ request }: ActionFunctionArgs) {
  try {
    const formData = await request.clone().formData();
    const code = formData.get('code')?.toString() || '';

    await verifyCode(code);
  } catch (error) {
    const t = await i18next.getFixedT(request, 'common');

    if (error instanceof Error) {
      return json({ error: t(error.message) });
    }

    return json({ error: t(ERROR.UNKNOWN_ERROR), detail: error });
  }

  return await authenticator.authenticate('user-pass', request, {
    successRedirect: '/',
    throwOnError: true,
  });
}

export async function loader({ params, request }: LoaderFunctionArgs) {
  const isExpired = await isVerificationCodeExpired({
    token: params.token || '',
  });
  if (isExpired) return redirect(ROUTE_NAME.SIGN_IN);

  await authenticator.isAuthenticated(request, {
    successRedirect: '/',
  });
  const session = await getSession(request.headers.get('cookie'));

  return json(
    {},
    {
      headers: {
        'Set-Cookie': await commitSession(session), // You must commit the session whenever you read a flash
      },
    },
  );
}

export default function Screen() {
  const { t } = useTranslation('authentication');
  const navigation = useNavigation();
  const submit = useSubmit();
  const { handleSubmit, formState, register } = useForm<{
    code: string;
  }>();

  const actionData = useActionData<typeof action>();
  useEffect(() => {
    if (actionData?.error) {
      toast({ description: actionData.error });
    }
  }, [actionData]);

  const onSubmit = (data: { code: string }) => {
    const formData = new FormData();
    formData.append('code', data.code);

    submit(formData, { method: 'post' });
  };

  return (
    <>
      <div className="flex flex-col space-y-1 text-start">
        <Typography variant="h3">{t('VERIFICATION_CODE')}</Typography>
        <Typography variant="p" affects="removePMargin">
          {t('VERIFICATION_CODE_HELPER')}
        </Typography>
      </div>
      <div className="grid gap-6">
        <form onSubmit={handleSubmit(onSubmit)}>
          <div className="grid gap-4">
            <div className="grid gap-2">
              <Label htmlFor="code">{t('VERIFICATION_CODE')}</Label>
              <Input
                {...register('code' as const, {
                  required: t('THIS_FIELD_IS_REQUIRED'),
                })}
                placeholder={t('ENTER_VERIFICATION_CODE')}
              />
              <ErrorMessageBase errors={formState.errors} name="code" />
            </div>

            <Button>
              {navigation.state !== 'idle' ? <LoadingSpinner /> : t('VERIFY')}
            </Button>
          </div>
        </form>
      </div>
    </>
  );
}
