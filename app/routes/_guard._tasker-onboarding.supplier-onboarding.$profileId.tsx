import { json, redirect } from '@remix-run/node';
import type { SerializeFrom } from '@remix-run/node';
import {
  isRouteErrorResponse,
  useActionData,
  useLoaderData,
  useNavigation,
  useRouteError,
  useSubmit,
} from '@remix-run/react';
import type { ColumnDef } from '@tanstack/react-table';
import type { ConfirmUpdatingProfileFormProps } from 'app/components/tasker-common';
import {
  CardProfileDescription,
  ConfirmUpdatingProfile,
  ConfirmationDescriptions,
  NoteForm,
  ProfileImageAccordion,
  TaskerProfileAction,
  TaskerProfileCard,
  TaskerProfileStatus,
  UpdateNoteDialog,
  UpdateScheduleDialog,
  WarningMissingReasonConfigurationDialog,
} from 'app/components/tasker-common';
import {
  ACTION_NAME,
  IMAGE_PROFILE_TYPE,
  PERMISSIONS,
  REASON_TYPE_IN_TASKER_ONBOARDING_SETTING,
  REASON_UPDATING_IMAGE_IN_TASKER_ONBOARDING_PROFILE,
  ROUTE_NAME,
  TASKER_ONBOARDING_IMAGE_KEY,
  TASKER_ONBOARDING_PROCESS_STATUS,
} from 'btaskee-constants';
import { useGlobalStore } from 'btaskee-hooks';
import {
  Breadcrumbs,
  BreadcrumbsLink,
  BtaskeeResponseError,
  Button,
  DataTableBasic,
  DataTableColumnHeader,
  Tabs,
  TabsContent,
  TabsList,
  TabsTrigger,
  Typography,
  toast,
  useBtaskeeFormController,
  useConfirm,
} from 'btaskee-ui';
import type { MappingOverallActionOnProfileDetailProps } from 'btaskee-utils';
import {
  getAllOfTaskerOnboardingMessageLanguage,
  getIsDisableApproveAction,
  getIsDisableSendingNotification,
  getLanguageOption,
  getOverallStatusInProfile,
  momentTz,
} from 'btaskee-utils';
import { format } from 'date-fns';
import i18n from 'i18next';
import { ChevronsRight } from 'lucide-react';
import { useCallback, useEffect, useMemo, useState } from 'react';
import { useForm } from 'react-hook-form';
import { useTranslation } from 'react-i18next';
import { z } from 'zod';
import { hoc404, hocAction, hocLoader } from '~/hoc/remix';
import i18next from '~/i18next.server';
import { getCitiesByUserId, getUserSession } from '~/services/helpers.server';
import { verifyManager } from '~/services/role-base-access-control.server';
import {
  getAllOfOffices,
  getAllReasonFromSetting,
  getTaskerProfileDetail,
  resetImageStatusInTaskerOrSupplierProfile,
  storeNoteIntoProfile,
  updateNoteByIndex,
  updateScheduleInfoInTaskerOrSupplierProfile,
  updateStatusOnTaskerProfile,
} from '~/services/tasker-profile.server';
import { sendNotification } from '~/services/utils.server';
import { formatPhoneNumber } from '~/utils/common';

export const handle = {
  breadcrumb: ({
    data,
  }: {
    data: SerializeFrom<
      ReturnValueIgnorePromise<typeof getTaskerProfileDetail>
    >['data'];
  }) => {
    return (
      <BreadcrumbsLink
        to={`${ROUTE_NAME.SUPPLIER_ONBOARDING}/${data?._id}`}
        label="SUPPLIER_PROFILE_DETAIL"
      />
    );
  },
  i18n: 'supplier-profile',
};

export function ErrorBoundary() {
  const error = useRouteError() as { message?: string };
  const { t: tCommon } = useTranslation('common');

  if (isRouteErrorResponse(error)) {
    if (error?.message) {
      toast({ description: JSON.stringify(error.message) });
    }

    return <BtaskeeResponseError t={tCommon} errorStatus={error.status} />;
  }

  return <BtaskeeResponseError t={tCommon} errorStatus={500} />;
}

export const loader = hocLoader(
  async ({ request, params }) => {
    const { isoCode, isSuperUser, userId } = await getUserSession({
      headers: request.headers,
    });
    const isManager = await verifyManager(userId);
    const cities = await getCitiesByUserId({
      userId,
      isManager: isManager || isSuperUser,
    });

    const [supplierProfileDetail, reasonsFromSetting, offices] =
      await Promise.all([
        hoc404(() =>
          getTaskerProfileDetail({
            profileId: params.profileId || '',
            isoCode,
            cities,
            isPartner: true,
          }),
        ),
        getAllReasonFromSetting({
          isoCode,
        }),
        getAllOfOffices({ isoCode }),
      ]);

    return json({
      ...supplierProfileDetail,
      reasonsFromSetting,
      offices,
    });
  },
  [
    PERMISSIONS.READ_VERIFYING_STEP_ON_SUPPLIER_AND_STAFF_ONBOARDING,
    PERMISSIONS.READ_APPROVED_STEP_ON_SUPPLIER_AND_STAFF_ONBOARDING,
    PERMISSIONS.READ_ELIMINATED_STEP_ON_SUPPLIER_AND_STAFF_ONBOARDING,
    PERMISSIONS.READ_REJECTED_STEP_ON_SUPPLIER_AND_STAFF_ONBOARDING,
  ],
);

export const action = hocAction(
  async ({ request, params }, { setInformationActionHistory }) => {
    const formData = await request.clone().formData();
    const tSupplierProfile = await i18next.getFixedT(
      request,
      'supplier-profile',
    );
    const { isoCode, userId, username } = await getUserSession({
      headers: request.headers,
    });

    const status = formData.get('status')?.toString() || '';
    const reason = formData.get('reason')?.toString() || '';
    const language = formData.get('language')?.toString() || 'en';
    const message = formData.get('message')?.toString() || '';
    const fieldName = formData.get('fieldName')?.toString() || '';
    const note = formData.get('note')?.toString() || '';
    const updatedNote = formData.get('updatedNote')?.toString() || '';
    const noteIndex = formData.get('noteIndex');
    const sendRemindMessage =
      formData.get('sendRemindMessage')?.toString() || '';
    const officeInfo = formData.get('officeInfo')?.toString() || '';
    const imageField = formData.get('imageField')?.toString() || '';

    if (
      reason === REASON_UPDATING_IMAGE_IN_TASKER_ONBOARDING_PROFILE.REVIEW_AGAIN
    ) {
      const responseMessage = await resetImageStatusInTaskerOrSupplierProfile({
        isoCode,
        profileId: params?.profileId || '',
        reason,
        username,
        userId,
        imageField,
      });

      setInformationActionHistory({
        action: ACTION_NAME.RESET_IMAGE_STATUS_ON_TASKER_ONBOARDING_PROFILE,
        dataRelated: {
          profileId: params.profileId || '',
          imageField,
        },
      });

      return json({ msg: tSupplierProfile(responseMessage.msg) });
    }

    if (sendRemindMessage) {
      await sendNotification({
        isSendNotificationId: true,
        userIds: [formData.get('taskerId')?.toString() || ''],
        locale: language as BtaskeeLanguage,
        message: JSON.parse(sendRemindMessage),
        isoCode,
      });

      await updateStatusOnTaskerProfile({
        profileId: params.profileId || '',
        status: TASKER_ONBOARDING_PROCESS_STATUS.NEEDS_UPDATE,
        userId,
        username,
        isoCode,
      });

      setInformationActionHistory({
        action: ACTION_NAME.UPDATE_TASKER_PROFILE_STATUS,
        dataRelated: {
          profileId: params.profileId || '',
          newStatus: TASKER_ONBOARDING_PROCESS_STATUS.NEEDS_UPDATE,
        },
      });

      return json({ msg: tSupplierProfile('SEND_NOTIFICATION_SUCCESSFULLY') });
    }

    if (officeInfo) {
      await updateScheduleInfoInTaskerOrSupplierProfile({
        isoCode,
        profileId: params.profileId || '',
        officeInfo: JSON.parse(officeInfo),
        date: momentTz(formData.get('date')?.toString() || '').toDate(),
      });

      await updateStatusOnTaskerProfile({
        profileId: params.profileId || '',
        status: TASKER_ONBOARDING_PROCESS_STATUS.RESCHEDULE,
        userId,
        username,
        isoCode,
      });

      setInformationActionHistory({
        action: ACTION_NAME.RESCHEDULE_PROFILE_IN_TASKER_ONBOARDING,
        dataRelated: {
          profileId: params.profileId || '',
          status: TASKER_ONBOARDING_PROCESS_STATUS.RESCHEDULE,
        },
      });

      return json({ msg: tSupplierProfile('UPDATE_SCHEDULE_SUCCESSFULLY') });
    }

    if (note) {
      const noteResponseMessage = await storeNoteIntoProfile({
        profileId: params.profileId || '',
        note,
        userId,
        username,
        isoCode,
      });

      setInformationActionHistory({
        action: ACTION_NAME.ADD_NOTE_IN_TASKER_ONBOARDING,
        dataRelated: {
          profileId: params.profileId || '',
          note,
        },
      });

      return json({ msg: tSupplierProfile(noteResponseMessage) });
    }

    if (updatedNote && noteIndex) {
      const noteResponseMessage = await updateNoteByIndex({
        profileId: params.profileId || '',
        note: updatedNote,
        userId,
        username,
        isoCode,
        noteIndex: Number(noteIndex || 0),
      });

      setInformationActionHistory({
        action: ACTION_NAME.UPDATE_NOTE_IN_TASKER_ONBOARDING,
        dataRelated: {
          profileId: params.profileId || '',
          noteIndex: Number(noteIndex || 0),
          note: updatedNote,
        },
      });

      return json({ msg: tSupplierProfile(noteResponseMessage) });
    }

    const statusFilterValue = Object.values(
      TASKER_ONBOARDING_PROCESS_STATUS,
    )?.find(profileStatus => profileStatus === status);

    if (!statusFilterValue) {
      return redirect(ROUTE_NAME.SUPPLIER_ONBOARDING);
    }

    const result = await updateStatusOnTaskerProfile({
      profileId: params.profileId || '',
      status: statusFilterValue,
      reason,
      userId,
      isoCode,
      username,
      language: language as BtaskeeLanguage,
      fieldName: fieldName as `${TASKER_ONBOARDING_IMAGE_KEY}`,
      ...(message ? { message: JSON.parse(message) } : {}),
    });

    setInformationActionHistory({
      action: ACTION_NAME.UPDATE_TASKER_PROFILE_STATUS,
      dataRelated: {
        profileId: params.profileId || '',
        newStatus: status,
      },
    });

    return json({ msg: tSupplierProfile(result.msg) });
  },
  [PERMISSIONS.WRITE_PARTNER_AND_PARTNER_EMPLOYEE_PROFILE_ON_TASKER_ONBOARDING],
);

export default function SupplierProfileDetail() {
  const { t: tSupplierProfile } = useTranslation('tasker-profile');

  const submit = useSubmit();
  const confirm = useConfirm();

  const {
    data: supplierProfile,
    reasonsFromSetting,
    offices,
    error: loaderError,
  } = useLoaderData<LoaderTypeWithError<typeof loader>>();
  const [openDialog, setOpenDialog] = useState<boolean>(false);
  const [openWarningMissingReasonDialog, setOpenWarningMissingReasonDialog] =
    useState<boolean>(false);

  const reversedNotes = useMemo(() => {
    // Need add index then passing it to server which update note by this index
    const notesWithIndex =
      supplierProfile?.notes?.map((note, index) => ({
        index,
        ...note,
      })) || [];
    return notesWithIndex.reverse();
  }, [supplierProfile?.notes]);
  const reversedActionHistories = useMemo(
    () => supplierProfile?.actionHistories?.reverse() || [],
    [supplierProfile?.actionHistories],
  );

  const permissions = useGlobalStore(state => state.permissions);
  const navigation = useNavigation();
  const actionData = useActionData<ActionTypeWithError<typeof action>>();

  const { control, getValues, setValue } =
    useForm<ConfirmUpdatingProfileFormProps>({
      defaultValues: {
        reason: '',
        status: TASKER_ONBOARDING_PROCESS_STATUS.VERIFYING,
      },
    });
  const {
    register: noteFormRegister,
    control: noteFormControl,
    resetField: resetNoteFormValue,
  } = useForm<{
    note: string;
  }>({
    defaultValues: {
      note: '',
    },
  });

  const sendingNotificationController =
    useBtaskeeFormController<FormSendingNotificationOnTaskerOnboardingDetail>({
      zodRaw: {
        language: z.string().min(1, tSupplierProfile('THIS_IS_REQUIRE_FIELD')),
        taskerId: z.string().min(1, tSupplierProfile('THIS_IS_REQUIRE_FIELD')),
        sendRemindMessage: z
          .string()
          .min(1, tSupplierProfile('THIS_IS_REQUIRE_FIELD')),
      },
      defaultValues: {
        language: '',
        taskerId: '',
        sendRemindMessage: '',
      },
      confirmParams: {
        title: tSupplierProfile('TITLE_CONFIRM_SEND_NOTIFICATION'),
        body: (
          <>
            <Typography variant="p">
              {tSupplierProfile('DESCRIPTION_CONFIRM_SEND_NOTIFICATION')}
            </Typography>
            <ConfirmationDescriptions
              contents={[
                {
                  title: tSupplierProfile('TASKER_NAME'),
                  description: supplierProfile.taskerName,
                },
                {
                  title: tSupplierProfile('TASKER_USER_NAME'),
                  description: supplierProfile.username,
                },
              ]}
            />
          </>
        ),
        actionButton: tSupplierProfile('SUBMIT'),
        cancelButton: tSupplierProfile('CANCEL'),
      },
      formDataProvided: ({ language, sendRemindMessage, taskerId }) => {
        const formData = new FormData();

        formData.append('language', language);
        formData.append('taskerId', taskerId);
        formData.append('sendRemindMessage', sendRemindMessage);

        return formData;
      },
    });

  const getAllLanguageByKey = ({
    key,
    reason,
  }: {
    key: string;
    reason?: ConfirmUpdatingProfileFormProps['reason'];
  }) => ({
    vi: tSupplierProfile(key, getLanguageOption('vi', reason)),
    en: tSupplierProfile(key, getLanguageOption('en', reason)),
    ko: tSupplierProfile(key, getLanguageOption('ko', reason)),
    th: tSupplierProfile(key, getLanguageOption('th', reason)),
    id: tSupplierProfile(key, getLanguageOption('id', reason)),
  });

  useEffect(() => {
    if (loaderError) toast({ description: loaderError });
  }, [loaderError]);

  useEffect(() => {
    if (actionData?.msg)
      toast({ description: actionData.msg, variant: 'success' });

    if (actionData?.error) toast({ description: actionData.error });
  }, [actionData]);

  const onTriggerProfile = async (
    values: MappingOverallActionOnProfileDetailProps,
  ) => {
    if (values.statusOptions?.[0]) {
      setValue('status', values.statusOptions[0].status);
    }

    const isConfirm = await confirm({
      title: tSupplierProfile(values.dialog?.title),
      body: (
        <ConfirmUpdatingProfile
          control={control}
          setValue={setValue}
          description={tSupplierProfile(values.dialog?.desc)}
          reasonsFromSetting={reasonsFromSetting}
          taskerInfo={{
            taskerName: supplierProfile.taskerName,
            username: supplierProfile.username,
          }}
        />
      ),
      cancelButton: tSupplierProfile('CANCEL'),
      actionButton: tSupplierProfile('CONFIRM'),
    });

    if (isConfirm) {
      const formData = new FormData();
      const { status, reason } = getValues();
      const message = values.statusOptions?.find(
        option => option.status === status,
      )?.message;

      formData.append('status', status);
      formData.append('language', supplierProfile?.taskerLanguage || 'en');

      if (reason) {
        formData.append('reason', reason);
      }

      if (message) {
        formData.append(
          'message',
          JSON.stringify({
            title: getAllLanguageByKey({
              key: message.title,
            }),
            body: getAllLanguageByKey({
              key: message.body,
              reason: reason,
            }),
          }),
        );
      }

      submit(formData, { method: 'post' });
    }
  };

  const onReviewImageAgain = async ({
    imageField,
  }: {
    imageField: `${TASKER_ONBOARDING_IMAGE_KEY}`;
  }) => {
    const isConfirm = await confirm({
      title: tSupplierProfile('TITLE_CONFIRM_REVIEW_AGAIN'),
      body: tSupplierProfile('DESCRIPTION_CONFIRM_REVIEW_AGAIN'),
      actionButton: tSupplierProfile('CONFIRM'),
      cancelButton: tSupplierProfile('CANCEL'),
    });

    if (isConfirm) {
      const formData = new FormData();

      formData.append(
        'reason',
        REASON_UPDATING_IMAGE_IN_TASKER_ONBOARDING_PROFILE.REVIEW_AGAIN,
      );
      formData.append('imageField', imageField);

      submit(formData, { method: 'post' });
    }
  };

  const onTriggerImageProfile = async ({
    title,
    desc,
    status,
    fieldName,
  }: ApprovalImageProfileProps) => {
    const rejectImageReasonsFromSetting = reasonsFromSetting?.filter(
      reasonFromSetting =>
        reasonFromSetting?.type === REASON_TYPE_IN_TASKER_ONBOARDING_SETTING,
    );

    if (status === TASKER_ONBOARDING_PROCESS_STATUS.REJECTED) {
      setValue('reason', rejectImageReasonsFromSetting?.[0]?.name);
      setValue('status', status);
    }

    const isConfirm = await confirm({
      title: tSupplierProfile(title),
      body: (
        <ConfirmUpdatingProfile
          control={control}
          description={tSupplierProfile(desc)}
          setValue={setValue}
          reasons={
            status === TASKER_ONBOARDING_PROCESS_STATUS.REJECTED
              ? rejectImageReasonsFromSetting.map(reason => reason?.name)
              : []
          }
        />
      ),
      cancelButton: tSupplierProfile('CANCEL'),
      actionButton: tSupplierProfile('CONFIRM'),
    });

    if (isConfirm) {
      const formData = new FormData();
      const { reason } = getValues();
      formData.append('status', status);
      formData.append('language', supplierProfile?.taskerLanguage || 'en');
      formData.append('fieldName', fieldName);

      if (reason) {
        formData.append('reason', reason);
      }

      submit(formData, { method: 'post' });
    }
  };

  const updateNote = useCallback(
    async ({
      noteIndex,
      updatedNote,
    }: {
      noteIndex: number;
      updatedNote: string;
    }) => {
      const formData = new FormData();

      formData.append('noteIndex', noteIndex?.toString());
      formData.append('updatedNote', updatedNote || '');

      submit(formData, { method: 'post' });
    },
    [submit],
  );

  const historyColumns: ColumnDef<
    NonNullable<
      SerializeFrom<
        ReturnValueIgnorePromise<typeof getTaskerProfileDetail>
      >['data']['actionHistories']
    >[0]
  >[] = useMemo(
    () => [
      {
        accessorKey: 'updatedBy',
        header: ({ column }) => (
          <DataTableColumnHeader
            className="whitespace-nowrap"
            column={column}
            title={tSupplierProfile('CONTENT_UPDATED')}
          />
        ),
        cell: ({ row }) => (
          <div className="flex gap-3 whitespace-nowrap">
            <TaskerProfileStatus status={row.original?.oldStatus} />
            <ChevronsRight className="text-primary" />
            <TaskerProfileStatus status={row.original?.newStatus} />
          </div>
        ),
        enableSorting: false,
      },
      {
        accessorKey: 'updatedByUsername',
        header: ({ column }) => (
          <DataTableColumnHeader
            className="whitespace-nowrap"
            column={column}
            title={tSupplierProfile('UPDATED_BY')}
          />
        ),
        cell: ({ row }) => (
          <Typography variant="p">{row.original?.updatedByUsername}</Typography>
        ),
        enableSorting: false,
      },
      {
        accessorKey: 'createdAt',
        header: ({ column }) => (
          <DataTableColumnHeader
            className="whitespace-nowrap"
            column={column}
            title={tSupplierProfile('DATE')}
          />
        ),
        cell: ({ row }) =>
          row.original?.createdAt ? (
            <Typography variant="p">
              {format(row.original?.createdAt, 'HH:mm - dd/MM/yyyy')}
            </Typography>
          ) : null,
      },
      {
        accessorKey: 'reason',
        header: ({ column }) => (
          <DataTableColumnHeader
            className="whitespace-nowrap"
            column={column}
            title={tSupplierProfile('REASON')}
          />
        ),
        enableSorting: false,
        cell: ({ row }) => (
          <Typography variant="p">{row.original?.reason}</Typography>
        ),
      },
    ],
    [tSupplierProfile],
  );

  const noteColumns: ColumnDef<
    NonNullable<
      SerializeFrom<
        ReturnValueIgnorePromise<typeof getTaskerProfileDetail>
      >['data']['notes']
    >[0] & { index: number }
  >[] = useMemo(
    () => [
      {
        accessorKey: 'description',
        header: ({ column }) => (
          <DataTableColumnHeader
            className="whitespace-nowrap"
            column={column}
            title={tSupplierProfile('NOTE')}
          />
        ),
        cell: ({ row }) => (
          <div className="flex gap-2 items-center">
            <Typography variant="p" affects="removePMargin">
              {row.original?.description}
            </Typography>
            {row.original?.notedAt &&
            momentTz(row.original.notedAt)
              .add(24, 'hours')
              .isAfter(momentTz().toDate()) ? (
              <UpdateNoteDialog
                noteInfo={row.original}
                onSubmit={async ({ updatedNote }) =>
                  await updateNote({
                    updatedNote,
                    noteIndex: row.original?.index,
                  })
                }
              />
            ) : null}
          </div>
        ),
        enableSorting: false,
      },
      {
        accessorKey: 'updatedBy',
        header: ({ column }) => (
          <DataTableColumnHeader
            className="whitespace-nowrap"
            column={column}
            title={tSupplierProfile('NOTE_BY')}
          />
        ),
        cell: ({ row }) => (
          <Typography variant="p">{row.original?.notedBy}</Typography>
        ),
        enableSorting: false,
      },
      {
        accessorKey: 'notedAt',
        header: ({ column }) => (
          <DataTableColumnHeader
            className="whitespace-nowrap"
            column={column}
            title={tSupplierProfile('NOTE_AT')}
          />
        ),
        cell: ({ row }) =>
          row.original?.notedAt ? (
            <Typography variant="p">
              {format(row.original?.notedAt, 'HH:mm - dd/MM/yyyy')}
            </Typography>
          ) : null,
      },
    ],
    [tSupplierProfile, updateNote],
  );

  return (
    <>
      <div className="flex bg-secondary p-4 justify-between align-middle">
        <div className="grid space-y-2 rounded-xl">
          <div className="flex items-center">
            <Typography className="capitalize mr-6" variant="h2">
              {tSupplierProfile('SUPPLIER_PROFILE_DETAIL')}
            </Typography>
            <TaskerProfileStatus
              status={getOverallStatusInProfile({
                processStatus: supplierProfile.processStatus,
                status: supplierProfile.status,
                isHaveAppointmentInfo: !!supplierProfile.appointmentInfo,
              })}
            />
          </div>
          <Breadcrumbs />
        </div>
        <div className="flex gap-6 my-auto pr-2">
          <TaskerProfileAction
            profileInfo={{
              taskerName: supplierProfile.taskerName,
              username: supplierProfile.username,
              appointmentInfo: supplierProfile.appointmentInfo,
            }}
            isSubmitLoading={navigation.state === 'submitting'}
            status={getOverallStatusInProfile({
              processStatus: supplierProfile.processStatus,
              status: supplierProfile.status,
              isHaveAppointmentInfo: !!supplierProfile.appointmentInfo,
            })}
            onTriggerProfile={onTriggerProfile}
            disableApproveAction={getIsDisableApproveAction({
              status: getOverallStatusInProfile({
                processStatus: supplierProfile.processStatus,
                status: supplierProfile.status,
                isHaveAppointmentInfo: !!supplierProfile.appointmentInfo,
              }),
              isHaveAppointmentInfo: !!supplierProfile.appointmentInfo,
              identityCardStatus: supplierProfile.identityCard?.status,
              householdStatus: supplierProfile.household?.status,
            })}
            disabled={
              !permissions.includes(
                PERMISSIONS.WRITE_PARTNER_AND_PARTNER_EMPLOYEE_PROFILE_ON_TASKER_ONBOARDING,
              )
            }
            control={control}
            setValue={setValue}
            setOpenScheduleDialog={setOpenDialog}
            setOpenWarningReasonDialog={setOpenWarningMissingReasonDialog}
          />
        </div>
      </div>
      {supplierProfile?.status === TASKER_ONBOARDING_PROCESS_STATUS?.APPROVED &&
      supplierProfile?.appointmentInfo ? (
        <TaskerProfileCard
          title={tSupplierProfile('INFORMATION_RECEPTION_ADDRESS')}>
          <CardProfileDescription
            descriptions={[
              {
                label: tSupplierProfile('OFFICE'),
                value: supplierProfile.appointmentInfo?.name,
              },
              {
                label: tSupplierProfile('ADDRESS'),
                value: supplierProfile.appointmentInfo?.address,
              },
            ]}
          />
        </TaskerProfileCard>
      ) : null}
      <TaskerProfileCard title={tSupplierProfile('SUPPLIER_INFORMATION')}>
        <CardProfileDescription
          descriptions={[
            {
              label: tSupplierProfile('TASKER_NAME'),
              value: formatPhoneNumber(supplierProfile?.taskerPhone),
            },
            {
              label: tSupplierProfile('PHONE_NUMBER'),
              value: supplierProfile?.taskerName,
            },
            {
              label: tSupplierProfile('TASKER_GENDER'),
              value: tSupplierProfile(supplierProfile?.taskerGender),
            },
            {
              label: tSupplierProfile('SUBMISSION_DATE'),
              value: supplierProfile?.createdAt
                ? format(supplierProfile.createdAt, 'HH:mm - dd/MM/yyyy')
                : '',
            },
            {
              label: tSupplierProfile('UPDATED_AT'),
              value: supplierProfile?.updatedAt
                ? format(supplierProfile.updatedAt, 'HH:mm - dd/MM/yyyy')
                : '',
            },
            {
              label: tSupplierProfile('SERVICES'),
              tags: supplierProfile?.services?.map(
                service =>
                  `${service.text?.[i18n.language || 'en'] ?? ''} ${service.isSubscription ? '(Subscription)' : ''}`,
              ),
            },
            {
              label: tSupplierProfile('CITY'),
              tags: Array.from(
                new Set(
                  supplierProfile?.taskerWorkingPlaces?.map(
                    workingPlace => workingPlace.city,
                  ) || [],
                ),
              ),
            },
          ]}
        />
      </TaskerProfileCard>
      <TaskerProfileCard
        title={tSupplierProfile('UPLOADED_DOCUMENTS')}
        extraContent={
          <Button
            className={
              getIsDisableSendingNotification({ profile: supplierProfile })
                ? 'bg-gray-300 text-white'
                : ''
            }
            onClick={async () => {
              await sendingNotificationController.onSubmit({
                language: supplierProfile?.taskerLanguage || 'en',
                taskerId: supplierProfile?.taskerId,
                sendRemindMessage: JSON.stringify(
                  getAllOfTaskerOnboardingMessageLanguage<
                    SerializeFrom<
                      ReturnValueIgnorePromise<
                        typeof getTaskerProfileDetail
                      >['data']
                    >
                  >({ profile: supplierProfile }),
                ),
              });
            }}
            disabled={getIsDisableSendingNotification({
              profile: supplierProfile,
            })}>
            {tSupplierProfile('SEND_NOTIFICATION')}
          </Button>
        }>
        <ProfileImageAccordion
          profileStatus={getOverallStatusInProfile({
            processStatus: supplierProfile.processStatus,
            status: supplierProfile.status,
            isHaveAppointmentInfo: !!supplierProfile.appointmentInfo,
          })}
          reasonsFromSetting={reasonsFromSetting || []}
          setOpenWarningMissingReasonDialog={setOpenWarningMissingReasonDialog}
          disabled={
            !permissions.includes(
              PERMISSIONS.WRITE_PARTNER_AND_PARTNER_EMPLOYEE_PROFILE_ON_TASKER_ONBOARDING,
            ) ||
            getOverallStatusInProfile({
              processStatus: supplierProfile.processStatus,
              status: supplierProfile.status,
              isHaveAppointmentInfo: !!supplierProfile.appointmentInfo,
            }) === TASKER_ONBOARDING_PROCESS_STATUS.APPROVED
          }
          profileType={IMAGE_PROFILE_TYPE.SUPPLIER}
          localeAddress="supplier-profile"
          isSubmitLoading={navigation.state === 'submitting'}
          onReviewImageAgain={onReviewImageAgain}
          onTrigger={onTriggerImageProfile}
          items={[
            {
              label: tSupplierProfile('IDENTITY_CARD'),
              info: supplierProfile.identityCard,
              imageField: TASKER_ONBOARDING_IMAGE_KEY.IDENTITY_CARD,
            },
            {
              label: tSupplierProfile('HOUSEHOLD_BOOKLET'),
              info: supplierProfile.household,
              imageField: TASKER_ONBOARDING_IMAGE_KEY.HOUSEHOLD,
            },
            {
              label: tSupplierProfile('CURRICULUM_VITAE'),
              info: supplierProfile.curriculumVitae,
              imageField: TASKER_ONBOARDING_IMAGE_KEY.CURRICULUM_VITAE,
            },
            {
              label: tSupplierProfile('CONFIRMATION_CONDUCT'),
              info: supplierProfile.confirmationConduct,
              imageField: TASKER_ONBOARDING_IMAGE_KEY.CONFIRMATION_CONDUCT,
            },
            {
              label: tSupplierProfile('CRIMINAL_RECORDS'),
              info: supplierProfile.criminalRecords,
              imageField: TASKER_ONBOARDING_IMAGE_KEY.CRIMINAL_RECORD,
            },
            {
              label: tSupplierProfile('MASSAGE_PRACTICE_CERTIFICATE'),
              info: supplierProfile.massagePracticeCertificate,
              imageField:
                TASKER_ONBOARDING_IMAGE_KEY.MASSAGE_PRACTICE_CERTIFICATE,
            },
          ]}
        />
      </TaskerProfileCard>
      <TaskerProfileCard>
        <Tabs defaultValue="note" className="pt-6">
          <TabsList className="grid grid-cols-2 w-[140px]">
            <TabsTrigger value="note">{tSupplierProfile('NOTE')}</TabsTrigger>
            <TabsTrigger value="history">
              {tSupplierProfile('HISTORY')}
            </TabsTrigger>
          </TabsList>
          <TabsContent value="note" className="mt-4">
            <NoteForm
              register={noteFormRegister}
              control={noteFormControl}
              resetField={resetNoteFormValue}
            />
            <DataTableBasic
              data={reversedNotes}
              columns={noteColumns}
              manualPagination
              isDisplayPagination={false}
            />
          </TabsContent>
          <TabsContent value="history" className="mt-4">
            <DataTableBasic
              manualPagination
              isDisplayPagination={false}
              columns={historyColumns}
              data={reversedActionHistories}
            />
          </TabsContent>
        </Tabs>
      </TaskerProfileCard>
      <UpdateScheduleDialog
        openDialog={openDialog}
        setOpenDialog={setOpenDialog}
        offices={offices}
        profileInfo={supplierProfile}
      />
      <WarningMissingReasonConfigurationDialog
        openDialog={openWarningMissingReasonDialog}
        taskerName={supplierProfile?.taskerName}
        setOpenDialog={setOpenWarningMissingReasonDialog}
      />
    </>
  );
}
