import type { SerializeFrom } from '@remix-run/node';
import {
  Link,
  isRouteErrorResponse,
  useOutletContext,
  useRouteError,
} from '@remix-run/react';
import {
  DAYS_OF_WEEK,
  OFFICE_STATUS_IN_CONFIGURATION,
  PERMISSIONS,
  ROUTE_NAME,
} from 'btaskee-constants';
import { useGlobalStore } from 'btaskee-hooks';
import {
  Badge,
  Breadcrumbs,
  BtaskeeResponseError,
  Button,
  Card,
  CardContent,
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
  Typography,
  cn,
  toast,
} from 'btaskee-ui';
import { getFormattedRangeHourText, momentTz } from 'btaskee-utils';
import { format } from 'date-fns';
import { useEffect } from 'react';
import { useTranslation } from 'react-i18next';
import { CardProfileDescription } from '~/components/tasker-common';
import type { getListOfficesFromSettingSystem } from '~/services/offices-configuration.server';

export function ErrorBoundary() {
  const error = useRouteError() as { message?: string };
  const { t: tCommon } = useTranslation('common');

  if (isRouteErrorResponse(error)) {
    if (error?.message) {
      toast({ description: JSON.stringify(error.message) });
    }

    return <BtaskeeResponseError t={tCommon} errorStatus={error.status} />;
  }

  return <BtaskeeResponseError t={tCommon} errorStatus={500} />;
}

export default function OfficeDetailContent() {
  const { t: tOfficeConfigs } = useTranslation('offices-configuration');
  const permissions = useGlobalStore(store => store.permissions);
  const outletData = useOutletContext<
    SerializeFrom<
      ReturnValueIgnorePromise<typeof getListOfficesFromSettingSystem>[0]
    > & { flashMessage: string; indexOffice: number }
  >();

  useEffect(() => {
    if (outletData?.flashMessage) {
      toast({ variant: 'success', description: outletData.flashMessage });
    }
  }, [outletData?.flashMessage]);

  return (
    <>
      <div className="mb-6 flex justify-between items-center rounded-xl bg-secondary py-4 px-6">
        <div>
          <Typography variant="h2">
            {tOfficeConfigs('OFFICE_DETAIL')}
          </Typography>
          <Breadcrumbs />
        </div>
        <Button
          asChild={permissions.includes(PERMISSIONS.WRITE_LIST_OFFICES)}
          disabled={!permissions.includes(PERMISSIONS.WRITE_LIST_OFFICES)}>
          <Link
            to={`${ROUTE_NAME.LIST_OFFICES}/${outletData.indexOffice}${ROUTE_NAME.EDIT}`}>
            {tOfficeConfigs('UPDATE')}
          </Link>
        </Button>
      </div>
      <Card className="bg-gray-50 pt-6 rounded-[15px]">
        <CardContent>
          <CardProfileDescription
            descriptions={[
              {
                label: tOfficeConfigs('OFFICE_NAME'),
                value: outletData?.name,
              },
              {
                label: tOfficeConfigs('ADDRESS'),
                value: outletData?.address,
              },
              {
                label: tOfficeConfigs('CITY'),
                value: outletData?.city,
              },
              {
                label: tOfficeConfigs('PHONE_NUMBER'),
                value: outletData?.phoneNumber,
              },
              {
                label: tOfficeConfigs('OPERATION_HOURS'),
                value: getFormattedRangeHourText({
                  startDate: outletData?.workingStartDate,
                  endDate: outletData?.workingEndDate,
                }),
              },
              {
                label: tOfficeConfigs('WORKING_DAY'),
                value: outletData?.workingDays?.length
                  ? DAYS_OF_WEEK.filter(day =>
                      outletData.workingDays.includes(day.value),
                    )
                      .map(day => tOfficeConfigs(day.label))
                      .join(', ')
                  : null,
              },
              {
                label: tOfficeConfigs('CREATED_AT'),
                value: outletData.createdAt
                  ? format(outletData.createdAt, 'HH:mm - dd/MM/yyyy')
                  : '',
              },
              {
                label: tOfficeConfigs('CREATED_BY'),
                value: outletData.createdBy,
              },
              {
                label: tOfficeConfigs('STATUS'),
                value: outletData.status ? (
                  <Badge
                    className={cn(
                      'rounded-md min-h-5',
                      outletData.status ===
                        OFFICE_STATUS_IN_CONFIGURATION.ACTIVE
                        ? 'text-green-500 bg-green-50'
                        : 'bg-gray-100 text-gray-500',
                    )}>
                    {tOfficeConfigs(outletData.status)}
                  </Badge>
                ) : (
                  ''
                ),
              },
            ]}
          />
        </CardContent>
      </Card>

      {outletData?.offDays?.filter(offDay => offDay.isActive).length ? (
        <div className="border rounded-md overflow-hidden mt-6">
          <Table className="max-h-[80vh] overflow-y-auto w-full overflow-x-auto">
            <TableHeader className="bg-gray-100">
              <TableRow>
                <TableHead className="whitespace-nowrap">
                  {tOfficeConfigs('OFFICE_OFF_DAYS')}
                </TableHead>
                <TableHead className="whitespace-nowrap">
                  {tOfficeConfigs('HOLIDAY_DATE_RANGE')}
                </TableHead>
                <TableHead>{tOfficeConfigs('CREATED_BY')}</TableHead>
                <TableHead className="whitespace-nowrap">
                  {tOfficeConfigs('CREATED_AT')}
                </TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {outletData?.offDays
                ?.filter(offDay => offDay.isActive)
                .map(offDay => (
                  <TableRow key={offDay._id}>
                    <TableCell className="whitespace-nowrap">
                      {offDay.reason}
                    </TableCell>
                    <TableCell className="whitespace-nowrap">
                      {offDay.isHoliday
                        ? `${momentTz(offDay.from).format('DD/MM')} - ${momentTz(offDay.to).format('DD/MM')}`
                        : `${momentTz(offDay.from).format('DD/MM/YYYY')} - ${momentTz(offDay.to).format('DD/MM/YYYY')}`}
                    </TableCell>
                    <TableCell>{offDay.createdBy}</TableCell>
                    <TableCell className="whitespace-nowrap">
                      {momentTz(offDay.createdAt).format('DD/MM/YYYY')}
                    </TableCell>
                  </TableRow>
                ))}
            </TableBody>
          </Table>
        </div>
      ) : null}
    </>
  );
}
