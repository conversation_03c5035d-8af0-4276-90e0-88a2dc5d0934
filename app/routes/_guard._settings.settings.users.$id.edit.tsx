import { zodResolver } from '@hookform/resolvers/zod';
import { type LoaderFunctionArgs, json, redirect } from '@remix-run/node';
import { useActionData, useNavigate, useSubmit } from '@remix-run/react';
import {
  ACTION_NAME,
  PERMISSIONS,
  ROUTE_NAME,
  STATUS,
} from 'btaskee-constants';
import { useLoaderDataSafely } from 'btaskee-hooks';
import {
  Breadcrumbs,
  BreadcrumbsLink,
  Button,
  ErrorMessageBase,
  Grid,
  GridItem,
  Input,
  Label,
  MultiSelectAdvance,
  SelectBase,
  Separator,
  Typography,
  toast,
  useConfirm,
} from 'btaskee-ui';
import { useEffect } from 'react';
import { Controller, useForm } from 'react-hook-form';
import { useTranslation } from 'react-i18next';
import { z } from 'zod';
import { hoc404, hocAction, hocLoader } from '~/hoc/remix';
import { getUserByUserId, updateUser } from '~/services/auth.server';
import { getCities, getUserSession } from '~/services/helpers.server';
import {
  getAllChildrenGroupOfUser,
  getGroupsOfUser,
} from '~/services/role-base-access-control.server';
import { commitSession, getSession } from '~/services/session.server';

export const handle = {
  breadcrumb: (data: { user: Users }) => {
    const { user } = data;

    return (
      <BreadcrumbsLink
        to={`${ROUTE_NAME.USER_SETTING}/${user._id}/edit`}
        label="UPDATE_USER"
      />
    );
  },
  i18n: 'user-settings',
};

export const action = hocAction(
  async ({ request, params }, { setInformationActionHistory }) => {
    const formData = await request.clone().formData();

    const username = formData.get('username')?.toString() || '';
    const email = formData.get('email')?.toString() || '';
    const name = formData.get('name')?.toString() || '';
    const status = formData.get('status')?.toString() || '';
    const cities = JSON.parse(formData.get('cities')?.toString() || '[]');
    const groupIds = JSON.parse(formData.get('groups')?.toString() || '[]');

    const [user, session] = await Promise.all([
      updateUser({
        name,
        email,
        username,
        cities,
        status,
        userId: params.id || '',
        groupIds,
      }),
      getSession(request.headers.get('cookie')),
    ]);

    session.flash('flashMessage', `User ${user?.username} updated`);

    setInformationActionHistory({
      action: ACTION_NAME.UPDATE_USER,
    });

    return redirect(ROUTE_NAME.USER_SETTING, {
      headers: {
        'Set-Cookie': await commitSession(session),
      },
    });
  },
  PERMISSIONS.MANAGER,
);

export const loader = hocLoader(
  async ({ request, params }: LoaderFunctionArgs) => {
    const { isoCode, userId: managerId } = await getUserSession({
      headers: request.headers,
    });

    const [cities, user, groupOfManager, groupOfUser] = await Promise.all([
      getCities(isoCode),
      hoc404(() => getUserByUserId({ userId: params.id || '' })),
      getAllChildrenGroupOfUser(managerId),
      getGroupsOfUser({
        userId: params.id || '',
        projection: { _id: 1, name: 1 },
      }),
    ]);

    if (!user || !user?.email) {
      throw new Error('USER_NOT_FOUND');
    }

    return json({ cities, user, groupOfUser, groupOfManager });
  },
  PERMISSIONS.MANAGER,
);

export default function Screen() {
  const { t: tUserSettings } = useTranslation('user-settings');
  const confirm = useConfirm();
  const navigate = useNavigate();

  const actionData = useActionData<ActionTypeWithError<typeof action>>();
  useEffect(() => {
    if (actionData?.error) {
      toast({ description: actionData.error });
    }
  }, [actionData]);

  const FormDataSchema = z.object({
    email: z.string().email(tUserSettings('INVALID_EMAIL_ADDRESS')),
    cities: z
      .array(z.any())
      .min(1, tUserSettings('MUST_HAVE_AT_LEAST_ONE_CITY')),
    username: z.string().min(1, tUserSettings('USERNAME_IS_REQUIRED')),
    groups: z
      .array(z.any())
      .min(1, tUserSettings('MUST_HAVE_AT_LEAST_ONE_GROUP')),
    name: z.string().min(1, tUserSettings('MEMBER_NAME_IS_REQUIRED')),
    status: z.string().min(1, tUserSettings('STATUS_IS_REQUIRED')),
  });

  const { user, groupOfManager, cities, groupOfUser } =
    useLoaderDataSafely<typeof loader>();

  const submit = useSubmit();
  const { register, control, handleSubmit, formState } = useForm<
    z.infer<typeof FormDataSchema>
  >({
    resolver: zodResolver(FormDataSchema),
    defaultValues: {
      name: user?.name,
      email: user?.email,
      cities: user?.cities ?? [],
      username: user?.username,
      groups: groupOfUser?.map(group => group?._id),
      status: user?.status || 'ACTIVE',
    },
  });

  async function onSubmit(data: z.infer<typeof FormDataSchema>) {
    const formData = new FormData();

    formData.append('cities', JSON.stringify(data.cities));
    formData.append('username', data.username);
    formData.append('email', data.email);
    formData.append('name', data.name);
    formData.append('status', data.status);
    formData.append('groups', JSON.stringify(data.groups));

    const isConfirm = await confirm({
      title: tUserSettings('EDIT'),
      body: tUserSettings('ARE_YOU_SURE_EDIT_THIS_RECORD'),
      cancelButton: tUserSettings('CANCEL'),
      actionButton: tUserSettings('CONFIRM'),
    });

    if (isConfirm) submit(formData, { method: 'post' });
  }

  return (
    <>
      <div className="flex flex-col gap-2 rounded-lg bg-secondary p-4">
        <Typography variant="h3">{tUserSettings('UPDATE_USER')}</Typography>
        <Breadcrumbs />
      </div>

      <form className="mt-4" onSubmit={handleSubmit(onSubmit)}>
        <Grid className="gap-4">
          <GridItem>
            <Label htmlFor="username">{tUserSettings('USERNAME')}</Label>
            <Input
              {...register('username', {
                required: tUserSettings('THIS_FIELD_IS_REQUIRED'),
              })}
              className="col-span-3"
              placeholder={tUserSettings('USERNAME')}
            />
            <ErrorMessageBase errors={formState.errors} name="username" />
          </GridItem>
          <GridItem>
            <Label htmlFor="name">{tUserSettings('MEMBER_NAME')}</Label>
            <Input
              {...register('name', {
                required: tUserSettings('THIS_FIELD_IS_REQUIRED'),
              })}
              className="col-span-3"
              placeholder={tUserSettings('ENTER_MEMBER_NAME')}
            />
            <ErrorMessageBase errors={formState.errors} name="name" />
          </GridItem>
          <GridItem>
            <Label htmlFor="email">{tUserSettings('EMAIL')}</Label>
            <Input
              {...register('email', {
                required: tUserSettings('THIS_FIELD_IS_REQUIRED'),
              })}
              type="email"
              disabled
              className="col-span-3"
              placeholder={tUserSettings('EMAIL')}
            />
            <ErrorMessageBase errors={formState.errors} name="email" />
          </GridItem>
          <GridItem>
            <Label>{tUserSettings('STATUS')}</Label>
            <div className="col-span-3">
              <Controller
                control={control}
                name="status"
                rules={{
                  required: tUserSettings('THIS_FIELD_IS_REQUIRED'),
                }}
                render={({ field: { onChange, value } }) => (
                  <SelectBase
                    placeholder={tUserSettings('CHOOSE_USER_STATUS')}
                    defaultValue={value}
                    onValueChange={onChange}
                    options={Object.values(STATUS).map(status => ({
                      label: tUserSettings(status),
                      value: status,
                    }))}
                  />
                )}
              />
              <ErrorMessageBase errors={formState.errors} name="status" />
            </div>
          </GridItem>
          <GridItem>
            <Label>{tUserSettings('CITY')}</Label>
            <div className="col-span-3">
              <Controller
                control={control}
                name="cities"
                rules={{
                  required: tUserSettings('THIS_FIELD_IS_REQUIRED'),
                }}
                render={({ field: { onChange, value } }) => (
                  <MultiSelectAdvance
                    options={cities?.map(city => ({
                      label: city,
                      value: city,
                    }))}
                    onValueChange={onChange}
                    defaultValue={value}
                    placeholder={tUserSettings('SELECT_CITY')}
                    variant="blue"
                    maxCount={7}
                  />
                )}
              />
              <ErrorMessageBase errors={formState.errors} name="cities" />
            </div>
          </GridItem>
          <GridItem>
            <Label>{tUserSettings('GROUPS')}</Label>
            <div className="col-span-3">
              <Controller
                control={control}
                name="groups"
                rules={{
                  required: tUserSettings('THIS_FIELD_IS_REQUIRED'),
                }}
                render={({ field: { onChange, value } }) => (
                  <MultiSelectAdvance
                    options={groupOfManager?.map(group => ({
                      label: group.name,
                      value: group._id,
                    }))}
                    onValueChange={onChange}
                    defaultValue={value}
                    placeholder={tUserSettings('CHOOSE_GROUP')}
                    variant="blue"
                    maxCount={4}
                  />
                )}
              />
              <ErrorMessageBase errors={formState.errors} name="groups" />
            </div>
          </GridItem>
        </Grid>
        <Separator className="my-6" />
        <div className="mt-6 flex justify-end gap-4">
          <Button
            variant="ghost"
            className="border border-primary text-primary"
            onClick={() => navigate(-1)}
            type="button">
            {tUserSettings('CANCEL')}
          </Button>
          <Button type="submit">{tUserSettings('SAVE_CHANGES')}</Button>
        </div>
      </form>
    </>
  );
}
