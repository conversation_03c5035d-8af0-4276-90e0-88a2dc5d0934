import { DotsHorizontalIcon } from '@radix-ui/react-icons';
import type { SerializeFrom } from '@remix-run/node';
import {
  Link,
  isRouteErrorResponse,
  useActionData,
  useNavigate,
  useOutletContext,
  useRouteError,
  useSearchParams,
  useSubmit,
} from '@remix-run/react';
import type { ColumnDef } from '@tanstack/react-table';
import {
  ACTION_NAME,
  OFFICE_STATUS_IN_CONFIGURATION,
  PERMISSIONS,
  ROUTE_NAME,
} from 'btaskee-constants';
import { useGlobalStore } from 'btaskee-hooks';
import {
  BTaskeeTable,
  Badge,
  Breadcrumbs,
  BtaskeeResponseError,
  Button,
  Card,
  CardContent,
  DataTableColumnHeader,
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
  Separator,
  Typography,
  cn,
  toast,
  useConfirm,
} from 'btaskee-ui';
import {
  getFormattedRangeHourText,
  getPageSizeAndPageIndex,
} from 'btaskee-utils';
import { useEffect } from 'react';
import { useTranslation } from 'react-i18next';
import { hocAction } from '~/hoc/remix';
import { getUserSession } from '~/services/helpers.server';
import {
  type CommonGettingOfficesFromSettingSystem,
  deleteOfficeByIndex,
  type getListOfficesFromSettingSystem,
  type getTotalOfficesFromSettingSystem,
} from '~/services/offices-configuration.server';
import { finalizeAction } from '~/utils/common';

export function ErrorBoundary() {
  const error = useRouteError() as { message?: string };
  const { t: tCommon } = useTranslation('common');

  if (isRouteErrorResponse(error)) {
    if (error?.message) {
      toast({ description: JSON.stringify(error.message) });
    }

    return <BtaskeeResponseError t={tCommon} errorStatus={error.status} />;
  }

  return <BtaskeeResponseError t={tCommon} errorStatus={500} />;
}
export const action = hocAction(
  async ({ request }, { setInformationActionHistory }) => {
    const { isoCode } = await getUserSession({
      headers: request.headers,
    });
    const formData = await request.clone().formData();
    const indexOfficeNeedDelete =
      formData.get('indexOfficeNeedDelete')?.toString() || '';
    const name = formData.get('name')?.toString() || '';

    if (indexOfficeNeedDelete) {
      const response = await deleteOfficeByIndex({
        isoCode,
        officeIndex: Number(indexOfficeNeedDelete),
      });

      setInformationActionHistory({
        action: ACTION_NAME.DELETE_REJECT_REASON,
        dataRelated: { name },
      });

      return finalizeAction({
        request,
        flashMessage: {
          message: response.msg,
          translationKey: 'offices-configuration',
        },
        destinationUrl: ROUTE_NAME.LIST_OFFICES,
      });
    }
  },
  PERMISSIONS.WRITE_LIST_OFFICES,
);

export default function ListOfficesContent() {
  const outletData = useOutletContext<{
    offices: SerializeFrom<
      ReturnValueIgnorePromise<typeof getListOfficesFromSettingSystem>[0]
    >[];
    total: SerializeFrom<
      ReturnValueIgnorePromise<typeof getTotalOfficesFromSettingSystem>
    >;
    flashMessage: string;
    filterValue: CommonGettingOfficesFromSettingSystem['filter'];
  }>();
  const actionData = useActionData<ActionTypeWithError<typeof action>>();

  useEffect(() => {
    if (actionData?.error) {
      toast({ description: actionData.error });
    }
  }, [actionData]);

  const confirm = useConfirm();
  const submit = useSubmit();
  const [searchParams] = useSearchParams();
  const navigate = useNavigate();
  const permissions = useGlobalStore(store => store.permissions);
  const { t } = useTranslation('offices-configuration');

  const BlockOfficeInfo = ({
    title,
    body,
  }: {
    title: string;
    body: string;
  }) => (
    <div className="flex flex-col">
      <Typography
        variant="p"
        affects="removePMargin"
        className="text-gray-400 font-normal">
        {title}
      </Typography>
      <Typography
        variant="p"
        affects="removePMargin"
        className="text-base font-normal text-gray-600 break-words">
        {body}
      </Typography>
    </div>
  );

  const deleteSpecificOffice = async ({
    name,
    address,
    workingStartDate,
    workingEndDate,
    officeIndex,
  }: SerializeFrom<
    ReturnValueIgnorePromise<typeof getListOfficesFromSettingSystem>[0]
  > & { officeIndex: number }) => {
    const isConfirm = await confirm({
      title: t('TITLE_CONFIRM_DELETE_OFFICE'),
      body: (
        <>
          <Typography variant="p">
            {t('DESCRIPTION_CONFIRM_DELETE_OFFICE')}
          </Typography>
          <Card className="bg-gray-100 my-4">
            <CardContent className="p-3">
              <BlockOfficeInfo title={t('ADDRESS')} body={address} />
              <Separator className="my-3" />
              <BlockOfficeInfo title={t('OFFICE_NAME')} body={name} />
              <Separator className="my-3" />
              <BlockOfficeInfo
                title={t('OPERATION_HOURS')}
                body={getFormattedRangeHourText({
                  startDate: workingStartDate,
                  endDate: workingEndDate,
                })}
              />
            </CardContent>
          </Card>
        </>
      ),
      actionButton: t('CONFIRM'),
      cancelButton: t('CANCEL'),
    });

    if (isConfirm) {
      const formData = new FormData();

      formData.append('indexOfficeNeedDelete', officeIndex.toString());
      formData.append('name', name || '');

      submit(formData, { method: 'post' });
    }
  };

  const columns: ColumnDef<
    SerializeFrom<
      ReturnValueIgnorePromise<typeof getListOfficesFromSettingSystem>[0]
    > & { officeIndex: number }
  >[] = [
    {
      accessorKey: 'officeName',
      header: ({ column }) => (
        <DataTableColumnHeader
          column={column}
          className="whitespace-nowrap"
          title={t('OFFICE_NAME')}
        />
      ),
      maxSize: 250,
      cell: ({ row }) => (
        <Typography
          className="w-[250px] break-words whitespace-normal"
          variant="p">
          {row.original?.name}
        </Typography>
      ),
      enableSorting: false,
    },
    {
      accessorKey: 'address',
      header: ({ column }) => (
        <DataTableColumnHeader
          column={column}
          className="whitespace-nowrap"
          title={t('ADDRESS')}
        />
      ),
      cell: ({ row }) => (
        <Typography
          className="min-w-[300px] break-words whitespace-normal"
          variant="p">
          {row.original?.address}
        </Typography>
      ),
      enableSorting: false,
    },
    {
      accessorKey: 'city',
      header: ({ column }) => (
        <DataTableColumnHeader
          column={column}
          className="whitespace-nowrap"
          title={t('CITY')}
        />
      ),
      cell: ({ row }) => (
        <Typography className="whitespace-nowrap" variant="p">
          {row.original?.city}
        </Typography>
      ),
      enableSorting: false,
    },
    {
      accessorKey: 'operationHours',
      header: ({ column }) => (
        <DataTableColumnHeader
          column={column}
          className="whitespace-nowrap"
          title={t('OPERATION_HOURS')}
        />
      ),
      maxSize: 130,
      cell: ({ row }) => (
        <Typography className="break-words" variant="p">
          {getFormattedRangeHourText({
            startDate: row.original?.workingStartDate,
            endDate: row.original?.workingEndDate,
          })}
        </Typography>
      ),
      enableSorting: false,
    },
    {
      accessorKey: 'status',
      maxSize: 80,
      header: ({ column }) => (
        <DataTableColumnHeader
          column={column}
          className="whitespace-nowrap"
          title={t('STATUS')}
        />
      ),
      cell: ({ row }) =>
        row.original?.status ? (
          <Badge
            className={cn(
              'rounded-md min-h-5',
              row.original.status === OFFICE_STATUS_IN_CONFIGURATION.ACTIVE
                ? 'text-green-500 bg-green-50'
                : 'bg-gray-100 text-gray-500',
            )}>
            {t(row.original.status)}
          </Badge>
        ) : null,
      enableSorting: false,
    },
    {
      accessorKey: 'action',
      header: ({ column }) => (
        <DataTableColumnHeader
          column={column}
          className="whitespace-nowrap"
          title={t('ACTION')}
        />
      ),
      cell: ({ row }) => (
        <DropdownMenu>
          <DropdownMenuTrigger asChild>
            <Button
              variant="ghost"
              className="ml-auto mr-5 flex h-8 w-8 p-0 data-[state=open]:bg-muted">
              <DotsHorizontalIcon className="h-4 w-4" />
              <Typography className="sr-only">{t('OPEN_MENU')}</Typography>
            </Button>
          </DropdownMenuTrigger>
          <DropdownMenuContent>
            <DropdownMenuItem>
              <Button
                variant="ghost"
                className="h-4"
                onClick={() =>
                  navigate(
                    `${ROUTE_NAME.LIST_OFFICES}/${row.original?.officeIndex}`,
                  )
                }>
                {t('VIEW_OFFICE')}
              </Button>
            </DropdownMenuItem>
            <DropdownMenuItem
              onClick={event => {
                event.stopPropagation();
              }}>
              <Button
                variant="ghost"
                className="h-4"
                disabled={!permissions.includes(PERMISSIONS.WRITE_LIST_OFFICES)}
                onClick={() =>
                  navigate(
                    `${ROUTE_NAME.LIST_OFFICES}/${row.original?.officeIndex}${ROUTE_NAME.EDIT}`,
                  )
                }>
                {t('EDIT_OFFICE')}
              </Button>
            </DropdownMenuItem>
            <DropdownMenuItem
              onClick={event => {
                event.stopPropagation();
              }}>
              <Button
                variant="ghost"
                disabled={!permissions.includes(PERMISSIONS.WRITE_LIST_OFFICES)}
                className="text-red-500 h-4"
                onClick={async () => {
                  await deleteSpecificOffice(row.original);
                }}>
                {t('DELETE_OFFICE')}
              </Button>
            </DropdownMenuItem>
          </DropdownMenuContent>
        </DropdownMenu>
      ),
      enableSorting: false,
    },
  ];

  const getIndexOfficeWithPagination = ({ index }: { index: number }) => {
    const pageSize = Number(searchParams.get('pageSize') || 10);
    const pageIndex = Number(searchParams.get('pageIndex') || 0);

    return pageSize * pageIndex + index;
  };

  return (
    <div className="max-w-[1176px] overflow-auto">
      <div className="mb-6 flex justify-between items-center rounded-xl bg-secondary py-4 px-6">
        <div className="grid gap-2">
          <Typography variant="h2">{t('LIST_OFFICE')}</Typography>
          <Breadcrumbs />
        </div>
        <Button
          asChild={permissions.includes(PERMISSIONS.WRITE_LIST_OFFICES)}
          disabled={!permissions.includes(PERMISSIONS.WRITE_LIST_OFFICES)}>
          <Link to={`${ROUTE_NAME.LIST_OFFICES}${ROUTE_NAME.CREATE}`}>
            {t('CREATE')}
          </Link>
        </Button>
      </div>
      <BTaskeeTable
        columns={columns}
        data={outletData?.offices?.map((office, index) => ({
          ...office,
          officeIndex: getIndexOfficeWithPagination({ index }),
        }))}
        isShowClearButton
        onClickRow={officeInfo => {
          navigate(`${ROUTE_NAME.LIST_OFFICES}/${officeInfo.officeIndex}`);
        }}
        filters={[
          {
            name: 'statuses',
            value: outletData?.filterValue?.statuses || '',
            placeholder: t('STATUS'),
            options: Object.values(OFFICE_STATUS_IN_CONFIGURATION).map(
              status => ({
                label: t(status),
                value: status,
              }),
            ),
          },
        ]}
        localeAddress="offices-configuration"
        pagination={getPageSizeAndPageIndex({
          total: outletData?.total || 0,
          pageSize: Number(searchParams.get('pageSize') || 0),
          pageIndex: Number(searchParams.get('pageIndex') || 0),
        })}
        search={{
          name: 'searchText',
          placeholder: t('SEARCH_BY_NAME_OR_ADDRESS'),
          defaultValue: outletData?.filterValue?.search || '',
        }}
        total={outletData?.total || 0}
      />
    </div>
  );
}
