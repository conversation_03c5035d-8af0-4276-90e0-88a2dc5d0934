import type { SerializeFrom } from '@remix-run/node';
import {
  isRouteErrorResponse,
  useRouteError,
  useSearchParams,
} from '@remix-run/react';
import type { ColumnDef } from '@tanstack/react-table';
import {
  AutoComplete,
  BTaskeeTable,
  Breadcrumbs,
  BtaskeeResponseError,
  DataTableColumnHeader,
  Input,
  Typography,
  toast,
} from 'btaskee-ui';
import {
  getOrderNumber,
  getPageSizeAndPageIndex,
  momentTz,
} from 'btaskee-utils';
import { useCallback, useMemo, useState } from 'react';
import { useTranslation } from 'react-i18next';
import { useOutletTaskerLocationDetected } from '~/hooks/useLoaderTaskerLocationDetected';
import type { getTaskerLocationDetected } from '~/services/location-detected.server';
import { formatPhoneNumber } from '~/utils/common';

export function ErrorBoundary() {
  const error = useRouteError() as { message?: string };
  const { t: tCommon } = useTranslation('common');

  if (isRouteErrorResponse(error)) {
    if (error?.message) {
      toast({ description: JSON.stringify(error.message) });
    }

    return <BtaskeeResponseError t={tCommon} errorStatus={error.status} />;
  }

  return <BtaskeeResponseError t={tCommon} errorStatus={500} />;
}

export default function TaskerLocationDetectedContent() {
  const { t: tTaskerLocation } = useTranslation('tasker-location-detected');
  const [selectedValue, setSelectedValue] = useState<string>('');
  const [searchParams, setSearchParams] = useSearchParams();
  const { totalTasker, taskers, filterValues, locations, searchText } =
    useOutletTaskerLocationDetected();

  const transformedLocations = useMemo(
    () =>
      locations.map((location, index) => ({
        label: location?.formatted_address || '',
        value: `${index}`,
        data: location?.geometry?.location || '',
      })),
    [locations],
  );

  const handleSelectedValueChange = useCallback(
    (value: string) => {
      setSelectedValue(value);
      const selectedLocation = transformedLocations.find(
        transformedLocation => transformedLocation.value === value,
      );
      if (selectedLocation) {
        setSearchParams(params => {
          params.set(
            'geoCoordinates',
            JSON.stringify(selectedLocation?.data || {}),
          );

          return params;
        });
      }
    },
    [setSearchParams, transformedLocations],
  );

  const columns: ColumnDef<
    SerializeFrom<ReturnValueIgnorePromise<typeof getTaskerLocationDetected>[0]>
  >[] = useMemo(
    () => [
      {
        accessorKey: 'no',
        size: 80,
        header: ({ column }) => (
          <DataTableColumnHeader
            column={column}
            className="whitespace-nowrap"
            title={tTaskerLocation('NO')}
          />
        ),
        enableSorting: false,
        cell: ({ row }) => (
          <Typography className="whitespace-nowrap" variant="p">
            {getOrderNumber({
              pageSize: Number(searchParams.get('pageSize') || 0),
              pageIndex: Number(searchParams.get('pageIndex') || 0),
              orderColumn: row.index,
            })}
          </Typography>
        ),
      },
      {
        accessorKey: 'name',
        header: ({ column }) => (
          <DataTableColumnHeader
            column={column}
            className="whitespace-nowrap"
            title={tTaskerLocation('NAME')}
          />
        ),
        enableSorting: false,
        cell: ({ row }) => (
          <Typography className="whitespace-nowrap" variant="p">
            {row.original?.name}
          </Typography>
        ),
      },
      {
        accessorKey: 'phone',
        header: ({ column }) => (
          <DataTableColumnHeader
            column={column}
            className="whitespace-nowrap"
            title={tTaskerLocation('PHONE')}
          />
        ),
        enableSorting: false,
        cell: ({ row }) => (
          <Typography className="whitespace-nowrap" variant="p">
            {formatPhoneNumber(row.original?.phone)}
          </Typography>
        ),
      },
      {
        accessorKey: 'movingHistoryCount',
        header: ({ column }) => (
          <DataTableColumnHeader
            column={column}
            className="whitespace-nowrap"
            title={tTaskerLocation('MOVING_HISTORY_COUNT')}
          />
        ),
        enableSorting: false,
        cell: ({ row }) => (
          <Typography className="whitespace-nowrap" variant="p">
            {row.original?.movingHistoryCount}
          </Typography>
        ),
      },
    ],
    [searchParams, tTaskerLocation],
  );

  return (
    <div>
      <div className="mb-6 flex justify-between items-center rounded-xl bg-secondary py-4 px-6">
        <div className="grid gap-2">
          <Typography variant="h2">
            {tTaskerLocation('TASKER_LOCATION_DETECTED')}
          </Typography>
          <Breadcrumbs />
        </div>
      </div>
      <BTaskeeTable
        total={totalTasker || 0}
        data={taskers || []}
        columns={columns}
        isShowClearButton
        filterDate={{
          name: 'rangeDate',
          defaultValue: {
            from: momentTz(filterValues?.rangeDate?.from).toDate(),
            to: momentTz(filterValues?.rangeDate?.to).toDate(),
          },
        }}
        localeAddress="tasker-location-detected"
        componentAfterFilter={
          <div className="flex items-center w-full">
            <Input
              className="max-w-[200px] h-8"
              placeholder={tTaskerLocation('ENTER_RADIUS')}
              onKeyDown={event => {
                if (event.key === 'Enter') {
                  setSearchParams(params => {
                    params.set(
                      'radius',
                      (event.target as HTMLInputElement).value,
                    );

                    return params;
                  });
                }
              }}
            />
            <div className="w-3/5 ml-2">
              <AutoComplete
                selectedValue={selectedValue}
                searchValue={searchParams.get('address') || ''}
                onSelectedValueChange={handleSelectedValueChange}
                onSearchValueChange={value => {
                  setSearchParams(params => {
                    params.set('address', value);
                    return params;
                  });
                }}
                items={transformedLocations}
                placeholder={tTaskerLocation('SEARCH_FOR_LOCATION')}
              />
            </div>
          </div>
        }
        search={{
          name: 'searchText',
          defaultValue: searchText || '',
          placeholder: tTaskerLocation('SEARCH_BY_NAME_OR_PHONE'),
        }}
        pagination={getPageSizeAndPageIndex({
          total: totalTasker || 0,
          pageSize: Number(searchParams.get('pageSize') || 0),
          pageIndex: Number(searchParams.get('pageIndex') || 0),
        })}
      />
    </div>
  );
}
