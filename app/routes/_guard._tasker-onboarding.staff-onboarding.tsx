import { Outlet } from '@remix-run/react';
import { PERMISSIONS, ROUTE_NAME } from 'btaskee-constants';
import { useLoaderDataSafely } from 'btaskee-hooks';
import { BreadcrumbsLink, toast } from 'btaskee-ui';
import { useEffect } from 'react';
import { hocLoader } from '~/hoc/remix';
import { willBecomeStaffOnboardingProfileLoader } from '~/hooks/useGetStaffOnboardingProfile';

export const handle = {
  breadcrumb: () => {
    return (
      <BreadcrumbsLink to={ROUTE_NAME.STAFF_ONBOARDING} label="STAFF_PROFILE" />
    );
  },
  i18n: 'tasker-profile',
};

export const loader = hocLoader(willBecomeStaffOnboardingProfileLoader, [
  PERMISSIONS.READ_VERIFYING_STEP_ON_SUPPLIER_AND_STAFF_ONBOARDING,
  PERMISSIONS.READ_APPROVED_STEP_ON_SUPPLIER_AND_STAFF_ONBOARDING,
  PERMISSIONS.READ_ELIMINATED_STEP_ON_SUPPLIER_AND_STAFF_ONBOARDING,
  PERMISSIONS.READ_REJECTED_STEP_ON_SUPPLIER_AND_STAFF_ONBOARDING,
]);

export default function StaffOnboardingRoot() {
  const { error: loaderError, ...resetLoaderData } =
    useLoaderDataSafely<typeof loader>();

  useEffect(() => {
    if (loaderError) toast({ description: loaderError });
  }, [loaderError]);

  return <Outlet context={resetLoaderData} />;
}
