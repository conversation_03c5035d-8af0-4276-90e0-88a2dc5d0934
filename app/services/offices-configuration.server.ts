import { momentTz } from 'btaskee-utils';
import type { PipelineStage } from 'mongo-connection';
import { getModels } from 'schemas';

import { newRecordCommonField } from './constants.server';

export type CommonGettingOfficesFromSettingSystem = {
    isoCode: IsoCode;
    filter?: {
        search: string;
        statuses: string;
    };
};

const getAggregateMatchers = ({
    filter,
}: Pick<CommonGettingOfficesFromSettingSystem, 'filter'>) => {
    const aggregateMatchers: Array<PipelineStage> = [
        {
            $unwind: {
                path: '$submitionAddressForTasker',
            },
        },
        {
            $project: {
                name: '$submitionAddressForTasker.name',
                address: '$submitionAddressForTasker.address',
                status: '$submitionAddressForTasker.status',
                workingStartDate: '$submitionAddressForTasker.workingStartDate',
                workingEndDate: '$submitionAddressForTasker.workingEndDate',
                createdAt: '$submitionAddressForTasker.createdAt',
                city: '$submitionAddressForTasker.city',
            },
        },
    ];
    const matcherByMatchCondition: PipelineStage.Match['$match'] = {
        ...(filter?.search
            ? {
                  $or: [
                      { name: { $regex: filter.search, $options: 'i' } },
                      { address: { $regex: filter.search, $options: 'i' } },
                  ],
              }
            : {}),
        ...(filter?.statuses
            ? { status: { $in: filter.statuses.split(',') } }
            : {}),
    };

    if (Object.keys(matcherByMatchCondition).length) {
        aggregateMatchers.push({ $match: matcherByMatchCondition });
    }

    return aggregateMatchers;
};

export async function getTotalOfficesFromSettingSystem({
    isoCode,
    filter,
}: CommonGettingOfficesFromSettingSystem) {
    const settingSystem = await getModels(isoCode).settingSystem.aggregate(
        getAggregateMatchers(filter ? { filter } : {}),
    );

    return Array.isArray(settingSystem) ? settingSystem.length : 0;
}

export async function getListOfficesFromSettingSystem({
    isoCode,
    filter,
    skip,
    limit,
}: CommonGettingOfficesFromSettingSystem & {
    skip: PipelineStage.Skip['$skip'];
    limit: PipelineStage.Limit['$limit'];
}): Promise<
    Array<
        Pick<
            SettingSystem['submitionAddressForTasker'][0],
            | 'address'
            | 'name'
            | 'workingStartDate'
            | 'workingEndDate'
            | 'status'
            | 'createdAt'
            | 'createdBy'
            | 'city'
            | 'phoneNumber'
            | 'workingDays'
            | 'offDays'
        >
    >
> {
    const settingSystem = await getModels(isoCode).settingSystem.aggregate([
        ...getAggregateMatchers(filter ? { filter } : {}),
        { $skip: skip },
        { $limit: limit },
    ]);

    return Array.isArray(settingSystem) ? settingSystem : [];
}

export async function getOfficeDetailByIndexInSetting({
    isoCode,
    indexOffice,
}: {
    isoCode: IsoCode;
    indexOffice: number;
}) {
    const settingSystem = await getModels(isoCode)
        .settingSystem?.findOne({})
        .lean();

    if (!settingSystem?.submitionAddressForTasker?.[indexOffice]) {
        throw new Error('SUBMISSION_ADDRESS_NOT_FOUND');
    }

    return settingSystem.submitionAddressForTasker[indexOffice];
}

export async function createOffice({
    isoCode,
    username,
    name,
    status,
    phoneNumber,
    city,
    address,
    workingStartDate,
    workingEndDate,
    workingDays,
    offDays,
    offDaysForOffice,
}: {
    isoCode: IsoCode;
    username: Users['username'];
} & Pick<
    SettingSystem['submitionAddressForTasker'][0],
    | 'name'
    | 'address'
    | 'workingStartDate'
    | 'workingEndDate'
    | 'status'
    | 'city'
    | 'phoneNumber'
    | 'workingDays'
    | 'offDays'
> & {
        offDaysForOffice: Array<
            Omit<OffDay, '_id' | 'createdAt' | 'createdBy'>
        >;
    }) {
    await getModels(isoCode).settingSystem.findOneAndUpdate(
        {},
        {
            $push: {
                submitionAddressForTasker: {
                    name,
                    address,
                    status,
                    city,
                    phoneNumber,
                    workingStartDate,
                    workingEndDate,
                    createdAt: momentTz().toDate(),
                    createdBy: username,
                    workingDays,
                    offDays: [
                        ...(offDays ?? []),
                        ...(offDaysForOffice?.map(offDays => ({
                            _id: newRecordCommonField()._id,
                            createdBy: username,
                            createdAt: newRecordCommonField().createdAt,
                            ...offDays,
                        })) || []),
                    ],
                },
            },
        },
    );

    return {
        msg: 'CREATE_OFFICE_SUCCESSFULLY',
    };
}

export async function deleteOfficeByIndex({
    isoCode,
    officeIndex,
}: {
    isoCode: IsoCode;
    officeIndex: number;
}) {
    const settingSystemFound = await getModels(isoCode)
        .settingSystem.findOne({}, { submitionAddressForTasker: 1 })
        .lean();

    await getModels(isoCode).settingSystem.findOneAndUpdate(
        {},
        {
            $set: {
                submitionAddressForTasker:
                    settingSystemFound?.submitionAddressForTasker.filter(
                        (...args) => args[1] !== officeIndex,
                    ),
            },
        },
    );

    return {
        msg: 'DELETE_OFFICE_SUCCESSFULLY',
    };
}

export async function updateOffice({
    isoCode,
    officeIndex,
    updateInfo,
}: {
    isoCode: IsoCode;
    officeIndex: number;
    updateInfo: Pick<
        SettingSystem['submitionAddressForTasker'][0],
        | 'address'
        | 'name'
        | 'workingStartDate'
        | 'workingEndDate'
        | 'status'
        | 'createdBy'
        | 'phoneNumber'
        | 'city'
        | 'workingDays'
        | 'offDays'
    > & {
        offDaysForOffice: Array<
            Omit<OffDay, '_id' | 'createdAt' | 'createdBy'>
        >;
    };
}) {
    if (Object.keys(updateInfo)?.length) {
        await getModels(isoCode).settingSystem.findOneAndUpdate(
            {},
            {
                $set: {
                    ...(updateInfo.address
                        ? {
                              [`submitionAddressForTasker.${officeIndex}.address`]:
                                  updateInfo.address,
                          }
                        : {}),
                    ...(updateInfo.name
                        ? {
                              [`submitionAddressForTasker.${officeIndex}.name`]:
                                  updateInfo.name,
                          }
                        : {}),
                    ...(updateInfo.workingStartDate
                        ? {
                              [`submitionAddressForTasker.${officeIndex}.workingStartDate`]:
                                  updateInfo.workingStartDate,
                          }
                        : {}),
                    ...(updateInfo.workingEndDate
                        ? {
                              [`submitionAddressForTasker.${officeIndex}.workingEndDate`]:
                                  updateInfo.workingEndDate,
                          }
                        : {}),
                    ...(updateInfo.createdBy
                        ? {
                              [`submitionAddressForTasker.${officeIndex}.createdBy`]:
                                  updateInfo.createdBy,
                          }
                        : {}),
                    ...(updateInfo.status
                        ? {
                              [`submitionAddressForTasker.${officeIndex}.status`]:
                                  updateInfo.status,
                          }
                        : {}),
                    ...(updateInfo.city
                        ? {
                              [`submitionAddressForTasker.${officeIndex}.city`]:
                                  updateInfo.city,
                          }
                        : {}),
                    ...(updateInfo.phoneNumber
                        ? {
                              [`submitionAddressForTasker.${officeIndex}.phoneNumber`]:
                                  updateInfo.phoneNumber,
                          }
                        : {}),
                    ...(updateInfo.workingDays
                        ? {
                              [`submitionAddressForTasker.${officeIndex}.workingDays`]:
                                  updateInfo.workingDays,
                          }
                        : {}),
                    ...(updateInfo.offDays
                        ? {
                              [`submitionAddressForTasker.${officeIndex}.offDays`]:
                                  updateInfo.offDays,
                          }
                        : {}),
                    ...(updateInfo.offDaysForOffice
                        ? {
                              [`submitionAddressForTasker.${officeIndex}.offDays`]:
                                  [
                                      ...(updateInfo.offDays || []),
                                      ...updateInfo.offDaysForOffice.map(
                                          offDay => ({
                                              _id: newRecordCommonField()._id,
                                              createdBy: updateInfo.createdBy,
                                              createdAt:
                                                  newRecordCommonField()
                                                      .createdAt,
                                              ...offDay,
                                              isActive: true,
                                              isHoliday: false,
                                          }),
                                      ),
                                  ],
                          }
                        : {}),
                    [`submitionAddressForTasker.${officeIndex}.createdAt`]:
                        momentTz().toDate(),
                },
            },
        );
    }

    return {
        msg: 'UPDATE_OFFICE_SUCCESSFULLY',
    };
}
