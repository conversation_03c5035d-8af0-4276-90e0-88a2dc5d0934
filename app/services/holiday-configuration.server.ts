import type { PipelineStage } from 'mongo-connection';
import { getModels } from 'schemas';

import { newRecordCommonField } from './constants.server';

export async function getHolidaysFromSettingSystem({
    isoCode,
    search,
}: {
    isoCode: IsoCode;
    search?: string;
}) {
    const pipeline: PipelineStage[] = [
        { $unwind: '$submitionAddressForTasker' },
        { $unwind: '$submitionAddressForTasker.offDays' },
        {
            $match: {
                'submitionAddressForTasker.offDays.isHoliday': true,
            },
        },
    ];

    if (search) {
        pipeline.push({
            $match: {
                'submitionAddressForTasker.offDays.reason': {
                    $regex: search,
                    $options: 'i',
                },
            },
        });
    }

    pipeline.push(
        {
            $group: {
                _id: '$submitionAddressForTasker.offDays.reason',
                holiday: { $first: '$submitionAddressForTasker.offDays' },
            },
        },
        {
            $replaceRoot: { newRoot: '$holiday' },
        },
        {
            $sort: {
                createdAt: -1,
            },
        },
        {
            $project: {
                reason: 1,
                from: 1,
                to: 1,
                createdAt: 1,
                createdBy: 1,
                isActive: 1,
                isHoliday: 1,
            },
        },
    );

    const holidays = await getModels(isoCode).settingSystem.aggregate(pipeline);

    return holidays;
}

export async function createHoliday({
    isoCode,
    username,
    holidayName,
    holidayDateRange,
}: {
    isoCode: IsoCode;
    username: Users['username'];
    holidayName: OffDay['reason'];
    holidayDateRange: { from: Date; to: Date };
}) {
    // Check if holiday with the same reason already exists and is a holiday
    const existingHoliday = await getModels(isoCode).settingSystem.findOne({
        'submitionAddressForTasker.offDays': {
            $elemMatch: {
                reason: holidayName,
                isHoliday: true,
            },
        },
    });

    if (existingHoliday) {
        throw new Error('HOLIDAY_NAME_ALREADY_EXISTS');
    }

    const newOffDay = {
        _id: newRecordCommonField()._id,
        reason: holidayName,
        from: holidayDateRange.from,
        to: holidayDateRange.to,
        isHoliday: true,
        isActive: false,
        createdAt: newRecordCommonField().createdAt,
        createdBy: username,
    };

    await getModels(isoCode).settingSystem.updateMany(
        {},
        {
            $push: {
                'submitionAddressForTasker.$[].offDays': newOffDay,
            },
        },
    );

    return {
        success: true,
        message: 'HOLIDAY_CREATED_SUCCESSFULLY',
    };
}

export async function updateHoliday({
    isoCode,
    previousHolidayName,
    holidayName,
    holidayDateRange,
}: {
    isoCode: IsoCode;
    previousHolidayName: OffDay['reason'];
    holidayName: OffDay['reason'];
    holidayDateRange: { from: Date; to: Date };
}) {
    // Check if holiday with the new name already exists (excluding the one being updated) and is a holiday
    const existingHoliday = await getModels(isoCode).settingSystem.findOne({
        'submitionAddressForTasker.offDays': {
            $elemMatch: {
                reason: { $eq: holidayName, $ne: previousHolidayName },
                isHoliday: true,
            },
        },
    });

    if (existingHoliday) {
        throw new Error('HOLIDAY_NAME_ALREADY_EXISTS');
    }

    await getModels(isoCode).settingSystem.updateMany(
        {},
        {
            $set: {
                'submitionAddressForTasker.$[].offDays.$[elem].reason':
                    holidayName,
                'submitionAddressForTasker.$[].offDays.$[elem].from':
                    holidayDateRange.from,
                'submitionAddressForTasker.$[].offDays.$[elem].to':
                    holidayDateRange.to,
            },
        },
        {
            arrayFilters: [
                { 'elem.reason': previousHolidayName, 'elem.isHoliday': true },
            ],
        },
    );

    return {
        success: true,
        message: 'HOLIDAY_UPDATED_SUCCESSFULLY',
    };
}

export async function deleteHoliday({
    isoCode,
    holidayName,
}: {
    isoCode: IsoCode;
    holidayName: OffDay['reason'];
}) {
    await getModels(isoCode).settingSystem.updateMany(
        {},
        {
            $pull: {
                'submitionAddressForTasker.$[].offDays': {
                    reason: holidayName,
                    isHoliday: true,
                },
            },
        },
    );

    return {
        success: true,
        message: 'HOLIDAY_DELETED_SUCCESSFULLY',
    };
}
