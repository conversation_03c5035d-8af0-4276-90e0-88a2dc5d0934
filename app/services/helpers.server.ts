import type { IsoCode } from 'btaskee-constants';
import type { PipelineStage } from 'mongoose';
import { getModels } from 'schemas';
import UsersModel from '~/services/model/users.server';
import { getSession } from '~/services/session.server';

export async function getUserId({ request }: { request: Request }) {
    const authSession = await getSession(request.headers.get('cookie'));
    return authSession.get('user')?.userId || '';
}

export async function getUserSession({
    headers,
}: {
    headers: Request['headers'];
}) {
    const authSession = await getSession(headers.get('cookie'));

    // Make sure if not cookie data, redirect login page
    // Just declare for checking by typescript
    const defaultReturnValue = {
        userId: '',
        isSuperUser: false,
        isoCode: '',
        username: '',
        email: '',
    };

    return authSession.get('user') || defaultReturnValue;
}

export async function getCities(isoCode: string) {
    const workingPlaces = await getModels(isoCode)
        .workingPlaces.findOne(
            {
                countryCode: isoCode,
            },
            {
                cities: 1,
            },
        )
        .lean();

    return workingPlaces?.cities?.map(city => city.name) || [];
}

export async function getCitiesByUserId({
    userId,
    isManager,
}: {
    userId: string;
    isManager: boolean;
}) {
    const user = await UsersModel.findOne(
        { _id: userId },
        { cities: 1, isoCode: 1 },
    ).lean<Users>();

    if (!user) {
        throw new Error('USER_NOT_FOUND');
    }

    // Manager can access all cities at country by iso code
    if (isManager) {
        if (!user.isoCode) return [];
        return getCities(user.isoCode);
    }

    return user.cities || [];
}

export async function getLanguageByUserId(userId: string) {
    const user = await UsersModel.findById(userId, {
        language: 1,
    }).lean<Users>();

    if (!user) {
        return 'en';
    }

    // Default language is English
    return user?.language || 'en';
}

export async function getServiceText(isoCode: `${IsoCode}`) {
    const service = await getModels(isoCode)
        .service.find({}, { text: 1, isSubscription: 1 })
        .lean();

    return service || [];
}

export function getSettingCountryByIsoCode({
    isoCode,
    projection,
}: {
    isoCode: string;
    projection: PipelineStage.Project['$project'];
}) {
    return getModels(isoCode)
        .settingCountry.findOne({ isoCode }, projection)
        .lean();
}

export async function getAllOffices({ isoCode }: { isoCode: IsoCode }) {
    const settingSystemFound = await getModels(isoCode).settingSystem.findOne(
        {},
        { submitionAddressForTasker: 1 },
    );

    return settingSystemFound?.submitionAddressForTasker || [];
}

export async function getAllLevelsOfJourney({ isoCode }: { isoCode: IsoCode }) {
    const journeySetting = await getModels(isoCode)
        .journeySetting.find({}, { levels: 1 })
        .lean();

    // Create a Map to store unique levels by name
    const uniqueLevels = new Map();

    journeySetting.forEach(setting => {
        (setting.levels ?? []).forEach(level => {
            if (level && !uniqueLevels.has(level.name)) {
                uniqueLevels.set(level.name, level);
            }
        });
    });

    return Array.from(uniqueLevels.values());
}
