import { momentTz } from 'btaskee-utils';
import type { PipelineStage } from 'mongoose';
import { getModels } from 'schemas';

import { newRecord<PERSON>ommonField } from './constants.server';

interface SpecialCampaignParams {
    skip: PipelineStage.Skip['$skip'];
    limit: PipelineStage.Limit['$limit'];
    sort: PipelineStage.Sort['$sort'];
    filter: {
        search: string;
        type: string;
        status: string;
        rangeDate: { from: Date; to: Date };
    };
    isoCode: IsoCode;
}

function getMatcher({ filter }: Pick<SpecialCampaignParams, 'filter'>) {
    const matcher: PipelineStage.Match['$match'] = {};

    if (filter.search) {
        matcher.$and = [];

        matcher.$and.push({
            name: { $regex: filter.search, $options: 'i' },
        });
    }

    if (filter.rangeDate?.from && filter.rangeDate?.to) {
        if (!matcher.$and) matcher.$and = [];

        matcher.$and.push({
            $or: [
                {
                    updatedAt: {
                        $gte: filter.rangeDate.from,
                        $lte: filter.rangeDate.to,
                    },
                },
                {
                    $and: [
                        { updatedAt: { $exists: false } },
                        {
                            createdAt: {
                                $gte: filter.rangeDate.from,
                                $lte: filter.rangeDate.to,
                            },
                        },
                    ],
                },
            ],
        });
    }

    if (filter.status) {
        matcher.status = { $in: filter.status.split(',') };
    }

    if (filter.type) {
        matcher.type = { $in: filter.type.split(',') };
    }

    return matcher;
}

export function getListSpecialCampaign({
    isoCode,
    filter,
    sort,
    skip,
    limit,
}: SpecialCampaignParams): Promise<
    Pick<
        SpecialCampaign,
        | '_id'
        | 'name'
        | 'startDate'
        | 'endDate'
        | 'type'
        | 'updatedAt'
        | 'updatedBy'
        | 'status'
    >[]
> {
    return getModels(isoCode)
        .taskerSpecialCampaign.aggregate([
            {
                $match: getMatcher({ filter }),
            },
            {
                $addFields: {
                    updatedAt: { $ifNull: ['$updatedAt', '$createdAt'] },
                    updatedBy: { $ifNull: ['$updatedBy', '$createdBy'] },
                },
            },
            {
                $sort: sort ? { ...sort, _id: 1 } : { updatedAt: -1, _id: 1 },
            },
            { $skip: skip },
            { $limit: limit },
            {
                $project: {
                    name: 1,
                    startDate: 1,
                    endDate: 1,
                    type: 1,
                    status: 1,
                    updatedAt: 1,
                    updatedBy: 1,
                },
            },
        ])
        .exec();
}

export function getTotalSpecialCampaign({
    isoCode,
    filter,
}: Pick<SpecialCampaignParams, 'isoCode' | 'filter'>) {
    return getModels(isoCode).taskerSpecialCampaign.countDocuments(
        getMatcher({ filter }),
    );
}

function mapRewards(rewards: SpecialCampaign['rewards']) {
    return rewards.map(reward => ({
        type: reward.type,
        amount: reward.amount,
        applyAccountType: reward.applyAccountType,
        ...(reward.taskerJourneyLevels?.length && {
            taskerJourneyLevels: reward.taskerJourneyLevels,
        }),
        ...(reward.applyForServices?.length && {
            applyForServices: reward.applyForServices,
        }),
        ...(reward.minRateTask && { minRateTask: reward.minRateTask }),
    }));
}

export async function createSpecialCampaign({
    isoCode,
    params,
}: {
    isoCode: IsoCode;
    params: Omit<
        SpecialCampaign,
        '_id' | 'createdAt' | 'updatedAt' | 'changeHistories'
    >;
}) {
    return getModels(isoCode).taskerSpecialCampaign.create({
        ...newRecordCommonField(),
        ...params,
        rewards: mapRewards(params.rewards),
    });
}

export function updateSpecialCampaign({
    isoCode,
    specialCampaignId,
    params,
}: {
    isoCode: IsoCode;
    specialCampaignId: SpecialCampaign['_id'];
    params: Omit<
        SpecialCampaign,
        '_id' | 'createdAt' | 'updatedAt' | 'changeHistories'
    >;
}) {
    const updateOperation: {
        $set: PipelineStage.Set['$set'];
        $unset?: PipelineStage.Set['$set'];
    } = {
        $set: {
            ...params,
            rewards: mapRewards(params.rewards),
            updatedAt: momentTz().toDate(),
        },
    };

    if (!params?.city?.length) {
        updateOperation.$unset = {
            ...(params?.city?.length ? {} : { city: '' }),
        };
    }

    return getModels(isoCode)
        .taskerSpecialCampaign.updateOne(
            { _id: specialCampaignId },
            updateOperation,
        )
        .lean();
}

export function getSpecialCampaignDetail({
    isoCode,
    specialCampaignId,
}: {
    isoCode: IsoCode;
    specialCampaignId: SpecialCampaign['_id'];
}) {
    return getModels(isoCode)
        .taskerSpecialCampaign.findById(specialCampaignId)
        .lean();
}
