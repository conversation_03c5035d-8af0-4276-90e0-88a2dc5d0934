import {
    BTASKEE_LANGUAGE,
    ROUTE_NAME,
    TASKER_PROFILE_IMAGE_STATUS_KEY_FROM_API,
} from 'btaskee-constants';
import { getEnvGoApi, getEnvMailServer, getEnvOriginal } from 'btaskee-dotenv';
import { momentTz } from 'btaskee-utils';
import { getModels } from 'schemas';
import type { EnumIsoCode } from 'schemas';
import type { v4 as uuidv4 } from 'uuid';
import {
    NOTIFICATION_TYPE_FROM_BACKEND,
    TASKER_ONBOARDING_IMAGE_KEY,
    newRecordCommonField,
} from '~/services/constants.server';
import { sendEmail } from '~/third-party/mail.server';

import getRestApiByMultiRegion, { API_KEY } from './api-proxy/index.server';

export interface FetchSendNotificationParams {
    userIds: Array<string>;
    message: {
        title: TextLang;
        body: TextLang;
    };
    payload?: {
        type?: number;
        taskId?: string;
        navigateTo?: string;
        notificationId?: string;
    };
}

async function fetchAPI<T>(url: string, params: T, isoCode: string) {
    const data = await fetch(`${getEnvGoApi().GO_REST_API_URI}/${url}`, {
        method: 'POST',
        headers: {
            'content-type': 'application/json',
            accessKey: getEnvGoApi().ACCESS_KEY,
        },
        // body data type must match "Content-Type" header
        body: JSON.stringify({ ...params, isoCode }),
    })
        .then(async res => {
            // Fetch successful
            if (res?.status === 200) {
                return res.json();
            }
            const error = await res.text();

            try {
                // Fetch error and server return JSON
                return Promise.reject(JSON.parse(error));
            } catch (err) {
                // Fetch error but server can not return JSON
                return Promise.reject(
                    new Error(`${res.status} ${res.statusText}`),
                );
            }
        })
        .then(response => Promise.resolve(response))
        .catch(error => {
            const errorMessage =
                error?.error?.errorText?.en ||
                error.error?.message ||
                error.error?.response?.data?.error?.message ||
                error.error?.response?.data?.message ||
                error.error?.response?.content ||
                error.error?.code ||
                error.error?.error?.message ||
                error.error?.error?.code ||
                error.error ||
                error.data ||
                error.error?.data ||
                JSON.stringify(error) ||
                '';

            throw new Error(
                `API Error with ${url}: [${momentTz().format('HHmmDDMMYY')}] ${errorMessage}`,
            );
        });

    return data;
}

const publishNotification = async ({
    title,
    body,
    data,
    isoCode,
}: MustBeAny) => {
    //TODO: MustBeAny needs to be replaced with the correct type
    const { taskId, userIds, ...params } = data;

    const paramsPublishNotification: MustBeAny = {
        //TODO: MustBeAny needs to be replaced with the correct type
        notificationType: data.notificationType,
        userIds: data.userIds,
        data: { title, body, ...params },
    };

    if (taskId) paramsPublishNotification.taskId = taskId;

    const apiUrl = getRestApiByMultiRegion({
        apiKey: API_KEY.WEBSOCKET_SEND_MESSAGE_NOTIFICATION,
        isoCode,
    });

    return fetchAPI(apiUrl, paramsPublishNotification, isoCode);
};

const fetchSendNotification = async ({
    userIds,
    message,
    payload = {},
    isoCode,
}: FetchSendNotificationParams & { isoCode: EnumIsoCode }) => {
    const arrUsers = await getModels(isoCode)
        .users.find({ _id: { $in: userIds } }, { language: 1 })
        .lean();

    const foundUsers =
        arrUsers?.map(user => ({
            userId: user?._id,
            language: user?.language,
        })) ?? [];

    const apiUrl = getRestApiByMultiRegion({
        apiKey: API_KEY.PUSH_NOTIFICATION,
        isoCode,
    });

    return fetchAPI(
        apiUrl,
        {
            userIds: foundUsers,
            payload,
            ...message,
        },
        isoCode,
    );
};

const sendNotification = async ({
    isSendNotificationId,
    locale = BTASKEE_LANGUAGE.EN,
    userIds,
    message,
    payload,
    publishData,
    notificationType,
    newDataInsertDb,
    isoCode,
}: FetchSendNotificationParams & {
    isSendNotificationId?: boolean;
    locale?: BtaskeeLanguage;
    publishData?: MustBeAny;
    notificationType?: number;
    newDataInsertDb?: MustBeAny;
    isoCode: EnumIsoCode;
}) => {
    const titleNotification = message.title?.[locale];
    const descriptionNotification = message.body?.[locale];

    if (!titleNotification || !descriptionNotification) {
        throw new Error(
            `Can not get notification message in ${locale} language`,
        );
    }

    const dataInsertDb = {
        type: notificationType || NOTIFICATION_TYPE_FROM_BACKEND.DEFAULT,
        title: titleNotification,
        description: descriptionNotification,
        ...newDataInsertDb,
    };

    // if have isSendNotificationId, always send for 1 user
    if (isSendNotificationId) {
        const notification = await getModels(isoCode).notification.create({
            _id: newRecordCommonField()._id,
            createdAt: newRecordCommonField().createdAt,
            userId: userIds[0],
            ...dataInsertDb,
            ...(publishData?.data?.name
                ? { data: { name: publishData.data.name } }
                : {}),
        });

        if (publishData) {
            await publishNotification({
                data: {
                    notificationType: '10',
                    userIds,
                    ...publishData,
                    notificationId: notification._id,
                },
                title: message.title[locale || 'en'],
                body: message.body[locale || 'en'],
                isoCode,
            });
        }
        const apiUrl = getRestApiByMultiRegion({
            apiKey: API_KEY.PUSH_NOTIFICATION,
            isoCode,
        });

        return fetchAPI(
            apiUrl,
            {
                userIds: [{ userId: userIds[0], language: locale || 'en' }],
                ...message,
                payload: {
                    ...payload,
                    notificationId: notification._id,
                },
            },
            isoCode,
        );
    }

    userIds.forEach(user => {
        getModels(isoCode).notification.create({
            _id: newRecordCommonField()._id,
            createdAt: newRecordCommonField().createdAt,
            userId: user,
            ...dataInsertDb,
        });
    });

    return fetchSendNotification({
        userIds,
        message,
        payload: payload ?? {},
        isoCode,
    });
};

export const getImageStatusKeyByImageFieldInTaskerOnboarding = ({
    imageField,
}: {
    imageField: string;
}) => {
    switch (imageField) {
        case TASKER_ONBOARDING_IMAGE_KEY.IDENTITY_CARD:
            return TASKER_PROFILE_IMAGE_STATUS_KEY_FROM_API.IDENTITY_CARD;
        case TASKER_ONBOARDING_IMAGE_KEY.HOUSEHOLD:
            return TASKER_PROFILE_IMAGE_STATUS_KEY_FROM_API.HOUSEHOLD;
        case TASKER_ONBOARDING_IMAGE_KEY.CRIMINAL_RECORD:
            return TASKER_PROFILE_IMAGE_STATUS_KEY_FROM_API.CRIMINAL_RECORD;
        case TASKER_ONBOARDING_IMAGE_KEY.MASSAGE_PRACTICE_CERTIFICATE:
            return TASKER_PROFILE_IMAGE_STATUS_KEY_FROM_API.MASSAGE_PRACTICE_CERTIFICATE;
        case TASKER_ONBOARDING_IMAGE_KEY.PORTRAIT:
            return TASKER_PROFILE_IMAGE_STATUS_KEY_FROM_API.PORTRAIT;
        case TASKER_ONBOARDING_IMAGE_KEY.PASSPORT:
            return TASKER_PROFILE_IMAGE_STATUS_KEY_FROM_API.PASSPORT;
        case TASKER_ONBOARDING_IMAGE_KEY.HOUSE_ELECTRIC_BILL:
            return TASKER_PROFILE_IMAGE_STATUS_KEY_FROM_API.HOUSE_ELECTRIC_BILL;
        case TASKER_ONBOARDING_IMAGE_KEY.WORK_PERMIT:
            return TASKER_PROFILE_IMAGE_STATUS_KEY_FROM_API.WORK_PERMIT;
        default:
            return '';
    }
};

export async function sendSetupPasswordEmailForUser({
    resetToken,
    email,
    username,
    name,
}: { resetToken: ReturnType<typeof uuidv4> } & Pick<
    Users,
    'email' | 'username' | 'name'
>) {
    await sendEmail({
        to: email,
        from: getEnvMailServer().MAIL_SERVER_USERNAME,
        subject: 'Chào mừng bạn đã được thêm vào hệ thống bTaskee!',
        html: `
    <!DOCTYPE html>
    <html lang="vi">
    <head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Kích Hoạt Tài Khoản bTaskee</title>
    <style>
        body {
        font-family: Arial, sans-serif;
        background-color: #f4f4f4;
        padding: 20px;
        }
        .container {
        max-width: 600px;
        margin: 0 auto;
        background-color: #fff;
        padding: 20px;
        border-radius: 8px;
        box-shadow: 0 0 10px rgba(0, 0, 0, 0.1);
        }
        .button {
        display: inline-block;
        padding: 10px 20px;
        background-color: #28a745;
        color: #fff !important;
        text-decoration: none;
        border-radius: 5px;
        margin-top: 10px;
        }
        .footer {
        margin-top: 20px;
        font-size: 0.9em;
        color: #777;
        text-align: center;
        }
    </style>
    </head>
    <body>
    <div class="container">
        <p>Chào <strong>${name}</strong>,</p>

        <p>Chúng tôi rất vui mừng thông báo rằng bạn đã được thêm vào hệ thống bTaskee. 
        Để bắt đầu, vui lòng truy cập vào liên kết dưới đây để thiết lập mật khẩu và kích hoạt tài khoản của bạn:</p>

        <p>Tên đăng nhập: <strong>${username}</strong></p>

        <a href=${getEnvOriginal().ORIGINAL_DOMAIN}${ROUTE_NAME.RESET_PASSWORD}/${resetToken} class="button">Thiết Lập Mật Khẩu</a>

        <p>Sau khi thiết lập mật khẩu, bạn có thể đăng nhập vào hệ thống bTaskee bằng thông tin của mình.</p>

        <p>Nếu bạn có bất kỳ câu hỏi nào, xin vui lòng liên hệ với chúng tôi qua email hoặc qua đường dây hỗ trợ.</p>

        <p>Chúng tôi hy vọng hệ thống sẽ giúp bạn quản lý công việc hiệu quả hơn.</p>

        <p>Trân trọng,</p>
        <p><strong>Đội ngũ bTaskee</strong></p>

        <div class="footer">
        <p>Đây là email tự động, vui lòng không trả lời.</p>
        </div>
    </div>
    </body>
    </html>
`,
    });
}

export {
    fetchAPI,
    fetchSendNotification,
    publishNotification,
    sendNotification,
};
