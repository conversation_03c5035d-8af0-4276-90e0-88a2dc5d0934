import { CODE_TYPE } from 'btaskee-constants';
import { type PipelineStage } from 'mongoose';
import { getCollectionNameByIsoCode, getModels } from 'schemas';

interface HistoryBRewardProps {
    skip: PipelineStage.Skip['$skip'];
    limit: PipelineStage.Limit['$limit'];
    sort: PipelineStage.Sort['$sort'];
    filter: {
        search: string;
        rangeDate: { from: Date; to: Date };
        city: string;
        codeType: string;
    };
    isoCode: string;
}
/**
 * @documentation
 * This function will $lookup to users (big collection) and taskerPointTransaction
 * which can impact on performance if users grows larger.
 */
export function getHistoryBReward({
    skip,
    limit,
    sort,
    filter,
    isoCode,
}: HistoryBRewardProps) {
    const query: PipelineStage.Match['$match'] = {};

    if (filter.search) {
        query.$or = [
            { 'title.en': { $regex: filter.search, $options: 'i' } },
            { 'tasker.phone': { $regex: filter.search, $options: 'i' } },
            { promotionCode: { $regex: filter.search, $options: 'i' } },
        ];
    }

    if (filter.rangeDate.from && filter.rangeDate.to) {
        query.usedAt = {
            $gte: filter.rangeDate.from,
            $lte: filter.rangeDate.to,
        };
    }

    if (filter.city) {
        query['tasker.city'] = { $in: filter.city.split(',') };
    }

    if (filter.codeType) {
        const codeTypeArray = filter.codeType.split(',');
        if (codeTypeArray.includes(CODE_TYPE.JOURNEY)) {
            query['reference.journeySettingId'] = { $exists: true };
        }

        if (
            codeTypeArray.includes(CODE_TYPE.SYSTEM_WITH_PARTNER) ||
            codeTypeArray.includes(CODE_TYPE.SYSTEM)
        ) {
            query.source = { $in: filter.codeType.split(',') };
            query['reference.journeySettingId'] = { $exists: false };
        }
    }

    return getModels(isoCode)
        .taskerGift.aggregate<HistoryBReward>([
            { $match: { used: true } },
            {
                $lookup: {
                    from: getCollectionNameByIsoCode(isoCode).users,
                    localField: 'userId',
                    foreignField: '_id',
                    as: 'tasker',
                },
            },
            {
                $unwind: {
                    path: '$tasker',
                    preserveNullAndEmptyArrays: true,
                },
            },
            {
                $lookup: {
                    from: getCollectionNameByIsoCode(isoCode)
                        .taskerPointTransaction,
                    localField: 'pointTransactionId',
                    foreignField: '_id',
                    as: 'pointTransaction',
                },
            },
            {
                $unwind: {
                    path: '$pointTransaction',
                    preserveNullAndEmptyArrays: true,
                },
            },
            {
                $project: {
                    tasker: {
                        _id: 1,
                        name: 1,
                        phone: 1,
                        city: {
                            $arrayElemAt: ['$tasker.workingPlaces.city', 0],
                        },
                    },
                    title: 1,
                    promotionCode: 1,
                    responsiblePerson: 1,
                    pointTransaction: 1,
                    usedAt: 1,
                    createdAt: 1,
                    expired: 1,
                    reference: 1,
                    source: 1,
                },
            },
            { $match: query },
            { $sort: { ...sort, _id: 1 } },
            { $skip: skip },
            { $limit: limit },
        ])
        .exec();
}

/**
 * @documentation
 * This function will $lookup to users (big collection) and taskerPointTransaction
 * which can impact on performance if users grows larger.
 * Note: refactor that remove $project since just count records by $count
 */
export async function getTotalHistoryBReward({
    filter,
    isoCode,
}: Pick<HistoryBRewardProps, 'filter' | 'isoCode'>) {
    const query: PipelineStage.Match['$match'] = {};

    if (filter.search) {
        query.$or = [
            { 'title.en': { $regex: filter.search, $options: 'i' } },
            { 'tasker.phone': { $regex: filter.search, $options: 'i' } },
            { promotionCode: { $regex: filter.search, $options: 'i' } },
        ];
    }

    if (filter.rangeDate.from && filter.rangeDate.to) {
        query.createdAt = {
            $gte: filter.rangeDate.from,
            $lte: filter.rangeDate.to,
        };
    }

    if (filter.city) {
        query['tasker.city'] = { $in: filter.city.split(',') };
    }

    if (filter.codeType) {
        const codeTypeArray = filter.codeType.split(',');
        if (codeTypeArray.includes(CODE_TYPE.JOURNEY)) {
            query['reference.journeySettingId'] = { $exists: true };
        }

        if (
            codeTypeArray.includes(CODE_TYPE.SYSTEM_WITH_PARTNER) ||
            codeTypeArray.includes(CODE_TYPE.SYSTEM)
        ) {
            query.source = { $in: filter.codeType.split(',') };
            query['reference.journeySettingId'] = { $exists: false };
        }
    }

    const result = await getModels(isoCode)
        .taskerGift.aggregate<HistoryBReward & { total: number }>([
            {
                $match: { used: true },
            },
            {
                $lookup: {
                    from: getCollectionNameByIsoCode(isoCode).users,
                    localField: 'userId',
                    foreignField: '_id',
                    as: 'tasker',
                },
            },
            {
                $unwind: {
                    path: '$tasker',
                    preserveNullAndEmptyArrays: true,
                },
            },
            {
                $lookup: {
                    from: getCollectionNameByIsoCode(isoCode)
                        .taskerPointTransaction,
                    localField: 'pointTransactionId',
                    foreignField: '_id',
                    as: 'pointTransaction',
                },
            },
            {
                $unwind: {
                    path: '$pointTransaction',
                    preserveNullAndEmptyArrays: true,
                },
            },
            {
                $project: {
                    tasker: {
                        _id: 1,
                        name: 1,
                        phone: 1,
                        city: {
                            $arrayElemAt: ['$tasker.workingPlaces.city', 0],
                        },
                    },
                    title: 1,
                    promotionCode: 1,
                    responsiblePerson: 1,
                    pointTransaction: 1,
                    usedAt: 1,
                    createdAt: 1,
                    expired: 1,
                    reference: 1,
                    source: 1,
                },
            },
            { $match: query },
            { $count: 'total' },
        ])
        .exec();

    return result.length > 0 ? result[0].total : 0;
}
