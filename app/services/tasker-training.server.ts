import type {
    COURSE_COMPLETION_IN_TRAINING,
    COURSE_CURRENT_STATUS_FROM_API,
    COURSE_VISIBILITY_IN_TRAINING,
} from 'btaskee-constants';
import {
    KEY_CHANGE_HISTORIES_IN_TRAINING,
    STATUS,
    SUBMISSION_STATUS_IN_TRAINING,
} from 'btaskee-constants';
import { momentTz } from 'btaskee-utils';
import type { PipelineStage } from 'mongoose';
import type { EnumIsoCode } from 'schemas';
import { getCollectionNameByIsoCode, getModels } from 'schemas';
import getRestApiByMultiRegion, {
    API_KEY,
} from '~/services/api-proxy/index.server';
import {
    FILTER_OPTION_IN_QUIZ,
    FILTER_OPTION_IN_QUIZ_COLLECTION,
    USER_STATUS,
    newRecordCommonField,
} from '~/services/constants.server';
import { fetchAPI } from '~/services/utils.server';

export interface QuizList {
    filter: {
        name?: string;
        code?: string;
        updatedAt?: {
            from: Date;
            to: Date;
        };
        filters?: {
            image?: string;
            randomAnswer?: string;
        };
    };
    isoCode: string;
    sort?: PipelineStage.Sort['$sort'];
    skip?: PipelineStage.Skip['$skip'];
    limit?: PipelineStage.Limit['$limit'];
}

export interface ListTrainingHistory {
    isoCode: EnumIsoCode;
    taskerId: Users['_id'];
    filter: {
        rangeDate?: { from: Date; to: Date };
        search?: string;
        services?: string[];
        visibilities?: string[];
        completions?: string[];
        statuses?: string[];
    };
    sort: PipelineStage.Sort['$sort'];
    pageSize: number;
    pageIndex: number;
}

export interface CourseList {
    filter: {
        search: string;
        status: string;
        service: string;
        type: string;
        rangeDate?: {
            from: Date;
            to: Date;
        };
    };
    isoCode: string;
    sort: PipelineStage.Sort['$sort'];
    skip: PipelineStage.Skip['$skip'];
    limit: PipelineStage.Limit['$limit'];
    projection: PipelineStage.Project['$project'];
}

function getMatcher({
    filter,
}: {
    filter?: {
        name?: string;
        code?: string;
        updatedAt?: { from: Date; to: Date };
    };
}) {
    const matcher: PipelineStage.Match['$match'] = {};

    if (filter) {
        const { name, code, updatedAt } = filter;

        if (name || code) {
            const searchBy = [];

            if (name) {
                searchBy.push({ title: { $regex: name, $options: 'i' } });
            }
            if (code) {
                searchBy.push({ code: { $regex: name, $options: 'i' } });
            }

            matcher.$and = [{ $or: searchBy }];
        }

        if (updatedAt) {
            if (!matcher.$and) matcher.$and = [];

            matcher.$and.push({
                $or: [
                    {
                        updatedAt: {
                            $gte: updatedAt.from,
                            $lte: updatedAt.to,
                        },
                    },
                    {
                        $and: [
                            { updatedAt: { $exists: false } },
                            {
                                createdAt: {
                                    $gte: filter.updatedAt?.from,
                                    $lte: filter.updatedAt?.to,
                                },
                            },
                        ],
                    },
                ],
            });
        }
    }

    return matcher;
}

function getQuizMatcher({ filter }: Pick<QuizList, 'filter'>) {
    const matcher = getMatcher({ filter });

    if (filter?.filters?.image) {
        const imageOptions = filter.filters.image.split(',');
        const filterImageBy$Or: PipelineStage.Match['$match']['$or'] = [];

        if (!matcher.$and) matcher.$and = [];

        if (imageOptions.includes(FILTER_OPTION_IN_QUIZ.YES)) {
            filterImageBy$Or.push({ image: { $exists: true } });
        }

        if (imageOptions.includes(FILTER_OPTION_IN_QUIZ.NO)) {
            filterImageBy$Or.push({ image: { $exists: false } });
        }

        if (filterImageBy$Or.length) {
            matcher.$and.push({ $or: filterImageBy$Or });
        }
    }

    if (filter?.filters?.randomAnswer) {
        const randomAnswerOptions = filter.filters.randomAnswer.split(',');
        const filterRandomAnswerBy$Or: PipelineStage.Match['$match']['$or'] =
            [];

        if (!matcher.$and) matcher.$and = [];

        if (randomAnswerOptions.includes(FILTER_OPTION_IN_QUIZ.YES)) {
            filterRandomAnswerBy$Or.push({ isRandomAnswer: true });
        }

        if (randomAnswerOptions.includes(FILTER_OPTION_IN_QUIZ.NO)) {
            filterRandomAnswerBy$Or.push({ isRandomAnswer: false });
            filterRandomAnswerBy$Or.push({
                isRandomAnswer: { $exists: false },
            });
        }

        if (filterRandomAnswerBy$Or.length) {
            matcher.$and.push({ $or: filterRandomAnswerBy$Or });
        }
    }

    return matcher;
}

function getCourseMatcher({ filter }: Pick<CourseList, 'filter'>) {
    const matcher: PipelineStage.Match['$match'] = {};

    if (filter.search) {
        matcher.$and = [];

        matcher.$and.push({
            $or: [
                { title: { $regex: filter.search, $options: 'i' } },
                { code: { $regex: filter.search, $options: 'i' } },
            ],
        });
    }

    if (filter.rangeDate?.from && filter.rangeDate?.to) {
        if (!matcher.$and) matcher.$and = [];

        matcher.$and.push({
            $or: [
                {
                    updatedAt: {
                        $gte: filter.rangeDate.from,
                        $lte: filter.rangeDate.to,
                    },
                },
                {
                    $and: [
                        { updatedAt: { $exists: false } },
                        {
                            createdAt: {
                                $gte: filter.rangeDate.from,
                                $lte: filter.rangeDate.to,
                            },
                        },
                    ],
                },
            ],
        });
    }

    if (filter.status) {
        matcher.status = { $in: filter.status.split(',') };
    }

    if (filter.type) {
        matcher.type = { $in: filter.type.split(',') };
    }

    if (filter.service) {
        matcher.relatedServices = {
            $elemMatch: {
                _id: { $in: filter.service.split(',') },
            },
        };
    }

    return matcher;
}

export function getTotalQuizzes({
    isoCode,
    filter,
}: Omit<QuizList, 'sort' | 'skip' | 'limit'>) {
    return getModels(isoCode).taskerTrainingQuiz.countDocuments(
        getQuizMatcher({ filter }),
    );
}

/**
 * @description
 * sort: updatedAt just get 1 & -1
 */
export async function getListQuizzes({
    isoCode,
    filter,
    sort,
    skip,
    limit,
}: QuizList) {
    return getModels(isoCode)
        .taskerTrainingQuiz.aggregate([
            {
                $match: getQuizMatcher({ filter }),
            },
            {
                $project: {
                    code: 1,
                    title: 1,
                    image: 1,
                    isRandomAnswer: 1,
                    answers: 1,
                    updatedAt: { $ifNull: ['$updatedAt', '$createdAt'] },
                    updatedByUsername: {
                        $ifNull: ['$updatedByUsername', '$createdByUsername'],
                    },
                },
            },
            {
                $sort: sort
                    ? { ...sort, _id: 1 }
                    : {
                          updatedAt: -1,
                          _id: 1,
                      },
            },
            { $skip: skip || 0 },
            { $limit: limit || 10 },
        ])
        .exec();
}

/**
 * @description
 * sort: updatedAt just get 1 & -1
 */
export async function getListQuizCollections({
    isoCode,
    filter,
    sort,
    skip,
    limit,
}: {
    filter: {
        name?: string;
        code?: string;
        filters?: string;
        updatedAt?: {
            from: Date;
            to: Date;
        };
    };
    isoCode: string;
    sort?: PipelineStage.Sort['$sort'];
    skip?: PipelineStage.Skip['$skip'];
    limit?: PipelineStage.Limit['$limit'];
}) {
    const matcher = getMatcher({ filter });
    const sorter: PipelineStage.Sort['$sort'] = {};

    if (sort?.updatedAt) {
        sorter.updatedAt = sort?.updatedAt;
    }

    if (sort?.code) {
        sorter.code = sort?.code;
    }

    if (sort?.numOfQuizzes) {
        sorter.numOfQuizzes = sort?.numOfQuizzes;
    }

    if (filter?.filters) {
        if (!matcher.$and) matcher.$and = [];

        const filterOptions = filter.filters.split(',');
        const filterBy$Or: PipelineStage.Match['$match']['$or'] = [];

        if (
            filterOptions.includes(FILTER_OPTION_IN_QUIZ_COLLECTION.IMAGE_ONLY)
        ) {
            filterBy$Or.push({ image: { $exists: true } });
        }

        if (
            filterOptions.includes(FILTER_OPTION_IN_QUIZ_COLLECTION.VIDEO_ONLY)
        ) {
            filterBy$Or.push({ video: { $exists: true } });
        }

        if (filterOptions.includes(FILTER_OPTION_IN_QUIZ_COLLECTION.NO_MEDIA)) {
            filterBy$Or.push({
                $and: [
                    { video: { $exists: false } },
                    { image: { $exists: false } },
                ],
            });
        }

        matcher.$and?.push({ $or: filterBy$Or });
    }

    return await getModels(isoCode)
        .taskerTrainingQuizCollection.aggregate<
            QuizCollection & { numOfQuizzes: number }
        >([
            {
                $match: matcher,
            },
            {
                $addFields: {
                    numOfQuizzes: { $size: '$quizzes' },
                    updatedAt: { $ifNull: ['$updatedAt', '$createdAt'] },
                },
            },
            {
                $sort: sort
                    ? { ...sorter, _id: 1 }
                    : {
                          updatedAt: -1,
                      },
            },
            { $skip: skip || 0 },
            { $limit: limit || 10 },
        ])
        .exec();
}

/**
 * @description
 * sort: updatedAt just get 1 & -1
 */
export async function getListCourses({
    isoCode,
    filter,
    sort,
    skip,
    limit,
    projection,
}: {
    filter: {
        search: string;
        status: string;
        service: string;
        type: string;
        rangeDate?: {
            from: Date;
            to: Date;
        };
    };
    isoCode: string;
    sort: PipelineStage.Sort['$sort'];
    skip: PipelineStage.Skip['$skip'];
    limit: PipelineStage.Limit['$limit'];
    projection: PipelineStage.Project['$project'];
}) {
    const courses = await getModels(isoCode)
        .taskerTrainingCourse.aggregate<Course>([
            {
                $match: getCourseMatcher({ filter }),
            },
            {
                $addFields: {
                    updatedAt: { $ifNull: ['$updatedAt', '$createdAt'] },
                },
            },
            {
                $sort: sort ? { ...sort, _id: 1 } : { updatedAt: -1, _id: 1 },
            },
            { $skip: skip || 0 },
            { $limit: limit || 10 },
            {
                $project: projection,
            },
        ])
        .exec();

    return courses;
}

/**
 * Get all courses, optionally excluding a specific course by ID.
 * @param isoCode The ISO code for the region
 * @param projection The fields to include in the result
 * @param courseId Optional ID of the course to exclude from the results
 */
export function getAllCourses({
    isoCode,
    projection,
    courseId,
}: {
    isoCode: IsoCode;
    projection: PipelineStage.Project['$project'];
    courseId?: string;
}) {
    const query: PipelineStage.Match['$match'] = {};

    if (courseId) {
        query._id = { $ne: courseId };
    }

    return getModels(isoCode)
        .taskerTrainingCourse.find(query, projection)
        .lean();
}

export function getTotalQuizCollections({
    isoCode,
    filter,
}: {
    filter: {
        name?: string;
        code?: string;
        filters?: string;
        updatedAt?: {
            from: Date;
            to: Date;
        };
    };
    isoCode: string;
}) {
    const matcher = getMatcher({ filter });

    if (filter?.filters) {
        if (!matcher.$and) matcher.$and = [];

        const filterOptions = filter.filters.split(',');
        const filterBy$Or: PipelineStage.Match['$match']['$or'] = [];

        if (
            filterOptions.includes(FILTER_OPTION_IN_QUIZ_COLLECTION.IMAGE_ONLY)
        ) {
            filterBy$Or.push({ image: { $exists: true } });
        }

        if (
            filterOptions.includes(FILTER_OPTION_IN_QUIZ_COLLECTION.VIDEO_ONLY)
        ) {
            filterBy$Or.push({ video: { $exists: true } });
        }

        if (filterOptions.includes(FILTER_OPTION_IN_QUIZ_COLLECTION.NO_MEDIA)) {
            filterBy$Or.push({
                $and: [
                    { video: { $exists: false } },
                    { image: { $exists: false } },
                ],
            });
        }

        matcher.$and?.push({ $or: filterBy$Or });
    }

    return getModels(isoCode).taskerTrainingQuizCollection.countDocuments(
        matcher,
    );
}

export function getTotalCourses({
    isoCode,
    filter,
}: {
    filter: {
        search: string;
        status: string;
        service: string;
        type: string;
        rangeDate?: {
            from: Date;
            to: Date;
        };
    };
    isoCode: string;
}) {
    return getModels(isoCode).taskerTrainingCourse.countDocuments(
        getCourseMatcher({ filter }),
    );
}

export async function createQuiz({
    isoCode,
    params,
}: {
    isoCode: string;
    params: Omit<Quiz, 'createdAt' | 'updatedAt' | '_id' | 'title'> & {
        name: string;
    };
}) {
    const { name, code, image, ...restQuizInfo } = params;

    const isExistQuiz = await getModels(isoCode).taskerTrainingQuiz.exists({
        code,
    });

    if (isExistQuiz) {
        throw new Error(`Code '${code}' is already exist!`);
    }

    return getModels(isoCode).taskerTrainingQuiz.create({
        ...newRecordCommonField(),
        ...(image ? { image } : {}),
        title: name,
        code,
        ...restQuizInfo,
    });
}

export async function verifyListQuizCode({
    isoCode,
    codes,
}: {
    isoCode: string;
    codes: string[];
}) {
    return getModels(isoCode).taskerTrainingQuiz.find({
        code: { $in: codes },
    });
}

export async function createQuizzes({
    isoCode,
    params,
}: {
    isoCode: string;
    params: Array<
        Omit<Quiz, 'createdAt' | 'updatedAt' | 'title'> & { name: string }
    >;
}) {
    const isExistQuiz = await getModels(isoCode).taskerTrainingQuiz.exists({
        code: { $in: params.map(item => item.code) },
    });

    if (isExistQuiz) {
        throw new Error(`There are quizzes with the exist code in Quiz List.`);
    }

    return await getModels(isoCode).taskerTrainingQuiz.insertMany(
        params.map(item => {
            const { name, image, ...QuizzesInfo } = item;

            return {
                ...newRecordCommonField(),
                ...(image ? { image } : {}),
                title: name,
                ...QuizzesInfo,
            };
        }),
    );
}

export async function createQuizCollection({
    isoCode,
    params,
}: {
    isoCode: string;
    params: Omit<
        QuizCollection,
        'createdAt' | 'updatedAt' | '_id' | 'title'
    > & {
        name: string;
        numberOfDisplayQuizzes: number;
        isRandomQuizzes: boolean;
    };
}) {
    const isExistQuizCollection = await getModels(
        isoCode,
    ).taskerTrainingQuizCollection.exists({
        code: params.code,
    });

    if (isExistQuizCollection) {
        throw new Error(
            `A quiz collection with the code ${params.code} already exists.`,
        );
    }

    const { name, image, video, ...restQuizCollectionInfo } = params;
    return await getModels(isoCode).taskerTrainingQuizCollection.create({
        ...newRecordCommonField(),
        ...(image ? { image } : {}),
        ...(video ? { video } : {}),
        title: name,
        ...restQuizCollectionInfo,
    });
}

async function checkTaskerEligibility({
    isoCode,
    taskerId,
    cities,
    relatedServices,
}: {
    isoCode: IsoCode;
    taskerId: UserApp['_id'];
    cities: Course['cities'];
    relatedServices: Course['relatedServices'];
}) {
    const matchStage: PipelineStage.Match['$match'] = {
        _id: taskerId,
        status: {
            $nin: [
                USER_STATUS.INACTIVE,
                USER_STATUS.LOCKED,
                USER_STATUS.DISABLED,
            ],
        },
    };

    if (cities?.length) {
        matchStage['workingPlaces.city'] = { $in: cities };
    }

    const taskerCondition = await getModels(isoCode).users.aggregate([
        {
            $match: matchStage,
        },
        {
            $lookup: {
                from: getCollectionNameByIsoCode(isoCode).serviceChannel,
                let: { taskerId: '$_id' },
                pipeline: [
                    {
                        $match: {
                            $expr: {
                                $and: [
                                    {
                                        $in: [
                                            '$$taskerId',
                                            { $ifNull: ['$taskerList', []] },
                                        ],
                                    },
                                    {
                                        $in: [
                                            '$serviceId',
                                            relatedServices.map(
                                                service => service._id,
                                            ),
                                        ],
                                    },
                                ],
                            },
                        },
                    },
                ],
                as: 'registeredServices',
            },
        },
        {
            $match: {
                registeredServices: { $ne: [] },
            },
        },
    ]);

    return taskerCondition.length > 0 ? taskerCondition[0] : null;
}

async function processCompletedCourses({
    isoCode,
    params,
    courseId,
}: {
    isoCode: IsoCode;
    params: Omit<Course, 'createdAt' | 'updatedAt' | '_id'>;
    courseId: Course['_id'];
}) {
    const taskerCompletedCourses = await getModels(isoCode)
        .taskerTrainingSubmission.find({
            status: SUBMISSION_STATUS_IN_TRAINING.PASSED,
            'course._id': {
                $in: params.condition?.coursesMustBeCompleted?.courseIds || [],
            },
        })
        .lean();

    if (!taskerCompletedCourses.length) return;

    await Promise.all(
        taskerCompletedCourses.map(async completedCourse => {
            const eligibleTasker = await checkTaskerEligibility({
                isoCode,
                taskerId: completedCourse.taskerId || '',
                cities: params.cities || [],
                relatedServices: params.relatedServices,
            });

            if (eligibleTasker) {
                await getModels(isoCode).taskerTrainingCourseStartDate.create({
                    _id: newRecordCommonField()._id,
                    courseId,
                    taskerId: eligibleTasker._id,
                    startDate: newRecordCommonField().createdAt,
                    ...(params.deadlineIn
                        ? { deadlineIn: params.deadlineIn }
                        : {}),
                });
            }
        }),
    );
}

export async function createCourse({
    isoCode,
    params,
}: {
    isoCode: IsoCode;
    params: Omit<Course, 'createdAt' | 'updatedAt' | '_id'>;
}) {
    const existingCourse = await getModels(
        isoCode,
    ).taskerTrainingCourse.findOne({
        code: params.code,
    });

    if (existingCourse) {
        throw new Error(
            `A course with the code "${params.code}" already exists.`,
        );
    }

    const courseCreated = await getModels(isoCode).taskerTrainingCourse.create({
        ...newRecordCommonField(),
        ...params,
    });

    // New behavior when flag is on
    if (
        params?.condition?.coursesMustBeCompleted?.courseIds?.length &&
        params.status === STATUS.ACTIVE
    ) {
        await processCompletedCourses({
            isoCode,
            params,
            courseId: courseCreated._id,
        });
    }

    return courseCreated;
}

export function getQuizDetail({
    isoCode,
    _id,
}: {
    isoCode: string;
    _id: string;
}) {
    return getModels(isoCode)
        .taskerTrainingQuiz.findOne({
            _id,
        })
        .lean<Quiz>();
}

export async function getQuizCollectionDetail({
    isoCode,
    _id,
}: {
    isoCode: string;
    _id: string;
}) {
    const quizCollectionFound = await getModels(isoCode)
        .taskerTrainingQuizCollection.findById(_id)
        .lean<QuizCollection>();

    if (!quizCollectionFound) {
        throw new Error('QUIZ_COLLECTION_NOT_FOUND');
    }

    const quizIds = quizCollectionFound.quizzes?.map(quiz => quiz?._id);
    const quizzesFound = await getModels(isoCode)
        .taskerTrainingQuiz.find({ _id: { $in: quizIds } })
        .lean<Quiz[]>();

    const quizzesWithOrder = quizCollectionFound.quizzes?.map(quiz => {
        const quizFoundById = quizzesFound.find(
            quizFound => quizFound?._id === quiz?._id,
        );

        return { ...quiz, ...quizFoundById };
    });

    return { ...quizCollectionFound, quizzes: quizzesWithOrder };
}

export function getCourseDetail({
    isoCode,
    _id,
}: {
    isoCode: string;
    _id: string;
}) {
    return getModels(isoCode)
        .taskerTrainingCourse.findOne({
            _id,
        })
        .lean<Course>();
}

export async function updateQuiz({
    isoCode,
    params,
    quizId,
}: {
    isoCode: string;
    params: Partial<Omit<Quiz, 'title'> & { name: string }>;
    quizId: string;
}) {
    const { name, image, code, ...restQuizInfo } = params;

    const isExistQuiz = await getModels(isoCode).taskerTrainingQuiz.exists({
        code,
        _id: { $ne: quizId },
    });

    if (isExistQuiz) {
        throw new Error(`Code '${code}' is already exist!`);
    }

    const updateMethod: {
        $set: PipelineStage.Set['$set'];
        $unset?: PipelineStage.Set['$set'];
    } = {
        $set: {
            updatedAt: momentTz().toDate(),
            title: name,
            code,
            ...restQuizInfo,
        },
    };

    if (image) {
        updateMethod.$set.image = image;
    } else {
        updateMethod.$unset = {
            image: '',
        };
    }

    return getModels(isoCode).taskerTrainingQuiz.updateOne(
        { _id: quizId },
        updateMethod,
    );
}

export async function updateQuizCollection({
    isoCode,
    params,
    _id,
}: {
    isoCode: string;
    params: Partial<Omit<QuizCollection, 'title'> & { name: string }>;
    _id: string;
}) {
    const { name, image, video, code, ...restQuizCollectionInfo } = params;

    const isExistQuizCollection = await getModels(
        isoCode,
    ).taskerTrainingQuizCollection.exists({
        code,
        _id: { $ne: _id },
    });

    if (isExistQuizCollection) {
        throw new Error(
            `A quiz collection with the code ${params.code} already exists.`,
        );
    }

    const oldRecord = await getModels(isoCode)
        .taskerTrainingQuizCollection.findOne({
            _id,
        })
        .lean();

    const timeToCompleteByMinutes = oldRecord?.timeToCompleteByMinutes || 0;

    const updateMethod: {
        $set: PipelineStage.Set['$set'];
        $unset?: PipelineStage.Set['$set'];
    } = {
        $set: {
            updatedAt: momentTz().toDate(),
            title: name,
            code,
            ...restQuizCollectionInfo,
        },
    };

    if (image) {
        updateMethod.$set.image = image;
    } else {
        updateMethod.$unset = {
            image: '',
        };
    }

    if (video) {
        updateMethod.$set.video = video;
    } else {
        if (!updateMethod.$unset) updateMethod.$unset = {};
        updateMethod.$unset.video = '';
    }

    await getModels(isoCode).taskerTrainingQuizCollection.updateOne(
        {
            _id,
        },
        updateMethod,
    );

    if (params.timeToCompleteByMinutes) {
        const courses = await getModels(isoCode)
            .taskerTrainingCourse.find(
                { 'quizCollections._id': _id },
                { timeToCompleteByMinutes: 1, quizCollections: 1 },
            )
            .lean();

        if (courses.length) {
            await Promise.all(
                courses.map(async course => {
                    if (
                        course?.timeToCompleteByMinutes &&
                        params?.timeToCompleteByMinutes
                    ) {
                        const newCourseTime =
                            course.timeToCompleteByMinutes -
                            (timeToCompleteByMinutes || 0) +
                            params.timeToCompleteByMinutes;
                        await getModels(isoCode).taskerTrainingCourse.updateOne(
                            { _id: course._id },
                            {
                                $set: {
                                    timeToCompleteByMinutes: newCourseTime,
                                },
                            },
                        );
                    }
                }),
            );
        }
    }
}

async function updateCourseStartDates({
    isoCode,
    courseId,
    taskerId,
    deadlineIn,
}: {
    isoCode: string;
    courseId: string;
    taskerId: string;
    deadlineIn?: number;
}) {
    const updateOperation = deadlineIn
        ? {
              // If deadlineIn exists, use $set
              $set: {
                  startDate: momentTz().toDate(),
                  deadlineIn,
              },
              $setOnInsert: {
                  _id: newRecordCommonField()._id,
                  courseId,
                  taskerId,
              },
          }
        : {
              // If no deadlineIn, use $set for startDate and $unset for deadlineIn
              $set: {
                  startDate: momentTz().toDate(),
              },
              $unset: {
                  deadlineIn: '',
              },
              $setOnInsert: {
                  _id: newRecordCommonField()._id,
                  courseId,
                  taskerId,
              },
          };

    return getModels(isoCode).taskerTrainingCourseStartDate.findOneAndUpdate(
        {
            courseId,
            taskerId,
        },
        updateOperation,
        { upsert: true, new: true },
    );
}

async function processCompletedCoursesForUpdate({
    isoCode,
    params,
    courseId,
}: {
    isoCode: IsoCode;
    params: Partial<Course>;
    courseId: Course['_id'];
}) {
    const taskerCompletedCourses = await getModels(isoCode)
        .taskerTrainingSubmission.find({
            status: SUBMISSION_STATUS_IN_TRAINING.PASSED,
            'course._id': {
                $in: params.condition?.coursesMustBeCompleted?.courseIds || [],
            },
        })
        .lean();

    if (!taskerCompletedCourses.length) return;

    await Promise.all(
        taskerCompletedCourses.map(async completedCourse => {
            const eligibleTasker = await checkTaskerEligibility({
                isoCode,
                taskerId: completedCourse.taskerId || '',
                cities: params.cities || [],
                relatedServices: params.relatedServices || [],
            });

            if (eligibleTasker) {
                const existingTaskerSubmission = await getModels(isoCode)
                    .taskerTrainingSubmission.findOne({
                        'course._id': courseId,
                        taskerId: eligibleTasker._id,
                    })
                    .lean();

                if (existingTaskerSubmission) {
                    return;
                }

                return updateCourseStartDates({
                    isoCode,
                    courseId,
                    taskerId: eligibleTasker._id,
                    ...(params.deadlineIn
                        ? { deadlineIn: params.deadlineIn }
                        : {}),
                });
            }
        }),
    );
}

export async function updateCourse({
    isoCode,
    params,
    _id,
}: {
    isoCode: IsoCode;
    params: Partial<Course>;
    _id: Course['_id'];
}) {
    const existingCourse = await getModels(
        isoCode,
    ).taskerTrainingCourse.findOne({
        code: params.code,
        _id: { $ne: _id },
    });

    if (existingCourse) {
        throw new Error(
            `A course with the code "${params.code}" already exists.`,
        );
    }

    if (params?.condition?.coursesMustBeCompleted?.courseIds?.length) {
        await processCompletedCoursesForUpdate({
            isoCode,
            params,
            courseId: _id,
        });
    }

    const updateOperation: {
        $set: PipelineStage.Set['$set'];
        $unset?: PipelineStage.Set['$set'];
    } = {
        $set: {
            updatedAt: momentTz().toDate(),
            ...params,
        },
    };

    if (!params?.condition || !params?.deadlineIn || !params?.cities) {
        updateOperation.$unset = {
            ...(params?.condition ? {} : { condition: '' }),
            ...(params?.deadlineIn ? {} : { deadlineIn: '' }),
            ...(params?.cities ? {} : { cities: '' }),
        };
    }

    return getModels(isoCode)
        .taskerTrainingCourse.updateOne({ _id }, updateOperation)
        .lean();
}

export async function getListQuizDetailSortByOrder({
    isoCode,
    quizzes,
}: {
    isoCode: string;
    quizzes: QuizCollection['quizzes'];
}) {
    const quizIds = quizzes.map(quiz => quiz._id);

    // Fetch quiz details from MongoDB
    const quizDetails = await getModels(isoCode)
        .taskerTrainingQuiz.find({ _id: { $in: quizIds } })
        .lean();

    // Create a mapping of _id to order
    const orderMap = quizzes.reduce(
        (acc, quiz) => {
            acc[quiz._id] = quiz.order;
            return acc;
        },
        {} as Record<string, number>,
    );

    // Sort quiz details based on the order
    quizDetails.sort((a, b) => orderMap[a._id] - orderMap[b._id]);

    return quizDetails;
}

export async function getListTrainingHistory({
    isoCode,
    taskerId,
    filter,
    sort,
    pageSize,
    pageIndex,
}: ListTrainingHistory): Promise<{
    data: Array<{
        _id: Course['_id'];
        title: Course['title'];
        code: Course['code'];
        relatedServices: Course['relatedServices'];
        condition: Course['condition'];
        updatedAt: Course['updatedAt'];
        visibility: `${COURSE_VISIBILITY_IN_TRAINING}`;
        completion: `${COURSE_COMPLETION_IN_TRAINING}`;
        status: `${SUBMISSION_STATUS_IN_TRAINING}`;
        currentCourseStatus: `${COURSE_CURRENT_STATUS_FROM_API}`;
    }>;
    total: number;
}> {
    const apiURL = getRestApiByMultiRegion({
        apiKey: API_KEY.GET_LIST_TRAINING_HISTORY,
        isoCode,
    });

    const trainingHistoryInfo = await fetchAPI(
        apiURL,
        { taskerId, filter, sort, pageSize, pageIndex },
        isoCode,
    );

    return {
        data: trainingHistoryInfo?.data || [],
        total: trainingHistoryInfo?.total || 0,
    };
}
export async function getServicesByTaskerId({
    taskerId,
    isoCode,
}: {
    taskerId: Users['_id'];
    isoCode: `${IsoCode}`;
}) {
    const serviceChannels = await getModels(isoCode)
        .serviceChannel.find({ taskerList: taskerId })
        .lean();

    const serviceIds =
        serviceChannels?.map(serviceChannel => serviceChannel.serviceId) || [];

    const servicesFoundByIds = await getModels(isoCode)
        .service.find(
            { _id: { $in: serviceIds } },
            { name: 1, text: 1, icon: 1, isSubscription: 1 },
        )
        .lean();

    return servicesFoundByIds;
}
// TODO: Write unit test for this func
export async function getListQuizzesDetailSortByOrder({
    isoCode,
    quizCollection,
}: {
    isoCode: string;
    quizCollection: Course['quizCollections'];
}) {
    const quizIds = quizCollection.map(quiz => quiz._id);

    const quizDetails = await getModels(isoCode)
        .taskerTrainingQuizCollection.find(
            { _id: { $in: quizIds } },
            { title: 1, code: 1, timeToCompleteByMinutes: 1, quizzes: 1 },
        )
        .lean<
            Pick<
                QuizCollection,
                '_id' | 'title' | 'code' | 'timeToCompleteByMinutes' | 'quizzes'
            >[]
        >();

    const orderMap = quizCollection.reduce(
        (acc, quiz) => {
            acc[quiz._id] = quiz.order;
            return acc;
        },
        {} as Record<string, number>,
    );

    quizDetails.sort((a, b) => orderMap[a._id] - orderMap[b._id]);

    return quizDetails;
}

export async function getTrainingHistoryDetail({
    isoCode,
    taskerId,
    courseId,
}: {
    isoCode: EnumIsoCode;
    taskerId: Users['_id'];
    courseId: Course['_id'];
}) {
    const apiURL = getRestApiByMultiRegion({
        apiKey: 'GET_TRAINING_HISTORY_DETAIL',
        isoCode,
    });

    const historyInfoFromAPI: {
        visibility: `${COURSE_VISIBILITY_IN_TRAINING}`;
        completion: `${COURSE_COMPLETION_IN_TRAINING}`;
        status: `${SUBMISSION_STATUS_IN_TRAINING}`;
        currentCourseStatus: `${COURSE_CURRENT_STATUS_FROM_API}`;
    } = await fetchAPI(
        apiURL,
        {
            taskerId,
            courseId,
        },
        isoCode,
    );

    const result = await getModels(isoCode)
        .taskerTrainingSubmission.findOne({ taskerId, 'course._id': courseId })
        .lean();
    const changeHistoriesSubmission = result?.changeHistories?.filter(
        record => record.key === KEY_CHANGE_HISTORIES_IN_TRAINING.SUBMIT_COURSE,
    );

    const numOfTimeOpened = result?.changeHistories?.filter(
        record => record.key === KEY_CHANGE_HISTORIES_IN_TRAINING.UNBLOCK_TEST,
    );

    const course = await getModels(isoCode)
        .taskerTrainingCourse.findOne({ _id: courseId })
        .lean<Course>();

    return {
        code: course?.code || '',
        title: course?.title || '',
        displayPosition: course?.displayPosition || '',
        condition: course?.condition,
        numOfTimeOpened: (numOfTimeOpened?.length || 0) + 1,
        maximumNumberOfRetries: course?.maximumNumberOfRetries || 0,
        numberOfSubmissions: changeHistoriesSubmission?.length || 0,
        ...historyInfoFromAPI,
        relatedServices: course?.relatedServices || [],
        updatedAt: result?.changeHistories?.slice?.(-1)?.[0]?.createdAt,
        submissionHistories:
            changeHistoriesSubmission?.map(record => ({
                status: record?.content?.statusSubmission,
                numOfCorrect:
                    record?.content?.quizCollections?.filter(
                        quizCollection => quizCollection?.status === 'PASSED',
                    )?.length || 0,
                total: record?.content?.quizCollections?.length || 0,
                createdAt: record?.createdAt,
            })) || [],
    };
}

export async function getNameOfCourseByIds({
    courseIds,
    isoCode,
}: {
    courseIds: Array<string>;
    isoCode: string;
}) {
    const foundedCourse = await getModels(isoCode)
        .taskerTrainingCourse.find({ _id: { $in: courseIds } })
        .lean<Course[]>();

    return foundedCourse?.map(course => course.title) || [];
}
