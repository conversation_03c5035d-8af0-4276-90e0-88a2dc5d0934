import { momentTz } from 'btaskee-utils';
import { type PipelineStage } from 'mongo-connection';
import { getModels } from 'schemas';

import { newRecordCommonField } from './constants.server';

interface RejectReason {
    name: string;
    type: string;
}

export interface GettingRejectReason {
    isoCode: string;
    type: string;
    search: string;
    limit: PipelineStage.Limit['$limit'];
    skip: PipelineStage.Skip['$skip'];
}

export async function getTotalRejectReasons({
    isoCode,
    type,
    search,
}: Omit<GettingRejectReason, 'skip' | 'limit'>) {
    const taskerOnboardingSetting = await getModels(isoCode)
        .taskerOnboardingSetting.findOne(
            { 'reasons.type': type },
            { reasons: 1 },
        )
        .lean();

    const rejectReasons = taskerOnboardingSetting?.reasons || [];
    const filteredRejectReasons = rejectReasons?.filter(
        item =>
            item?.name?.match(new RegExp(search, 'i')) && item?.type === type,
    );

    return filteredRejectReasons?.length || 0;
}

export async function getListRejectReasons({
    isoCode,
    type,
    search,
    skip,
    limit,
}: GettingRejectReason) {
    const taskerOnboardingSetting = await getModels(isoCode)
        .taskerOnboardingSetting.findOne({}, { reasons: 1 })
        .lean<TaskerOnboardingSetting>();

    const rejectReasons = taskerOnboardingSetting?.reasons || [];

    const filteredRejectReasons = rejectReasons
        ?.filter(
            item =>
                item?.name?.match(new RegExp(search, 'i')) &&
                item?.type === type,
        )
        .slice(skip, skip + limit);

    return filteredRejectReasons;
}

export async function createRejectReasons({
    isoCode,
    createdBy,
    rejectReasonsData,
}: {
    isoCode: IsoCode;
    createdBy: Users['username'];
    rejectReasonsData: Array<RejectReason>;
}) {
    // Check if any of the reject reasons already exist with the same name
    const reasonsNameSet = new Set<string>();
    rejectReasonsData.forEach(reason => {
        if (reasonsNameSet.has(reason.name)) {
            throw new Error(
                `Reject reason with name "${reason.name}" already exists in the array`,
            );
        }
        reasonsNameSet.add(reason.name);
    });

    // Check if any of the reject reasons already exist with both name and type
    const existsReasons = await getModels(isoCode)
        .taskerOnboardingSetting.findOne({
            reasons: {
                $elemMatch: {
                    $or: rejectReasonsData,
                },
            },
        })
        .lean();

    if (existsReasons) {
        const existingNamesAndTypes = existsReasons.reasons
            .filter(reason =>
                rejectReasonsData.some(
                    inputReason =>
                        reason.name === inputReason.name &&
                        reason.type === inputReason.type,
                ),
            )
            .map(reason => `${reason.name} (${reason.type})`);

        throw new Error(
            `Reject reasons with name and type "${existingNamesAndTypes.join(
                ', ',
            )}" already exist`,
        );
    }

    // Check if taskerOnboardingSetting exists, if not, initialize it
    const taskerOnboardingSetting = await getModels(isoCode)
        .taskerOnboardingSetting.findOne()
        .lean();
    if (!taskerOnboardingSetting) {
        await getModels(isoCode).taskerOnboardingSetting.create({
            ...newRecordCommonField(),
            reasons: [],
        });
    }

    // Add all reject reasons in the array
    const newRejectReasons = rejectReasonsData.map(reason => ({
        ...newRecordCommonField(),
        ...reason,
        createdBy,
    }));

    return getModels(isoCode).taskerOnboardingSetting.updateOne(
        {},
        {
            $push: {
                reasons: { $each: newRejectReasons },
            },
        },
    );
}

export async function editRejectReason({
    isoCode,
    id,
    updateData,
    updatedBy,
}: {
    isoCode: IsoCode;
    id: string;
    updateData: RejectReason;
    updatedBy: string;
}) {
    const existsReason = await getModels(isoCode)
        .taskerOnboardingSetting.findOne({
            'reasons.name': updateData.name,
            'reasons.type': updateData.type,
        })
        .lean();

    if (existsReason) {
        throw new Error(
            `Reject reason with name "${updateData.name}" already exists`,
        );
    }
    const taskerOnboardingSetting = await getModels(isoCode)
        .taskerOnboardingSetting.findOne({})
        .lean();

    const oldReason = taskerOnboardingSetting?.reasons?.find(
        reason => reason._id === id,
    );

    if (!oldReason) {
        throw new Error('Reject reason not found');
    }

    return await getModels(isoCode)
        .taskerOnboardingSetting.updateOne(
            { 'reasons._id': id },
            {
                $set: {
                    'reasons.$': {
                        ...oldReason,
                        ...updateData,
                        updatedAt: momentTz().toDate(),
                        updatedBy,
                    },
                },
            },
        )
        .lean();
}

export function deleteRejectReason({
    isoCode,
    id,
}: {
    isoCode: IsoCode;
    id: string;
}) {
    return getModels(isoCode)
        .taskerOnboardingSetting.updateOne(
            { 'reasons._id': id },
            {
                $pull: { reasons: { _id: id } },
            },
        )
        .lean();
}

export async function getRejectReasonById({
    isoCode,
    id,
}: {
    isoCode: IsoCode;
    id: string;
}) {
    const result = await getModels(isoCode)
        .taskerOnboardingSetting.findOne(
            { 'reasons._id': id },
            { 'reasons.$': 1 },
        )
        .lean();

    return result?.reasons[0];
}
