import { momentTz } from 'btaskee-utils';
import { type PipelineStage } from 'mongo-connection';
import { getModels } from 'schemas';

import { newRecordCommonField } from './constants.server';

export interface GettingToolItems {
    isoCode: IsoCode;
    search?: string;
    rangeDate: { from: Date; to: Date };
    sort: PipelineStage.Sort['$sort'];
    skip: PipelineStage.Skip['$skip'];
    limit: PipelineStage.Limit['$limit'];
}

const getToolItemMatcher = ({
    search,
    rangeDate,
}: Pick<GettingToolItems, 'search' | 'rangeDate'>) => {
    const matcher: PipelineStage.Match['$match'] = {};

    if (search) {
        matcher.$and = [
            {
                $or: [
                    { 'text.vi': { $regex: search, $options: 'i' } },
                    { 'text.en': { $regex: search, $options: 'i' } },
                    { 'text.ko': { $regex: search, $options: 'i' } },
                    { 'text.th': { $regex: search, $options: 'i' } },
                    { 'text.id': { $regex: search, $options: 'i' } },
                ],
            },
        ];
    }

    if (rangeDate?.from && rangeDate?.to) {
        if (!matcher.$and) matcher.$and = [];

        matcher.$and.push({
            createdAt: {
                $gte: rangeDate.from,
                $lte: rangeDate.to,
            },
        });
    }

    return matcher;
};

export async function createToolItem({
    isoCode,
    toolkitItem,
}: {
    isoCode: IsoCode;
    toolkitItem: Omit<ToolKitItem, 'createdAt' | '_id'>;
}) {
    await getModels(isoCode).toolKitItems.create({
        ...newRecordCommonField(),
        ...toolkitItem,
    });

    return {
        msg: 'CREATE_TOOLKIT_ITEM_SUCCESSFULLY',
    };
}

export async function updateToolItem({
    isoCode,
    toolkitItemId,
    updateInfo,
}: {
    isoCode: IsoCode;
    toolkitItemId: ToolKitItem['_id'];
    updateInfo: Partial<ToolKitItem>;
}) {
    if (updateInfo?.price === 0) {
        throw new Error('PRICE_MUST_BE_MORE_THAN_0');
    }

    await getModels(isoCode).toolKitItems.findByIdAndUpdate(toolkitItemId, {
        $set: { ...updateInfo, updatedAt: momentTz().toDate() },
    });

    return {
        msg: 'UPDATE_TOOLKIT_ITEM_SUCCESSFULLY',
    };
}

export async function deleteToolItem({
    isoCode,
    toolkitItemId,
}: {
    isoCode: IsoCode;
    toolkitItemId: ToolKitItem['_id'];
}) {
    await getModels(isoCode).toolKitItems.findByIdAndDelete(toolkitItemId);

    await getModels(isoCode).toolKitSetting.updateMany(
        { 'toolKitItems._id': toolkitItemId },
        { $pull: { toolKitItems: { _id: toolkitItemId } } },
    );

    return {
        msg: 'DELETE_TOOLKIT_ITEM_SUCCESSFULLY',
    };
}

export async function getToolItemDetail({
    isoCode,
    toolkitItemId,
}: {
    isoCode: IsoCode;
    toolkitItemId: ToolKitItem['_id'];
}) {
    const toolkitItemFound = await getModels(isoCode)
        .toolKitItems.findById(toolkitItemId)
        .lean<ToolKitItem>();

    if (!toolkitItemFound) {
        throw new Error('TOOLKIT_ITEM_NOT_FOUND');
    }

    return toolkitItemFound;
}

export async function getTotalToolItems({
    isoCode,
    search,
    rangeDate,
}: Omit<GettingToolItems, 'sort' | 'skip' | 'limit'>) {
    return getModels(isoCode).toolKitItems.countDocuments(
        getToolItemMatcher({ search: search ?? '', rangeDate }),
    );
}

export async function getListToolItems({
    isoCode,
    search,
    rangeDate,
    sort,
    skip,
    limit,
}: GettingToolItems) {
    const toolkitItems = getModels(isoCode)
        .toolKitItems.find(
            getToolItemMatcher({ search: search ?? '', rangeDate }),
        )
        .sort({ ...sort, _id: 1 })
        .skip(skip)
        .limit(limit)
        .lean();

    return toolkitItems;
}
