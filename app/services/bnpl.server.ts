import {
    ACCOUNT_TYPE_IN_FA_TRANSACTION,
    BNPL_TRACKING_DAILY_TRANSACTION_REASON,
    BNPL_TRANSACTION_TYPE,
    BTASKEE_LANGUAGE,
    BUSINESS_TRANSACTION_TYPE,
    TASKER_BNPL_PROCESS_STATUS,
} from 'btaskee-constants';
import { momentTz } from 'btaskee-utils';
import type { PipelineStage } from 'mongoose';
import {
    getCollectionNameByIsoCode,
    getFieldNameByIsoCode,
    getModels,
} from 'schemas';
import type { EnumIsoCode } from 'schemas';
import { newRecordCommonField } from '~/services/constants.server';
import { sendNotification } from '~/services/utils.server';

export interface InstallmentPaymentListParams {
    skip: PipelineStage.Skip['$skip'];
    limit: PipelineStage.Limit['$limit'];
    sort: PipelineStage.Sort['$sort'];
    filter: {
        search: string;
        service: string;
        status: string;
        rangeDate: { from: Date; to: Date };
    };
    isoCode: string;
}

export interface FullPaymentListParams {
    isoCode: string;
    skip: PipelineStage.Skip['$skip'];
    limit: PipelineStage.Limit['$limit'];
    sort: PipelineStage.Sort['$sort'];
    filter: {
        search: string;
        service: string;
        city: string;
        rangeDate: { from: Date; to: Date };
    };
}

function getAmountSummary(result: MustBeAny[]) {
    if (!result?.length) {
        return {
            totalAmount: 0,
            totalPaidAmount: 0,
            totalRemainingAmount: 0,
        };
    }

    const totalAmount = result?.[0]?.totalAmount?.[0]?.amount ?? 0;
    const totalRemainingAmount =
        result?.[0]?.totalRemainingAmount?.[0]?.amount ?? 0;
    const totalPaidAmount = totalAmount - totalRemainingAmount;

    return {
        totalAmount,
        totalPaidAmount,
        totalRemainingAmount,
    };
}

function getInstallmentPaymentMatch({
    filter,
}: Pick<InstallmentPaymentListParams, 'filter'>) {
    const matcher: PipelineStage.Match['$match'] = {};

    if (filter.search) {
        matcher.$or = [
            { 'tasker.name': { $regex: filter.search, $options: 'i' } },
            { 'tasker.phone': { $regex: filter.search, $options: 'i' } },
        ];
    }

    if (filter.status) {
        matcher.status = { $in: filter.status.split(',') };
    }

    if (filter.service) {
        matcher['tasker.registeredServices'] = {
            $in: filter.service.split(','),
        };
    }

    if (filter.rangeDate) {
        matcher.createdAt = {
            $gte: filter.rangeDate.from,
            $lte: filter.rangeDate.to,
        };
    }

    return matcher;
}

function getCommonPipeline({
    filter,
    isoCode,
}: {
    filter: InstallmentPaymentListParams['filter'];
    isoCode: string;
}): PipelineStage[] {
    return [
        {
            $lookup: {
                from: getCollectionNameByIsoCode(isoCode).users,
                localField: 'taskerId',
                foreignField: '_id',
                as: 'tasker',
            },
        },
        {
            $unwind: {
                path: '$tasker',
                preserveNullAndEmptyArrays: true,
            },
        },
        {
            $lookup: {
                from: getCollectionNameByIsoCode(isoCode).serviceChannel,
                let: {
                    taskerId: '$taskerId',
                },
                pipeline: [
                    {
                        $match: {
                            $expr: {
                                $in: [
                                    '$$taskerId',
                                    { $ifNull: ['$taskerList', []] },
                                ],
                            },
                        },
                    },
                ],
                as: 'registeredServices',
            },
        },
        {
            $project: {
                tasker: {
                    _id: 1,
                    name: 1,
                    phone: 1,
                    city: { $arrayElemAt: ['$tasker.workingPlaces.city', 0] },
                    registeredServices: {
                        $map: {
                            input: '$registeredServices',
                            as: 'service',
                            in: '$$service.serviceId',
                        },
                    },
                },
                status: 1,
                amount: 1,
                remainingAmount: 1,
                createdAt: 1,
                expiredAt: 1,
            },
        },
        {
            $match: getInstallmentPaymentMatch({ filter }),
        },
    ];
}

function getFullPaymentMatch({
    filter,
}: Pick<FullPaymentListParams, 'filter'>) {
    const matcher: PipelineStage.Match['$match'] = {};

    if (filter.search) {
        matcher.$or = [
            { 'tasker.name': { $regex: filter.search, $options: 'i' } },
            { 'tasker.phone': { $regex: filter.search, $options: 'i' } },
        ];
    }

    if (filter.service) {
        matcher['tasker.registeredServices'] = {
            $in: filter.service.split(','),
        };
    }

    if (filter.rangeDate) {
        matcher.createdAt = {
            $gte: filter.rangeDate.from,
            $lte: filter.rangeDate.to,
        };
    }

    if (filter.city) {
        matcher['tasker.city'] = { $in: filter.city.split(',') };
    }

    return matcher;
}

function getCommonPipeLineFullPayment({
    filter,
    isoCode,
}: {
    filter: FullPaymentListParams['filter'];
    isoCode: string;
}): PipelineStage[] {
    return [
        {
            $match: {
                type: BNPL_TRANSACTION_TYPE.ONCE_PAY,
                'payment.status': 'PAID',
            },
        },
        {
            $lookup: {
                from: getCollectionNameByIsoCode(isoCode).users,
                localField: 'taskerId',
                foreignField: '_id',
                as: 'tasker',
            },
        },
        {
            $unwind: {
                path: '$tasker',
                preserveNullAndEmptyArrays: true,
            },
        },
        {
            $lookup: {
                from: getCollectionNameByIsoCode(isoCode).serviceChannel,
                let: {
                    taskerId: '$taskerId',
                },
                pipeline: [
                    {
                        $match: {
                            $expr: {
                                $in: [
                                    '$$taskerId',
                                    { $ifNull: ['$taskerList', []] },
                                ],
                            },
                        },
                    },
                ],
                as: 'registeredServices',
            },
        },
        {
            $project: {
                tasker: {
                    _id: 1,
                    name: 1,
                    phone: 1,
                    city: { $arrayElemAt: ['$tasker.workingPlaces.city', 0] },
                    registeredServices: {
                        $map: {
                            input: '$registeredServices',
                            as: 'service',
                            in: '$$service.serviceId',
                        },
                    },
                },
                listTool: {
                    $map: {
                        input: '$listTool',
                        as: 'tool',
                        in: {
                            text: '$$tool.text.en',
                            quantity: '$$tool.quantity',
                            price: {
                                $multiply: ['$$tool.price', '$$tool.quantity'],
                            },
                        },
                    },
                },
                amount: 1,
                createdAt: 1,
            },
        },
        { $match: getFullPaymentMatch({ filter }) },
    ];
}

/**
 * @documentation
 * This function will $lookup to users (big collection) and serviceChannel
 * which can impact on performance if users grows larger.
 * Note: need use $project to reduce amount of data being processed
 */
export async function getInstallmentPayment({
    skip,
    limit,
    sort,
    filter,
    isoCode,
}: InstallmentPaymentListParams): Promise<{
    data: InstallmentPayment[];
    totalAmount: number;
    totalPaidAmount: number;
    totalRemainingAmount: number;
}> {
    const data = await getModels(isoCode)
        .taskerBNPLProcess.aggregate([
            ...getCommonPipeline({ filter, isoCode }),
            {
                $facet: {
                    data: [
                        { $sort: { ...sort, _id: 1 } },
                        { $skip: skip },
                        { $limit: limit },
                    ],
                    totalAmount: [
                        {
                            $group: {
                                _id: null,
                                amount: { $sum: '$amount' },
                            },
                        },
                    ],
                    totalRemainingAmount: [
                        {
                            $group: {
                                _id: null,
                                remainingAmount: { $sum: '$remainingAmount' },
                            },
                        },
                    ],
                },
            },
        ])
        .exec();

    return {
        data: data[0].data || [],
        ...getAmountSummary(data),
    };
}

export async function getTotalInstallmentPayment({
    isoCode,
    filter,
}: Pick<InstallmentPaymentListParams, 'isoCode' | 'filter'>) {
    const result = await getModels(isoCode)
        .taskerBNPLProcess.aggregate([
            ...getCommonPipeline({ filter, isoCode }),
            { $count: 'total' },
        ])
        .exec();

    return result?.[0]?.total || 0;
}

/**
 * @documentation
 * This function will $lookup to users (big collection) and serviceChannel
 * which can impact on performance if users grows larger.
 */
export async function getInstallmentPaymentDetail({
    isoCode,
    id,
}: {
    isoCode: string;
    id: string;
}) {
    const data = await getModels(isoCode)
        .taskerBNPLProcess.aggregate<
            Pick<
                InstallmentPayment,
                | '_id'
                | 'tasker'
                | 'remainingAmount'
                | 'createdAt'
                | 'status'
                | 'amount'
                | 'expiredAt'
            >
        >([
            { $match: { _id: id } },
            {
                $lookup: {
                    from: getCollectionNameByIsoCode(isoCode).users,
                    localField: 'taskerId',
                    foreignField: '_id',
                    as: 'tasker',
                },
            },
            {
                $unwind: {
                    path: '$tasker',
                    preserveNullAndEmptyArrays: true,
                },
            },
            {
                $lookup: {
                    from: getCollectionNameByIsoCode(isoCode).serviceChannel,
                    let: {
                        taskerId: '$taskerId',
                    },
                    pipeline: [
                        {
                            $match: {
                                $expr: {
                                    $in: [
                                        '$$taskerId',
                                        { $ifNull: ['$taskerList', []] },
                                    ],
                                },
                            },
                        },
                    ],
                    as: 'registeredServices',
                },
            },
            {
                $project: {
                    tasker: {
                        _id: 1,
                        name: 1,
                        phone: 1,
                        city: {
                            $arrayElemAt: ['$tasker.workingPlaces.city', 0],
                        },
                        registeredServices: {
                            $map: {
                                input: '$registeredServices',
                                as: 'service',
                                in: '$$service.serviceId',
                            },
                        },
                    },
                    amount: 1,
                    status: 1,
                    remainingAmount: 1,
                    expiredAt: 1,
                    createdAt: 1, // Payment date
                },
            },
        ])
        .exec();
    return data?.[0] || {};
}

export function getBNPLTransactionByTaskerId({
    isoCode,
    taskerId,
}: {
    isoCode: string;
    taskerId: string;
}) {
    return getModels(isoCode)
        .taskerBNPLTransaction.aggregate([
            { $match: { taskerId } },
            {
                $lookup: {
                    from: getCollectionNameByIsoCode(isoCode).task,
                    localField: 'source.taskId',
                    foreignField: '_id',
                    as: 'task',
                },
            },
            {
                $lookup: {
                    from: getCollectionNameByIsoCode(isoCode).historyTasks,
                    localField: 'source.taskId',
                    foreignField: '_id',
                    as: 'taskHistory',
                },
            },
            {
                $unwind: {
                    path: '$task',
                    preserveNullAndEmptyArrays: true,
                },
            },
            {
                $unwind: {
                    path: '$taskHistory',
                    preserveNullAndEmptyArrays: true,
                },
            },
            {
                $addFields: {
                    task: {
                        $ifNull: ['$task', '$taskHistory'],
                    },
                },
            },
            {
                $project: {
                    task: {
                        _id: 1,
                        costDetail: 1,
                        newCostDetail: 1,
                    },
                    source: 1,
                    amount: 1,
                    createdAt: 1,
                },
            },
            { $sort: { createdAt: -1, _id: 1 } },
        ])
        .exec();
}

export async function getFullPayment({
    skip,
    limit,
    sort,
    filter,
    isoCode,
}: FullPaymentListParams) {
    const data = await getModels(isoCode)
        .paymentToolKitTransaction.aggregate([
            ...getCommonPipeLineFullPayment({ filter, isoCode }),
            {
                $facet: {
                    data: [
                        { $sort: { ...sort, _id: 1 } },
                        { $skip: skip },
                        { $limit: limit },
                    ],
                    totalAmount: [
                        { $group: { _id: null, amount: { $sum: '$amount' } } },
                    ],
                },
            },
        ])
        .exec();

    return {
        data: data[0].data || [],
        totalAmount: data[0].totalAmount[0]?.amount || 0,
    };
}

export async function getTotalFullPayment({
    isoCode,
    filter,
}: Pick<FullPaymentListParams, 'isoCode' | 'filter'>) {
    const result = await getModels(isoCode)
        .paymentToolKitTransaction.aggregate([
            ...getCommonPipeLineFullPayment({ filter, isoCode }),
            { $count: 'total' },
        ])
        .exec();

    return result?.[0]?.total || 0;
}

export async function deductInstallmentPaymentInTaskerBNPL({
    isoCode,
    reason,
    bnplId,
}: {
    isoCode: EnumIsoCode;
    reason: string;
    bnplId: string;
}) {
    const taskerBNPLProcessFound = await getModels(isoCode)
        .taskerBNPLProcess.findById(bnplId)
        .select({ remainingAmount: 1, taskerId: 1, _id: 1 })
        .lean();

    if (!taskerBNPLProcessFound) {
        throw new Error('TASKER_BNPL_PROCESS_NOT_FOUND');
    }

    const taskerFound = await getModels(isoCode)
        .users.findById(taskerBNPLProcessFound?.taskerId)
        .select({ fAccountId: 1, language: 1 })
        .lean();

    if (!taskerFound) {
        throw new Error('TASKER_NOT_FOUND');
    }

    const [createdTransaction, updatedBNPLProcess] = await Promise.all([
        getModels(isoCode).taskerBNPLTransaction.create({
            ...newRecordCommonField(),
            taskerId: taskerBNPLProcessFound.taskerId,
            BNPLId: taskerBNPLProcessFound._id,
            source: {
                name: BNPL_TRACKING_DAILY_TRANSACTION_REASON.CHARGE_BNPL,
                reason,
            },
            amount: taskerBNPLProcessFound.remainingAmount ?? 0,
            accountType: ACCOUNT_TYPE_IN_FA_TRANSACTION.MAIN,
            type: BUSINESS_TRANSACTION_TYPE.C,
        }),
        getModels(isoCode).taskerBNPLProcess.findByIdAndUpdate(bnplId, {
            $set: {
                status: TASKER_BNPL_PROCESS_STATUS.DONE,
                remainingAmount: 0,
                updatedAt: momentTz().toDate(),
            },
        }),
    ]);

    if (!createdTransaction?._id) {
        throw new Error(
            'UPDATE_TRANSACTION_HISTORY_IN_BNPL_TRANSACTION_FAILED',
        );
    }

    if (!updatedBNPLProcess) {
        throw new Error('DEDUCT_INSTALLMENT_PAYMENT_FAILED');
    }

    const financeField = getFieldNameByIsoCode({
        fieldName: 'FMainAccount',
        isoCode,
    });

    await Promise.all([
        getModels(isoCode).financialAccount.findByIdAndUpdate(
            taskerFound?.fAccountId,
            {
                $inc: {
                    [financeField]: -Number(
                        taskerBNPLProcessFound.remainingAmount ?? 0,
                    ),
                },
                $set: {
                    updatedAt: momentTz().toDate(),
                },
            },
        ),
        getModels(isoCode).FATransaction.create({
            ...newRecordCommonField(),
            userId: taskerBNPLProcessFound?.taskerId,
            accountType: ACCOUNT_TYPE_IN_FA_TRANSACTION.MAIN,
            type: BUSINESS_TRANSACTION_TYPE.C,
            source: {
                name: BNPL_TRACKING_DAILY_TRANSACTION_REASON.CHARGE_BNPL,
                BNPLTransactionId: createdTransaction._id,
            },
            amount: taskerBNPLProcessFound?.remainingAmount ?? 0,
            createdAt: momentTz().toDate(),
            isoCode,
        }),
        sendNotification({
            isSendNotificationId: true,
            userIds: [taskerFound?._id],
            locale: taskerFound?.language ?? BTASKEE_LANGUAGE.EN,
            message: {
                title: {
                    vi: 'Xử lý chấm dứt góp mua trước trả sau',
                    en: 'Handling termination of the Buy Now, Pay Later plan',
                    ko: '구매 후 지불 연기 서비스 종료 처리',
                    th: 'การดำเนินการยกเลิกแผนการซื้อก่อนจ่ายทีหลัง',
                    id: 'Penanganan penghentian paket beli sekarang, bayar nanti',
                },
                body: {
                    vi: `Yêu cầu chấm dứt gói mua trước trả sau của bạn đang được xử lý. Dựa trên điều khoản cam kết tham gia gói mua trước trả sau, hệ thống đã cấn trừ số tiền trả góp còn lại là ${taskerBNPLProcessFound?.remainingAmount ?? 0}vnd vào số dư khả dụng trong tài khoản chính của bạn.`,
                    en: `Your request to terminate your Buy Now, Pay Later plan is being processed. Based on the terms and conditions of the Buy Now, Pay Later package, the remaining installment amount of ${taskerBNPLProcessFound?.remainingAmount ?? 0} VND has been deducted from the available balance in your main account.`,
                    ko: `고객님께서 요청하신 구매 후 지불 연기 서비스 종료가 진행 중입니다. 구매 후 지불 연기 패키지에 참여하실 때 동의하신 조건에 따라, 남은 할부 금액 ${taskerBNPLProcessFound?.remainingAmount ?? 0} VND가 고객님의 주요 계좌 잔액에서 차감되었습니다.`,
                    th: `คำขอยกเลิกแผนการซื้อก่อนจ่ายทีหลังของคุณกำลังถูกดำเนินการ ตามเงื่อนไขที่ตกลงในการเข้าร่วมแผนการซื้อก่อนจ่ายทีหลัง ระบบได้หักจำนวนเงินงวดที่เหลือ ${taskerBNPLProcessFound?.remainingAmount ?? 0} vnd จากยอดเงินที่มีอยู่ในบัญชีหลักของคุณ`,
                    id: `Permintaan Anda untuk menghentikan paket beli sekarang, bayar nanti sedang diproses. Berdasarkan syarat dan ketentuan bergabung dengan paket beli sekarang, bayar nanti, jumlah cicilan yang tersisa sebesar ${taskerBNPLProcessFound?.remainingAmount ?? 0} VND telah dipotong dari saldo yang tersedia di akun utama Anda.`,
                },
            },
            isoCode,
        }),
    ]);

    return {
        msg: 'DEDUCT_INSTALLMENT_PAYMENT_IN_TASKER_BNPL_SUCCESSFULLY',
    };
}
