import { PERMISSIONS, res404 } from 'btaskee-constants';
import { getEnvMailServer } from 'btaskee-dotenv';
import {
    convertRolesToPermissions,
    momentTz,
    removeDuplicatedItem,
} from 'btaskee-utils';
import type { PipelineStage, Projection } from 'mongo-connection';
import {
    newRecordCommonField,
    statusOriginal,
} from '~/services/constants.server';
import GroupsModel from '~/services/model/groups.server';
import PermissionsModel from '~/services/model/permissions.server';
import RolesModel from '~/services/model/roles.servers';
import UsersModel from '~/services/model/users.server';
import { sendEmail } from '~/third-party/mail.server';

interface GettingMemberAddingToGroup {
    groupId: Groups['_id'];
    isoCode: IsoCode;
    filter: {
        searchText?: string;
    };
}

function getParamsForSendingEmailToAddUserIntoGroup({
    email,
    groupName,
    userFullName,
}: {
    email: Users['email'];
    groupName: GroupsV2['name'];
    userFullName: Users['name'];
}) {
    return {
        to: email,
        from: getEnvMailServer().MAIL_SERVER_USERNAME,
        subject: `Bạn đã được thêm vào nhóm ${groupName} trên hệ thống bTaskee`,
        html: `
    <!DOCTYPE html>
    <html lang="vi">
    <head>
      <meta charset="UTF-8">
      <meta name="viewport" content="width=device-width, initial-scale=1.0">
      <title>Thông Báo Thêm Vào Nhóm</title>
      <style>
        body {
          font-family: Arial, sans-serif;
          background-color: #f4f4f4;
          padding: 20px;
        }
        .container {
          max-width: 600px;
          margin: 0 auto;
          background-color: #fff;
          padding: 20px;
          border-radius: 8px;
          box-shadow: 0 0 10px rgba(0, 0, 0, 0.1);
        }
        .footer {
          margin-top: 20px;
          font-size: 0.9em;
          color: #777;
          text-align: center;
        }
      </style>
    </head>
    <body>
      <div class="container">
        <p>Chào <strong>${userFullName}</strong>,</p>

        <p>Chúng tôi xin thông báo rằng bạn đã được thêm vào nhóm <strong>${groupName}</strong> trong hệ thống bTaskee.</p>

        <p>Bạn có thể truy cập vào nhóm của mình và bắt đầu công việc ngay lập tức. 
        Nếu bạn có bất kỳ câu hỏi nào, xin vui lòng liên hệ với quản trị viên nhóm hoặc đội ngũ hỗ trợ của chúng tôi.</p>

        <p>Chúng tôi rất mong đợi sự đóng góp của bạn trong nhóm!</p>

        <p>Trân trọng,</p>
        <p><strong>Đội ngũ bTaskee</strong></p>

        <div class="footer">
          <p>Đây là email tự động, vui lòng không trả lời.</p>
        </div>
      </div>
    </body>
    </html>
  `,
    };
}

async function getMemberAddingToGroupMatcher({
    groupId,
    filter,
    isoCode,
}: GettingMemberAddingToGroup) {
    const groupFound = await GroupsModel.findById(groupId)
        .select({ users: 1, createdBy: 1, genealogy: 1 })
        .lean<Pick<GroupsV2, 'users' | 'createdBy' | 'genealogy'>>();

    const parentGroups = await GroupsModel.find({
        _id: { $in: groupFound?.genealogy ?? [] },
    })
        .select({ createdBy: 1 })
        .lean();

    const createdByGroupUserIds =
        parentGroups?.map(parentGroup => parentGroup?.createdBy ?? '') ?? [];

    const userIdsInGroup = groupFound?.users?.map(user => user?._id) ?? [];

    const matcher: PipelineStage.Match['$match'] = {
        _id: {
            $nin: [
                ...userIdsInGroup,
                groupFound?.createdBy,
                ...createdByGroupUserIds,
            ],
        },
        status: statusOriginal.ACTIVE,
        isoCode,
        ...(filter.searchText
            ? {
                  $or: [
                      {
                          name: { $regex: filter.searchText, $options: 'i' },
                      },
                      {
                          email: filter.searchText,
                      },
                  ],
              }
            : {}),
    };

    return matcher;
}

export async function verifySuperUser(userId: string) {
    const permissions = await getUserPermissions(userId);

    const rootPermissionId =
        (
            await PermissionsModel.findOne({
                key: PERMISSIONS.ROOT,
            }).lean<BtaskeePermissions>()
        )?._id || '';
    return Boolean(permissions.includes(rootPermissionId));
}

export async function verifyManager(userId: string) {
    const permissions = await getUserPermissions(userId);

    const rootPermissionId =
        (
            await PermissionsModel.findOne({
                key: PERMISSIONS.MANAGER,
            }).lean<BtaskeePermissions>()
        )?._id || '';
    return Boolean(permissions.includes(rootPermissionId));
}

// V2
export async function getUserPermissionIdsGlobal(userId: string) {
    const permissions = await getUserPermissionsGlobal(userId);
    return permissions.map(permission => permission.key);
}

// V2
export async function getUserPermissionsGlobal(userId: Users['_id']) {
    const groups = await GroupsModel.aggregate<GroupsV2 & { roles: Roles[] }>([
        {
            $match: {
                users: {
                    $elemMatch: {
                        _id: userId,
                        status: statusOriginal.ACTIVE,
                    },
                },
                status: statusOriginal.ACTIVE,
            },
        },
        {
            $lookup: {
                from: 'roles',
                localField: 'roleAssignedIds',
                foreignField: '_id',
                as: 'roles',
            },
        },
    ]).exec();

    const roles: Roles[] = [];
    groups.forEach(group => {
        if (group.roles) {
            group.roles.forEach(role => roles.push(role));
        }
    });
    const permissionIds = convertRolesToPermissions(roles);

    const permissions = await PermissionsModel.find({
        _id: { $in: permissionIds },
    }).lean();
    const permissionKeys = permissions?.map(permission => permission.key);

    if (
        permissionKeys.includes(PERMISSIONS.ROOT) ||
        permissionKeys.includes(PERMISSIONS.MANAGER)
    ) {
        const allPermissions = await getAllPermissionsIgnoreRoot();
        return allPermissions;
    }

    return permissions;
}

// V2
export async function getUserPermissions(userId: string) {
    const groups = await GroupsModel.aggregate<GroupsV2 & { roles: Roles[] }>([
        {
            $match: {
                users: {
                    $elemMatch: {
                        _id: userId,
                        status: statusOriginal.ACTIVE,
                    },
                },
                status: statusOriginal.ACTIVE,
            },
        },
        {
            $lookup: {
                from: 'roles',
                localField: 'roleAssignedIds',
                foreignField: '_id',
                as: 'roles',
            },
        },
    ]).exec();

    const roles: Roles[] = [];
    groups.forEach(group => {
        if (group.roles) {
            group.roles.forEach(role => roles.push(role));
        }
    });
    const permissions = convertRolesToPermissions(roles);

    return permissions;
}

// V2 Main function to get the deeply nested structure
export async function getGroupsForTreeViewByUserId(
    createdBy: GroupsV2['createdBy'],
) {
    const groups = await GroupsModel.aggregate<
        GroupsV2 & { children: GroupsV2[] }
    >([
        {
            $match: {
                createdBy,
            },
        },
        {
            $lookup: {
                from: 'groups',
                localField: 'nearestChildren',
                foreignField: '_id',
                as: 'children',
            },
        },
        {
            $sort: {
                status: 1, // Sort by status in descending order (ACTIVE first)
                createdAt: -1,
            },
        },
    ]).exec();

    async function getParentGroup(group: GroupsV2 & { children: GroupsV2[] }) {
        if (!group?.genealogy?.length) {
            return { ...group, parentGroupName: '' };
        }
        const parentId = [...group.genealogy].pop();
        if (!parentId) {
            return { ...group, parentGroupName: '' };
        }

        const parentGroup = await GroupsModel.findOne({ _id: parentId }).lean();
        if (!parentGroup) {
            return { ...group, parentGroupName: '' };
        }
        return {
            ...group,
            parentGroupName: parentGroup.name,
        };
    }

    return await Promise.all(groups.map(group => getParentGroup(group)));
}

// V2
export async function createGroup({
    name,
    description,
    parentId,
    userId,
    status,
}: Pick<GroupsV2, 'name' | 'description' | 'status'> & {
    parentId?: string;
    userId: string;
}) {
    let parentGroup;
    if (!parentId) {
        parentGroup = await GroupsModel.findOne({
            'users._id': userId,
            status: statusOriginal.ACTIVE,
        }).lean();
    } else {
        parentGroup = await GroupsModel.findOne({
            _id: parentId,
            status: statusOriginal.ACTIVE,
        }).lean();
    }
    if (!parentGroup) {
        throw new Error('PARENT_GROUP_NOT_FOUND');
    }

    // root group (hierarchy 1) not have genealogy field
    const genealogy = [...(parentGroup.genealogy || []), parentGroup._id];

    const groupExisted = await GroupsModel.findOne({ name });
    if (groupExisted) {
        throw new Error('GROUP_EXISTED');
    }

    const createdRoleAssigned = await RolesModel.create({
        ...newRecordCommonField(),
        permissions: [],
    });

    const groupCreated = await GroupsModel.create({
        ...newRecordCommonField(),
        status,
        name,
        description,
        users: [],
        roleAssignedIds: [createdRoleAssigned._id],
        genealogy,
        hierarchy: parentGroup.hierarchy + 1, // increase hierarchy
        createdBy: userId,
        iconType: Math.floor(Math.random() * 5), // icon random 0 -5 and hardcode
    });

    // update parent group
    await GroupsModel.updateOne(
        { _id: parentGroup._id },
        {
            $set: { updateAt: momentTz().toDate() },
            $push: { nearestChildren: groupCreated._id },
        },
    );

    return groupCreated;
}

// V2
export async function updateGroupInformation({
    groupId,
    name,
    description,
    status,
}: Pick<GroupsV2, 'name' | 'description' | 'status'> & {
    groupId: string;
}) {
    await GroupsModel.findOneAndUpdate(
        { _id: groupId },
        {
            $set: {
                updatedAt: momentTz().toDate(),
                name,
                status,
                description: description ? description : '',
            },
        },
    ).lean<GroupsV2>();

    if (status === statusOriginal.INACTIVE) {
        await GroupsModel.updateMany(
            {
                status: statusOriginal.ACTIVE,
                genealogy: groupId,
            },
            {
                $set: {
                    updatedAt: momentTz().toDate(),
                    status: statusOriginal.INACTIVE,
                },
            },
        );
    }
}

// V2
export async function getUsersByGroupId(groupIds: string[]) {
    const groups = await GroupsModel.find({
        status: statusOriginal.ACTIVE,
        _id: { $in: groupIds },
    }).lean();

    let userIds: Array<string> = [];
    groups.forEach(group => {
        userIds = [
            ...userIds,
            ...(group.users?.map(user => user?._id ?? '') ?? []),
        ];
    });

    // remove duplicate user id
    return [...new Set(userIds)];
}

// V2
export async function getPermissionsCreatedByGroupId({
    groupId,
}: {
    groupId: GroupsV2['_id'];
}) {
    const group = await GroupsModel.findOne(
        {
            _id: groupId,
        },
        { roleAssignedIds: 1 },
    ).lean();

    if (!group) return [];

    const roles = await RolesModel.find({
        _id: { $in: group.roleAssignedIds },
    }).lean<Roles[]>();

    return convertRolesToPermissions(roles);
}

export async function getGroupStatus({
    groupId,
}: {
    groupId: GroupsV2['_id'];
}) {
    const group = await GroupsModel.findOne(
        {
            _id: groupId,
        },
        { _id: 0, status: 1 },
    ).lean();

    return group?.status || statusOriginal.INACTIVE;
}

export async function searchUser(searchText: string) {
    const pattern = new RegExp(searchText, 'i');
    const users = await UsersModel.find(
        {
            status: statusOriginal.ACTIVE,
            $or: [
                {
                    email: { $regex: pattern },
                },
                {
                    username: { $regex: pattern },
                },
            ],
        },
        {
            language: 1,
            isoCode: 1,
            username: 1,
            email: 1,
            cities: 1,
            avatarUrl: 1,
            status: 1,
        },
    ).lean();
    return users;
}

// V2
export async function getGroupPermissions({
    groupId,
    isSuperUser,
    isManager,
}: {
    groupId: GroupsV2['_id'];
    isSuperUser: boolean;
    isManager: boolean;
}) {
    const group = await GroupsModel.findOne({
        _id: groupId,
    }).lean();
    if (!group) return [];

    // with super user & manager, show all permissions in create/update roles
    // apply when
    // super user is standing group's root
    // manager in group's manager (level 2)
    if (
        (isSuperUser || isManager) &&
        (group.genealogy?.length == 1 || group.genealogy?.length == 2)
    ) {
        return group.genealogy?.length == 2
            ? getAllPermissionsIgnoreManager()
            : getAllPermissionsIgnoreRoot();
    }

    if (!group.genealogy) return [];

    const parent = await GroupsModel.findOne({
        _id: [...group.genealogy].pop(),
    }).lean();
    if (!parent) {
        // Maybe parent INACTIVE
        return [];
    }

    const roles = await RolesModel.find({
        _id: { $in: parent.roleAssignedIds },
    }).lean<Roles[]>();
    if (!roles.length) return [];

    return PermissionsModel.find({
        _id: { $in: convertRolesToPermissions(roles) },
    }).lean();
}

// V2
export async function verifyUserInGroup({
    userId,
    groupId,
}: {
    userId: string;
    groupId: string;
}) {
    const group = await GroupsModel.findOne({
        status: statusOriginal.ACTIVE,
        _id: groupId,
        'users._id': userId,
    }).lean();
    return Boolean(group);
}

export async function getGroupsOfUser<T extends Projection = Projection>({
    projection,
    userId,
}: {
    projection: Projection;
    userId: string;
}) {
    const groups = await GroupsModel.aggregate<T>([
        {
            $match: {
                'users._id': userId,
                'users.status': statusOriginal.ACTIVE,
                status: statusOriginal.ACTIVE,
            },
        },
        {
            $lookup: {
                from: 'groups',
                localField: 'nearestChildren',
                foreignField: '_id',
                as: 'children',
            },
        },
        {
            $lookup: {
                from: 'users',
                localField: 'users._id',
                foreignField: '_id',
                as: 'users',
            },
        },
        { $project: projection },
    ]).exec();

    return groups;
}

// V2
export function getAllPermissionsIgnoreRoot() {
    return PermissionsModel.find({ key: { $ne: PERMISSIONS.ROOT } }).lean();
}

export function getAllPermissionsIgnoreManager() {
    return PermissionsModel.find({
        key: { $nin: [PERMISSIONS.ROOT, PERMISSIONS.MANAGER] },
    }).lean();
}

// V2
export function getAllPermissions() {
    return PermissionsModel.find({}).lean();
}

export async function getUsersInGroupsByUserId(userId: string) {
    const groupIdsOfUser = await GroupsModel.find({
        createdBy: userId,
    }).lean();
    return getUsersByGroupId(groupIdsOfUser.map(e => e._id));
}

export async function updateUser({
    userId,
    email,
    username,
    cities,
}: Pick<Users, 'email' | 'username' | 'cities'> & {
    userId: string;
}) {
    await UsersModel.updateOne(
        { _id: userId, status: statusOriginal.ACTIVE },
        {
            $set: {
                updatedAt: momentTz().toDate(),
                email,
                username,
                cities,
            },
        },
    );
}

export async function deleteUser(userId: string) {
    await UsersModel.updateOne(
        { _id: userId, status: statusOriginal.ACTIVE },
        {
            $set: {
                status: statusOriginal.REMOVED,
                updatedAt: momentTz().toDate(),
            },
        },
    );

    // remove user id in group they're in
    await GroupsModel.updateMany(
        {
            'users._id': userId,
            status: statusOriginal.ACTIVE,
        },
        {
            $pull: {
                'users._id': userId,
            },
        },
    );
}

export async function deleteGroup({ groupId }: { groupId: string }) {
    const updatedAt = momentTz().toDate();

    // get group and children of it
    // declare together
    const groupsChildren = await GroupsModel.find({
        status: statusOriginal.ACTIVE,
        $or: [{ genealogy: groupId }],
    }).lean();
    const group = await GroupsModel.findOne({
        status: statusOriginal.ACTIVE,
        _id: groupId,
    }).lean<Groups>();

    // get role's ids will be deleted
    const rolesWillBeDeleted = removeDuplicatedItem([
        group?.roleIds || [], // don't remove roles of parent, just remove in children
        ...groupsChildren.map(e => [
            ...(e?.roleIds || []),
            ...e.roleAssignedIds,
        ]),
    ]);

    // remove roles
    await RolesModel.deleteMany({ _id: { $in: rolesWillBeDeleted } });

    // remove group and children group
    await GroupsModel.updateMany(
        {
            status: statusOriginal.ACTIVE,
            $or: [{ _id: groupId }, { genealogy: groupId }],
        },
        {
            $set: {
                updatedAt,
                status: statusOriginal.REMOVED,
            },
        },
    );

    // update nearestChildren
    await GroupsModel.updateOne(
        {
            status: statusOriginal.ACTIVE,
            nearestChildren: groupId,
        },
        {
            $pull: {
                nearestChildren: groupId,
            },
        },
    );

    return group?.name || '';
}

export async function isParentOfGroup({
    parentId,
    groupId,
}: {
    parentId: string;
    groupId: string;
}) {
    const groupChecking = await GroupsModel.findOne({
        _id: groupId,
        status: statusOriginal.ACTIVE,
    }).lean<Groups>();
    if (!groupChecking?.genealogy?.length) return false;

    // get list parent of group checking
    // genealogy store ids of parent
    const groupParent = await GroupsModel.find({
        _id: { $in: groupChecking.genealogy },
        status: statusOriginal.ACTIVE,
        'users._id': parentId,
    }).lean();

    return Boolean(groupParent.length);
}

// V2
export async function getGroupDetail<T = Projection>({
    isSuperUser,
    userId,
    groupId,
    projection,
}: {
    userId: string;
    groupId: string;
    isSuperUser: boolean;
    projection: Projection;
}) {
    const lookupUsers = {
        $lookup: {
            from: 'users',
            let: { groupUsers: '$users' },
            pipeline: [
                { $match: { $expr: { $in: ['$_id', '$$groupUsers._id'] } } },
                {
                    $project: {
                        _id: 1,
                        name: 1,
                        email: 1,
                        username: 1,
                        addedAt: {
                            $getField: {
                                field: 'addedAt',
                                input: {
                                    $first: {
                                        $filter: {
                                            input: '$$groupUsers',
                                            cond: {
                                                $eq: ['$$this._id', '$_id'],
                                            },
                                        },
                                    },
                                },
                            },
                        },
                        status: {
                            $getField: {
                                field: 'status',
                                input: {
                                    $first: {
                                        $filter: {
                                            input: '$$groupUsers',
                                            cond: {
                                                $eq: ['$$this._id', '$_id'],
                                            },
                                        },
                                    },
                                },
                            },
                        },
                        // Add any other fields you want from the group's users array or the users collection
                    },
                },
            ],
            as: 'users',
        },
    };

    // case super user: allow access all groups
    if (isSuperUser) {
        const groupAsSuperUser = await GroupsModel.aggregate([
            {
                $match: { _id: groupId },
            },
            lookupUsers,
            {
                $project: {
                    ...projection,
                },
            },
        ]).exec();

        // group not found
        if (!groupAsSuperUser?.[0]) {
            throw new Response(null, res404);
        }
        return groupAsSuperUser[0] as T;
    }

    // Just owner can view this group
    const groupOfOwner = await GroupsModel.aggregate([
        {
            $match: { _id: groupId, createdBy: userId },
        },
        lookupUsers,
        {
            $project: projection,
        },
    ]).exec();

    // TODO need throw 403 if this group exist but user not owner
    // group not found
    if (!groupOfOwner?.[0]) {
        throw new Response(null, res404);
    }
    return groupOfOwner[0] as T;
}

export async function getAllChildrenGroupOfUser(userId: string) {
    const groups = await GroupsModel.aggregate([
        {
            $match: {
                'users._id': userId,
            },
        },
        {
            $lookup: {
                from: 'groups',
                localField: '_id',
                foreignField: 'genealogy',
                as: 'children',
            },
        },
        {
            $unwind: {
                path: '$children',
                preserveNullAndEmptyArrays: true,
            },
        },
    ]).exec();

    return groups
        .map(group => group.children)
        .filter(
            childrenGroup =>
                !!childrenGroup &&
                childrenGroup.status === statusOriginal.ACTIVE,
        );
}

async function fetchGroupAndChildren(id: string) {
    // Find the group by its ID
    const group = await GroupsModel.findOne({ _id: id }).lean();
    if (!group) {
        return null;
    }

    // Fetch the children groups
    const childrenPromises: MustBeAny = (group.nearestChildren || []).map(
        childId => fetchGroupAndChildren(childId),
    );
    const childrenDetails = await Promise.all(childrenPromises);

    // Construct the result for the current group
    return {
        ...group,
        id: group._id,
        children: childrenDetails.filter(child => child !== null),
    };
}

// Main function to get the deeply nested structure
export async function getGroupsForTreeView(userId: string) {
    const groups = await GroupsModel.find({
        'users._id': userId,
        status: statusOriginal.ACTIVE,
    }).lean();
    // Fetch groups and their children concurrently
    const results = await Promise.all(
        groups.map(group => fetchGroupAndChildren(group._id)),
    );
    // Filter out any null results and return the combined result
    return results.filter(result => result !== null);
}

export async function updatePermission({
    permissionId,
    params,
}: {
    permissionId: string;
    params: Pick<BtaskeePermissions, 'name' | 'description'>;
}) {
    const updatedPermission = await PermissionsModel.findOneAndUpdate(
        { _id: permissionId },
        {
            $set: {
                name: params.name,
                description: params.description,
            },
        },
        { new: true, runValidators: true },
    ).lean<BtaskeePermissions>();

    if (!updatedPermission) {
        throw new Error('PERMISSION_NOT_FOUND');
    }

    return updatedPermission;
}

export function getUsersByEmail(email: Users['email']) {
    return UsersModel.findOne({ email }).lean();
}

export async function addMultiUserToGroup({
    userIds,
    groupId,
}: {
    userIds: Array<Users['_id']>;
    groupId: GroupsV2['_id'];
}) {
    const [users, groupFound] = await Promise.all([
        UsersModel.find({
            _id: { $in: userIds ?? [] },
            status: statusOriginal.ACTIVE,
        }).lean(),
        GroupsModel.findById(groupId),
    ]);

    if (!groupFound) {
        throw new Error('GROUP_NOT_FOUND');
    }

    if (!users?.length) {
        throw new Error('USER_NOT_FOUND');
    }

    const groupUserIds = groupFound.users?.map(user => user._id) ?? [];

    const filteredUsers = users.filter(
        user => !groupUserIds.includes(user._id),
    );

    await Promise.all([
        GroupsModel.findByIdAndUpdate(groupId, {
            $push: {
                users: {
                    $each: filteredUsers.map(filteredUser => ({
                        _id: filteredUser._id,
                        status: statusOriginal.ACTIVE,
                        addedAt: momentTz().toDate(),
                    })),
                },
            },
        }).lean(),
        ...filteredUsers.map(filteredUser =>
            sendEmail(
                getParamsForSendingEmailToAddUserIntoGroup({
                    email: filteredUser?.email ?? '',
                    groupName: groupFound?.name,
                    userFullName: filteredUser?.name ?? '',
                }),
            ),
        ),
    ]);

    return {
        msg: 'ADD_USER_TO_GROUP_SUCCESSFULLY',
    };
}

export async function addUserToGroupByEmail({
    email,
    groupId,
    groupName,
}: {
    email: Users['email'];
    groupId: Groups['_id'];
    groupName?: string;
}) {
    const [user, group] = await Promise.all([
        getUsersByEmail(email),
        GroupsModel.findOne({ _id: groupId }).lean(),
    ]);

    if (user && user.status !== statusOriginal.ACTIVE) {
        throw new Error('ACCOUNT_INACTIVE');
    }

    if (!user) {
        throw new Error('USER_NOT_FOUND');
    }

    if (!group) {
        throw new Error('GROUP_NOT_FOUND');
    }

    if (group.users.some(userInGroup => userInGroup._id === user._id)) {
        throw new Error('USER_ALREADY_IN_GROUP');
    }

    if (group.genealogy?.length) {
        const genealogyCreators = await GroupsModel.find(
            { _id: { $in: group.genealogy } },
            { createdBy: 1, _id: 0 },
        ).lean();

        if (genealogyCreators.some(creator => creator.createdBy === user._id)) {
            throw new Error('CAN_NOT_ADD_YOUR_MANAGER');
        }
    }

    const updateGroup = await GroupsModel.updateOne(
        { _id: groupId },
        {
            $push: {
                users: {
                    _id: user._id,
                    status: statusOriginal.ACTIVE,
                    addedAt: new Date(),
                },
            },
        },
    ).lean();

    if (!updateGroup) {
        throw new Error('CAN_NOT_ADD_USER_INTO_GROUP');
    }

    await sendEmail(
        getParamsForSendingEmailToAddUserIntoGroup({
            email: email ?? '',
            groupName: group.name ?? groupName,
            userFullName: user.name ?? '',
        }),
    );

    return updateGroup;
}

export function getUserById(id: string) {
    return UsersModel.findOne({ _id: id }).lean();
}

export async function editMember({
    userId,
    paramsUpdate,
}: {
    userId: string;
    paramsUpdate: Pick<Users, 'name' | 'email' | 'status'>;
}) {
    const existEmail = await UsersModel.findOne({
        _id: { $ne: userId },
        email: paramsUpdate.email,
    }).lean();

    if (existEmail) {
        throw new Error('EMAIL_ALREADY_EXIST');
    }

    return UsersModel.updateOne({ _id: userId }, { $set: paramsUpdate });
}

export async function removeUserFromGroup({
    userId,
    groupId,
}: {
    userId: string;
    groupId: string;
}) {
    const user = await UsersModel.findOne(
        { _id: userId },
        { email: 1, name: 1 },
    );

    if (!user) {
        throw new Error('USER_NOT_FOUND');
    }

    const [updateGroup, group] = await Promise.all([
        GroupsModel.updateOne(
            { _id: groupId },
            { $pull: { users: { _id: userId } } },
        ),
        GroupsModel.findOne(
            {
                _id: groupId,
            },
            { name: 1, _id: 0 },
        ),
    ]);

    if (!updateGroup) {
        throw new Error('INTERNAL_SERVER_ERROR');
    }

    sendEmail({
        to: user.email,
        from: getEnvMailServer().MAIL_SERVER_USERNAME,
        subject: 'Thông Báo: Bạn Đã Bị Xóa Khỏi Nhóm',
        html: `
    <!DOCTYPE html>
    <html lang="vi">
    <head>
      <meta charset="UTF-8">
      <meta name="viewport" content="width=device-width, initial-scale=1.0">
      <title>Thông Báo Xóa Khỏi Nhóm</title>
      <style>
        body {
          font-family: Arial, sans-serif;
          background-color: #f4f4f4;
          padding: 20px;
        }
        .container {
          max-width: 600px;
          margin: 0 auto;
          background-color: #fff;
          padding: 20px;
          border-radius: 8px;
          box-shadow: 0 0 10px rgba(0, 0, 0, 0.1);
        }
        .footer {
          margin-top: 20px;
          font-size: 0.9em;
          color: #777;
          text-align: center;
        }
      </style>
    </head>
    <body>
      <div class="container">
        <p>Chào <strong>${user.name}</strong>,</p>

        <p>Chúng tôi xin thông báo rằng bạn đã bị xóa khỏi nhóm <strong>${group?.name}</strong> trong hệ thống bTaskee. 
        Quyết định này có thể do hoàn thành công việc hoặc do yêu cầu của quản trị viên nhóm.</p>

        <p>Nếu bạn có bất kỳ câu hỏi nào hoặc nghĩ rằng đây là sự nhầm lẫn, 
        xin vui lòng liên hệ với quản trị viên nhóm hoặc bộ phận hỗ trợ của chúng tôi.</p>

        <p>Cảm ơn bạn đã đóng góp cho nhóm.</p>

        <p>Trân trọng,</p>
        <p><strong>Đội ngũ bTaskee</strong></p>

        <div class="footer">
          <p>Đây là email tự động, vui lòng không trả lời.</p>
        </div>
      </div>
    </body>
    </html>
  `,
    });
}

export async function updatePermissionsByGroupId({
    groupId,
    permissions,
}: {
    groupId: GroupsV2['_id'];
    permissions: Array<string>;
}) {
    const group = await GroupsModel.findOne({ _id: groupId }).lean();
    if (!group) {
        throw new Error('GROUP_NOT_FOUND');
    }

    await RolesModel.updateOne(
        { _id: group.roleAssignedIds[0] },
        {
            $set: {
                permissions,
            },
        },
    );
}

export function updateStatusUserInGroup({
    userId,
    groupId,
    status,
}: {
    userId: string;
    groupId: string;
    status: string;
}) {
    return GroupsModel.updateOne(
        { _id: groupId, 'users._id': userId },
        { $set: { 'users.$.status': status } },
    );
}

export async function getTotalMemberAddingToGroup({
    groupId,
    filter,
    isoCode,
}: GettingMemberAddingToGroup) {
    const matcher = await getMemberAddingToGroupMatcher({
        groupId,
        filter,
        isoCode,
    });

    const totalUserAddingToGroup = await UsersModel.countDocuments(matcher);

    return totalUserAddingToGroup ?? 0;
}

export async function getListMemberAddingToGroup({
    groupId,
    isoCode,
    filter,
    skip,
    limit,
}: GettingMemberAddingToGroup & {
    skip: PipelineStage.Skip['$skip'];
    limit: PipelineStage.Limit['$limit'];
}) {
    const matcher = await getMemberAddingToGroupMatcher({
        groupId,
        filter,
        isoCode,
    });

    const usersAddingToGroup = await UsersModel.aggregate<
        Users & { groups: Array<GroupsV2> }
    >([
        {
            $match: matcher,
        },
        {
            $lookup: {
                from: 'groups',
                localField: '_id',
                foreignField: 'users._id',
                as: 'groups',
            },
        },
        {
            $skip: skip,
        },
        {
            $limit: limit,
        },
    ]).exec();

    return usersAddingToGroup ?? [];
}
