import type { PipelineStage } from 'mongo-connection';
import { getModels } from 'schemas';

interface SpecialCampaignTransactionParams {
    skip: PipelineStage.Skip['$skip'];
    limit: PipelineStage.Limit['$limit'];
    sort: PipelineStage.Sort['$sort'];
    filter: {
        search: string;
        status: string;
        rangeDate: { from: Date; to: Date };
    };
    isoCode: IsoCode;
}

function getMatcher({
    filter,
}: Pick<SpecialCampaignTransactionParams, 'filter'>) {
    const matcher: PipelineStage.Match['$match'] = {};

    if (filter.search) {
        matcher.$or = [
            { campaignName: { $regex: filter.search, $options: 'i' } },
            { phone: { $regex: filter.search, $options: 'i' } },
            { campaignId: { $regex: filter.search, $options: 'i' } },
        ];
    }

    if (filter.rangeDate?.from && filter.rangeDate?.to) {
        matcher.createdAt = {
            $gte: filter.rangeDate.from,
            $lte: filter.rangeDate.to,
        };
    }

    if (filter.status) {
        matcher.status = { $in: filter.status.split(',') };
    }

    return matcher;
}

export function getListSpecialCampaignTransaction({
    isoCode,
    filter,
    sort,
    skip,
    limit,
}: SpecialCampaignTransactionParams) {
    return getModels(isoCode)
        .taskerSpecialCampaignTransaction.find(getMatcher({ filter }))
        .sort({ ...sort, _id: 1 })
        .skip(skip)
        .limit(limit)
        .lean();
}

export function getTotalSpecialCampaignTransaction({
    isoCode,
    filter,
}: Pick<SpecialCampaignTransactionParams, 'isoCode' | 'filter'>) {
    return getModels(isoCode).taskerSpecialCampaignTransaction.countDocuments(
        getMatcher({ filter }),
    );
}
