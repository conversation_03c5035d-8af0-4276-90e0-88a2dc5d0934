import { momentTz } from 'btaskee-utils';
import type { PipelineStage } from 'mongo-connection';
import { getModels } from 'schemas';

export async function getBtaskeeCommunityDetail({
    isoCode,
}: {
    isoCode: IsoCode;
}) {
    const btaskeeCommunityFound = await getModels(isoCode)
        .communityUser.findOne({
            isAdmin: true,
        })
        .select({
            avatar: 1,
            name: 1,
            bio: 1,
            numberOfPosts: 1,
            numberOfShares: 1,
            numberOfLikes: 1,
            numberOfAskerViews: 1,
            updatedBy: 1,
            updatedAt: 1,
        })
        .lean();

    if (!btaskeeCommunityFound) {
        throw new Error('BTASKEE_COMMUNITY_NOT_FOUND');
    }

    return btaskeeCommunityFound;
}

export async function updateBtaskeeCommunity({
    isoCode,
    updateInfo,
    updatedByUserName,
}: {
    isoCode: IsoCode;
    updateInfo: Partial<Pick<CommunityUser, 'avatar' | 'name' | 'bio'>>;
    updatedByUserName: Users['username'];
}) {
    const btaskeeCommunityFound = await getModels(
        isoCode,
    ).communityUser.findOne({
        isAdmin: true,
    });

    if (!btaskeeCommunityFound) {
        throw new Error('BTASKEE_COMMUNITY_NOT_FOUND');
    }

    if (!updateInfo.bio) {
        throw new Error('BIO_IS_REQUIRED');
    }

    if (!updateInfo.name) {
        throw new Error('NAME_IS_REQUIRED');
    }

    const updateInfos: {
        $set: PipelineStage.Set['$set'];
        $unset?: PipelineStage.Set['$set'];
    } = {
        $set: {
            name: updateInfo.name,
            bio: updateInfo.bio,
            updatedBy: updatedByUserName,
            updatedAt: momentTz().toDate(),
        },
    };

    if (updateInfo.avatar) {
        updateInfos.$set.avatar = updateInfo.avatar;
    } else {
        if (!updateInfos.$unset) updateInfos.$unset = {};

        updateInfos.$unset.avatar = btaskeeCommunityFound.avatar;
    }

    await getModels(isoCode).communityUser.findByIdAndUpdate(
        btaskeeCommunityFound._id,
        updateInfos,
    );

    return {
        msg: 'UPDATE_BTASKEE_COMMUNITY_SUCCESSFULLY',
    };
}
