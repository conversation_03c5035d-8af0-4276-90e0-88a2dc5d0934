import { type PipelineStage } from 'mongo-connection';
import { getCollectionNameByIsoCode, getModels } from 'schemas';
import {
    SERVICE_TEXT_IN_REPORT_TASKER_OPERATION,
    TASK_STATUS,
    USER_STATUS,
    USER_TYPE,
} from '~/services/constants.server';

export interface GettingReportTaskerOperation {
    isoCode: `${IsoCode}`;
    rangeDate: {
        from: Date;
        to: Date;
    };
    cities: string;
    serviceText: string;
}

/**
 * @documentation
 * This function called 3 aggregate query in task collection and 1 aggregate query in users
 * which can impact on performance if users and task grows larger.
 * Note: need refactor ASAP to increase performance
 */
export async function getReportTaskerOperation({
    isoCode,
    rangeDate,
    cities,
    serviceText,
}: GettingReportTaskerOperation) {
    const matchTask: PipelineStage.Match = {
        $match: {
            $and: [
                {
                    date: {
                        $gte: rangeDate.from,
                        $lte: rangeDate.to,
                    },
                    'taskPlace.city': { $in: cities.split(',') },
                },
            ],
        },
    };

    /**
     * Avoid getting the tasker created after the filter date
     */
    const matchAllTasker: PipelineStage.Match = {
        $match: {
            $and: [
                {
                    type: USER_TYPE.TASKER,
                    status: USER_STATUS.ACTIVE,
                    createdAt: { $lte: rangeDate.to },
                    'workingPlaces.city': { $in: cities.split(',') },
                },
            ],
        },
    };

    if (
        serviceText === SERVICE_TEXT_IN_REPORT_TASKER_OPERATION.PREMIUM_SERVICE
    ) {
        matchTask.$match.isPremium = true;
        matchAllTasker.$match.isPremiumTasker = true;
    }

    if (
        serviceText !== SERVICE_TEXT_IN_REPORT_TASKER_OPERATION.ALL_SERVICE &&
        serviceText !== SERVICE_TEXT_IN_REPORT_TASKER_OPERATION.PREMIUM_SERVICE
    ) {
        matchTask.$match?.$and?.push({
            'serviceText.en': serviceText,
        });

        const serviceFound = await getModels(isoCode)
            .service.findOne(
                { 'text.en': serviceText, isSubscription: { $exists: false } },
                { _id: 1 },
            )
            .lean();
        const serviceChannelFound = await getModels(isoCode)
            .serviceChannel.findOne({ serviceId: serviceFound?._id })
            .lean();

        matchAllTasker.$match._id = {
            $in: serviceChannelFound?.taskerList || [],
        };
    }

    const matchTaskWorking = {
        $match: {
            status: {
                $in: [
                    TASK_STATUS.WAITING_ASKER_CONFIRMATION,
                    TASK_STATUS.CONFIRMED,
                    TASK_STATUS.DONE,
                ],
            },
        },
    };

    const lookupTasker = {
        $lookup: {
            from: getCollectionNameByIsoCode(isoCode).users,
            localField: '_id',
            foreignField: '_id',
            as: 'tasker',
        },
    };

    const unwindTask = {
        $unwind: {
            path: '$acceptedTasker',
            preserveNullAndEmptyArrays: true,
        },
    };

    const groupAcceptedTasker = {
        $group: {
            _id: '$acceptedTasker.taskerId',
            totalTask: { $sum: 1 },
        },
    };

    const unwindTasker = {
        $unwind: {
            path: '$tasker',
            preserveNullAndEmptyArrays: true,
        },
    };

    const groupTask = {
        $group: {
            _id: '$status',
            count: { $sum: 1 },
        },
    };

    const groupTasker = {
        $group: {
            _id: null,
            count: { $sum: 1 },
        },
    };

    const aggregateTaskerAcceptedTask = [
        matchTask,
        matchTaskWorking,
        { $project: { acceptedTasker: 1 } },
        unwindTask,
        groupAcceptedTasker,
        lookupTasker,
        unwindTasker,
        { $project: { _id: 1 } },
    ];

    const aggregateTotalTask: Array<PipelineStage> = [
        matchTask,
        { $project: { status: 1 } },
        groupTask,
        { $sort: { _id: 1 } },
    ];

    const aggregateTotalTasker: Array<PipelineStage> = [
        matchAllTasker,
        { $project: { _id: 1 } },
        groupTasker,
    ];

    const aggregateTaskOfDistrict: Array<PipelineStage> = [
        matchTask,
        { $project: { taskPlace: 1, status: 1 } },
        {
            $group: {
                _id: {
                    district: '$taskPlace.district',
                    status: '$status',
                },
                statusCount: { $sum: 1 },
            },
        },
        {
            $group: {
                _id: '$_id.district',
                ratioStatus: {
                    $push: {
                        status: '$_id.status',
                        count: '$statusCount',
                    },
                },
                count: { $sum: '$statusCount' },
            },
        },
        { $sort: { count: -1, _id: 1 } },
    ];

    const result = await Promise.all([
        getModels(isoCode).users.aggregate(aggregateTotalTasker),
        getModels(isoCode).task.aggregate(aggregateTaskerAcceptedTask),
        getModels(isoCode).task.aggregate(aggregateTotalTask),
        getModels(isoCode).task.aggregate(aggregateTaskOfDistrict),
    ]).then(values => {
        const taskerActive = values[0];
        const taskerAccepted = values[1];
        const ratioTask = values[2];
        const ratioTaskOfDistrict = values[3];

        const totalTasker = taskerActive[0]?.count || 0;
        const taskerWorking = taskerAccepted?.length || 0;

        return {
            ratioTasker: [
                {
                    label: 'TASKER_WORKING',
                    value: taskerWorking,
                },
                {
                    label: 'TASKER_NOT_WORKING',
                    value: totalTasker - taskerWorking,
                },
            ],
            totalTasker,
            ratioTask,
            ratioTaskOfDistrict,
        };
    });

    return result;
}
