import type { COURSE_CURRENT_STATUS_FROM_API } from 'btaskee-constants';
import {
    KEY_CHANGE_HISTORIES_IN_TRAINING,
    TYPE as TASKER_TRAINING_COURSE_TYPE,
} from 'btaskee-constants';
import { type PipelineStage } from 'mongoose';
import { getCollectionNameByIsoCode, getModels } from 'schemas';

interface TrainingHistoryAllTaskerParams {
    skip: PipelineStage.Skip['$skip'];
    limit: PipelineStage.Limit['$limit'];
    sort: PipelineStage.Sort['$sort'];
    filter: {
        search: string;
        status: string;
        rangeDate: { from: Date; to: Date };
    };
    isoCode: string;
}

function getTrainingHistoryAllTaskerMatch({
    filter,
}: Pick<TrainingHistoryAllTaskerParams, 'filter'>) {
    const matcher: PipelineStage.Match['$match'] = {};

    if (filter.search) {
        matcher.$or = [
            { 'tasker.phone': { $regex: filter.search, $options: 'i' } },
            { 'tasker.name': { $regex: filter.search, $options: 'i' } },
        ];
    }

    if (filter.status) {
        matcher.status = { $in: filter.status.split(',') };
    }

    if (filter.rangeDate) {
        matcher.createdAt = {
            $gte: filter.rangeDate.from,
            $lte: filter.rangeDate.to,
        };
    }

    return matcher;
}

function getCommonPipelineTrainingHistoryAllTasker({
    filter,
    isoCode,
}: Pick<TrainingHistoryAllTaskerParams, 'isoCode' | 'filter'>) {
    return [
        {
            $match: {
                'course.type': TASKER_TRAINING_COURSE_TYPE.TEST,
            },
        },
        {
            $lookup: {
                from: getCollectionNameByIsoCode(isoCode).users,
                localField: 'taskerId',
                foreignField: '_id',
                as: 'tasker',
            },
        },
        {
            $unwind: {
                path: '$tasker',
                preserveNullAndEmptyArrays: true,
            },
        },
        {
            $lookup: {
                from: getCollectionNameByIsoCode(isoCode).taskerTrainingCourse,
                localField: 'course._id',
                foreignField: '_id',
                as: 'taskerTrainingCourse',
            },
        },
        {
            $unwind: {
                path: '$taskerTrainingCourse',
                preserveNullAndEmptyArrays: true,
            },
        },
        {
            $project: {
                taskerTrainingCourse: {
                    _id: 1,
                    title: 1,
                    code: 1,
                    status: 1,
                },
                tasker: {
                    _id: 1,
                    name: 1,
                    phone: 1,
                },
                createdAt: 1,
                status: {
                    $cond: [
                        { $eq: ['$status', 'PASSED'] },
                        'PASSED',
                        {
                            $cond: [
                                {
                                    $eq: [
                                        '$numberOfSubmissions',
                                        '$taskerTrainingCourse.maximumNumberOfRetries',
                                    ],
                                },
                                'BLOCKED',
                                'FAILED',
                            ],
                        },
                    ],
                },
                numberOfSubmissions: {
                    $size: {
                        $ifNull: [
                            {
                                $filter: {
                                    input: {
                                        $ifNull: ['$changeHistories', []],
                                    },
                                    as: 'record',
                                    cond: {
                                        $eq: [
                                            '$$record.key',
                                            KEY_CHANGE_HISTORIES_IN_TRAINING.SUBMIT_COURSE,
                                        ],
                                    },
                                },
                            },
                            [],
                        ],
                    },
                },
                numOfTimeOpened: {
                    $add: [
                        {
                            $size: {
                                $ifNull: [
                                    {
                                        $filter: {
                                            input: '$changeHistories',
                                            as: 'record',
                                            cond: {
                                                $eq: [
                                                    '$$record.key',
                                                    KEY_CHANGE_HISTORIES_IN_TRAINING.UNBLOCK_TEST,
                                                ],
                                            },
                                        },
                                    },
                                    [],
                                ],
                            },
                        },
                        1,
                    ],
                },
            },
        },
        {
            $match: getTrainingHistoryAllTaskerMatch({ filter }),
        },
    ];
}

/**
 * @documentation
 * This function will $lookup to users (big collection) and taskerTrainingCourse
 * which can impact on performance if users and taskerTrainingSubmission grows larger.
 */
export function getTrainingHistoryAllTasker({
    isoCode,
    filter,
    sort,
    skip,
    limit,
}: TrainingHistoryAllTaskerParams): Promise<
    Array<{
        _id: string;
        taskerTrainingCourse: {
            _id: string;
            title: string;
            code: string;
            status: `${COURSE_CURRENT_STATUS_FROM_API}`;
        };
        tasker: {
            _id: string;
            name: string;
            phone: string;
        };
        createdAt: Date;
        status: string;
        numberOfSubmissions: number;
        numOfTimeOpened: number;
    }>
> {
    return getModels(isoCode)
        .taskerTrainingSubmission.aggregate([
            ...getCommonPipelineTrainingHistoryAllTasker({ filter, isoCode }),
            { $sort: { ...sort, _id: 1 } },
            { $skip: skip },
            { $limit: limit },
        ])
        .exec();
}

export async function getTotalTrainingHistoryAllTasker({
    isoCode,
    filter,
}: Pick<
    TrainingHistoryAllTaskerParams,
    'isoCode' | 'filter'
>): Promise<number> {
    const result = await getModels(isoCode)
        .taskerTrainingSubmission.aggregate([
            ...getCommonPipelineTrainingHistoryAllTasker({ filter, isoCode }),
            { $count: 'total' },
        ])
        .exec();

    return result?.[0]?.total || 0;
}
