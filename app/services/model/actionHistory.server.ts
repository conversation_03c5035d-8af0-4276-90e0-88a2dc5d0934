import { mongoClientBE } from 'mongo-connection';
import { Schema } from 'mongoose';

const ActionsHistorySchema = new Schema<ActionsHistory>(
    {
        _id: {
            $type: String,
            required: true,
        },
        actorId: {
            $type: String,
        },
        action: { $type: String },
        requestFormData: { $type: Schema.Types.Mixed },
        createdAt: { $type: Date, required: true },
    },
    { typeKey: '$type', collection: 'actionHistory' },
);

const ActionsHistoryModel = mongoClientBE.model(
    'ActionsHistory',
    ActionsHistorySchema,
);

export default ActionsHistoryModel;
