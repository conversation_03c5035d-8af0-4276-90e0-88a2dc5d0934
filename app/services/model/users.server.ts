import { mongoClient<PERSON> } from 'mongo-connection';
import { Schema } from 'mongoose';

const UsersSchema = new Schema<Users>(
    {
        _id: {
            $type: String,
            required: true,
        },
        username: {
            $type: String,
            required: true,
        },
        email: {
            $type: String,
            required: true,
            unique: true,
        },
        isoCode: {
            $type: String,
        },
        createdAt: { $type: Date, require: true },
        status: {
            $type: String,
            required: true,
        },
        cities: {
            $type: [String],
        },
        language: {
            $type: String,
            require: true,
        },
        avatarUrl: {
            $type: String,
        },
        services: {
            password: {
                bcrypt: {
                    $type: String,
                },
            },
            google: {
                $type: String,
            },
        },
        verification: {
            code: {
                $type: String,
            },
            token: {
                $type: String,
            },
            expired: {
                $type: Date,
            },
        },
        resetPassword: {
            token: {
                $type: String,
            },
            expired: {
                $type: Date,
            },
        },
        name: {
            $type: String,
        },
    },
    { typeKey: '$type', collection: 'users' },
);

const UsersModel = mongoClientBE.model('Users', UsersSchema);
export default UsersModel;
