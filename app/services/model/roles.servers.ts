import { mongoClientBE } from 'mongo-connection';
import { Schema } from 'mongoose';

const RolesSchema = new Schema<Roles>(
    {
        _id: {
            $type: String,
            required: true,
        },
        name: {
            $type: String,
        },
        description: {
            $type: String,
        },
        permissions: {
            $type: [String],
            required: true,
        },
        slug: {
            $type: String,
        },
        createdAt: { $type: Date, default: Date.now },
        updatedAt: {
            $type: Date,
        },
        status: {
            $type: String,
        },
    },
    { typeKey: '$type', collection: 'roles' },
);

const RolesModel = mongoClientBE.model('Roles', RolesSchema);
export default RolesModel;
