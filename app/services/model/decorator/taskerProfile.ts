import { TASKER_ONBOARDING_PROCESS_STATUS } from 'btaskee-constants';
import type { PipelineStage } from 'mongo-connection';
import { getCollectionNameByIsoCode, getModels } from 'schemas';

export function taskerProfile() {
    let aggregateQuery: Array<PipelineStage> = [];

    const wrapper = {
        match,
        lookupAndUnwindUser,
        lookupServiceChannel,
        lookupService,
        search,
        projection,
        addUpdatedDateField,
        sortSkipLimit,
        count,
        fetch,
    };

    function grow(value: Array<PipelineStage>) {
        aggregateQuery = [...aggregateQuery, ...value];
    }

    function match($match: PipelineStage.Match['$match']) {
        grow([
            {
                $match,
            },
        ]);

        return wrapper;
    }

    function lookupAndUnwindUser({
        cities,
        isoCode,
    }: {
        cities: string[];
        isoCode: IsoCode;
    }) {
        grow([
            {
                $lookup: {
                    from: getCollectionNameByIsoCode(isoCode).users,
                    localField: 'taskerId',
                    foreignField: '_id',
                    as: 'tasker',
                },
            },
            {
                $unwind: {
                    path: '$tasker',
                    preserveNullAndEmptyArrays: true,
                },
            },
            {
                $match: {
                    $expr: {
                        $anyElementTrue: {
                            $map: {
                                input: {
                                    $ifNull: ['$tasker.workingPlaces', []],
                                },
                                as: 'place',
                                in: {
                                    $in: ['$$place.city', cities ?? []],
                                },
                            },
                        },
                    },
                },
            },
        ]);

        return wrapper;
    }

    function search(searchText: string) {
        if (searchText) {
            grow([
                {
                    $match: {
                        $or: [
                            {
                                'tasker.name': {
                                    $regex: searchText,
                                    $options: 'i',
                                },
                            },
                            {
                                'tasker.phone': {
                                    $regex: searchText,
                                    $options: 'i',
                                },
                            },
                        ],
                    },
                },
            ]);
        }

        return wrapper;
    }

    function lookupService({ isoCode }: { isoCode: IsoCode }) {
        grow([
            {
                $lookup: {
                    from: getCollectionNameByIsoCode(isoCode).service,
                    localField: 'serviceChannel.serviceId',
                    foreignField: '_id',
                    as: 'services',
                },
            },
        ]);

        return wrapper;
    }

    function lookupServiceChannel({ isoCode }: { isoCode: IsoCode }) {
        grow([
            {
                $lookup: {
                    from: getCollectionNameByIsoCode(isoCode).serviceChannel,
                    localField: 'taskerId',
                    foreignField: 'taskerList',
                    as: 'serviceChannel',
                },
            },
        ]);

        return wrapper;
    }

    function count() {
        grow([{ $count: 'total' }]);

        return wrapper;
    }

    function sortSkipLimit({
        sort,
        skip,
        limit,
    }: {
        sort: PipelineStage.Sort['$sort'];
        skip: PipelineStage.Skip['$skip'];
        limit: PipelineStage.Limit['$limit'];
    }) {
        grow([{ $sort: sort }, { $skip: skip }, { $limit: limit }]);

        return wrapper;
    }

    function addUpdatedDateField() {
        grow([
            {
                $addFields: {
                    updatedAt: { $ifNull: ['$updatedAt', '$createdAt'] },
                },
            },
        ]);
        return wrapper;
    }

    function projection() {
        grow([
            {
                $project: {
                    _id: 1,
                    taskerPhone: '$tasker.phone',
                    taskerName: '$tasker.name',
                    taskerId: 1,
                    status: {
                        $ifNull: [
                            '$status',
                            TASKER_ONBOARDING_PROCESS_STATUS.VERIFYING,
                        ],
                    },
                    identityCard: 1,
                    household: 1,
                    criminalRecords: 1,
                    massagePracticeCertificate: 1,
                    curriculumVitae: 1,
                    confirmationConduct: 1,
                    taskerGender: '$tasker.gender',
                    taskerLanguage: '$tasker.language',
                    taskerWorkingPlaces: '$tasker.workingPlaces',
                    isPartner: 1,
                    username: '$taskerName',
                    'services.text': 1,
                    'services.name': 1,
                    'services.isSubscription': 1,
                    'services.icon': 1,
                    createdAt: 1,
                    actionHistories: 1,
                    appointmentInfo: 1,
                    processStatus: 1,
                    notes: 1,
                    updatedAt: 1,
                    passport: 1,
                    workPermit: 1,
                    houseElectricBill: 1,
                },
            },
        ]);

        return wrapper;
    }

    async function fetch(isoCode: string) {
        const result =
            await getModels(isoCode).taskerProfile.aggregate(aggregateQuery);

        return result;
    }

    return wrapper;
}
