import { TASKER_ONBOARDING_PROCESS_STATUS } from 'btaskee-constants';
import type { PipelineStage } from 'mongo-connection';
import { getCollectionNameByIsoCode, getModels } from 'schemas';

export function employeeProfile() {
    let aggregateQuery: Array<PipelineStage> = [];

    const wrapper = {
        match,
        lookupAndUnwindUser,
        lookupServiceChannel,
        lookupService,
        lookupAndUnwindCompany,
        search,
        projection,
        addUpdatedDateField,
        sortSkipLimit,
        count,
        fetch,
    };

    function grow(value: Array<PipelineStage>) {
        aggregateQuery = [...aggregateQuery, ...value];
    }

    function match($match: PipelineStage.Match['$match']) {
        grow([
            {
                $match,
            },
        ]);

        return wrapper;
    }

    function lookupAndUnwindUser({ isoCode }: { isoCode: IsoCode }) {
        grow([
            {
                $lookup: {
                    from: getCollectionNameByIsoCode(isoCode).users,
                    localField: 'companyId',
                    foreignField: '_id',
                    as: 'tasker',
                },
            },
            {
                $unwind: {
                    path: '$tasker',
                    preserveNullAndEmptyArrays: true,
                },
            },
        ]);

        return wrapper;
    }

    function search(searchText: string) {
        if (searchText) {
            grow([
                {
                    $match: {
                        $or: [
                            {
                                'tasker.name': {
                                    $regex: searchText,
                                    $options: 'i',
                                },
                            },
                            {
                                'tasker.phone': {
                                    $regex: searchText,
                                    $options: 'i',
                                },
                            },
                        ],
                    },
                },
            ]);
        }

        return wrapper;
    }

    function lookupService({ isoCode }: { isoCode: IsoCode }) {
        grow([
            {
                $lookup: {
                    from: getCollectionNameByIsoCode(isoCode).service,
                    localField: 'serviceChannel.serviceId',
                    foreignField: '_id',
                    as: 'services',
                },
            },
        ]);

        return wrapper;
    }

    function lookupAndUnwindCompany({ isoCode }: { isoCode: IsoCode }) {
        grow([
            {
                $lookup: {
                    from: getCollectionNameByIsoCode(isoCode).users,
                    localField: 'companyId',
                    foreignField: '_id',
                    as: 'companyInformation',
                },
            },
            {
                $unwind: {
                    path: '$companyInformation',
                    preserveNullAndEmptyArrays: true,
                },
            },
        ]);

        return wrapper;
    }

    function lookupServiceChannel({ isoCode }: { isoCode: IsoCode }) {
        grow([
            {
                $lookup: {
                    from: getCollectionNameByIsoCode(isoCode).serviceChannel,
                    localField: 'companyId',
                    foreignField: 'taskerList',
                    as: 'serviceChannel',
                },
            },
        ]);

        return wrapper;
    }

    function count() {
        grow([{ $count: 'total' }]);

        return wrapper;
    }

    function sortSkipLimit({
        skip,
        limit,
    }: {
        skip: PipelineStage.Skip['$skip'];
        limit: PipelineStage.Limit['$limit'];
    }) {
        grow([
            {
                $sort: { createdAt: -1 },
            },
            { $skip: skip },
            { $limit: limit },
        ]);

        return wrapper;
    }

    function addUpdatedDateField() {
        grow([
            {
                $addFields: {
                    updatedAt: { $ifNull: ['$updatedAt', '$createdAt'] },
                },
            },
        ]);
        return wrapper;
    }

    function projection() {
        grow([
            {
                $project: {
                    _id: 1,
                    taskerPhone: '$tasker.phone',
                    taskerName: '$tasker.name',
                    username: 1,
                    taskerId: 1,
                    company: 1,
                    status: {
                        $ifNull: [
                            '$status',
                            TASKER_ONBOARDING_PROCESS_STATUS.VERIFYING,
                        ],
                    },
                    identityCard: 1,
                    portrait: 1,
                    dob: 1,
                    taskerGender: '$tasker.gender',
                    taskerLanguage: '$tasker.language',
                    taskerWorkingPlaces: '$tasker.workingPlaces',
                    'services.text': 1,
                    'services.name': 1,
                    'services.isSubscription': 1,
                    'services.icon': 1,
                    actionHistories: 1,
                    'companyInformation.name': 1,
                    createdAt: 1,
                    notes: 1,
                    updatedAt: 1,
                },
            },
        ]);

        return wrapper;
    }

    async function fetch(isoCode: string) {
        const result =
            await getModels(isoCode).employeeProfile.aggregate(aggregateQuery);

        return result;
    }

    return wrapper;
}
