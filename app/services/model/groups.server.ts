import { mongoClientBE } from 'mongo-connection';
import { Schema } from 'mongoose';

const GroupsSchema = new Schema<GroupsV2>(
    {
        _id: {
            $type: String,
            required: true,
        },
        name: {
            $type: String,
            unique: true,
            required: true,
        },
        description: {
            $type: String,
        },
        users: {
            $type: [
                {
                    _id: {
                        $type: String,
                        required: true,
                    },
                    addedAt: {
                        $type: Date,
                        required: true,
                    },
                    status: {
                        $type: String,
                        required: true,
                    },
                },
            ],
            required: true,
        },
        roleAssignedIds: {
            $type: [String],
            required: true,
        },
        roleIds: {
            $type: [String],
        },
        genealogy: {
            $type: [String],
        },
        nearestChildren: {
            $type: [String],
        },
        hierarchy: {
            $type: Number,
            required: true,
        },
        createdAt: {
            $type: Date,
            default: Date.now,
        },
        updatedAt: {
            $type: Date,
        },
        status: {
            $type: String,
            required: true,
        },
        createdBy: {
            $type: String,
            required: true,
        },
        iconType: {
            $type: Number,
        },
    },
    { typeKey: '$type', collection: 'groups' },
);

const GroupsModel = mongoClientBE.model('Groups', GroupsSchema);
export default GroupsModel;
