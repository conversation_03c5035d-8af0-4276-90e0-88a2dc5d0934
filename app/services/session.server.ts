import { createCookieSessionStorage } from '@remix-run/node';
import { getEnvSession } from 'btaskee-dotenv';

// export the whole sessionStorage object
export const sessionStorage = createCookieSessionStorage({
    cookie: {
        name: getEnvSession().SESSION_NAME, // use any name you want here
        sameSite: 'lax', // this helps with CSRF
        path: '/', // remember to add this so the cookie will work in all routes
        httpOnly: true, // for security reasons, make this cookie http only
        secrets: [getEnvSession().SESSION_SECRET], // replace this with an actual secret
        maxAge: getEnvSession().MAX_AGE_SESSION,

        // remix-serve just run with production NODE_ENV
        // But I want to make my env's testing (sandbox) like production mode
        // So ignore the http only in sandbox env
        secure:
            process.env.REMIX_ENV === 'sandbox'
                ? false
                : process.env.NODE_ENV === 'production',
    },
});

// you can also export the methods individually for your own usage
export const { getSession, commitSession, destroySession } = sessionStorage;
