import type { EnumIsoCode } from 'schemas';
import { getFieldNameByIsoCode, getModels } from 'schemas';

import getRestApiByMultiRegion, { API_KEY } from './api-proxy/index.server';
import { fetchAPI } from './utils.server';

interface FinancialAccount {
    _id: string;
    createdAt?: Date | null;
    updatedAt?: Date | null;
    FMainAccount?: string | null;
    TH_FMainAccount?: string | null;
    ID_FMainAccount?: string | null;
}

export async function getTaskerDetail({
    isoCode,
    taskerId,
}: {
    isoCode: EnumIsoCode;
    taskerId: UserApp['_id'];
}) {
    const taskerServices = await getModels(isoCode)
        .serviceChannel.find({
            taskerList: taskerId,
        })
        .lean();

    const taskerServicesIds = taskerServices.map(service => service.serviceId);

    const services = await getModels(isoCode)
        .service.find(
            {
                _id: { $in: taskerServicesIds },
            },
            {
                text: 1,
                isSubscription: 1,
                icon: 1,
            },
        )
        .lean<Service[]>();

    const tasker = await getModels(isoCode)
        .users.findOne({ _id: taskerId })
        // TODO: update UserApp type
        .lean<UserApp & { isEco?: boolean }>();

    if (!tasker) {
        throw new Error('Tasker not found');
    }

    // Get Main Account Money
    const fMainAccountMoney = await getModels(isoCode)
        .financialAccount.findOne({ _id: tasker?.fAccountId })
        .lean();

    const fMainAccountFieldName = getFieldNameByIsoCode({
        isoCode,
        fieldName: 'FMainAccount',
    });

    const apiUrl = getRestApiByMultiRegion({
        apiKey: API_KEY.GET_TASKER_MONEY,
        isoCode,
    });
    // Get available money of tasker
    const taskerMoney = await fetchAPI(
        apiUrl,
        {
            taskerId,
        },
        isoCode,
    );

    return {
        ...tasker,
        servicesOfTasker: services,
        fMainAccountMoney:
            fMainAccountMoney?.[
                fMainAccountFieldName as keyof FinancialAccount
            ] ?? 0,
        taskerMoney,
    };
}
