import { TYPE_LADING } from 'btaskee-constants';
import { momentTz } from 'btaskee-utils';
import type { PipelineStage } from 'mongoose';
import { getCollectionNameByIsoCode, getModels } from 'schemas';

import { newR<PERSON>ord<PERSON><PERSON><PERSON>Field } from './constants.server';

/**
 * @documentation
 * This function will $lookup to 3 tables, includes:
 *   - users (big collection)
 *   - taskerToolkitLadingDetails
 *   - serviceChannel
 * which can impact on performance if users grows larger.
 * Note: refactor that move $project to bottom of aggregate pipelines
 */
export async function getPaymentToolKitTransaction({
    isoCode,
    filter,
    sort,
    skip,
    limit,
}: {
    filter: {
        search: string;
        service: string;
        typeOfPayment: string;
        status: string;
        rangeDate: {
            from: Date;
            to: Date;
        };
    };
    isoCode: IsoCode;
    sort: PipelineStage.Sort['$sort'];
    skip: PipelineStage.Skip['$skip'];
    limit: PipelineStage.Limit['$limit'];
}) {
    const getMatcher: PipelineStage.Match['$match'] = {};

    if (filter.search) {
        getMatcher.$or = [
            { 'tasker.name': { $regex: filter.search, $options: 'i' } },
            { 'tasker.phone': { $regex: filter.search, $options: 'i' } },
        ];
    }

    if (filter.typeOfPayment) {
        getMatcher.type = { $in: filter.typeOfPayment.split(',') };
    }

    if (filter.rangeDate.from && filter.rangeDate.to) {
        getMatcher.createdAt = {
            $gte: filter.rangeDate.from,
            $lte: filter.rangeDate.to,
        };
    }

    if (filter.status) {
        getMatcher.status = { $in: filter.status.split(',') };
    }

    if (filter.service) {
        getMatcher.registeredServices = { $in: filter.service.split(',') };
    }

    const data = await getModels(isoCode)
        .paymentToolKitTransaction.aggregate<{
            data: Array<
                Omit<PaymentTransaction, 'registeredServices'> & {
                    registeredServices: Array<string>;
                }
            >;
            totalAmount: Array<{
                _id: string;
                amount: number;
            }>;
        }>([
            {
                $lookup: {
                    from: getCollectionNameByIsoCode(isoCode)
                        .taskerToolkitLadingDetails,
                    foreignField: 'toolKitTransactionId',
                    localField: '_id',
                    as: 'ladingDetails',
                },
            },
            {
                $unwind: {
                    path: '$ladingDetails',
                    preserveNullAndEmptyArrays: true,
                },
            },
            {
                $lookup: {
                    from: getCollectionNameByIsoCode(isoCode).users,
                    localField: 'taskerId',
                    foreignField: '_id',
                    as: 'tasker',
                },
            },
            {
                $unwind: {
                    path: '$tasker',
                    preserveNullAndEmptyArrays: true,
                },
            },
            {
                $lookup: {
                    from: getCollectionNameByIsoCode(isoCode).serviceChannel,
                    let: {
                        taskerId: '$taskerId',
                    },
                    pipeline: [
                        {
                            $match: {
                                $expr: {
                                    $in: [
                                        '$$taskerId',
                                        { $ifNull: ['$taskerList', []] },
                                    ],
                                },
                            },
                        },
                    ],
                    as: 'registeredServices',
                },
            },
            {
                $match: {
                    'payment.status': 'PAID',
                },
            },
            {
                $addFields: {
                    status: {
                        $cond: [
                            { $ifNull: ['$ladingDetails', false] },
                            {
                                $cond: [
                                    {
                                        $ifNull: [
                                            '$ladingDetails.isReceived',
                                            false,
                                        ],
                                    },
                                    'RECEIVED',
                                    'DELIVERED',
                                ],
                            },
                            'PAID',
                        ],
                    },
                },
            },
            {
                $project: {
                    tasker: {
                        _id: 1,
                        name: 1,
                        phone: 1,
                    },
                    type: 1, // ONCE_PAY | BNPL
                    status: 1, // PAID | RECEIVED | DELIVERED
                    amount: 1,
                    createdAt: 1,
                    registeredServices: {
                        $map: {
                            input: '$registeredServices',
                            as: 'service',
                            in: '$$service.serviceId',
                        },
                    },
                },
            },
            { $match: getMatcher },
            {
                $facet: {
                    data: [
                        { $sort: { ...sort, _id: 1 } },
                        { $skip: skip },
                        { $limit: limit },
                    ],
                    totalAmount: [
                        {
                            $group: {
                                _id: null,
                                amount: { $sum: '$amount' },
                            },
                        },
                    ],
                },
            },
        ])
        .exec();

    return {
        data: data[0].data || [],
        totalAmount: data[0].totalAmount[0]?.amount || 0,
    };
}

export async function getTotalPaymentToolKitTransaction({
    isoCode,
    filter,
}: {
    isoCode: IsoCode;
    filter: {
        search: string;
        service: string;
        typeOfPayment: string;
        status: string;
        rangeDate: {
            from: Date;
            to: Date;
        };
    };
}) {
    const getMatcher: PipelineStage.Match['$match'] = {};

    if (filter.search) {
        getMatcher.$or = [
            { 'tasker.name': { $regex: filter.search, $options: 'i' } },
            { 'tasker.phone': { $regex: filter.search, $options: 'i' } },
        ];
    }

    if (filter.typeOfPayment) {
        getMatcher.type = { $in: filter.typeOfPayment.split(',') };
    }

    if (filter.rangeDate.from && filter.rangeDate.to) {
        getMatcher.createdAt = {
            $gte: filter.rangeDate.from,
            $lte: filter.rangeDate.to,
        };
    }

    if (filter.status) {
        getMatcher.status = { $in: filter.status.split(',') };
    }

    if (filter.service) {
        getMatcher.registeredServices = { $in: filter.service.split(',') };
    }

    const result = await getModels(isoCode)
        .paymentToolKitTransaction.aggregate([
            {
                $lookup: {
                    from: getCollectionNameByIsoCode(isoCode)
                        .taskerToolkitLadingDetails,
                    foreignField: 'toolKitTransactionId',
                    localField: '_id',
                    as: 'ladingDetails',
                },
            },
            {
                $unwind: {
                    path: '$ladingDetails',
                    preserveNullAndEmptyArrays: true,
                },
            },
            {
                $lookup: {
                    from: getCollectionNameByIsoCode(isoCode).users,
                    localField: 'taskerId',
                    foreignField: '_id',
                    as: 'tasker',
                },
            },
            {
                $unwind: {
                    path: '$tasker',
                    preserveNullAndEmptyArrays: true,
                },
            },
            {
                $lookup: {
                    from: getCollectionNameByIsoCode(isoCode).serviceChannel,
                    let: {
                        taskerId: '$taskerId',
                    },
                    pipeline: [
                        {
                            $match: {
                                $expr: {
                                    $in: [
                                        '$$taskerId',
                                        { $ifNull: ['$taskerList', []] },
                                    ],
                                },
                            },
                        },
                    ],
                    as: 'registeredServices',
                },
            },
            {
                $match: {
                    'payment.status': 'PAID',
                },
            },
            {
                $addFields: {
                    status: {
                        $cond: [
                            { $ifNull: ['$ladingDetails', false] },
                            {
                                $cond: [
                                    {
                                        $ifNull: [
                                            '$ladingDetails.isReceived',
                                            false,
                                        ],
                                    },
                                    'RECEIVED',
                                    'DELIVERED',
                                ],
                            },
                            'PAID',
                        ],
                    },
                },
            },
            {
                $project: {
                    tasker: {
                        _id: 1,
                        name: 1,
                        phone: 1,
                    },
                    type: 1, // ONCE_PAY | BNPL
                    status: 1, // PAID | RECEIVED | DELIVERED
                    amount: 1,
                    createdAt: 1,
                    registeredServices: {
                        $map: {
                            input: '$registeredServices',
                            as: 'service',
                            in: '$$service.serviceId',
                        },
                    },
                },
            },
            { $match: getMatcher },
            { $count: 'total' },
        ])
        .exec();

    return result.length > 0 ? result[0].total : 0;
}

export async function confirmPaymentToolKitTransactionByTypeLading({
    isoCode,
    type,
    taskerId,
    toolKitTransactionId,
    params,
}: {
    isoCode: IsoCode;
    type: TYPE_LADING;
    taskerId: string;
    toolKitTransactionId: string;
    params: {
        placeOfReceipt?: string;
        amount: number;
        billOfLading?: string;
        domesticRouting?: string;
        createdBy: string;
    };
}) {
    if (type === TYPE_LADING.OFFICE) {
        await getModels(isoCode).taskerToolkitLadingDetails.create({
            _id: newRecordCommonField()._id,
            createdAt: newRecordCommonField().createdAt,
            dateOfLading: momentTz().toDate(),
            typeOfLading: TYPE_LADING.OFFICE,
            placeOfReceipt: params.placeOfReceipt,
            amount: params.amount,
            createdBy: params.createdBy,
            taskerId,
            toolKitTransactionId,
        });
    } else if (type === TYPE_LADING.LADING) {
        await getModels(isoCode).taskerToolkitLadingDetails.create({
            _id: newRecordCommonField()._id,
            createdAt: newRecordCommonField().createdAt,
            typeOfLading: TYPE_LADING.LADING,
            dateOfLading: momentTz().toDate(),
            billOfLading: params.billOfLading,
            domesticRouting: params.domesticRouting,
            taskerId,
            createdBy: params.createdBy,
            amount: params.amount,
            toolKitTransactionId,
        });
    }

    return { msg: 'Successfully' };
}

export async function getDetailPaymentToolKitTransaction({
    paymentToolKitTransactionId,
    isoCode,
}: {
    isoCode: IsoCode;
    paymentToolKitTransactionId: string;
}) {
    const result = await getModels(isoCode)
        .paymentToolKitTransaction.aggregate([
            {
                $match: { _id: paymentToolKitTransactionId },
            },
            {
                $lookup: {
                    from: getCollectionNameByIsoCode(isoCode)
                        .taskerToolkitLadingDetails,
                    foreignField: 'toolKitTransactionId',
                    localField: '_id',
                    as: 'ladingDetails',
                },
            },
            {
                $unwind: {
                    path: '$ladingDetails',
                    preserveNullAndEmptyArrays: true,
                },
            },
            {
                $lookup: {
                    from: getCollectionNameByIsoCode(isoCode).users,
                    localField: 'taskerId',
                    foreignField: '_id',
                    as: 'tasker',
                },
            },
            {
                $unwind: {
                    path: '$tasker',
                    preserveNullAndEmptyArrays: true,
                },
            },
            {
                $project: {
                    tasker: {
                        _id: 1,
                        name: 1,
                        phone: 1,
                    },
                    amount: 1,
                    type: 1, // ONCE_PAY | BNPL
                    listTool: {
                        $map: {
                            input: '$listTool',
                            as: 'tool',
                            in: {
                                text: '$$tool.text.en',
                                quantity: '$$tool.quantity',
                                price: {
                                    $multiply: [
                                        '$$tool.price',
                                        '$$tool.quantity',
                                    ],
                                },
                            },
                        },
                    },
                    ladingDetails: {
                        typeOfLading: 1,
                        placeOfReceipt: 1,
                        billOfLading: 1,
                        domesticRouting: 1,
                        createdBy: 1,
                    },
                },
            },
        ])
        .exec();

    return result.length > 0 ? result[0] : {};
}
