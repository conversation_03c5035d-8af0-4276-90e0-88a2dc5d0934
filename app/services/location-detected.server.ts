import { getEnvGoogleMapAPI } from 'btaskee-dotenv';
import type { PipelineStage } from 'mongo-connection';
import { getModels } from 'schemas';
import {
    COMMON_MIN_SEARCH_LENGTH,
    EARTH_RADIUS,
    USER_TYPE,
} from '~/services/constants.server';
import { getCountryByIsoCode } from '~/utils/common';

type GoogleMapApiResponse = {
    candidates: Array<{
        formatted_address: string;
        geometry: {
            location: {
                lat: number;
                long: number;
            };
        };
        name: string;
    }>;
};

type GettingUserLocationDetected = {
    isoCode: string;
    rangeDate: {
        from: Date;
        to: Date;
    };
    radius: number;
    location?: {
        lat: number;
        lng: number;
    };
    searchText: string;
};

const getPipelinesGetTaskerLocationDetected = ({
    rangeDate,
    location,
    radius,
    searchText,
}: Omit<GettingUserLocationDetected, 'isoCode' | 'location'> & {
    location: NonNullable<GettingUserLocationDetected['location']>;
}) => [
    {
        $match: {
            ...(searchText?.length >= COMMON_MIN_SEARCH_LENGTH
                ? {
                      $or: [
                          { name: { $regex: searchText, $options: 'i' } },
                          { phone: searchText },
                      ],
                  }
                : {}),
            type: USER_TYPE.TASKER,
        },
    },
    {
        /**
         * - Adds a `filteredHistory` field by filtering each tasker's `history` array. The filter conditions include:
         * - The history entry's `time` is within the specified `rangeDate`.
         * - The distance between the history entry's location (`$$entry.lat`, `$$entry.lng`) and the provided `location` is less than or equal to the specified `radius`.
         * - Explanation of complex code: The distance calculation uses the Haversine formula,
         *   which accurately computes the great-circle distance between two points on a sphere given their longitudes and latitudes.
         *   This accounts for the Earth's curvature and provides an accurate measurement in meters.
         */
        $addFields: {
            filteredHistory: {
                $filter: {
                    input: '$history',
                    as: 'entry',
                    cond: {
                        $and: [
                            { $gte: ['$$entry.time', rangeDate.from] },
                            { $lte: ['$$entry.time', rangeDate.to] },
                            {
                                $lte: [
                                    {
                                        $multiply: [
                                            EARTH_RADIUS,
                                            {
                                                $acos: {
                                                    $add: [
                                                        {
                                                            $multiply: [
                                                                {
                                                                    $sin: {
                                                                        $degreesToRadians:
                                                                            location.lat,
                                                                    },
                                                                },
                                                                {
                                                                    $sin: {
                                                                        $degreesToRadians:
                                                                            '$$entry.lat',
                                                                    },
                                                                },
                                                            ],
                                                        },
                                                        {
                                                            $multiply: [
                                                                {
                                                                    $cos: {
                                                                        $degreesToRadians:
                                                                            location.lat,
                                                                    },
                                                                },
                                                                {
                                                                    $cos: {
                                                                        $degreesToRadians:
                                                                            '$$entry.lat',
                                                                    },
                                                                },
                                                                {
                                                                    $cos: {
                                                                        $subtract:
                                                                            [
                                                                                {
                                                                                    $degreesToRadians:
                                                                                        '$$entry.lng',
                                                                                },
                                                                                {
                                                                                    $degreesToRadians:
                                                                                        location.lng,
                                                                                },
                                                                            ],
                                                                    },
                                                                },
                                                            ],
                                                        },
                                                    ],
                                                },
                                            },
                                        ],
                                    },
                                    radius,
                                ],
                            },
                        ],
                    },
                },
            },
        },
    },
    {
        $addFields: {
            movingHistoryCount: {
                $cond: {
                    if: { $isArray: '$filteredHistory' },
                    then: { $size: '$filteredHistory' },
                    else: 0,
                },
            },
        },
    },
    {
        $match: {
            movingHistoryCount: { $gt: 0 },
        },
    },
];

export async function getAddressOnGoogleMapBySearchingText({
    searchText,
    isoCode,
}: {
    searchText: string;
    isoCode: IsoCode;
}) {
    if (searchText?.length <= COMMON_MIN_SEARCH_LENGTH) {
        return [];
    }

    const country = getCountryByIsoCode({ isoCode });
    const params = new URLSearchParams({
        fields: 'formatted_address,geometry,name',
        input: `${searchText}, ${country}`,
        inputtype: 'textquery',
        key: getEnvGoogleMapAPI().GOOGLE_API_KEY,
    });

    const response: GoogleMapApiResponse = await fetch(
        `${getEnvGoogleMapAPI().GOOGLE_MAP_URL}/api/place/findplacefromtext/json?${params.toString()}`,
        {
            method: 'GET',
        },
    )
        .then(res => {
            if (res.status === 200) {
                return res.json();
            }

            return Promise.reject(
                new Error(
                    `Google Map API Error [${res.status}]: ${res.statusText}`,
                ),
            );
        })
        .catch(error => {
            throw new Error(`Google Map API Error: ${error}`);
        });

    return response?.candidates || [];
}

export async function getTotalTaskerLocationDetected({
    isoCode,
    rangeDate,
    location,
    radius,
    searchText,
}: GettingUserLocationDetected): Promise<number> {
    if (!location?.lat || !location?.lng) {
        return 0;
    }

    const taskerLocations = await getModels(
        isoCode,
    ).userLocationHistory.aggregate([
        ...getPipelinesGetTaskerLocationDetected({
            rangeDate,
            location,
            radius,
            searchText,
        }),
        {
            $count: 'total',
        },
    ]);

    return taskerLocations?.[0]?.total || 0;
}

export async function getTaskerLocationDetected({
    isoCode,
    rangeDate,
    location,
    radius,
    sort,
    skip,
    limit,
    searchText,
}: GettingUserLocationDetected & {
    sort: PipelineStage.Sort['$sort'];
    skip: PipelineStage.Skip['$skip'];
    limit: PipelineStage.Limit['$limit'];
}) {
    if (!location?.lat || !location?.lng) {
        return [];
    }

    const taskerLocations = await getModels(
        isoCode,
    ).userLocationHistory.aggregate([
        ...getPipelinesGetTaskerLocationDetected({
            rangeDate,
            location,
            radius,
            searchText,
        }),
        {
            $sort: sort,
        },
        {
            $skip: skip,
        },
        {
            $limit: limit,
        },
        {
            $project: {
                userId: 1,
                phone: 1,
                name: 1,
                type: 1,
                userStatus: 1,
                movingHistoryCount: 1,
            },
        },
    ]);

    return taskerLocations;
}
