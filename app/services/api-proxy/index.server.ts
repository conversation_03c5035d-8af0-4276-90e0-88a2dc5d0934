import { EnumIsoCode } from 'schemas';

import id from './id.server';
import my from './my.server';
import th from './th.server';
import vn from './vn.server';

export enum API_KEY {
    UPDATE_TASKER_PROFILE = 'UPDATE_TASKER_PROFILE',
    UPDATE_EMPLOYEE_PROFILE = 'UPDATE_EMPLOYEE_PROFILE',
    GET_TRAINING_HISTORY_DETAIL = 'GET_TRAINING_HISTORY_DETAIL',
    GET_LIST_TRAINING_HISTORY = 'GET_LIST_TRAINING_HISTORY',
    UNBLOCK_TRAINING_HISTORY_MANUALLY = 'UNBLOCK_TRAINING_HISTORY_MANUALLY',
    UNBLOCK_TRAINING = 'UNBLOCK_TRAINING',
    UNBLOCK_TRAINING_ONCE = 'UNBLOCK_TRAINING_ONCE',
    GET_TASKER_MONEY = 'GET_TASKER_MONEY',
    PUSH_NOTIFICATION = 'PUSH_NOTIFICATION',
    WEBSOCKET_SEND_MESSAGE_NOTIFICATION = 'WEBSOCKET_SEND_MESSAGE_NOTIFICATION',
}

export type ApiUrl = {
    [k in Exclude<keyof typeof API_KEY, 'UNBLOCK_TRAINING_ONCE'>]: string;
} & {
    UNBLOCK_TRAINING_ONCE?: string; //NOTE: Send invitation currently not support in Malaysia
};

const storageApi: {
    [key in EnumIsoCode]: ApiUrl;
} = {
    [EnumIsoCode.VN]: vn,
    [EnumIsoCode.MY]: my,
    [EnumIsoCode.TH]: th,
    [EnumIsoCode.ID]: id,
};

function getRestApiByMultiRegion({
    apiKey,
    isoCode,
}: {
    apiKey: keyof typeof API_KEY;
    isoCode: EnumIsoCode;
}) {
    if (!(isoCode in EnumIsoCode)) {
        throw new Error(`Invalid ISO code: ${isoCode}`);
    }

    const endpoint = storageApi[isoCode][apiKey];

    if (!endpoint) {
        throw new Error(
            `API endpoint not found for ${apiKey} in region ${isoCode}`,
        );
    }

    return endpoint;
}

export default getRestApiByMultiRegion;
