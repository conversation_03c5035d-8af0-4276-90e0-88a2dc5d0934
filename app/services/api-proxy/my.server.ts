import { type ApiUrl } from './index.server';

const key: ApiUrl = {
    UPDATE_TASKER_PROFILE: 'v2/backend-user/update-tasker-profile',
    UPDATE_EMPLOYEE_PROFILE: '',
    WEBSOCKET_SEND_MESSAGE_NOTIFICATION:
        'v3/websocket-my/send-message-notification',
    PUSH_NOTIFICATION: 'v3/push-notification-my/send',
    GET_TRAINING_HISTORY_DETAIL:
        'v2/training-tasker-my/get-training-submission-history-detail',
    GET_LIST_TRAINING_HISTORY:
        'v2/training-tasker-my/get-training-submission-histories',
    UNBLOCK_TRAINING_HISTORY_MANUALLY:
        'v2/training-tasker-my/open-training-manually',
    UNBLOCK_TRAINING: 'v2/training-tasker-my/unblock-training',
    GET_TASKER_MONEY: 'v2/backend-user/get-tasker-money',
};

export default key;
