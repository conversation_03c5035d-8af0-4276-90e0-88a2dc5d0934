import { MIN_SEARCH_LENGTH_FOR_ALL_TASKER_PAGE } from 'btaskee-constants';
import type { PipelineStage } from 'mongo-connection';
import { getCollectionNameByIsoCode, getModels } from 'schemas';

import { USER_TYPE } from './constants.server';

interface AllTaskerParams {
    isoCode: `${IsoCode}`;
    filterValue: {
        search: string;
        status: string;
        services: string;
    };
    sort: PipelineStage.Sort['$sort'];
    skip: PipelineStage.Skip['$skip'];
    limit: PipelineStage.Limit['$limit'];
    city: string[];
}

function getPipelinesInAggregate({
    filterValue,
    city,
    isoCode,
    extraProjectField,
}: Pick<AllTaskerParams, 'filterValue' | 'city' | 'isoCode'> & {
    extraProjectField?: PipelineStage.Project['$project'];
}) {
    const pipelineGettingTaskers: Array<PipelineStage> = [
        {
            $match: {
                type: USER_TYPE.TASKER,
                'workingPlaces.city': { $in: city },
                $or: [
                    { phone: filterValue.search },
                    { name: { $regex: filterValue.search, $options: 'i' } },
                ],
                ...(filterValue.status
                    ? { status: { $in: filterValue.status.split(',') } }
                    : {}),
            },
        },
        {
            $lookup: {
                from: getCollectionNameByIsoCode(isoCode).serviceChannel,
                let: {
                    taskerId: '$_id',
                },
                pipeline: [
                    {
                        $match: {
                            $expr: {
                                $in: [
                                    '$$taskerId',
                                    { $ifNull: ['$taskerList', []] },
                                ],
                            },
                        },
                    },
                ],
                as: 'registeredServices',
            },
        },
        {
            $project: {
                registeredServices: {
                    $map: {
                        input: '$registeredServices',
                        as: 'service',
                        in: '$$service.serviceId',
                    },
                },
                ...extraProjectField,
            },
        },
    ];

    if (filterValue.services) {
        pipelineGettingTaskers.push({
            $match: {
                registeredServices: { $in: filterValue.services.split(',') },
            },
        });
    }

    return pipelineGettingTaskers;
}

// This is total base on filter table
export async function getTotalTaskerOnAllTaskerPage({
    filterValue,
    isoCode,
    city,
}: Pick<AllTaskerParams, 'filterValue' | 'isoCode' | 'city'>) {
    if (filterValue?.search?.length < MIN_SEARCH_LENGTH_FOR_ALL_TASKER_PAGE) {
        return 0;
    }

    const allTaskers = await getModels(isoCode).users.aggregate([
        ...getPipelinesInAggregate({
            city,
            isoCode,
            filterValue,
        }),
        { $count: 'total' },
    ]);

    return allTaskers?.[0]?.total > 0 ? allTaskers[0].total : 0;
}

export async function getListTaskerOnAllTaskerPage({
    isoCode,
    filterValue,
    sort,
    skip,
    limit,
    city,
}: AllTaskerParams) {
    if (filterValue?.search?.length < MIN_SEARCH_LENGTH_FOR_ALL_TASKER_PAGE) {
        return [];
    }

    const allTasker = await getModels(isoCode)
        .users.aggregate<
            Pick<
                UserApp,
                | '_id'
                | 'avatar'
                | 'name'
                | 'phone'
                | 'createdAt'
                | 'avgRating'
                | 'lastDoneTask'
                | 'status'
                | 'taskDone'
                | 'gender'
            > & { registeredServices: Array<Service['_id']> }
        >([
            ...getPipelinesInAggregate({
                city,
                isoCode,
                filterValue,
                extraProjectField: {
                    name: 1,
                    phone: 1,
                    avatar: 1,
                    createdAt: 1,
                    avgRating: 1,
                    lastDoneTask: 1,
                    status: 1,
                    taskDone: 1,
                    gender: 1,
                },
            }),
            {
                $sort: { ...sort, _id: 1 },
            },
            {
                $skip: skip,
            },
            {
                $limit: limit,
            },
        ])
        .exec();

    return Array.isArray(allTasker) ? allTasker : [];
}

// For 2 card total user and user active
export async function getTotalUsers({
    isoCode,
    userType,
    userStatus,
    cities,
}: {
    isoCode: IsoCode;
    userType: UserApp['type'];
    userStatus?: UserApp['status'];
    cities: Array<NonNullable<UserApp['cities']>[0]['city']>;
}) {
    const totalUser = await getModels(isoCode)
        .users.aggregate([
            {
                $match: {
                    type: userType,
                    'workingPlaces.city': { $in: cities },
                    ...(userStatus ? { status: userStatus } : {}),
                },
            },
            {
                $count: 'total',
            },
        ])
        .exec();

    return totalUser.length > 0 ? totalUser[0].total : 0;
}
