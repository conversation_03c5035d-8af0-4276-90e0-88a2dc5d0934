import { type PipelineStage } from 'mongo-connection';
import { getCollectionNameByIsoCode, getModels } from 'schemas';
import { newRecordCommonField } from '~/services/constants.server';

export interface GettingToolkitSetting {
    isoCode: IsoCode;
    search?: string;
    rangeDate: {
        from: Date;
        to: Date;
    };
    services?: string;
}

const getMatcher = ({
    search,
    services,
    rangeDate,
}: Omit<GettingToolkitSetting, 'isoCode'>) => {
    const matcher: PipelineStage.Match['$match'] = {};

    if (search) {
        matcher.$and = [
            {
                $or: [
                    { 'text.vi': { $regex: search, $options: 'i' } },
                    { 'text.en': { $regex: search, $options: 'i' } },
                    { 'text.ko': { $regex: search, $options: 'i' } },
                    { 'text.th': { $regex: search, $options: 'i' } },
                    { 'text.id': { $regex: search, $options: 'i' } },
                ],
            },
        ];
    }

    if (services) {
        if (!matcher.$and) matcher.$and = [];

        matcher.$and.push({
            serviceIds: { $in: services.split(',') },
        });
    }

    if (rangeDate) {
        if (!matcher.$and) matcher.$and = [];

        matcher.$and.push({
            createdAt: {
                $gte: rangeDate.from,
                $lte: rangeDate.to,
            },
        });
    }

    return matcher;
};

export async function getToolkitSettingDetail({
    isoCode,
    toolkitSettingId,
}: {
    isoCode: IsoCode;
    toolkitSettingId: ToolkitSetting['_id'];
}) {
    const toolkitSettingFound = await getModels(isoCode)
        .toolKitSetting.findById(toolkitSettingId)
        .lean<ToolkitSetting>();

    if (!toolkitSettingFound) {
        throw new Error('TOOLKIT_NOT_FOUND');
    }
    return toolkitSettingFound;
}

export async function getTotalToolkitSetting({
    isoCode,
    services,
    search,
    rangeDate,
}: GettingToolkitSetting) {
    return getModels(isoCode).toolKitSetting.countDocuments(
        getMatcher({
            services: services ?? '',
            search: search ?? '',
            rangeDate,
        }),
    );
}

export async function getListToolkitSetting({
    isoCode,
    search,
    services,
    rangeDate,
    sort,
    skip,
    limit,
}: GettingToolkitSetting & {
    sort: PipelineStage.Sort['$sort'];
    skip: PipelineStage.Skip['$skip'];
    limit: PipelineStage.Limit['$limit'];
}) {
    const matcher = getMatcher({
        services: services ?? '',
        search: search ?? '',
        rangeDate,
    });

    const toolkitSettings: Array<
        ToolkitSetting & {
            serviceInfos: Service[];
            toolkitItemsInfos: ToolKitItem[];
            totalPrice: number;
        }
    > = await getModels(isoCode)
        .toolKitSetting.aggregate([
            {
                $match: matcher,
            },
            {
                $lookup: {
                    from: getCollectionNameByIsoCode(isoCode).service,
                    localField: 'serviceIds',
                    foreignField: '_id',
                    as: 'serviceInfos',
                },
            },
            {
                $lookup: {
                    from: getCollectionNameByIsoCode(isoCode).toolKitItems,
                    localField: 'toolKitItems._id',
                    foreignField: '_id',
                    as: 'toolkitItemsInfos',
                },
            },
            {
                $addFields: {
                    totalPrice: {
                        $sum: '$toolkitItemsInfos.price',
                    },
                },
            },
            {
                $project: {
                    text: 1,
                    image: 1,
                    createdAt: 1,
                    updatedAt: 1,
                    toolKitItems: 1,
                    serviceInfos: 1,
                    toolkitItemsInfos: 1,
                    totalPrice: 1,
                },
            },
            {
                $sort: sort.price
                    ? { totalPrice: sort.price, ...sort, _id: 1 }
                    : { ...sort, _id: 1 },
            },
            {
                $skip: skip,
            },
            {
                $limit: limit,
            },
        ])
        .exec();

    return toolkitSettings || [];
}

export async function createToolkitSetting({
    isoCode,
    toolkitSetting,
}: {
    isoCode: IsoCode;
    toolkitSetting: Omit<ToolkitSetting, '_id' | 'createdAt'>;
}) {
    const createdToolkitSettingId = await getModels(
        isoCode,
    ).toolKitSetting.create({
        ...newRecordCommonField(),
        ...toolkitSetting,
    });

    return createdToolkitSettingId;
}

export async function updateToolkitSetting({
    isoCode,
    toolkitSettingId,
    updateInfos,
}: {
    isoCode: IsoCode;
    toolkitSettingId: ToolkitSetting['_id'];
    updateInfos: Partial<Omit<ToolkitSetting, '_id' | 'createdAt'>>;
}) {
    const toolkitSettingFoundById = await getModels(isoCode)
        .toolKitSetting.findById(toolkitSettingId)
        .lean<ToolkitSetting>();

    if (!toolkitSettingFoundById) {
        throw new Error('TOOLKIT_NOT_FOUND');
    }

    await getModels(isoCode).toolKitSetting.findByIdAndUpdate(
        toolkitSettingId,
        {
            $set: updateInfos,
        },
    );

    return {
        msg: 'UPDATE_TOOLKIT_SUCCESSFULLY',
    };
}

export async function getListToolkitItems({
    isoCode,
}: {
    isoCode: IsoCode;
}): Promise<ToolKitItem[]> {
    const toolkitItems = await getModels(isoCode).toolKitItems.find({});

    return toolkitItems || [];
}
