import { defaultLanguage } from 'btaskee-constants';
import { getEnvMailServer } from 'btaskee-dotenv';
import { getFutureTimeFromToday, momentTz } from 'btaskee-utils';
import { type PipelineStage } from 'mongo-connection';
import { v4 as uuidv4 } from 'uuid';
import ActionsHistoryModel from '~/services/model/actionHistory.server';
import UsersModel from '~/services/model/users.server';
import { sendSetupPasswordEmailForUser } from '~/services/utils.server';
import { sendEmail } from '~/third-party/mail.server';

import {
    EXPIRED_RESET_PASSWORD,
    newRecordCommonField,
    statusOriginal,
} from './constants.server';
import GroupsModel from './model/groups.server';
import {
    getGroupsOfUser,
    getUsersInGroupsByUserId,
} from './role-base-access-control.server';

interface ISearch {
    $match: {
        'user.username'?: {
            $regex: string;
            $options: string;
        };
    };
}

export async function getTotalActionsHistoryManageByManagerId({
    searchText,
    managerId,
}: {
    searchText: string;
    managerId: Users['_id'];
}) {
    const $search: ISearch = { $match: {} };

    if (searchText) {
        $search.$match['user.username'] = {
            $regex: searchText,
            $options: 'i',
        };
    }
    const userIdsManaged = await getUsersInGroupsByUserId(managerId);

    const result = await ActionsHistoryModel.aggregate<{ total: number }>([
        {
            $match: {
                $or: [
                    { actorId: { $in: userIdsManaged } },
                    { actorId: managerId },
                ],
            },
        },
        {
            $lookup: {
                from: 'users',
                localField: 'actorId',
                foreignField: '_id',
                as: 'user',
            },
        },
        {
            $unwind: {
                path: '$user',
                preserveNullAndEmptyArrays: true,
            },
        },
        $search,
        { $count: 'total' },
    ]).exec();

    return result.length > 0 ? result?.[0].total : 0;
}

export async function getActionsHistoryManagedByManagerId({
    skip,
    limit,
    projection,
    searchText,
    managerId,
}: {
    searchText: string;
    skip: PipelineStage.Skip['$skip'];
    limit: PipelineStage.Limit['$limit'];
    projection: PipelineStage.Project['$project'];
    managerId: Users['_id'];
}) {
    const $search: ISearch = { $match: {} };
    const userIdsManaged = await getUsersInGroupsByUserId(managerId);

    if (searchText) {
        $search.$match['user.username'] = {
            $regex: searchText,
            $options: 'i',
        };
    }

    const actionsHistory = await ActionsHistoryModel.aggregate<
        ActionsHistory & {
            user: Users;
        }
    >([
        {
            $match: {
                $or: [
                    { actorId: { $in: userIdsManaged } },
                    { actorId: managerId },
                ],
            },
        },
        {
            $lookup: {
                from: 'users',
                localField: 'actorId',
                foreignField: '_id',
                as: 'user',
            },
        },
        {
            $unwind: {
                path: '$user',
                preserveNullAndEmptyArrays: true,
            },
        },
        $search,
        { $sort: { createdAt: -1, _id: 1 } },
        { $project: { ...projection } },
        { $skip: skip },
        { $limit: limit },
    ]).exec();

    return actionsHistory;
}

export async function getTotalUsersManagedByManagerId(
    managerId: string,
    search: string,
    isSuperUser: boolean,
) {
    const userIds = await getUsersInGroupsByUserId(managerId);
    const query: PipelineStage.Match['$match'] = {};

    if (search) {
        query.$or = [
            { username: { $regex: search, $options: 'i' } },
            { email: { $regex: search, $options: 'i' } },
        ];
    }

    if (isSuperUser) {
        return await UsersModel.countDocuments({
            ...query,
        }).exec();
    }

    return await UsersModel.countDocuments({
        _id: { $in: userIds },
        ...query,
    }).exec();
}

export async function getUsersManagedByManagerId({
    skip,
    limit,
    sort,
    projection,
    managerId,
    search,
    isSuperUser,
}: {
    skip: PipelineStage.Skip['$skip'];
    limit: PipelineStage.Limit['$limit'];
    sort: PipelineStage.Sort['$sort'];
    projection: PipelineStage.Project['$project'];
    managerId: Users['_id'];
    search: string;
    isSuperUser: boolean;
}) {
    const query: PipelineStage.Match['$match'] = {};

    if (!isSuperUser) {
        const userIds = await getUsersInGroupsByUserId(managerId);
        query._id = { $in: userIds };
    }

    if (search) {
        query.$or = [
            { username: { $regex: search, $options: 'i' } },
            { email: { $regex: search, $options: 'i' } },
        ];
    }

    const users = await UsersModel.find(query, projection, {
        sort: { ...sort, _id: 1 },
        skip,
        limit,
    }).lean<Users[]>();

    const usersWithGroups = await Promise.all(
        users.map(async user => ({
            ...user,
            groupOfUser: await getGroupsOfUser({
                userId: user._id,
                projection: {
                    _id: 1,
                    name: 1,
                    iconType: 1,
                },
            }),
        })),
    );

    return usersWithGroups;
}

export function getUserProfile(_id: string) {
    return UsersModel.findOne(
        { _id },
        {
            language: 1,
            name: 1,
            isoCode: 1,
            username: 1,
            email: 1,
            cities: 1,
            avatarUrl: 1,
            status: 1,
        },
    ).lean<Users>();
}

// Helper function to update user groups and send an email
async function updateUserGroupsAndSendEmail({
    userId,
    groupIds,
    name,
    email,
}: {
    userId: Users['_id'];
    groupIds: Array<GroupsV2['_id']>;
    name: Users['name'];
    email: Users['email'];
}) {
    const [updateGroups, groupNames] = await Promise.all([
        GroupsModel.updateMany(
            {
                _id: { $in: groupIds },
            },
            {
                $push: {
                    users: {
                        _id: userId,
                        addedAt: momentTz().toDate(),
                        status: statusOriginal.ACTIVE,
                    },
                },
            },
        ),
        GroupsModel.find(
            {
                _id: { $in: groupIds },
            },
            { name: 1, _id: 0 },
        ),
    ]);

    if (!updateGroups) {
        throw new Error('INTERNAL_SERVER_ERROR');
    }

    const groupNameString = groupNames.map(group => group.name).join(', ');

    sendEmail({
        to: email,
        from: getEnvMailServer().MAIL_SERVER_USERNAME,
        subject: `Bạn đã được thêm vào nhóm ${groupNameString} trên hệ thống bTaskee`,
        html: `
    <!DOCTYPE html>
    <html lang="vi">
    <head>
      <meta charset="UTF-8">
      <meta name="viewport" content="width=device-width, initial-scale=1.0">
      <title>Thông Báo Thêm Vào Nhóm</title>
      <style>
        body {
          font-family: Arial, sans-serif;
          background-color: #f4f4f4;
          padding: 20px;
        }
        .container {
          max-width: 600px;
          margin: 0 auto;
          background-color: #fff;
          padding: 20px;
          border-radius: 8px;
          box-shadow: 0 0 10px rgba(0, 0, 0, 0.1);
        }
        .footer {
          margin-top: 20px;
          font-size: 0.9em;
          color: #777;
          text-align: center;
        }
      </style>
    </head>
    <body>
      <div class="container">
        <p>Chào <strong>${name}</strong>,</p>

        <p>Chúng tôi xin thông báo rằng bạn đã được thêm vào nhóm <strong>${groupNameString}</strong> trong hệ thống bTaskee.</p>

        <p>Bạn có thể truy cập vào nhóm của mình và bắt đầu công việc ngay lập tức. 
        Nếu bạn có bất kỳ câu hỏi nào, xin vui lòng liên hệ với quản trị viên nhóm hoặc đội ngũ hỗ trợ của chúng tôi.</p>

        <p>Chúng tôi rất mong đợi sự đóng góp của bạn trong nhóm!</p>

        <p>Trân trọng,</p>
        <p><strong>Đội ngũ bTaskee</strong></p>

        <div class="footer">
          <p>Đây là email tự động, vui lòng không trả lời.</p>
        </div>
      </div>
    </body>
    </html>
  `,
    });
}

export async function createNewUserWithGroups({
    username,
    email,
    cities,
    isoCode,
    groupIds,
    name,
}: Pick<Users, 'username' | 'email' | 'cities' | 'name'> & {
    groupIds: Array<string>;
    isoCode: IsoCode;
}) {
    const existingUser = await UsersModel.findOne({
        $or: [{ username }, { email }],
    });

    if (existingUser) {
        throw new Error('USER_ALREADY_EXISTS');
    }

    // If no user exists, create a new one
    const newUser = await createNewUser({
        email,
        name,
        username,
        cities: cities ?? [],
        isoCode,
    });

    await updateUserGroupsAndSendEmail({
        userId: newUser?._id,
        groupIds,
        name,
        email,
    });
    return newUser;
}

export async function setUserLanguage({
    language,
    userId,
}: Pick<Users, 'language'> & { userId: string }) {
    await UsersModel.updateOne(
        { _id: userId },
        {
            $set: {
                updatedAt: momentTz().toDate(),
                language,
            },
        },
    );
}

export async function changeUserAvatar({
    avatarUrl,
    userId,
}: Pick<Users, 'avatarUrl'> & { userId: string }) {
    await UsersModel.updateOne(
        { _id: userId },
        {
            $set: {
                updatedAt: momentTz().toDate(),
                avatarUrl,
            },
        },
    );
}

export async function createNewUser({
    username,
    name,
    email,
    cities,
    isoCode,
}: Pick<Users, 'username' | 'name' | 'email' | 'cities' | 'isoCode'>) {
    try {
        const resetToken = uuidv4();
        const newUser = await UsersModel.create({
            ...newRecordCommonField(),
            username,
            name,
            email,
            cities,
            isoCode,
            language: defaultLanguage,
            resetPassword: {
                expired: getFutureTimeFromToday(
                    EXPIRED_RESET_PASSWORD,
                    'minutes',
                ).toDate(),
                token: resetToken,
            },
        });

        await sendSetupPasswordEmailForUser({
            resetToken,
            email,
            username,
            name,
        });

        return newUser;
    } catch (error) {
        throw new Error('INTERNAL_SERVER_ERROR');
    }
}

export async function setupPasswordForExpiredLinkOfNewUser({
    email,
}: Pick<Users, 'email'>) {
    const resetToken = uuidv4();

    const updatedUser = await UsersModel.findOneAndUpdate(
        { email },
        {
            $set: {
                resetPassword: {
                    expired: getFutureTimeFromToday(
                        EXPIRED_RESET_PASSWORD,
                        'minutes',
                    ).toDate(),
                    token: resetToken,
                },
            },
        },
    );

    if (!updatedUser) {
        throw new Error('RESET_PASSWORD_FOR_USER_FAILED');
    }

    await sendSetupPasswordEmailForUser({
        resetToken,
        email,
        username: updatedUser.username,
        name: updatedUser.username,
    });
}
