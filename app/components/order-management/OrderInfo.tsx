import { FORM_TYPE, PAYMENT_TYPE, TYPE_LADING } from 'btaskee-constants';
import { useCopyToClipboard } from 'btaskee-hooks';
import { Button, Typography, cn } from 'btaskee-ui';
import { createUID, formatNumberWithCommas } from 'btaskee-utils';
import { CopyCheck } from 'lucide-react';
import { useTranslation } from 'react-i18next';

import { CopyWithRoundedBackgroundIcon } from '../common/FigmaIcon';

interface OrderInfoProps {
  order?: OrderDetail;
  typeOfForm: FORM_TYPE;
  currency: string;
}

const OrderInfo = ({ order, typeOfForm, currency }: OrderInfoProps) => {
  const { t } = useTranslation('order-management');
  const {
    isCopied: isCopiedYoutubeLink,
    copyToClipboard: copyYoutubeLinkToClipboard,
  } = useCopyToClipboard();

  return (
    <>
      <div className="flex flex-col gap-1 pb-3 border-b border-gray-200">
        <Typography className="font-medium text-xs text-gray-400">
          {t('TASKER_NAME')}
        </Typography>
        <Typography className="font-medium text-base text-gray-600">
          {order?.tasker?.name || ''}
        </Typography>
      </div>
      <div className="flex flex-col gap-1 pb-3 border-b border-gray-200">
        <Typography className="font-medium text-xs text-gray-400">
          {t('LIST_TOOL')}
        </Typography>
        <div>
          {order?.listTool?.map((tool: ToolItem, idx) => (
            <div key={createUID()} className="flex justify-between items-end">
              <Typography className="font-medium text-base text-gray-600">
                {tool?.text || ''}
                <span className="text-primary ml-2">
                  {tool?.quantity ? `x${tool.quantity}` : ''}
                </span>
              </Typography>
              <Typography className="font-medium text-base text-gray-600">
                {formatNumberWithCommas(tool?.price || 0) + currency}
              </Typography>
            </div>
          ))}
        </div>
      </div>
      <div className="flex flex-col gap-1 pb-3 border-b border-gray-200">
        <Typography className="font-medium text-xs text-gray-400">
          {t('AMOUNT')}
        </Typography>
        <Typography className="font-medium text-base text-gray-600">
          {formatNumberWithCommas(order?.amount || 0) + currency}
        </Typography>
      </div>
      <div
        className={`flex flex-col gap-1 ${typeOfForm === FORM_TYPE.VIEW ? 'pb-3 border-b border-gray-200' : ''}`}>
        <Typography className="font-medium text-xs text-gray-400">
          {typeOfForm === FORM_TYPE.VIEW
            ? t('DELIVERY_METHOD')
            : t('TYPE_OF_PAYMENT')}
        </Typography>
        <Typography className="font-medium text-base text-gray-600">
          {typeOfForm === FORM_TYPE.VIEW
            ? t(
                order?.ladingDetails?.typeOfLading === TYPE_LADING.OFFICE
                  ? 'IN_THE_OFFICE'
                  : 'DELIVERY',
              )
            : t(PAYMENT_TYPE[order?.type as keyof typeof PAYMENT_TYPE])}
        </Typography>
      </div>
      {typeOfForm === FORM_TYPE.VIEW &&
      order?.ladingDetails?.typeOfLading === TYPE_LADING.OFFICE ? (
        <div className="flex flex-col gap-1">
          <Typography className="font-medium text-xs text-gray-400">
            {t('OFFICE')}
          </Typography>
          <Typography className="font-medium text-base text-gray-600">
            {order?.ladingDetails?.placeOfReceipt || ''}
          </Typography>
        </div>
      ) : null}
      {typeOfForm === FORM_TYPE.VIEW &&
      order?.ladingDetails?.typeOfLading === TYPE_LADING.LADING ? (
        <>
          <div className="flex flex-col gap-1 pb-3 border-b border-gray-200">
            <Typography className="font-medium text-xs text-gray-400">
              {t('LADING_CODE_BILL')}
            </Typography>
            <Typography className="font-medium text-base text-gray-600 truncate">
              {order?.ladingDetails?.billOfLading || ''}
            </Typography>
          </div>
          <div className="flex flex-col gap-1">
            <Typography className="font-medium text-xs text-gray-400">
              {t('ORDER_TRACKING_URL')}
            </Typography>
            <div className="flex items-center justify-between gap-1">
              <Typography className="font-medium text-base text-gray-600 truncate">
                {order?.ladingDetails?.domesticRouting || ''}
              </Typography>
              <Button
                variant="ghost"
                size="icon"
                className={cn(
                  'w-[18px] h-[18px]',
                  isCopiedYoutubeLink ? 'text-green-500' : 'text-orange-500',
                )}
                onClick={() =>
                  copyYoutubeLinkToClipboard(
                    order?.ladingDetails?.domesticRouting || '',
                  )
                }>
                {isCopiedYoutubeLink ? (
                  <CopyCheck className="-scale-x-100" />
                ) : (
                  <CopyWithRoundedBackgroundIcon
                    props={{ className: 'min-h-7, min-w-7' }}
                  />
                )}
              </Button>
            </div>
          </div>
        </>
      ) : null}
    </>
  );
};

export { OrderInfo };
