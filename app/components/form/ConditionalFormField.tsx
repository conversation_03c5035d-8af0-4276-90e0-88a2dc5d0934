import { type ReactNode, useEffect } from 'react';
import {
  type FieldError,
  type RegisterOptions,
  type UseControllerProps,
  useController,
  useFormContext,
} from 'react-hook-form';

interface ConditionalFormFieldProps {
  name: UseControllerProps['name'];
  rules: RegisterOptions;
  shouldRender: boolean;
  render: (props: {
    field: {
      onChange: (value: MustBeAny) => void; //TODO: MustBeAny needs to be replaced with the correct type
      value: MustBeAny;
      name: string;
    };
    fieldState: { error?: FieldError };
  }) => ReactNode;
}

const ConditionalFormField = ({
  name,
  rules,
  shouldRender,
  render,
}: ConditionalFormFieldProps) => {
  const { control, unregister, resetField } = useFormContext();
  const { field, fieldState } = useController({ name, control, rules });

  useEffect(() => {
    if (!shouldRender) {
      unregister(name);

      resetField(name);
    }
  }, [shouldRender, unregister, name, resetField]);

  return shouldRender ? render({ field, fieldState }) : null;
};

export default ConditionalFormField;
