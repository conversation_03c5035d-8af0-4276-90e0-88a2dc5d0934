import { cn } from 'btaskee-ui';

interface DashedBorderButtonProps {
  label: string;
  onClick?: () => void;
  className?: string;
  disabled?: boolean;
}

export const DashedBorderButton = ({
  label,
  onClick,
  className,
  disabled = false,
}: DashedBorderButtonProps) => {
  return (
    <button
      disabled={disabled}
      type={'button'}
      onClick={onClick ?? undefined}
      className={cn(
        'w-full border border-dashed border-primary rounded-md px-6 py-7 flex gap-2 items-center justify-center mt-4',
        className,
      )}>
      <svg
        width="20"
        height="20"
        viewBox="0 0 20 20"
        fill="none"
        xmlns="http://www.w3.org/2000/svg">
        <path
          d="M10 4.16602V15.8327"
          stroke="#F97316"
          strokeWidth="1.66667"
          strokeLinecap="round"
          strokeLinejoin="round"
        />
        <path
          d="M4.1665 10H15.8332"
          stroke="#F97316"
          strokeWidth="1.66667"
          strokeLinecap="round"
          strokeLinejoin="round"
        />
      </svg>
      <span className={'text-primary font-medium'}>{label}</span>
    </button>
  );
};
