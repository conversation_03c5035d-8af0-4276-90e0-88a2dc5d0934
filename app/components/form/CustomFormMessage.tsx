import { FormMessage, Typography } from 'btaskee-ui';
import { useFormContext } from 'react-hook-form';

const CustomFormMessage = ({ name }: { name: string }) => {
  const {
    formState: { errors },
  } = useFormContext();
  const error = errors[name];

  if (!error || !error.message) {
    return null;
  }

  let parsedErrors: CustomValidationError[];
  if (typeof error.message === 'string') {
    try {
      parsedErrors = JSON.parse(error.message);
    } catch {
      return <FormMessage />;
    }
  } else {
    return <FormMessage />;
  }

  return (
    <div className="form-error-message h-60 overflow-y-auto py-2 pl-2 border border-destructive rounded-md">
      <Typography variant="h4" className="mb-2 text-destructive">
        CSV import errors
      </Typography>
      {parsedErrors.map((rowError, index) => (
        <div key={index} className="error-row">
          <strong>Row {rowError.row}:</strong>
          <ul>
            {rowError.errors.map((err, errIndex) => (
              <li key={errIndex}>{err}</li>
            ))}
          </ul>
        </div>
      ))}
    </div>
  );
};

export default CustomFormMessage;
