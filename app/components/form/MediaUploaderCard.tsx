import { Button, cn } from 'btaskee-ui';
import { Trash2 } from 'lucide-react';
import type { ReactNode } from 'react';
import { useTranslation } from 'react-i18next';

export const MediaUploaderCard = ({
  children,
  className,
  onClose,
  isShowCloseButton = true,
}: {
  children?: ReactNode;
  className?: string;
  isShowCloseButton?: boolean;
  onClose?: () => void;
}) => {
  const { t } = useTranslation('common');

  return (
    <div
      className={cn(
        `p-6 flex flex-col gap-6 border border-dashed rounded-2xl`,
        className,
      )}>
      {children}

      {isShowCloseButton && (
        <Button
          type={'button'}
          variant="ghost"
          onClick={onClose}
          className={
            'w-fit flex items-center gap-2 self-end text-sm text-gray-500 leading-[16px] h-8 -mt-2'
          }>
          <Trash2 className="w-4 h-4" />
          {t('REMOVE')}
        </Button>
      )}
    </div>
  );
};
