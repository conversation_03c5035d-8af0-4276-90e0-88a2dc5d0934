import {
  Card,
  CardContent,
  CardHeader,
  Checkbox,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
  Input,
  Typography,
} from 'btaskee-ui';
import { Trash2 } from 'lucide-react';
import type { Control } from 'react-hook-form';
import { useTranslation } from 'react-i18next';

interface AnswerProps {
  control: Control<MustBeAny>; //TODO: MustBeAny needs to be replaced with the correct type
  index: number;
  fieldId: string;
  remove: (index: number) => void;
  selectedCorrectAnswer: string;
  setSelectedCorrectAnswer: (id: string) => void;
}

export const AnswerCard = ({
  control,
  index,
  fieldId,
  remove,
  selectedCorrectAnswer,
  setSelectedCorrectAnswer,
}: AnswerProps) => {
  const { t } = useTranslation('quiz');

  return (
    <Card>
      <CardHeader
        className={
          '!p-4 bg-gray-100 flex flex-row justify-between items-center'
        }>
        <Typography
          variant={'h4'}
          className={'text-sm font-medium text-gray-700'}>
          {t('ANSWER')} {index + 1}
        </Typography>
        <div className={'flex items-center gap-2 !mt-0'}>
          <Trash2 className="h-4 w-4 text-gray-500" />
          <button
            type="button"
            className={'cursor-pointer text-gray-500 text-sm font-medium !mt-0'}
            onClick={() => remove(index)}>
            {t('REMOVE')}
          </button>
        </div>
      </CardHeader>
      <CardContent className={'p-4'}>
        <FormField
          name={`answers.${index}.content`}
          control={control}
          render={({ field }) => (
            <FormItem>
              <FormControl>
                <Input {...field} placeholder={t('ENTER_ANSWER')} />
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />
        <FormField
          name={`answers.${index}.isCorrect`}
          control={control}
          render={() => (
            <FormItem className="flex items-center justify-end mt-4 space-y-0 mb-2">
              <FormControl>
                <Checkbox
                  className="rounded-full relative data-[state=checked]:before:content-[''] data-[state=checked]:before:absolute data-[state=checked]:before:top-0 data-[state=checked]:before:left-0 data-[state=checked]:before:w-3.5 data-[state=checked]:before:h-3.5 data-[state=checked]:before:bg-primary data-[state=checked]:before:rounded-full data-[state=checked]:before:border-2 data-[state=checked]:before:border-white"
                  checked={selectedCorrectAnswer === fieldId}
                  onCheckedChange={() => {
                    setSelectedCorrectAnswer(fieldId);
                  }}
                />
              </FormControl>
              <FormLabel className={'text-gray-700 pl-2'}>
                {t('RIGHT_ANSWER')}
              </FormLabel>
              <FormMessage />
            </FormItem>
          )}
        />
      </CardContent>
    </Card>
  );
};
