import {
  AspectRatio,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
  Input,
  Textarea,
  Typography,
} from 'btaskee-ui';
import type { Control } from 'react-hook-form';
import { useTranslation } from 'react-i18next';
import { YoutubeVector } from '~/components/common/FigmaIcon';
import { MediaUploaderCard } from '~/components/form/MediaUploaderCard';

interface VideoUploaderCardProps {
  control: Control<FormCreateQuizCollection>;
  onClose: () => void;
}

export default function VideoUploaderCard({
  control,
  onClose,
}: VideoUploaderCardProps) {
  const { t } = useTranslation('common');

  return (
    <MediaUploaderCard className="pb-4" onClose={onClose}>
      <Typography
        variant="p"
        affects="removePMargin"
        className="text-sm text-gray-700 font-medium self-center">
        {t('UPLOAD_VIDEO')}
      </Typography>
      <div className="w-20 self-center">
        <AspectRatio ratio={1}>
          <YoutubeVector />
        </AspectRatio>
      </div>

      <FormField
        name={'video.url'}
        control={control}
        render={({ field }) => (
          <FormItem>
            <FormLabel className={'text-gray-700'}>
              {t('YOUTUBE_LINK')}
            </FormLabel>
            <FormControl>
              <Input {...field} placeholder={t('ENTER_YOUTUBE_LINK')} />
            </FormControl>
            <FormMessage />
          </FormItem>
        )}
      />

      <FormField
        name="video.description"
        control={control}
        render={({ field }) => (
          <FormItem>
            <FormLabel className={'text-gray-700'}>
              {t('VIDEO_DESCRIPTION')}
            </FormLabel>
            <FormControl>
              <Textarea placeholder={t('ENTER_VIDEO_DESCRIPTION')} {...field} />
              {/*<div className="px-1 pb-3 rounded-md border">*/}
              {/*  <BasicEditor onChange={field.onChange} />*/}
              {/*</div>*/}
            </FormControl>
            <FormMessage />
          </FormItem>
        )}
      />
    </MediaUploaderCard>
  );
}
