import { MAXIMUM_IMAGE_FILE_LENGTH_TRAINING } from 'btaskee-constants';
import {
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
  Textarea,
} from 'btaskee-ui';
import { useTranslation } from 'react-i18next';
import { MediaUploaderCard } from '~/components/form/MediaUploaderCard';
import { SimpleImageUpload } from '~/components/form/SimpleImageUpload';

interface ImageUploaderCardProps {
  form: MustBeAny; //TODO: Re-use Component so we cant declare exact type, need research later
  onClose: () => void;
  defaultValue?: {
    url?: string;
    value?: string;
    description: string;
  };
}

export default function ImageUploaderCard({
  form,
  onClose,
  defaultValue,
}: ImageUploaderCardProps) {
  const { t } = useTranslation('common');

  return (
    <MediaUploaderCard className="pb-4" onClose={onClose}>
      <FormField
        control={form.control}
        name="image.value"
        render={({ field: { onChange, ref }, fieldState }) => (
          <FormItem
            className={`flex flex-col items-center h-fit ${fieldState.error ? 'border-red-500' : 'border-gray-200'}`}>
            <FormControl>
              <SimpleImageUpload
                fieldRef={ref}
                avatarUrl={defaultValue?.url || ''}
                cardTitle={t('UPLOAD_IMAGE')}
                onFileChange={file => onChange(file)}
                maxContentLength={MAXIMUM_IMAGE_FILE_LENGTH_TRAINING.VALUE}
                description={t('IMAGE_MAX_SIZE', {
                  size: MAXIMUM_IMAGE_FILE_LENGTH_TRAINING.DISPLAY_TEXT,
                })}
                formValue={form.watch('image.value')}
              />
            </FormControl>
            <FormMessage />
          </FormItem>
        )}
      />
      <FormField
        name="image.description"
        control={form.control}
        render={({ field }) => (
          <FormItem>
            <FormLabel className={'text-gray-700'}>
              {t('DESCRIPTION')}
            </FormLabel>
            <FormControl>
              <Textarea placeholder={t('ENTER_DESCRIPTION')} {...field} />
            </FormControl>
            <FormMessage />
          </FormItem>
        )}
      />
    </MediaUploaderCard>
  );
}
