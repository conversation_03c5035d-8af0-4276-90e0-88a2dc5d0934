import DefaultImage from '@/images/default-image.svg';
import { AspectRatio, Button, Typography, toast } from 'btaskee-ui';
import { CloudUpload } from 'lucide-react';
import type { ChangeEvent } from 'react';
import { useCallback, useEffect, useRef, useState } from 'react';
import type { RefCallBack } from 'react-hook-form';
import { useTranslation } from 'react-i18next';

interface PortraitUploadProps {
  cardTitle?: string;
  onFileChange: (args: File) => void;
  description?: string;
  subDescription?: string;
  avatarUrl?: string;
  maxContentLength: number;
  ratio?: number;
  fieldRef?: RefCallBack;
  formValue: MustBeAny;
}

export const SimpleImageUpload = ({
  cardTitle,
  onFileChange,
  description,
  subDescription,
  avatarUrl = '',
  maxContentLength,
  ratio,
  fieldRef,
  formValue,
}: PortraitUploadProps) => {
  const { t } = useTranslation('common');

  const [imagePreview, setImagePreview] = useState<string>('');
  const fileInputWrapperRef = useRef<HTMLSpanElement>(null);

  const handleButtonClick = useCallback(() => {
    fileInputWrapperRef?.current?.querySelector('input')?.click();
  }, []);

  const handleFileChange = (event: ChangeEvent<HTMLInputElement>) => {
    const fileUpload = event.target.files?.[0];
    if (!fileUpload) return null;
    if (fileUpload.size > maxContentLength) {
      toast({ description: t('ERROR_BY_MAX_FILE_SIZE') });
      // Reset the input value. In case upload the same file name, the browser will cache the file, so onChange event will not trigger
      if (fileInputWrapperRef.current)
        // eslint-disable-next-line @typescript-eslint/no-non-null-assertion
        fileInputWrapperRef.current.querySelector('input')!.value = '';
      return;
    }

    const reader = new FileReader();
    reader.onloadend = () => {
      if (typeof reader.result === 'string') {
        setImagePreview(reader.result);
        onFileChange(fileUpload);
      }
    };
    reader.readAsDataURL(fileUpload);
  };

  useEffect(() => {
    if (!formValue) {
      setImagePreview('');
    } else if (avatarUrl && !imagePreview) {
      setImagePreview(avatarUrl);
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [formValue, avatarUrl]);

  return (
    <div className="relative w-full items-center flex flex-col gap-4">
      {cardTitle && (
        <Typography
          variant="p"
          affects="removePMargin"
          className="text-sm text-gray-700 font-medium">
          {cardTitle}
        </Typography>
      )}
      {/*
      Because input need fieldRef to using formSate, in case using custom UI with invisible input,
      we need to use a wrapperRef to get the value of input.
      */}
      <span ref={fileInputWrapperRef} className="absolute top-1/2">
        <input
          type="file"
          accept="image/*"
          ref={fieldRef}
          onChange={handleFileChange}
          className="opacity-0 -z-10 absolute top-1/2"
        />
      </span>

      {imagePreview || (formValue && avatarUrl) ? (
        <div
          className={`${ratio === 1 ? 'w-20 h-20 flex justify-center' : 'w-full'}`}>
          <AspectRatio ratio={ratio || 16 / 9}>
            <img
              src={imagePreview || avatarUrl}
              className="object-cover rounded-md h-full w-full"
              alt="ImageUpload"
            />
          </AspectRatio>
        </div>
      ) : (
        <div className="w-20">
          <AspectRatio ratio={1}>
            <img
              src={DefaultImage}
              className="object-cover rounded-md h-full"
              alt="DefaultImage"
            />
          </AspectRatio>
        </div>
      )}

      <div>
        {description && (
          <Typography
            className="text-center text-gray-400"
            variant="p"
            affects="removePMargin">
            {description}
          </Typography>
        )}
        {subDescription && (
          <Typography
            className="text-center text-gray-400"
            variant="p"
            affects="removePMargin">
            {subDescription}
          </Typography>
        )}
      </div>

      <Button
        color="primary"
        className="items-center flex gap-2 rounded-md bg-white border-[1px] border-primary text-primary"
        variant="outline"
        type="button"
        onClick={handleButtonClick}>
        <CloudUpload className="h-4 w-4" />
        {t('UPLOAD')}
      </Button>
    </div>
  );
};
