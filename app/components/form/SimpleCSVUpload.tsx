import { Button, Typography, toast } from 'btaskee-ui';
import { validateQuizCSVData } from 'btaskee-utils';
import { CloudUpload } from 'lucide-react';
import Papa from 'papaparse';
import type { ChangeEvent } from 'react';
import { useCallback, useRef } from 'react';
import type { RefCallBack } from 'react-hook-form';
import { useTranslation } from 'react-i18next';

interface CSVUploadProps {
  cardTitle?: string;
  onInputChange?: () => void;
  onFileValidData: (args: MustBeAny) => void; //TODO: MustBeAny needs to be replaced with the correct type
  onFileErrors: (args: MustBeAny) => void;
  onFileValidUploaded: (args: File) => void;
  description?: string;
  maxContentLength: number;
  fieldRef?: RefCallBack;
}

export const SimpleCSVUpload = ({
  cardTitle,
  onInputChange,
  onFileValidData,
  onFileErrors,
  description,
  maxContentLength,
  fieldRef,
  onFileValidUploaded,
}: CSVUploadProps) => {
  const { t } = useTranslation('common');
  const fileInputWrapperRef = useRef<HTMLSpanElement>(null);

  const handleButtonClick = useCallback(() => {
    fileInputWrapperRef?.current?.querySelector('input')?.click();
  }, []);

  const handleFileChange = (event: ChangeEvent<HTMLInputElement>) => {
    const fileUpload = event.target.files?.[0];
    if (!fileUpload) return null;
    if (fileUpload.size > maxContentLength) {
      toast({ description: t('ERROR_BY_MAX_FILE_SIZE') });
      return;
    }

    Papa.parse(fileUpload, {
      complete(results) {
        const validateErrors = validateQuizCSVData(results.data as CSVData[]);
        if (validateErrors.length) {
          onFileErrors(validateErrors);
        } else {
          onFileValidUploaded(fileUpload);
          onFileValidData(results.data);
        }

        // Reset the input value to allow re-uploading the same file name
        if (fileInputWrapperRef.current) {
          // eslint-disable-next-line @typescript-eslint/no-non-null-assertion
          fileInputWrapperRef.current.querySelector('input')!.value = '';
        }
      },
      header: true,
      skipEmptyLines: true,
    });
  };

  return (
    <div className="relative">
      {cardTitle && (
        <Typography
          variant="p"
          affects="removePMargin"
          className="text-sm text-gray-700 font-medium">
          {cardTitle}
        </Typography>
      )}

      <span ref={fileInputWrapperRef} className="absolute top-1/2">
        <input
          type="file"
          accept=".csv,text/csv"
          ref={fieldRef}
          onChange={e => {
            onInputChange && onInputChange();
            handleFileChange(e);
          }}
          className="opacity-0 -z-10 absolute top-1/2"
        />
      </span>

      {description && (
        <Typography
          className="text-center text-gray-400"
          variant="p"
          affects="removePMargin">
          {description}
        </Typography>
      )}

      <Button
        color="primary"
        className="items-center flex gap-2 rounded-md bg-white border-[1px] border-primary text-primary hover:text-primary"
        variant="outline"
        type="button"
        onClick={handleButtonClick}>
        <CloudUpload className="h-4 w-4" />
        {t('UPLOAD_CSV_FILE')}
      </Button>
    </div>
  );
};
