import CSVTemplate from '@/templates/example-quizzes.csv';
import type { FetcherWithComponents } from '@remix-run/react';
import { useNavigate } from '@remix-run/react';
import { BlockDescription } from 'app/components/tasker-common';
import {
  MAXIMUM_CSV_FILE_LENGTH,
  PERMISSIONS,
  ROUTE_NAME,
} from 'btaskee-constants';
import { useGlobalStore } from 'btaskee-hooks';
import {
  AspectRatio,
  Button,
  Card,
  CardContent,
  Dialog,
  DialogClose,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
  Input,
  Separator,
  Switch,
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
  Textarea,
  TimePickerInput,
  Typography,
} from 'btaskee-ui';
import {
  downloadFileFromClient,
  momentTz,
  parseCSVToQuizzes,
} from 'btaskee-utils';
import type { TFunction } from 'i18next';
import { Download, Plus, Trash2 } from 'lucide-react';
import type { Dispatch, SetStateAction } from 'react';
import { useEffect, useState } from 'react';
import type {
  Control,
  UseFormReturn,
  UseFormSetError,
  UseFormSetValue,
} from 'react-hook-form';
import { useTranslation } from 'react-i18next';
import {
  CreateDocumentIcon,
  DocumentUploadedIcon,
  DoubleCheckIcon,
  ExternalDocumentIcon,
  UploadDocumentActiveIcon,
  UploadDocumentIcon,
} from '~/components/common/FigmaIcon';
import { DashedBorderButton } from '~/components/form/DashedBorderButton';
import { MediaUploaderCard } from '~/components/form/MediaUploaderCard';
import { SimpleCSVUpload } from '~/components/form/SimpleCSVUpload';
import ImageUploaderCard from '~/components/form/cards/ImageUploaderCard';
import VideoUploaderCard from '~/components/form/cards/VideoUploaderCard';

interface TableQuizListProps {
  quizList: MustBeAny[]; //TODO: MustBeAny needs to be replaced with the correct type
  onRemoveQuiz: (quiz: MustBeAny) => void;
}

const TableQuizList = ({ quizList, onRemoveQuiz }: TableQuizListProps) => {
  const { t } = useTranslation('quiz');

  return (
    <Table className="rounded-md border">
      <TableHeader className="bg-gray-100 rounded-t-md ">
        <TableRow>
          {['NUMBER', 'QUIZ_CODE', 'QUESTION', 'ACTION'].map((header, idx) => (
            <TableHead
              key={idx}
              className={`${header === 'ACTION' ? 'w-48 text-center' : 'whitespace-nowrap'}`}>
              <Typography
                variant="p"
                affects="removePMargin"
                className="text-gray-700 font-medium">
                {t(header)}
              </Typography>
            </TableHead>
          ))}
        </TableRow>
      </TableHeader>
      <TableBody className="max-h-[400px] overflow-y-auto">
        {quizList.length ? (
          quizList.map((quiz, index) => (
            <TableRow key={quiz._id || quiz.code}>
              <TableCell width={100}>
                <Typography
                  variant="p"
                  affects="removePMargin"
                  className="text-gray-700">
                  {index + 1}
                </Typography>
              </TableCell>
              <TableCell width={100}>
                <Typography
                  variant="p"
                  affects="removePMargin"
                  className="text-gray-700">
                  {quiz.code}
                </Typography>
              </TableCell>
              <TableCell>
                <Typography
                  variant="p"
                  affects="removePMargin"
                  className="text-gray-700">
                  {/*This still name, not title because complex logic was handle before*/}
                  {/*TODO: refactor to title to sync value with database*/}
                  {quiz?.title || quiz.name}
                </Typography>
              </TableCell>
              <TableCell className="flex gap-4 items-center w-fit">
                <Dialog>
                  <DialogTrigger asChild>
                    <Button
                      variant="ghost"
                      className="flex items-center gap-2 text-primary hover:text-primary hover:bg-primary-50 px-1 py-0.5 h-8">
                      <ExternalDocumentIcon /> {t('VIEW')}
                    </Button>
                  </DialogTrigger>
                  <DialogContent className="max-w-5xl max-h-[90vh] overflow-y-auto">
                    <DialogHeader className="space-y-2">
                      <DialogTitle className="text-xl tracking-tighter font-semibold">
                        {t('QUIZ_DETAIL')}
                      </DialogTitle>
                    </DialogHeader>

                    <div className="grid grid-cols-2 gap-4">
                      <Card className="bg-gray-100">
                        <CardContent className="p-4 flex flex-col gap-3">
                          <BlockDescription
                            desc={{
                              label: t('QUIZ_CODE'),
                              value: quiz.code,
                            }}
                            variant="sm"
                          />
                          <Separator />
                          <BlockDescription
                            desc={{
                              label: t('QUESTION'),
                              value: quiz?.title || quiz.name,
                            }}
                            variant="sm"
                          />
                          <Separator />
                          <div className="flex items-center gap-4">
                            <Switch
                              checked={quiz.isRandomAnswer}
                              className="cursor-default opacity-50"
                            />
                            <Typography
                              variant="p"
                              affects="removePMargin"
                              className="text-gray-600 text-sm">
                              {t('RANDOM_ANSWER')}
                            </Typography>
                          </div>
                        </CardContent>
                      </Card>
                      <Card className="bg-gray-100">
                        <CardContent className="p-6 flex flex-col gap-5">
                          {quiz.image && !quiz.isNewQuiz ? (
                            <>
                              <AspectRatio ratio={16 / 9}>
                                <img
                                  src={quiz.image.url}
                                  alt={quiz.image.description}
                                  className="object-cover h-full rounded-md"
                                />
                              </AspectRatio>
                              <BlockDescription
                                desc={{
                                  label: t('DESCRIPTION'),
                                  value: quiz.image.description,
                                }}
                              />
                            </>
                          ) : !quiz.image ? (
                            <Typography
                              variant="p"
                              affects="removePMargin"
                              className="text-gray-700 text-center">
                              {t('THIS_QUIZ_HAS_NO_IMAGE')}
                            </Typography>
                          ) : (
                            <Typography
                              variant="p"
                              affects="removePMargin"
                              className="text-gray-700 text-center">
                              {t(
                                'THE_IMAGE_WILL_BE_UPLOADED_AFTER_CREATING_QUIZ_COLLECTION',
                              )}
                            </Typography>
                          )}
                        </CardContent>
                      </Card>

                      <Separator className="col-span-2" />

                      {quiz.answers?.map(
                        (
                          answer: {
                            content: string;
                            isCorrect: boolean;
                          },
                          idx: number,
                        ) => (
                          <Card
                            key={idx}
                            className={`bg-gray-100 border-dashed ${answer?.isCorrect ? 'border-secondary-foreground bg-secondary' : 'border-red-500 bg-red-50'}`}>
                            <CardContent className="p-6">
                              <BlockDescription
                                desc={{
                                  label: t('ANSWER_INDEX', { index: idx + 1 }),
                                  value: answer?.content,
                                }}
                              />
                            </CardContent>
                          </Card>
                        ),
                      )}
                      <div className="col-span-2 flex justify-end">
                        <DialogClose asChild>
                          <Button type="button">{t('CLOSE')}</Button>
                        </DialogClose>
                      </div>
                    </div>
                  </DialogContent>
                </Dialog>
                <Button
                  type="button"
                  variant="ghost"
                  className="flex items-center gap-2 text-gray-500 hover:text-gray-600 hover:bg-gray-100 px-1 py-0.5 h-8"
                  onClick={() => onRemoveQuiz(quiz)}>
                  <Trash2 className="w-4 h-4" /> {t('REMOVE')}
                </Button>
              </TableCell>
            </TableRow>
          ))
        ) : (
          <TableRow>
            <TableCell colSpan={4}>
              <Typography
                variant="p"
                affects="removePMargin"
                className="text-gray-700 text-center">
                {t('NO_DATA')}
              </Typography>
            </TableCell>
          </TableRow>
        )}
      </TableBody>
    </Table>
  );
};

const QuizCSVUploaderCard = ({
  control,
  setValue,
  setError,
  selectedQuizzes,
  setSelectedQuizzes,
  uploadedCSVFileInfo,
  setUploadedCSVFileInfo,
  fetcherCheckUniqueCode,
  setExistQuizList,
}: {
  control: Control<FormCreateQuizCollection>;
  setValue: UseFormSetValue<MustBeAny>;
  setError: UseFormSetError<FormCreateQuizCollection>;
  selectedQuizzes: MustBeAny[]; //TODO: MustBeAny needs to be replaced with the correct type
  setSelectedQuizzes: (quizzes: MustBeAny[]) => void;
  uploadedCSVFileInfo: File | undefined;
  setUploadedCSVFileInfo: (file: File | undefined) => void;
  fetcherCheckUniqueCode: FetcherWithComponents<MustBeAny>;
  setExistQuizList: Dispatch<SetStateAction<string>>;
}) => {
  const { t } = useTranslation('quiz');
  const [parsedValidData, setParsedValidData] = useState<
    (Quiz & { isNewQuiz: boolean; isCSVQuiz: boolean })[]
  >([]);
  const [validFileUploaded, setValidFileUploaded] = useState<
    File | undefined
  >();

  useEffect(() => {
    const existingCodes = fetcherCheckUniqueCode?.data?.listExist;
    if (existingCodes?.length > 0) {
      setExistQuizList(
        t('EXISTING_CODE', {
          codes: existingCodes.map((item: MustBeAny) => item.code).join(', '),
        }),
      );
      setUploadedCSVFileInfo(undefined);
      setSelectedQuizzes(selectedQuizzes.filter(quiz => !quiz.isCSVQuiz));
      setValue('csvUpload', []);
    } else if (
      parsedValidData.length &&
      !selectedQuizzes.some(quiz =>
        selectedQuizzes
          .filter(quiz => quiz.isCSVQuiz)
          .map(quiz => quiz.code)
          .includes(quiz.code),
      )
    ) {
      setExistQuizList('');
      setUploadedCSVFileInfo(validFileUploaded);
      const updatedQuizzes = parsedValidData.map((quiz, index) => ({
        ...quiz,
        quizOrder: selectedQuizzes.length + index,
      }));
      setSelectedQuizzes([...selectedQuizzes, ...updatedQuizzes]);
      setError('csvUpload', { type: 'manual', message: '' });
      setValue('csvUpload', updatedQuizzes);
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [t, fetcherCheckUniqueCode?.data]);

  return (
    <MediaUploaderCard
      className={`items-center gap-4 mt-2 ${uploadedCSVFileInfo ? 'border-primary bg-primary-50' : ''}`}
      isShowCloseButton={false}>
      <Typography
        variant="p"
        affects="removePMargin"
        className="text-sm text-gray-700 font-medium self-center">
        {t('IMPORT_CSV')}
      </Typography>
      <div className="w-20 self-center">
        <AspectRatio ratio={1}>
          {validFileUploaded ? (
            <UploadDocumentActiveIcon props={{ className: 'w-20 h-20 p-2' }} />
          ) : (
            <UploadDocumentIcon props={{ className: 'w-20 h-20 p-2' }} />
          )}
        </AspectRatio>
      </div>
      {!uploadedCSVFileInfo ? (
        <Typography
          variant="p"
          affects="removePMargin"
          className="text-sm text-gray-700 font-medium self-center">
          {t('MAX_FILE_SIZE', { size: MAXIMUM_CSV_FILE_LENGTH.DISPLAY_TEXT })}
        </Typography>
      ) : (
        <div className="flex items-center gap-2">
          <DocumentUploadedIcon />
          <Typography
            variant="p"
            affects="removePMargin"
            className="text-sm text-gray-700 font-medium self-center">
            {uploadedCSVFileInfo.name}
          </Typography>
          <DoubleCheckIcon />
          <button
            type="button"
            className={'cursor-pointer'}
            onClick={() => {
              setUploadedCSVFileInfo(undefined);
              setValue('csvUpload', []);
              setValidFileUploaded(undefined);
              setExistQuizList('');
              setSelectedQuizzes(
                selectedQuizzes.filter(quiz => !quiz.isCSVQuiz),
              );
            }}>
            <Trash2 className="text-gray-500 w-4 h-4 ml-2" />
          </button>
        </div>
      )}

      <div className="flex gap-4 text-primary">
        <FormField
          control={control}
          name={'csvUpload'}
          render={({ field: { onChange, ref }, fieldState }) => (
            <FormItem
              className={`max-w-[50%] flex flex-col items-end h-fit ${fieldState.error ? 'border-red-500' : 'border-gray-200'} ${uploadedCSVFileInfo ? 'hidden' : ''}`}>
              <FormControl>
                <SimpleCSVUpload
                  fieldRef={ref}
                  onFileErrors={(errors: CustomValidationError[]) => {
                    setExistQuizList('');
                    setError('csvUpload', {
                      type: 'manual',
                      message: JSON.stringify(errors),
                    });
                    setUploadedCSVFileInfo(undefined);
                  }}
                  onFileValidData={data => {
                    const parsedCSVToQuizzes: MustBeAny =
                      parseCSVToQuizzes(data);
                    if (parsedCSVToQuizzes.length) {
                      setExistQuizList('');
                      setParsedValidData(parsedCSVToQuizzes);
                      fetcherCheckUniqueCode.load(
                        `${ROUTE_NAME.CHECK_QUIZ_UNIQUE_CODE}?codes=${parsedCSVToQuizzes
                          .map((quiz: MustBeAny) => quiz.code)
                          .join(',')}`,
                      );
                    }
                  }}
                  onFileValidUploaded={setValidFileUploaded}
                  maxContentLength={MAXIMUM_CSV_FILE_LENGTH.VALUE}
                />
              </FormControl>
            </FormItem>
          )}
        />

        <Button
          type="button"
          variant="outline"
          className="border-primary hover:text-primary gap-2"
          onClick={() =>
            downloadFileFromClient(CSVTemplate, 'example-quizzes.csv')
          }>
          <Download className="w-4 h-4" />
          {t('DOWNLOAD_TEMPLATE')}
        </Button>
      </div>
    </MediaUploaderCard>
  );
};

interface TimeToCompleteByMinutesFieldProps {
  form: UseFormReturn<FormCreateQuizCollection, MustBeAny, undefined>;
  t: TFunction<string, undefined>;
  defaultValue?: number;
}

const TimeToCompleteByMinutesField = ({
  form,
  t,
  defaultValue,
}: TimeToCompleteByMinutesFieldProps) => {
  //TODO: update minutes -> hours and seconds -> minutes to match with the schema
  const [minutes, setMinutes] = useState<Date | undefined>(
    momentTz()
      .startOf('day')
      .add(Math.floor(defaultValue ? defaultValue / 60 : 0), 'minutes')
      .toDate(),
  );
  const [seconds, setSeconds] = useState<Date | undefined>(
    momentTz()
      .startOf('day')
      .add(Math.floor(defaultValue ? defaultValue % 60 : 0), 'seconds')
      .toDate(),
  );

  return (
    <div className={`space-y-2 relative`}>
      <FormLabel className={'text-gray-700'}>
        {t('TIME_TO_COMPLETE')} <span className="italic">(HH:MM)</span>
      </FormLabel>
      <div className="flex items-center gap-2">
        <FormField
          name={'timeToCompleteByMinutes.hours'}
          control={form.control}
          render={({ field }) => (
            <FormItem>
              <FormControl>
                <TimePickerInput
                  className="font-sans"
                  picker="minutes"
                  date={minutes}
                  setDate={date => {
                    if (date) {
                      setMinutes(date);
                      if (date.getMinutes() > 0 || date.getSeconds() > 0) {
                        form.clearErrors('timeToCompleteByMinutes');
                      } else if (
                        !(date.getMinutes() > 0 || date.getSeconds() > 0) &&
                        form.getValues('timeToCompleteByMinutes.minutes') === 0
                      ) {
                        form.setError('timeToCompleteByMinutes', {
                          type: 'manual',
                          message: t('MIN_MINUTE_TO_COMPLETE', {
                            time: 1,
                          }),
                        });
                      }
                      field.onChange(date.getMinutes());
                    }
                  }}
                />
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />
        <span>:</span>
        <FormField
          name={'timeToCompleteByMinutes.minutes'}
          control={form.control}
          render={({ field }) => (
            <FormItem>
              <FormControl>
                <TimePickerInput
                  className="font-sans"
                  picker="seconds"
                  date={seconds}
                  setDate={date => {
                    if (date) {
                      setSeconds(date);
                      //if date have value, clear timeToCompleteByMinutes error
                      if (date.getMinutes() > 0 || date.getSeconds() > 0) {
                        form.clearErrors('timeToCompleteByMinutes');
                      } else if (
                        !(date.getMinutes() > 0 || date.getSeconds() > 0) &&
                        form.getValues('timeToCompleteByMinutes.hours') === 0
                      ) {
                        form.setError('timeToCompleteByMinutes', {
                          type: 'manual',
                          message: t('MIN_MINUTE_TO_COMPLETE', {
                            time: 1,
                          }),
                        });
                      }
                      field.onChange(date.getSeconds());
                    }
                  }}
                />
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />
      </div>
      {form.getFieldState('timeToCompleteByMinutes').error && (
        <Typography
          variant="p"
          affects="removePMargin"
          className="text-sm font-medium text-destructive">
          {form.getFieldState('timeToCompleteByMinutes')?.error?.root
            ?.message ||
            form.getFieldState('timeToCompleteByMinutes')?.error?.message}
        </Typography>
      )}
    </div>
  );
};

interface QuizzesMediaSectionProps {
  t: TFunction<string, undefined>;
  form: UseFormReturn<FormCreateQuizCollection, MustBeAny, undefined>;
  defaultValue?: {
    image?:
      | {
          value: string;
          description: string;
        }
      | {
          url: string;
          description: string;
        };
    video?: {
      url: string;
      description: string;
    };
  };
}

const QuizzesMediaSection = ({
  t,
  form,
  defaultValue,
}: QuizzesMediaSectionProps) => {
  const [isShowImage, setIsShowImage] = useState(defaultValue?.image || false);
  const [isShowVideo, setIsShowVideo] = useState(defaultValue?.video || false);

  return (
    <>
      <div className="space-y-2">
        <div className="flex justify-between items-center h-6">
          <FormLabel className={'text-gray-700'}>{t('IMAGE')}</FormLabel>
          <Typography
            variant="p"
            affects="removePMargin"
            className="text-sm text-gray-400 leading-tight">
            {t('OPTIONAL')}
          </Typography>
        </div>

        {!isShowImage && (
          <DashedBorderButton
            label={t('ADD_IMAGE')}
            onClick={() => {
              setIsShowImage(true);
              setIsShowVideo(false);
              form.setValue('image', {
                value: null,
                description: '',
              });
              form.setValue('video', null);
            }}
          />
        )}
        {isShowImage && (
          <ImageUploaderCard
            defaultValue={defaultValue?.image ?? { description: '' }}
            onClose={() => {
              setIsShowImage(false);
              form.setValue('image', null);
              form.trigger('image');
            }}
            form={form}
          />
        )}
      </div>

      <div className="space-y-2">
        <div className="flex justify-between items-center h-6">
          <FormLabel className={'text-gray-700'}>{t('VIDEO')}</FormLabel>
          <Typography
            variant="p"
            affects="removePMargin"
            className="text-gray-400 leading-tight">
            {t('OPTIONAL')}
          </Typography>
        </div>

        {!isShowVideo && (
          <DashedBorderButton
            label={t('ADD_VIDEO')}
            onClick={() => {
              setIsShowVideo(true);
              setIsShowImage(false);
              form.setValue('video', {
                url: '',
                description: '',
              });
              form.setValue('image', null);
            }}
          />
        )}

        {isShowVideo && (
          <VideoUploaderCard
            control={form.control}
            onClose={() => {
              setIsShowVideo(false);
              form.setValue('video', null);
            }}
          />
        )}
      </div>
    </>
  );
};

interface QuestionDisplayConfigsProps {
  t: TFunction<string, undefined>;
  control: Control<FormCreateQuizCollection>;
  totalSelectedQuizzes: number;
}

const QuestionDisplayConfigs = ({
  t,
  control,
  totalSelectedQuizzes = 1,
}: QuestionDisplayConfigsProps) => (
  <>
    <FormField
      name="numberOfDisplayQuizzes"
      control={control}
      render={({ field }) => (
        <FormItem>
          <FormLabel className={'text-gray-700'}>
            {t('NUMBER_OF_QUESTIONS_DISPLAYED')}
          </FormLabel>
          <FormControl>
            <Input
              {...field}
              type="number"
              min={0}
              max={totalSelectedQuizzes}
              placeholder={t('ENTER_NUMBER_OF_QUESTIONS')}
            />
          </FormControl>
          <FormMessage />
        </FormItem>
      )}
    />
    <FormField
      name="isRandomQuizzes"
      control={control}
      render={({ field }) => (
        <FormItem>
          <FormControl>
            <div className="h-full flex items-center gap-4">
              <Switch checked={field.value} onCheckedChange={field.onChange} />
              {t('RANDOM_QUESTIONS')}
            </div>
          </FormControl>
          <FormMessage />
        </FormItem>
      )}
    />
  </>
);

interface QuizzesGeneralInfoProps {
  t: TFunction<string, undefined>;
  control: Control<FormCreateQuizCollection>;
}

const QuizzesGeneralInfo = ({ t, control }: QuizzesGeneralInfoProps) => {
  return (
    <>
      <FormField
        name="code"
        control={control}
        render={({ field }) => (
          <FormItem>
            <FormLabel className={'text-gray-700'}>
              {t('QUIZ_COLLECTION_CODE')}
            </FormLabel>
            <FormControl>
              <Input {...field} placeholder={t('ENTER_QUIZ_COLLECTION_CODE')} />
            </FormControl>
            <FormMessage />
          </FormItem>
        )}
      />
      <FormField
        name={'name'}
        control={control}
        render={({ field }) => (
          <FormItem>
            <FormLabel className={'text-gray-700'}>
              {t('QUIZ_COLLECTION_NAME')}
            </FormLabel>
            <FormControl>
              <Input {...field} placeholder={t('ENTER_QUIZ_COLLECTION_NAME')} />
            </FormControl>
            <FormMessage />
          </FormItem>
        )}
      />
      <FormField
        name={'description'}
        control={control}
        render={({ field }) => (
          <FormItem>
            <FormLabel className={'text-gray-700'}>
              {t('DESCRIPTION')}
            </FormLabel>
            <FormControl>
              <Textarea placeholder={t('ENTER_DESCRIPTION')} {...field} />
            </FormControl>
            <FormMessage />
          </FormItem>
        )}
      />
    </>
  );
};

interface TableListQuizOfCollectionProps {
  t: TFunction<string, undefined>;
  selectedQuizzes: MustBeAny[]; //TODO: MustBeAny needs to be replaced with the correct type
  setSelectedQuizzes: Dispatch<
    SetStateAction<(QuizWillBeAddedToCollection & { isCSVQuiz?: boolean })[]>
  >;
  form: UseFormReturn<FormCreateQuizCollection, MustBeAny, undefined>;
  loaderFetcherCheckUniqueCode: MustBeAny;
  setExistQuizList: Dispatch<SetStateAction<string>>;
}

const TableListQuizOfCollection = ({
  t,
  form,
  loaderFetcherCheckUniqueCode,
  selectedQuizzes,
  setSelectedQuizzes,
  setExistQuizList,
}: TableListQuizOfCollectionProps) => {
  const permissions = useGlobalStore(state => state.permissions);
  const [uploadedCSVFileInfo, setUploadedCSVFileInfo] = useState<File>();
  const navigate = useNavigate();

  return (
    <section className={'flex flex-col gap-4'}>
      <div className="flex items-center">
        <Typography
          variant={'h4'}
          className={'mb-4 text-xl tracking-tighter font-semibold'}>
          {t('QUIZ_LIST')}
        </Typography>
        <div className="ml-auto flex gap-4 items-center text-primary">
          {permissions?.includes(PERMISSIONS.WRITE_TRAINING_QUIZ) ? (
            <Button
              type="button"
              variant="outline"
              className="border-primary hover:text-primary gap-2"
              onClick={() =>
                navigate(ROUTE_NAME.CREATE_QUIZ_ON_CREATE_QUIZZES_VIA_DIALOG, {
                  replace: true,
                  preventScrollReset: true,
                })
              }>
              <CreateDocumentIcon />
              {t('CREATE_NEW_QUIZ')}
            </Button>
          ) : null}
          <Button
            type="button"
            variant="outline"
            className="border-primary hover:text-primary gap-2"
            onClick={() =>
              navigate(ROUTE_NAME.ADD_QUIZ_LIST_ON_CREATE_QUIZZES_VIA_DIALOG, {
                replace: true,
                preventScrollReset: true,
              })
            }>
            <Plus className="w-5 h-5" />
            {t('ADD_QUIZ_LIST')}
          </Button>
        </div>
      </div>

      <TableQuizList
        quizList={selectedQuizzes}
        onRemoveQuiz={quiz => {
          setSelectedQuizzes(
            selectedQuizzes.filter(item => item._id !== quiz._id),
          );
        }}
      />

      <QuizCSVUploaderCard
        control={form.control}
        setError={form.setError}
        setValue={form.setValue}
        selectedQuizzes={selectedQuizzes}
        setSelectedQuizzes={setSelectedQuizzes}
        fetcherCheckUniqueCode={loaderFetcherCheckUniqueCode}
        uploadedCSVFileInfo={uploadedCSVFileInfo}
        setUploadedCSVFileInfo={setUploadedCSVFileInfo}
        setExistQuizList={setExistQuizList}
      />
    </section>
  );
};

interface FormFooterProps {
  t: TFunction<string, undefined>;
  form: UseFormReturn<FormCreateQuizCollection, MustBeAny, undefined>;
  isEdit: boolean;
}

const FormFooterQuizzes = ({ form, t, isEdit = false }: FormFooterProps) => {
  return (
    <div className="w-full flex flex-col items-end gap-2">
      {form.getFieldState('quizzes').error && (
        <Typography
          variant="p"
          affects="removePMargin"
          className="text-red-500">
          {form.getFieldState('quizzes')?.error?.message}
        </Typography>
      )}
      <FormButton isEdit={isEdit} t={t} />
    </div>
  );
};

const FormFooterQuiz = ({
  form,
  t,
  isEdit = false,
}: {
  form: UseFormReturn<FormQuiz, MustBeAny, undefined>;
  t: TFunction<string, undefined>;
  isEdit: boolean;
}) => {
  return (
    <div className="w-full flex flex-col items-end gap-2">
      {form.getFieldState('answers').error && (
        <Typography
          variant="p"
          affects="removePMargin"
          className="text-red-500">
          {form.getFieldState('answers')?.error?.root?.message}
        </Typography>
      )}
      <FormButton isEdit={isEdit} t={t} />
    </div>
  );
};

const FormButton = ({
  isEdit = false,
  t,
}: {
  isEdit: boolean;
  t: TFunction<string, undefined>;
}) => {
  const navigate = useNavigate();

  return (
    <div className="flex justify-end gap-4">
      <Button
        type="button"
        variant="outline"
        className="border-primary text-primary hover:text-primary"
        onClick={() => navigate(-1)}>
        {t('CANCEL')}
      </Button>

      <Button type="submit" variant={'default'}>
        {t(isEdit ? 'SAVE_CHANGE' : 'SUBMIT')}
      </Button>
    </div>
  );
};

export {
  FormButton,
  FormFooterQuiz,
  FormFooterQuizzes,
  QuizCSVUploaderCard,
  QuizzesGeneralInfo,
  QuizzesMediaSection,
  TableListQuizOfCollection,
  TableQuizList,
  TimeToCompleteByMinutesField,
  QuestionDisplayConfigs,
};
