import type { SerializeFrom } from '@remix-run/node';
import {
  Link,
  Outlet,
  useNavigate,
  useParams,
  useSubmit,
} from '@remix-run/react';
import type { Column, ColumnDef, Row } from '@tanstack/react-table';
import {
  CONDITION,
  DISPLAY_POSITION,
  ROUTE_NAME,
  STATUS,
  TASKER_STAR,
  TYPE,
} from 'btaskee-constants';
import {
  <PERSON>ton,
  Card,
  CardContent,
  CardHeader,
  ControllerCombobox,
  DataTableBasic,
  DataTableColumnHeader,
  Form,
  FormControl,
  FormDescription,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
  FormSubmissionButton,
  Input,
  Label,
  MultiSelectAdvance,
  SelectBase,
  Separator,
  Switch,
  Typography,
  useConfirm,
} from 'btaskee-ui';
import {
  calculateDays,
  convertToSeconds,
  formatTimeToCompleteFromSecond,
} from 'btaskee-utils';
import i18n from 'i18next';
import {
  CircleX,
  Clock,
  Plus,
  SquareArrowOutUpRight,
  Trash2,
} from 'lucide-react';
import { useEffect, useMemo, useState } from 'react';
import { Controller, type UseFormReturn } from 'react-hook-form';
import { useTranslation } from 'react-i18next';
import type { willBecomeTaskerTrainingCourseLoader } from '~/hooks/useGetListTaskerTrainingCourse';
import type { getCities } from '~/services/helpers.server';

type QuizCol = Pick<
  QuizCollection,
  '_id' | 'title' | 'code' | 'timeToCompleteByMinutes' | 'quizzes'
>;

interface CourseFormProps {
  form: UseFormReturn<FormCreateCourse>;
  services: SerializeFrom<
    typeof willBecomeTaskerTrainingCourseLoader
  >['services'];
  allCourseExcludeCourseId: Array<Pick<Course, '_id' | 'title' | 'code'>>;
  quizCollection?: QuizCol[];
  isEdit?: boolean;
  cities: SerializeFrom<typeof getCities>;
}

export default function CourseForm({
  form,
  services,
  allCourseExcludeCourseId,
  quizCollection,
  isEdit,
  cities,
}: CourseFormProps) {
  const { t } = useTranslation('course');
  const navigate = useNavigate();
  const confirm = useConfirm();

  const submit = useSubmit();

  const [openDialog, setOpenDialog] = useState(true);
  const [selectedQuizzes, setSelectedQuizzes] = useState<
    CourseFormProps['quizCollection']
  >(quizCollection || []);
  const { control, handleSubmit, watch, setValue, clearErrors } = form;

  const watchCondition = watch('condition.optionCondition');
  const watchType = watch('type');

  const params = useParams();

  const columns: ColumnDef<
    NonNullable<CourseFormProps['quizCollection']>[0]
  >[] = useMemo(
    () => [
      {
        accessorKey: 'order',
        header: ({ column }) => (
          <DataTableColumnHeader column={column} title={t('NUMBER_ORDER')} />
        ),
        cell: ({ row }) => <span>{row.index + 1}</span>,
        size: 90,
        enableSorting: false,
      },
      {
        accessorKey: 'code',
        header: ({ column }) => (
          <DataTableColumnHeader column={column} title={t('CODE')} />
        ),
        size: 120,
        cell: ({ row }) => (
          <span className="text-gray-800">{row.original.code}</span>
        ),
        enableSorting: false,
      },
      {
        accessorKey: 'title',
        header: ({ column }) => (
          <DataTableColumnHeader
            column={column}
            title={t('QUIZ_COLLECTION_NAME')}
          />
        ),
        cell: ({ row }) => (
          <span className="text-gray-800">{row.original.title}</span>
        ),
        size: 794,
        enableSorting: false,
      },
      {
        accessorKey: 'numberOfQuiz',
        header: ({ column }: { column: Column<QuizCol> }) => (
          <DataTableColumnHeader column={column} title={t('NUMBER_OF_QUIZ')} />
        ),
        cell: ({ row }: { row: Row<QuizCol> }) => (
          <span className="text-gray-800">
            {row.original.quizzes?.length || '-'}
          </span>
        ),
        size: 120,
        enableSorting: false,
      },
      {
        accessorKey: 'action',
        header: ({ column }) => (
          <DataTableColumnHeader
            column={column}
            className="text-center"
            title={t('ACTION')}
          />
        ),
        cell: ({ row }) => {
          return (
            <div className="flex gap-5 justify-center">
              <Button
                type="button"
                variant="ghost"
                className="text-primary hover:text-primary hover:bg-primary-50 gap-2 h-6 py-0.5"
                asChild>
                <Link
                  to={`${ROUTE_NAME.TEST_AND_REVIEW}/${isEdit ? `${params.id}/edit` : 'create'}/${row.original._id}/view-quizzes-dialog`}
                  replace
                  preventScrollReset>
                  <SquareArrowOutUpRight className="w-4 h-4" />
                  {t('VIEW')}
                </Link>
              </Button>
              <Button
                type="button"
                variant="ghost"
                className="text-gray-500 hover:text-gray-600 hover:bg-gray-200 gap-2 h-6 py-0.5"
                onClick={() => {
                  setSelectedQuizzes(prev =>
                    prev?.filter(quiz => quiz._id !== row.original._id),
                  );
                  row.toggleSelected(false);
                }}>
                <Trash2 className="w-4 h-4" />
                {t('REMOVE')}
              </Button>
            </div>
          );
        },
        size: 150,
        enableSorting: false,
      },
    ],
    [t, isEdit, params.id],
  );

  const onSubmit = async (data: FormCreateCourse) => {
    const formData = new FormData();
    const pureData = {
      code: data?.code,
      title: data?.title,
      status: data?.status,
      relatedServices: data?.relatedServices,
      type: data?.type,
      ...(data?.condition?.optionCondition !== CONDITION.NONE
        ? {
            condition: {
              ...(data?.condition?.optionCondition ===
                CONDITION.WHEN_COMPLETE && {
                coursesMustBeCompleted: {
                  courseIds: [
                    data?.condition?.coursesMustBeCompleted?.courseIds,
                  ],
                },
              }),
              ...(data?.condition?.optionCondition ===
                CONDITION.TASKER_STAR && {
                byTasker: {
                  minimumStar: Number(data?.condition?.byTasker?.minimumStar),
                },
              }),
              ...(data?.condition?.optionCondition ===
                CONDITION.MANUALLY_UNBLOCK && {
                manuallyUnblock: data?.condition?.manuallyUnblock,
              }),
            },
          }
        : {}),
      ...(data?.type === TYPE.TEST && {
        timeToCompleteByMinutes: convertToSeconds(
          data?.timeToCompleteByMinutes,
        ),
        maximumNumberOfRetries: data?.maximumNumberOfRetries,
        percentageToPass: data?.percentageToPass / 100,
        displayPosition: data?.displayPosition,
      }),
      ...(data?.type === TYPE.REVIEW && {
        isDisplayAllAnswer: Boolean(data?.isDisplayAllAnswer),
        percentageToPass: data?.percentageToPass / 100,
      }),
      ...(data?.numberDays &&
        data?.typeOfDays && {
          deadlineIn: calculateDays({
            numberDays: Number(data?.numberDays),
            typeOfDays: data?.typeOfDays,
          }),
        }),
      quizCollections: selectedQuizzes?.map((quiz, index) => ({
        _id: quiz._id,
        order: index,
      })),
      cities: data.cities,
    };

    formData.append('data', JSON.stringify(pureData));

    const isConfirm = await confirm({
      title: isEdit ? t('UPDATE_COURSE') : t('CREATE_COURSE'),
      body: isEdit
        ? t('ARE_YOU_SURE_UPDATE_THIS_RECORD')
        : t('ARE_YOU_SURE_CREATE_THIS_RECORD'),
      actionButton: t('SUBMIT'),
      cancelButton: t('CANCEL'),
    });

    if (isConfirm) submit(formData, { method: 'post' });
  };

  useEffect(() => {
    const totalSeconds = selectedQuizzes?.reduce(
      (acc, quiz) => acc + (quiz.timeToCompleteByMinutes || 0),
      0,
    );

    const timeToCompleteByMinutes = formatTimeToCompleteFromSecond(
      totalSeconds || 0,
    );

    setValue('timeToCompleteByMinutes', timeToCompleteByMinutes);

    setValue('selectedQuizzes', selectedQuizzes || []);
  }, [selectedQuizzes, setValue]);

  useEffect(() => {
    if (selectedQuizzes?.length) {
      clearErrors('selectedQuizzes');
    }
  }, [clearErrors, selectedQuizzes?.length]);

  return (
    <>
      <Form {...form}>
        <form onSubmit={handleSubmit(onSubmit)}>
          <Typography className="mb-4 mt-6" variant="h4">
            {t('GENERAL_INFORMATION')}
          </Typography>

          <div className="grid grid-cols-3 gap-6">
            <FormField
              name="code"
              rules={{
                required: t('REQUIRED'),
                maxLength: {
                  value: 20,
                  message: t('MAXIMUM_CHARACTERS', { number: 20 }),
                },
                pattern: {
                  value: /^[a-zA-Z0-9]*$/,
                  message: t('MUST_NOT_CONTAIN_SPACES_OR_SPECIAL_CHARACTER'),
                },
              }}
              control={control}
              render={({ field }) => (
                <FormItem>
                  <FormLabel className="text-gray-700">{t('CODE')}</FormLabel>
                  <FormControl>
                    <Input placeholder={t('ENTER_CODE')} {...field} />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />
            <FormField
              name={'title'}
              rules={{ required: t('REQUIRED') }}
              control={control}
              render={({ field }) => (
                <FormItem>
                  <FormLabel className="text-gray-700">
                    {t('COURSE_NAME')}
                  </FormLabel>
                  <FormControl>
                    <Input {...field} placeholder={t('ENTER_COURSE_NAME')} />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />
            <FormField
              name={'status'}
              rules={{ required: t('REQUIRED') }}
              control={control}
              render={({ field: { onChange, value } }) => (
                <FormItem>
                  <FormLabel text-gray-700>{t('STATUS')}</FormLabel>
                  <FormControl>
                    <SelectBase
                      defaultValue={value}
                      onValueChange={onChange}
                      options={Object.values(STATUS).map(status => ({
                        label: t(status),
                        value: status,
                      }))}
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />
            <FormField
              name="relatedServices"
              rules={{ required: t('REQUIRED') }}
              control={control}
              render={({ field: { onChange, value } }) => (
                <FormItem>
                  <FormLabel className="text-gray-700">
                    {t('SERVICE')}
                  </FormLabel>
                  <FormControl>
                    <MultiSelectAdvance
                      placeholder={t('SELECT_SERVICE')}
                      onValueChange={selectedValues => {
                        const formattedValues = selectedValues
                          ?.map(value => {
                            const service = services.find(
                              service => service._id === value,
                            );
                            return service
                              ? { _id: service._id, name: service?.name }
                              : null;
                          })
                          .filter(Boolean);
                        onChange(formattedValues);
                      }}
                      defaultValue={
                        value
                          ? value?.map(
                              (service: { _id: string }) => service._id,
                            )
                          : []
                      }
                      options={services?.map(service => ({
                        label: `${service?.text?.[i18n.language || 'en']}${service.isSubscription ? ' (Subscription)' : ''}`,
                        value: service._id,
                      }))}
                      variant="blue"
                      maxCount={2}
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />
            <FormField
              name="type"
              rules={{ required: t('REQUIRED') }}
              control={control}
              render={({ field: { onChange, value } }) => (
                <FormItem>
                  <FormLabel className="text-gray-700">{t('TYPE')}</FormLabel>
                  <FormControl>
                    <SelectBase
                      backgroundColor={isEdit ? 'bg-gray-400' : ''}
                      disabled={!!isEdit}
                      defaultValue={value}
                      onValueChange={onChange}
                      options={Object.values(TYPE).map(type => ({
                        label: t(type),
                        value: type,
                      }))}
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />
            <FormField
              name="cities"
              control={control}
              render={({ field: { onChange, value } }) => (
                <FormItem>
                  <FormLabel className="text-gray-700">{t('CITY')}</FormLabel>
                  <FormControl>
                    <MultiSelectAdvance
                      options={cities?.map(city => ({
                        label: city,
                        value: city,
                      }))}
                      onValueChange={onChange}
                      defaultValue={value || []}
                      placeholder={t('CHOOSE_CITY')}
                      variant="blue"
                      maxCount={1}
                    />
                  </FormControl>
                  <FormDescription>
                    {t('TOOL_TIP_APPLY_FOR_ALL_CITIES')}
                  </FormDescription>
                  <FormMessage />
                </FormItem>
              )}
            />
            <Card>
              <CardHeader className="rounded-t-lg bg-gray-100 p-4">
                <Label className="text-gray-700">
                  {t('DISPLAY_CONDITION')}
                </Label>
              </CardHeader>
              <CardContent className="flex flex-col gap-4 p-4">
                <FormField
                  name="condition.optionCondition"
                  rules={{ required: t('REQUIRED') }}
                  control={control}
                  render={({ field: { onChange, value } }) => (
                    <FormItem>
                      <FormControl>
                        <SelectBase
                          defaultValue={value}
                          onValueChange={val => {
                            if (val === CONDITION.MANUALLY_UNBLOCK) {
                              setValue(
                                'condition.manuallyUnblock.taskerIdsAllowed',
                                [],
                              );
                            }

                            onChange(val);
                          }}
                          options={Object.values(CONDITION)
                            .filter(
                              condition =>
                                !(
                                  watchType === TYPE.REVIEW &&
                                  condition === CONDITION.MANUALLY_UNBLOCK
                                ),
                            )
                            .map(condition => ({
                              label: t(condition),
                              value: condition,
                            }))}
                        />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                {watchCondition === CONDITION.WHEN_COMPLETE ? (
                  <>
                    <Separator />
                    <FormField
                      name="condition.coursesMustBeCompleted.courseIds"
                      rules={{ required: t('REQUIRED') }}
                      control={control}
                      render={({ field }) => (
                        <FormItem>
                          <ControllerCombobox
                            options={allCourseExcludeCourseId?.map(course => ({
                              label: `${course.code} - ${course.title}`,
                              value: course._id,
                            }))}
                            placeholder={t('SELECT_COURSE')}
                            searchPlaceholder={t('FIND_COURSE')}
                            emptyMessage={t('NO_COURSE_FOUND')}
                            field={field}
                          />
                          <FormMessage />
                        </FormItem>
                      )}
                    />
                  </>
                ) : null}
                {watchCondition === CONDITION.TASKER_STAR ? (
                  <>
                    <Separator />
                    <FormField
                      name="condition.byTasker.minimumStar"
                      rules={{ required: t('REQUIRED') }}
                      control={control}
                      render={({ field: { onChange, value } }) => (
                        <FormItem>
                          <FormControl>
                            <SelectBase
                              placeholder={t('SELECT_TASKER_STAR')}
                              defaultValue={value?.toString() || ''}
                              onValueChange={onChange}
                              options={TASKER_STAR?.map(taskerStar => ({
                                label: t(taskerStar.label),
                                value: taskerStar.value.toString(),
                              }))}
                            />
                          </FormControl>
                          <FormMessage />
                        </FormItem>
                      )}
                    />
                  </>
                ) : null}
              </CardContent>
            </Card>
          </div>

          <Separator className="my-8" />

          <Typography className="mb-4 mt-6" variant="h4">
            {t('SPECIFIC')}
          </Typography>
          {watchType === TYPE.TEST ? (
            <div className="grid grid-cols-3 gap-6">
              <FormField
                name="timeToCompleteByMinutes"
                control={control}
                render={({ field }) => (
                  <FormItem>
                    <FormLabel className="text-gray-700">
                      {t('TOTAL_TIME_TO_COMPLETE_THE_TEST')}
                    </FormLabel>
                    <FormControl>
                      <Input
                        {...field}
                        disabled
                        endAdornment={<Clock className="w-4 h-4" />}
                        className="text-gray-600"
                        backgroundColor="bg-gray-400"
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
              <FormField
                name="maximumNumberOfRetries"
                rules={{
                  required: t('REQUIRED'),
                  validate: value =>
                    value > 0 || t('VALUE_MUST_GREATER_THAN_0'),
                }}
                control={control}
                render={({ field }) => (
                  <FormItem>
                    <FormLabel className="text-gray-700">
                      {t('MAXIMUM_NUMBER_OF_TIMES')}
                    </FormLabel>
                    <FormControl>
                      <Input
                        {...field}
                        type="number"
                        placeholder={t('ENTER_VALUE')}
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
              <FormField
                name="percentageToPass"
                rules={{
                  required: t('REQUIRED'),
                  validate: {
                    validPercentage: value => {
                      const num = Number(value);
                      return !isNaN(num) &&
                        num >= 50 &&
                        num <= 100 &&
                        (num - 50) % 5 === 0
                        ? true
                        : t('PERCENTAGE_50_100_STEP_5');
                    },
                  },
                }}
                control={control}
                render={({ field }) => (
                  <FormItem>
                    <FormLabel className="text-gray-700">
                      {t('PERCENTAGE_TO_PASS')}
                    </FormLabel>
                    <FormControl>
                      <Input
                        value={field.value}
                        onChange={field.onChange}
                        type="number"
                        endAdornment={<span>%</span>}
                        placeholder={t('ENTER_VALUE')}
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
              <div
                className={`space-y-2 ${form.getFieldState('timeToCompleteByMinutes').error ? 'pb-7' : ''}`}>
                <div className="flex justify-between items-center h-6">
                  <Label className="text-gray-700">{t('TEST_DEADLINE')}</Label>
                  <div className="flex items-center gap-2">
                    <Button
                      type="button"
                      variant="link"
                      className="border-primary text-primary gap-1"
                      onClick={() => {
                        setValue('numberDays', '');
                        setValue('typeOfDays', '');

                        clearErrors(['numberDays', 'typeOfDays']);
                      }}>
                      <CircleX width={16} height={16} />
                      Clear
                    </Button>
                    <Typography
                      variant="p"
                      affects="removePMargin"
                      className="text-sm text-gray-400 leading-tight">
                      {t('OPTIONAL')}
                    </Typography>
                  </div>
                </div>
                <div className="flex gap-2">
                  <FormField
                    name="numberDays"
                    rules={{
                      validate: value => {
                        if (value === '' || value === undefined) {
                          return true;
                        }
                        return (
                          Number(value) > 0 || t('VALUE_MUST_GREATER_THAN_0')
                        );
                      },
                    }}
                    control={control}
                    render={({ field }) => (
                      <FormItem>
                        <FormControl>
                          <Input
                            value={field.value}
                            onChange={field.onChange}
                            type="number"
                            placeholder={t('ENTER_VALUE')}
                          />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                  <FormField
                    name="typeOfDays"
                    control={control}
                    rules={{
                      validate: value => {
                        const numberDays = form.getValues('numberDays');
                        if (numberDays && !value) {
                          return t('REQUIRED');
                        }
                        return true;
                      },
                    }}
                    render={({ field: { onChange, value } }) => (
                      <FormItem className="w-full">
                        <FormControl>
                          <SelectBase
                            placeholder={t('DAYS_WEEKS_MONTHS')}
                            defaultValue={value}
                            value={value}
                            onValueChange={onChange}
                            options={[
                              { label: t('DAY'), value: 'days' },
                              { label: t('WEEK'), value: 'weeks' },
                              { label: t('MONTH'), value: 'months' },
                            ]}
                          />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                </div>
              </div>
              <FormField
                name="displayPosition"
                rules={{ required: t('REQUIRED') }}
                control={control}
                render={({ field: { onChange, value } }) => (
                  <FormItem>
                    <FormLabel className="text-gray-700">
                      {t('DISPLAY_POSITION')}
                    </FormLabel>
                    <FormControl>
                      <SelectBase
                        placeholder={t('CHOOSE_DISPLAY_POSITION')}
                        defaultValue={value}
                        onValueChange={onChange}
                        options={Object.values(DISPLAY_POSITION).map(
                          position => ({
                            label: t(position),
                            value: position,
                          }),
                        )}
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
            </div>
          ) : null}
          {watchType === TYPE.REVIEW ? (
            <div className="grid grid-cols-3 gap-6">
              <div className="flex items-center gap-3 space-y-0">
                <Controller
                  control={control}
                  name="isDisplayAllAnswer"
                  render={({ field: { onChange, value } }) => {
                    return (
                      <Switch checked={!!value} onCheckedChange={onChange} />
                    );
                  }}
                />
                <Label className="text-gray-700">{t('DISPLAY_ALL_QA')}</Label>
              </div>

              <FormField
                name="percentageToPass"
                rules={{
                  required: t('REQUIRED'),
                  validate: {
                    validPercentage: value => {
                      const num = Number(value);
                      return !isNaN(num) &&
                        num >= 50 &&
                        num <= 100 &&
                        (num - 50) % 5 === 0
                        ? true
                        : t('PERCENTAGE_50_100_STEP_5');
                    },
                  },
                }}
                control={control}
                render={({ field }) => (
                  <FormItem>
                    <FormLabel className="text-gray-700">
                      {t('PERCENTAGE_TO_PASS')}
                    </FormLabel>
                    <FormControl>
                      <Input
                        value={field.value}
                        onChange={field.onChange}
                        type="number"
                        endAdornment={<span>%</span>}
                        placeholder={t('ENTER_VALUE')}
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
              <div
                className={`space-y-2 ${form.getFieldState('timeToCompleteByMinutes').error ? 'pb-7' : ''}`}>
                <div className="flex justify-between items-center h-6">
                  <Label className="text-gray-700">{t('TEST_DEADLINE')}</Label>
                  <div className="flex items-center gap-2">
                    <Button
                      type="button"
                      variant="link"
                      className="border-primary text-primary gap-1"
                      onClick={() => {
                        setValue('numberDays', '');
                        setValue('typeOfDays', '');

                        clearErrors(['numberDays', 'typeOfDays']);
                      }}>
                      <CircleX width={16} height={16} />
                      Clear
                    </Button>
                    <Typography
                      variant="p"
                      affects="removePMargin"
                      className="text-sm text-gray-400 leading-tight">
                      {t('OPTIONAL')}
                    </Typography>
                  </div>
                </div>
                <div className="flex gap-2">
                  <FormField
                    name="numberDays"
                    rules={{
                      validate: value => {
                        if (value === '' || value === undefined) {
                          return true;
                        }
                        return (
                          Number(value) > 0 || t('VALUE_MUST_GREATER_THAN_0')
                        );
                      },
                    }}
                    control={control}
                    render={({ field }) => (
                      <FormItem>
                        <FormControl>
                          <Input
                            value={field.value}
                            onChange={field.onChange}
                            type="number"
                            placeholder={t('ENTER_VALUE')}
                          />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                  <FormField
                    name="typeOfDays"
                    control={control}
                    rules={{
                      validate: value => {
                        const numberDays = form.getValues('numberDays');
                        if (numberDays && !value) {
                          return t('REQUIRED');
                        }
                        return true;
                      },
                    }}
                    render={({ field: { onChange, value } }) => (
                      <FormItem className="w-full">
                        <FormControl>
                          <SelectBase
                            placeholder={t('DAYS_WEEKS_MONTHS')}
                            defaultValue={value}
                            value={value}
                            onValueChange={onChange}
                            options={[
                              { label: t('DAY'), value: 'days' },
                              { label: t('WEEK'), value: 'weeks' },
                              { label: t('MONTH'), value: 'months' },
                            ]}
                          />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                </div>
              </div>
            </div>
          ) : null}

          <Separator className="my-8" />

          <section className="flex flex-col gap-4">
            <div className="flex items-center justify-between">
              <Typography variant="h3">{t('QUIZ_COLLECTION_LIST')}</Typography>
              <Button
                type="button"
                variant="outline"
                className="border-primary text-primary gap-2"
                asChild>
                <Link
                  replace
                  preventScrollReset
                  to={`${ROUTE_NAME.TEST_AND_REVIEW}${isEdit ? `/${params.id}/edit` : '/create'}/add-quizzes-dialog`}>
                  <Plus />
                  {t('ADD_QUIZ_COLLECTION')}
                </Link>
              </Button>
            </div>
            <FormField
              name="selectedQuizzes"
              rules={{
                validate: value =>
                  (value?.length > 0 &&
                    selectedQuizzes &&
                    selectedQuizzes.length > 0) ||
                  t('MUST_SELECT_ONE_QUIZ_COLLECTION'),
              }}
              control={control}
              render={() => (
                <FormItem>
                  <FormControl>
                    <DataTableBasic
                      manualPagination
                      isDisplayPagination={false}
                      columns={columns}
                      data={selectedQuizzes || []}
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />
          </section>

          <Separator className="my-8" />

          <FormSubmissionButton
            cancelText={t('CANCEL')}
            submitText={t(isEdit ? 'SAVE_CHANGE' : 'SUBMIT')}
            onCancel={() => navigate(-1)}
          />
        </form>
      </Form>

      <Outlet
        context={{
          open: openDialog,
          setOpen: setOpenDialog,
          onClose: () =>
            navigate(
              `${ROUTE_NAME.TEST_AND_REVIEW}${isEdit ? `/${params.id}/edit` : `/create`}`,
              { replace: true, preventScrollReset: true },
            ),
          selectedQuizzes,
          setSelectedQuizzes,
        }}
      />
    </>
  );
}
