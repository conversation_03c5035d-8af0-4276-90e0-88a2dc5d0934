import { Label, Typography, cn } from 'btaskee-ui';
import type { ReactNode } from 'react';

export interface InfoCardProps {
  className?: string;
  children?: ReactNode;
}

export interface InfoBlockProps {
  className?: string;
  label: string;
  value: string | number | ReactNode;
  variant?: 'normal' | 'small';
}

export const InfoCardWrapper = ({
  className,
  children,
}: {
  className?: string;
  children?: ReactNode;
}) => {
  return (
    <div
      className={cn([
        'p-6 bg-gray-50 border border-gray-200 rounded-2xl flex flex-col gap-6',
        className,
      ])}>
      {children}
    </div>
  );
};

export const InfoBlock = ({
  label,
  value,
  variant = 'normal',
}: {
  className?: string;
  label: string;
  value: string | number | ReactNode;
  variant?: 'normal' | 'small';
}) => {
  return (
    <div className="flex flex-col gap-1">
      <Label className="text-sm font-normal text-gray-400">{label}</Label>
      <Typography
        className={`${variant === 'normal' ? 'text-lg font-semibold' : 'text-base font-medium'} leading-tight text-gray-600`}
        variant="p"
        affects="removePMargin">
        {value}
      </Typography>
    </div>
  );
};
