import type { SVGProps } from 'react';

export const EditIcon = ({ props }: { props?: SVGProps<SVGSVGElement> }) => (
  <svg
    width="20"
    height="20"
    viewBox="0 0 20 20"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
    {...props}>
    <path
      d="M13.7308 2.50355C13.87 2.51605 13.9308 2.68605 13.8308 2.78521L6.89751 9.71855C6.81919 9.79692 6.76311 9.8947 6.73501 10.0019L5.90168 13.1935C5.87417 13.299 5.87473 13.4099 5.90329 13.5151C5.93185 13.6202 5.98743 13.7161 6.06451 13.7932C6.14159 13.8703 6.23748 13.9259 6.34268 13.9544C6.44787 13.983 6.55871 13.9836 6.66418 13.956L9.85501 13.1227C9.96227 13.0943 10.06 13.038 10.1383 12.9594L17.17 5.92771C17.192 5.90513 17.22 5.88932 17.2507 5.88216C17.2814 5.87499 17.3135 5.87678 17.3433 5.8873C17.373 5.89782 17.3991 5.91664 17.4184 5.94152C17.4378 5.96641 17.4496 5.99632 17.4525 6.02771C17.7451 8.81907 17.7283 11.6342 17.4025 14.4219C17.2167 16.0094 15.9408 17.2552 14.3592 17.4327C11.4622 17.7539 8.53864 17.7539 5.64168 17.4327C4.05918 17.2552 2.78335 16.0094 2.59751 14.4219C2.25395 11.4844 2.25395 8.51687 2.59751 5.57938C2.78335 3.99105 4.05918 2.74521 5.64168 2.56855C8.32878 2.27076 11.0393 2.24898 13.7308 2.50355Z"
      fill="#F97316"
    />
    <path
      d="M14.8525 3.53093C14.8719 3.51153 14.8949 3.49614 14.9202 3.48564C14.9455 3.47513 14.9726 3.46973 15 3.46973C15.0274 3.46973 15.0546 3.47513 15.0799 3.48564C15.1052 3.49614 15.1282 3.51153 15.1475 3.53093L16.3259 4.7101C16.3648 4.74915 16.3866 4.80204 16.3866 4.85718C16.3866 4.91232 16.3648 4.96521 16.3259 5.00427L9.41502 11.9168C9.38867 11.9429 9.3559 11.9615 9.32002 11.9709L7.72502 12.3876C7.68986 12.3968 7.65292 12.3966 7.61785 12.3871C7.58279 12.3775 7.55082 12.359 7.52513 12.3333C7.49944 12.3076 7.48091 12.2757 7.47139 12.2406C7.46187 12.2055 7.46169 12.1686 7.47085 12.1334L7.88752 10.5384C7.8968 10.5025 7.91549 10.4697 7.94169 10.4434L14.8525 3.53093Z"
      fill="#F97316"
    />
  </svg>
);

export const CreateDocumentIcon = ({
  props,
}: {
  props?: SVGProps<SVGSVGElement>;
}) => (
  <svg
    width="20"
    height="20"
    viewBox="0 0 20 20"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
    {...props}>
    <path
      d="M17.5 11.6667V15.8333C17.5 16.2754 17.3244 16.6993 17.0118 17.0118C16.6993 17.3244 16.2754 17.5 15.8333 17.5H4.16667C3.72464 17.5 3.30072 17.3244 2.98816 17.0118C2.67559 16.6993 2.5 16.2754 2.5 15.8333V4.16667C2.5 3.72464 2.67559 3.30072 2.98816 2.98816C3.30072 2.67559 3.72464 2.5 4.16667 2.5H8.33333V4.16667H4.16667V15.8333H15.8333V11.6667H17.5Z"
      fill="#F97316"
    />
    <path
      d="M17.5001 5.83333H14.1667V2.5H12.5001V5.83333H9.16675V7.5H12.5001V10.8333H14.1667V7.5H17.5001V5.83333Z"
      fill="#F97316"
    />
  </svg>
);

export const UploadDocumentIcon = ({
  props,
}: {
  props?: SVGProps<SVGSVGElement>;
}) => (
  <svg
    width="60"
    height="66"
    viewBox="0 0 60 66"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
    {...props}>
    <path
      fillRule="evenodd"
      clipRule="evenodd"
      d="M46.7 21.9143C41.2133 21.9143 36.7333 17.4676 36.7333 11.981V5.17431L54.34 21.9143H46.7ZM35.06 37.551C34.5733 38.0376 33.9333 38.281 33.2966 38.281C32.6533 38.281 32.0133 38.0376 31.5266 37.5476L27.98 33.9843V48.0643C27.98 49.4443 26.86 50.5643 25.48 50.5643C24.1 50.5643 22.98 49.4443 22.98 48.0643V33.981L19.4333 37.5476C18.46 38.5243 16.8766 38.5243 15.8966 37.551C14.92 36.5776 14.9166 34.9976 15.89 34.0176L23.7066 26.1676C24.16 25.711 24.7866 25.4276 25.48 25.4276C26.1733 25.4276 26.8 25.711 27.2533 26.1676L35.0666 34.0176C36.0433 34.9976 36.0366 36.5776 35.06 37.551ZM59.0166 22.501C59.0166 22.3676 59.0166 22.2676 58.9833 22.1343C58.8833 21.5343 58.7833 20.9343 58.6833 20.3676C58.6166 20.001 58.45 19.701 58.1833 19.4343L39.2833 1.43431C39.05 1.20098 38.7166 1.03431 38.3833 1.00098C37.8166 0.93431 37.2166 0.867643 36.6166 0.800977C36.5166 0.767643 36.3833 0.767643 36.2833 0.767643C34.35 0.600977 32.2166 0.500977 29.9833 0.500977C8.11664 0.500977 0.349976 9.00098 0.349976 33.001C0.349976 56.9676 8.11664 65.501 29.9833 65.501C51.8833 65.501 59.65 56.9676 59.65 33.001C59.65 29.001 59.45 25.5343 59.0166 22.501Z"
      fill="#E5E5E5"
    />
  </svg>
);

export const UploadDocumentActiveIcon = ({
  props,
}: {
  props?: SVGProps<SVGSVGElement>;
}) => (
  <svg
    width="60"
    height="66"
    viewBox="0 0 60 66"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
    {...props}>
    <path
      fillRule="evenodd"
      clipRule="evenodd"
      d="M46.6999 21.9143C41.2133 21.9143 36.7333 17.4676 36.7333 11.981V5.17431L54.3399 21.9143H46.6999ZM35.0599 37.551C34.5733 38.0376 33.9333 38.281 33.2966 38.281C32.6533 38.281 32.0133 38.0376 31.5266 37.5476L27.9799 33.9843V48.0643C27.9799 49.4443 26.8599 50.5643 25.4799 50.5643C24.0999 50.5643 22.9799 49.4443 22.9799 48.0643V33.981L19.4333 37.5476C18.4599 38.5243 16.8766 38.5243 15.8966 37.551C14.9199 36.5776 14.9166 34.9976 15.8899 34.0176L23.7066 26.1676C24.1599 25.711 24.7866 25.4276 25.4799 25.4276C26.1733 25.4276 26.7999 25.711 27.2533 26.1676L35.0666 34.0176C36.0433 34.9976 36.0366 36.5776 35.0599 37.551ZM59.0166 22.501C59.0166 22.3676 59.0166 22.2676 58.9833 22.1343C58.8833 21.5343 58.7833 20.9343 58.6833 20.3676C58.6166 20.001 58.4499 19.701 58.1833 19.4343L39.2833 1.43431C39.0499 1.20098 38.7166 1.03431 38.3833 1.00098C37.8166 0.93431 37.2166 0.867643 36.6166 0.800977C36.5166 0.767643 36.3833 0.767643 36.2833 0.767643C34.3499 0.600977 32.2166 0.500977 29.9833 0.500977C8.11661 0.500977 0.349945 9.00098 0.349945 33.001C0.349945 56.9676 8.11661 65.501 29.9833 65.501C51.8833 65.501 59.6499 56.9676 59.6499 33.001C59.6499 29.001 59.4499 25.5343 59.0166 22.501Z"
      fill="#FED7AA"
    />
  </svg>
);

export const ExternalDocumentIcon = ({
  props,
}: {
  props?: SVGProps<SVGSVGElement>;
}) => (
  <svg
    width="16"
    height="16"
    viewBox="0 0 16 16"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
    {...props}>
    <path
      d="M6.66669 2.66699H4.00002C3.6464 2.66699 3.30726 2.80747 3.05721 3.05752C2.80716 3.30756 2.66669 3.6467 2.66669 4.00033V12.0003C2.66669 12.3539 2.80716 12.6931 3.05721 12.9431C3.30726 13.1932 3.6464 13.3337 4.00002 13.3337H12C12.3536 13.3337 12.6928 13.1932 12.9428 12.9431C13.1929 12.6931 13.3334 12.3539 13.3334 12.0003V9.33366M8.00002 8.00033L13.3334 2.66699M13.3334 2.66699V6.00033M13.3334 2.66699H10"
      stroke="#F97316"
      strokeWidth="1.33333"
      strokeLinecap="round"
      strokeLinejoin="round"
    />
  </svg>
);

export const EditDocumentIcon = ({
  props,
}: {
  props?: SVGProps<SVGSVGElement>;
}) => (
  <svg
    width="18"
    height="19"
    viewBox="0 0 18 19"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
    {...props}>
    <g clipPath="url(#clip0_8954_26928)">
      <path
        d="M8.25 3.57715H3C2.60218 3.57715 2.22064 3.73518 1.93934 4.01649C1.65804 4.29779 1.5 4.67932 1.5 5.07715V15.5771C1.5 15.975 1.65804 16.3565 1.93934 16.6378C2.22064 16.9191 2.60218 17.0771 3 17.0771H13.5C13.8978 17.0771 14.2794 16.9191 14.5607 16.6378C14.842 16.3565 15 15.975 15 15.5771V10.3271"
        stroke="#F97316"
        strokeWidth="1.5"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
      <path
        d="M13.875 2.45232C14.1734 2.15395 14.578 1.98633 15 1.98633C15.422 1.98633 15.8266 2.15395 16.125 2.45232C16.4234 2.75069 16.591 3.15536 16.591 3.57732C16.591 3.99928 16.4234 4.40395 16.125 4.70232L9 11.8273L6 12.5773L6.75 9.57732L13.875 2.45232Z"
        stroke="#F97316"
        strokeWidth="1.5"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
    </g>
    <defs>
      <clipPath id="clip0_8954_26928">
        <rect
          width="18"
          height="18"
          fill="white"
          transform="translate(0 0.577148)"
        />
      </clipPath>
    </defs>
  </svg>
);

export const DocumentUploadedIcon = ({
  props,
}: {
  props?: SVGProps<SVGSVGElement>;
}) => (
  <svg
    width="20"
    height="20"
    viewBox="0 0 20 20"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
    {...props}>
    <path
      fillRule="evenodd"
      clipRule="evenodd"
      d="M11.2082 4.06713H13.7666C16.8416 4.06713 18.3416 5.7088 18.3332 9.07547V13.1338C18.3332 16.3505 16.3499 18.3338 13.1249 18.3338H6.86656C3.65823 18.3338 1.66656 16.3505 1.66656 13.1255V6.86713C1.66656 3.41713 3.1999 1.66713 6.2249 1.66713H7.54156C8.3174 1.6588 9.04156 2.01713 9.51656 2.62547L10.2499 3.60047C10.4832 3.89213 10.8332 4.06713 11.2082 4.06713ZM6.14156 12.742H13.8582C14.1999 12.742 14.4749 12.4587 14.4749 12.117C14.4749 11.767 14.1999 11.492 13.8582 11.492H6.14156C5.79156 11.492 5.51656 11.767 5.51656 12.117C5.51656 12.4587 5.79156 12.742 6.14156 12.742Z"
      fill="#F97316"
    />
  </svg>
);

export const DoubleCheckIcon = ({
  props,
}: {
  props?: SVGProps<SVGSVGElement>;
}) => (
  <svg width="20" height="20" viewBox="0 0 20 20" fill="none" {...props}>
    <path
      d="M15.0003 5L5.83366 14.1667L1.66699 10"
      stroke="#22C55E"
      strokeWidth="1.66667"
      strokeLinecap="round"
      strokeLinejoin="round"
    />
    <path
      d="M18.333 8.33398L12.083 14.584L10.833 13.334"
      stroke="#22C55E"
      strokeWidth="1.66667"
      strokeLinecap="round"
      strokeLinejoin="round"
    />
  </svg>
);

export const YoutubeVector = ({
  props,
}: {
  props?: SVGProps<SVGSVGElement>;
}) => (
  <svg
    width="66"
    height="56"
    viewBox="0 0 66 56"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
    {...props}>
    <path
      d="M65.5 53C65.5 53.663 65.2366 54.2989 64.7678 54.7678C64.2989 55.2366 63.663 55.5 63 55.5H3C2.33696 55.5 1.70107 55.2366 1.23223 54.7678C0.763392 54.2989 0.5 53.663 0.5 53C0.5 52.337 0.763392 51.7011 1.23223 51.2322C1.70107 50.7634 2.33696 50.5 3 50.5H63C63.663 50.5 64.2989 50.7634 64.7678 51.2322C65.2366 51.7011 65.5 52.337 65.5 53ZM65.5 5.5V40.5C65.5 41.8261 64.9732 43.0979 64.0355 44.0355C63.0979 44.9732 61.8261 45.5 60.5 45.5H5.5C4.17392 45.5 2.90215 44.9732 1.96447 44.0355C1.02678 43.0979 0.5 41.8261 0.5 40.5V5.5C0.5 4.17392 1.02678 2.90215 1.96447 1.96447C2.90215 1.02678 4.17392 0.5 5.5 0.5H60.5C61.8261 0.5 63.0979 1.02678 64.0355 1.96447C64.9732 2.90215 65.5 4.17392 65.5 5.5ZM44.25 23C44.2499 22.5983 44.153 22.2026 43.9675 21.8463C43.782 21.49 43.5134 21.1836 43.1844 20.9531L30.6844 12.2031C30.3098 11.9407 29.8704 11.7861 29.4141 11.7561C28.9577 11.7261 28.5019 11.8218 28.0962 12.0329C27.6905 12.244 27.3504 12.5623 27.1131 12.9532C26.8757 13.3441 26.7501 13.7927 26.75 14.25V31.75C26.7501 32.2073 26.8757 32.6559 27.1131 33.0468C27.3504 33.4377 27.6905 33.756 28.0962 33.9671C28.5019 34.1782 28.9577 34.2739 29.4141 34.2439C29.8704 34.2139 30.3098 34.0593 30.6844 33.7969L43.1844 25.0469C43.5134 24.8164 43.782 24.51 43.9675 24.1537C44.153 23.7974 44.2499 23.4017 44.25 23Z"
      fill="#F5F5F5"
    />
  </svg>
);

export const CopyWithRoundedBackgroundIcon = ({
  props,
}: {
  props?: SVGProps<SVGSVGElement>;
}) => (
  <svg
    width="28"
    height="28"
    viewBox="0 0 28 28"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
    {...props}>
    <circle cx="14" cy="14" r="14" fill="#FFF7ED" />
    <path
      d="M15.458 11.083H8.16634C7.36207 11.083 6.70801 11.7371 6.70801 12.5413V19.833C6.70801 20.6373 7.36207 21.2913 8.16634 21.2913H15.458C16.2623 21.2913 16.9163 20.6373 16.9163 19.833V12.5413C16.9163 11.7371 16.2623 11.083 15.458 11.083Z"
      fill="#F97316"
    />
    <path
      d="M19.833 6.70801H12.5413C12.1546 6.70801 11.7836 6.86165 11.5101 7.13514C11.2367 7.40863 11.083 7.77957 11.083 8.16634V9.62467H16.9163C17.3031 9.62467 17.674 9.77832 17.9475 10.0518C18.221 10.3253 18.3747 10.6962 18.3747 11.083V16.9163H19.833C20.2198 16.9163 20.5907 16.7627 20.8642 16.4892C21.1377 16.2157 21.2913 15.8448 21.2913 15.458V8.16634C21.2913 7.77957 21.1377 7.40863 20.8642 7.13514C20.5907 6.86165 20.2198 6.70801 19.833 6.70801Z"
      fill="#F97316"
    />
  </svg>
);

export const VietnamFlagIcon = ({
  props,
}: {
  props?: SVGProps<SVGSVGElement>;
}) => (
  <svg
    width="20"
    height="20"
    viewBox="0 0 20 20"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
    {...props}>
    <path
      d="M17.7778 2.77783H2.22222C1.63285 2.77783 1.06762 3.01196 0.650874 3.42871C0.234126 3.84545 0 4.41068 0 5.00005L0 15.0001C0 15.5894 0.234126 16.1547 0.650874 16.5714C1.06762 16.9882 1.63285 17.2223 2.22222 17.2223H17.7778C18.3671 17.2223 18.9324 16.9882 19.3491 16.5714C19.7659 16.1547 20 15.5894 20 15.0001V5.00005C20 4.41068 19.7659 3.84545 19.3491 3.42871C18.9324 3.01196 18.3671 2.77783 17.7778 2.77783Z"
      fill="#DA251D"
    />
    <path
      d="M10.9739 8.90945L10 5.91223L9.02611 8.90945H5.875L8.42444 10.7611L7.45056 13.7583L10 11.9061L12.5494 13.7583L11.5756 10.7611L14.125 8.90945H10.9739Z"
      fill="#FFFF00"
    />
  </svg>
);

export const ThailandFlagIcon = ({
  props,
}: {
  props?: SVGProps<SVGSVGElement>;
}) => (
  <svg
    width="20"
    height="20"
    viewBox="0 0 20 20"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
    {...props}>
    <path
      d="M0 14.7322V15C0 15.5893 0.234126 16.1546 0.650874 16.5713C1.06762 16.9881 1.63285 17.2222 2.22222 17.2222H17.7778C18.3671 17.2222 18.9324 16.9881 19.3491 16.5713C19.7659 16.1546 20 15.5893 20 15V14.7322H0Z"
      fill="#A7122D"
    />
    <path d="M0 12.3228H20V14.8144H0V12.3228Z" fill="#EEEEEE" />
    <path d="M0 7.5072H20V12.4078H0V7.5072Z" fill="#292648" />
    <path d="M0 5.10059H20V7.59225H0V5.10059Z" fill="#EEEEEE" />
    <path
      d="M0 5.18505V5.00005C0 4.41068 0.234126 3.84545 0.650874 3.42871C1.06762 3.01196 1.63285 2.77783 2.22222 2.77783H17.7778C18.3671 2.77783 18.9324 3.01196 19.3491 3.42871C19.7659 3.84545 20 4.41068 20 5.00005V5.18505H0Z"
      fill="#A7122D"
    />
  </svg>
);

export const USAFlagIcon = ({ props }: { props?: SVGProps<SVGSVGElement> }) => (
  <svg
    width="20"
    height="20"
    viewBox="0 0 20 20"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
    {...props}>
    <path
      d="M17.7778 2.77783H2.22222C1.63285 2.77783 1.06762 3.01196 0.650874 3.42871C0.234126 3.84545 0 4.41068 0 5.00005L0 15.0001C0 15.5894 0.234126 16.1547 0.650874 16.5714C1.06762 16.9882 1.63285 17.2223 2.22222 17.2223H17.7778C18.3671 17.2223 18.9324 16.9882 19.3491 16.5714C19.7659 16.1547 20 15.5894 20 15.0001V5.00005C20 4.41068 19.7659 3.84545 19.3491 3.42871C18.9324 3.01196 18.3671 2.77783 17.7778 2.77783Z"
      fill="#DA251D"
    />
    <path
      d="M10.9739 8.90945L10 5.91223L9.02611 8.90945H5.875L8.42444 10.7611L7.45056 13.7583L10 11.9061L12.5494 13.7583L11.5756 10.7611L14.125 8.90945H10.9739Z"
      fill="#FFFF00"
    />
    <g clipPath="url(#clip0_5200_17787)">
      <path
        fillRule="evenodd"
        clipRule="evenodd"
        d="M0 2.77783V17.7778H20V2.77783H0Z"
        fill="#2E42A5"
      />
      <mask
        id="mask0_5200_17787"
        style={{ maskType: 'luminance' }}
        maskUnits="userSpaceOnUse"
        x="0"
        y="2"
        width="20"
        height="16">
        <path
          fillRule="evenodd"
          clipRule="evenodd"
          d="M0 2.77783V17.7778H20V2.77783H0Z"
          fill="white"
        />
      </mask>
      <g mask="url(#mask0_5200_17787)">
        <path
          d="M-2.22656 16.706L2.17469 18.5678L20.0997 4.80158L22.4216 2.03533L17.7153 1.41345L10.4041 7.34533L4.51906 11.3428L-2.22656 16.706Z"
          fill="white"
        />
        <path
          d="M-1.625 18.0103L0.618125 19.0903L21.5875 1.77844H18.4394L-1.625 18.0103Z"
          fill="#F50100"
        />
        <path
          d="M22.2272 16.706L17.826 18.5678L-0.0990231 4.80158L-2.4209 2.03533L2.28535 1.41345L9.5966 7.34533L15.4816 11.3428L22.2272 16.706Z"
          fill="white"
        />
        <path
          d="M22.0773 17.6422L19.8348 18.7222L10.9061 11.3097L8.25859 10.4822L-2.64453 2.04468H0.504219L11.4005 10.2822L14.2948 11.2747L22.0773 17.6422Z"
          fill="#F50100"
        />
        <path
          fillRule="evenodd"
          clipRule="evenodd"
          d="M12.3613 1.52783H7.63883V7.77783H-1.23242V12.7778H7.63883V19.0278H12.3613V12.7778H21.2676V7.77783H12.3613V1.52783Z"
          fill="#F50100"
        />
        <mask
          id="mask1_5200_17787"
          style={{ maskType: 'luminance' }}
          maskUnits="userSpaceOnUse"
          x="-2"
          y="1"
          width="24"
          height="19">
          <path
            fillRule="evenodd"
            clipRule="evenodd"
            d="M12.3613 1.52783H7.63883V7.77783H-1.23242V12.7778H7.63883V19.0278H12.3613V12.7778H21.2676V7.77783H12.3613V1.52783Z"
            fill="white"
          />
        </mask>
        <g mask="url(#mask1_5200_17787)">
          <path
            d="M7.63883 1.52783V0.277832H6.38883V1.52783H7.63883ZM12.3613 1.52783H13.6113V0.277832H12.3613V1.52783ZM7.63883 7.77783V9.02783H8.88883V7.77783H7.63883ZM-1.23242 7.77783V6.52783H-2.48242V7.77783H-1.23242ZM-1.23242 12.7778H-2.48242V14.0278H-1.23242V12.7778ZM7.63883 12.7778H8.88883V11.5278H7.63883V12.7778ZM7.63883 19.0278H6.38883V20.2778H7.63883V19.0278ZM12.3613 19.0278V20.2778H13.6113V19.0278H12.3613ZM12.3613 12.7778V11.5278H11.1113V12.7778H12.3613ZM21.2676 12.7778V14.0278H22.5176V12.7778H21.2676ZM21.2676 7.77783H22.5176V6.52783H21.2676V7.77783ZM12.3613 7.77783H11.1113V9.02783H12.3613V7.77783ZM7.63883 2.77783H12.3613V0.277832H7.63883V2.77783ZM8.88883 7.77783V1.52783H6.38883V7.77783H8.88883ZM-1.23242 9.02783H7.63883V6.52783H-1.23242V9.02783ZM0.0175781 12.7778V7.77783H-2.48242V12.7778H0.0175781ZM7.63883 11.5278H-1.23242V14.0278H7.63883V11.5278ZM8.88883 19.0278V12.7778H6.38883V19.0278H8.88883ZM12.3613 17.7778H7.63883V20.2778H12.3613V17.7778ZM11.1113 12.7778V19.0278H13.6113V12.7778H11.1113ZM21.2676 11.5278H12.3613V14.0278H21.2676V11.5278ZM20.0176 7.77783V12.7778H22.5176V7.77783H20.0176ZM12.3613 9.02783H21.2676V6.52783H12.3613V9.02783ZM11.1113 1.52783V7.77783H13.6113V1.52783H11.1113Z"
            fill="white"
          />
        </g>
      </g>
    </g>
    <defs>
      <clipPath id="clip0_5200_17787">
        <rect y="2.77783" width="20" height="15" rx="2.22222" fill="white" />
      </clipPath>
    </defs>
  </svg>
);

export const IndonesiaFlagIcon = ({
  props,
}: {
  props?: SVGProps<SVGSVGElement>;
}) => (
  <svg
    width="20"
    height="20"
    viewBox="0 0 20 20"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
    {...props}>
    <path
      d="M17.7778 2.77783H2.22222C1.63285 2.77783 1.06762 3.01196 0.650874 3.42871C0.234126 3.84545 0 4.41068 0 5.00005L0 10.0001H20V5.00005C20 4.41068 19.7659 3.84545 19.3491 3.42871C18.9324 3.01196 18.3671 2.77783 17.7778 2.77783Z"
      fill="#DC1F26"
    />
    <path
      d="M20 15C20 15.5894 19.7659 16.1546 19.3491 16.5713C18.9324 16.9881 18.3671 17.2222 17.7778 17.2222H2.22222C1.63285 17.2222 1.06762 16.9881 0.650874 16.5713C0.234126 16.1546 0 15.5894 0 15V10H20V15Z"
      fill="#EEEEEE"
    />
  </svg>
);

export const SouthKoreaFlagIcon = ({
  props,
}: {
  props?: SVGProps<SVGSVGElement>;
}) => (
  <svg
    width="20"
    height="20"
    viewBox="0 0 20 20"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
    {...props}>
    <path
      d="M20 15.0001C20 15.5894 19.7659 16.1547 19.3491 16.5714C18.9324 16.9882 18.3671 17.2223 17.7778 17.2223H2.22222C1.63285 17.2223 1.06762 16.9882 0.650874 16.5714C0.234126 16.1547 0 15.5894 0 15.0001V5.00005C0 4.41068 0.234126 3.84545 0.650874 3.42871C1.06762 3.01196 1.63285 2.77783 2.22222 2.77783H17.7778C18.3671 2.77783 18.9324 3.01196 19.3491 3.42871C19.7659 3.84545 20 4.41068 20 5.00005V15.0001Z"
      fill="#EEEEEE"
    />
    <path
      d="M11.9115 7.26939C11.1873 6.76241 10.2914 6.56387 9.4208 6.71744C8.55022 6.87101 7.7763 7.36412 7.26928 8.08828C7.02986 8.45072 6.94135 8.89228 7.02258 9.319C7.10381 9.74571 7.34837 10.1239 7.70421 10.373C8.06005 10.6221 8.49906 10.7225 8.92781 10.6528C9.35656 10.5832 9.74119 10.3489 9.99984 9.99995C10.1254 9.82066 10.285 9.66785 10.4696 9.55026C10.6543 9.43266 10.8602 9.35258 11.0758 9.31459C11.2913 9.27659 11.5123 9.28143 11.726 9.32882C11.9397 9.37621 12.1419 9.46522 12.3212 9.59078C12.5005 9.71634 12.6533 9.87598 12.7709 10.0606C12.8885 10.2452 12.9686 10.4512 13.0066 10.6667C13.0446 10.8823 13.0397 11.1032 12.9924 11.3169C12.945 11.5306 12.8559 11.7329 12.7304 11.9122C12.9815 11.5536 13.1595 11.149 13.2543 10.7216C13.349 10.2942 13.3587 9.8523 13.2826 9.42116C13.2066 8.99003 13.0463 8.5781 12.8111 8.2089C12.5758 7.83969 12.2702 7.52045 11.9115 7.26939Z"
      fill="#C60C30"
    />
    <path
      d="M12.3214 9.5911C11.9594 9.33756 11.5115 9.2382 11.0762 9.31488C10.6409 9.39156 10.2539 9.638 10.0003 9.99999C9.7423 10.3508 9.35727 10.5867 8.92756 10.6573C8.49785 10.7279 8.05757 10.6276 7.70087 10.3778C7.34417 10.128 7.09936 9.74857 7.0188 9.32062C6.93823 8.89266 7.02831 8.45017 7.26976 8.08777C7.01132 8.44598 6.82665 8.85198 6.72648 9.28219C6.62632 9.71239 6.61266 10.1582 6.68629 10.5937C6.75992 11.0293 6.91938 11.4458 7.1554 11.8192C7.39142 12.1925 7.69929 12.5153 8.06112 12.7686C8.42296 13.022 8.83153 13.2009 9.26311 13.2949C9.69469 13.389 10.1407 13.3964 10.5751 13.3166C11.0095 13.2368 11.4238 13.0714 11.7938 12.8302C12.1638 12.5889 12.4821 12.2765 12.7303 11.9111C12.8559 11.732 12.9449 11.5298 12.9924 11.3162C13.0398 11.1027 13.0446 10.8818 13.0067 10.6664C12.9687 10.4509 12.8886 10.245 12.7711 10.0605C12.6535 9.87604 12.5007 9.71652 12.3214 9.5911Z"
      fill="#003478"
    />
    <path
      d="M13.5187 14.2067L14.5898 12.9301L15.0153 13.2873L13.9442 14.5639L13.5187 14.2067ZM14.9464 12.5051L16.0175 11.2289L16.4431 11.5862L15.372 12.8623L14.9464 12.5051ZM14.3698 14.9223L15.4409 13.6462L15.8664 14.0034L14.7953 15.2795L14.3698 14.9223ZM15.7987 13.2189L16.8709 11.9428L17.2964 12.3006L16.2242 13.5762L15.7987 13.2189ZM15.2203 15.6367L16.2925 14.3606L16.7175 14.7178L15.6459 15.9939L15.2203 15.6367ZM16.6487 13.9339L17.7192 12.6562L18.1453 13.0134L17.0748 14.2912L16.6487 13.9339ZM16.6509 6.06617L17.0764 5.70895L18.1487 6.98617L17.7225 7.34339L16.6509 6.06617ZM15.222 4.36284L15.6475 4.00562L16.7186 5.28284L16.2925 5.6395L15.222 4.36284ZM14.3703 5.07839L14.7959 4.72117L17.2959 7.70006L16.8703 8.05728L14.3703 5.07839ZM13.5192 5.79173L13.9453 5.4345L15.0159 6.71117L14.5903 7.06784L13.5192 5.79173ZM14.947 7.4945L15.3725 7.13728L16.4448 8.41339L16.0198 8.77062L14.947 7.4945ZM3.55532 11.5856L3.98087 11.2284L6.48032 14.2078L6.05421 14.5651L3.55532 11.5856ZM2.70532 12.3001L3.13032 11.9434L4.20198 13.2201L3.77587 13.5773L2.70532 12.3001ZM4.13143 14.0034L4.55698 13.6462L5.62921 14.9223L5.20365 15.2795L4.13143 14.0034ZM1.85309 13.0139L2.27865 12.6567L4.77865 15.6356L4.35254 15.9928L1.85309 13.0139ZM1.85254 6.98506L4.35143 4.00617L4.77754 4.36284L2.27754 7.34228L1.85254 6.98506ZM2.70365 7.70006L5.20365 4.72062L5.62921 5.07784L3.12921 8.05673L2.70365 7.70006ZM3.55532 8.41395L6.05532 5.4345L6.48087 5.79173L3.98087 8.77117L3.55532 8.41395Z"
      fill="#292F33"
    />
  </svg>
);

export const FiveStarBadgeIcon = ({
  props,
}: {
  props?: SVGProps<SVGSVGElement>;
}) => (
  <svg
    xmlns="http://www.w3.org/2000/svg"
    width="16"
    height="22"
    viewBox="0 0 16 22"
    fill="none"
    {...props}>
    <path
      d="M7.55152 9.4085C7.59278 9.32477 7.65666 9.25426 7.73592 9.20495C7.81518 9.15565 7.90667 9.12952 8.00001 9.12952C8.09336 9.12952 8.18485 9.15565 8.26411 9.20495C8.34337 9.25426 8.40725 9.32477 8.44851 9.4085L9.94102 12.4325C9.97684 12.5053 10.0298 12.5683 10.0954 12.616C10.161 12.6637 10.2372 12.6948 10.3175 12.7065L13.655 13.1915C13.7473 13.205 13.8339 13.244 13.9052 13.3041C13.9764 13.3642 14.0294 13.4431 14.0582 13.5317C14.0871 13.6204 14.0905 13.7154 14.0682 13.8059C14.0459 13.8964 13.9987 13.9789 13.932 14.044L11.517 16.398C11.4589 16.4546 11.4155 16.5244 11.3904 16.6015C11.3653 16.6786 11.3593 16.7606 11.373 16.8405L11.943 20.1645C11.9587 20.2564 11.9484 20.3508 11.9133 20.4372C11.8781 20.5235 11.8195 20.5983 11.7441 20.6531C11.6687 20.7079 11.5795 20.7405 11.4866 20.7473C11.3936 20.7541 11.3006 20.7348 11.218 20.6915L8.23301 19.1225C8.16124 19.0847 8.08136 19.065 8.00026 19.065C7.91917 19.065 7.83929 19.0847 7.76751 19.1225L4.78252 20.6915C4.69993 20.7351 4.60679 20.7546 4.51366 20.748C4.42053 20.7413 4.33112 20.7087 4.25556 20.6539C4.18 20.599 4.12131 20.5241 4.08613 20.4376C4.05095 20.3511 4.04069 20.2565 4.05652 20.1645L4.62651 16.8405C4.64025 16.7606 4.63436 16.6786 4.60936 16.6015C4.58436 16.5245 4.541 16.4546 4.48301 16.398L2.06801 14.044C2.00116 13.9789 1.95387 13.8964 1.93148 13.8058C1.90909 13.7152 1.91249 13.6201 1.94132 13.5314C1.97014 13.4426 2.02322 13.3637 2.09457 13.3036C2.16591 13.2434 2.25266 13.2044 2.34501 13.191L5.68251 12.706C5.76274 12.6944 5.83893 12.6634 5.90452 12.6157C5.97011 12.5681 6.02313 12.5052 6.05901 12.4325L7.55152 9.4085ZM14 1H2.00001V6L8.00001 8.5L14 6V1Z"
      stroke="#737373"
      strokeWidth="2"
      strokeLinecap="round"
      strokeLinejoin="round"
    />
  </svg>
);

export const AccountMultiCheck = ({
  props,
}: {
  props?: SVGProps<SVGSVGElement>;
}) => (
  <svg
    xmlns="http://www.w3.org/2000/svg"
    width="58"
    height="58"
    viewBox="0 0 58 58"
    fill="none"
    {...props}>
    <circle cx="29" cy="29" r="29" fill="#F0FDF4" />
    <path
      d="M40.6667 37.3334V40.6667H20.6667V37.3334C20.6667 37.3334 20.6667 30.6667 30.6667 30.6667C40.6667 30.6667 40.6667 37.3334 40.6667 37.3334ZM35.6667 22.3334C35.6667 21.3445 35.3735 20.3778 34.8241 19.5555C34.2747 18.7333 33.4938 18.0924 32.5802 17.714C31.6665 17.3355 30.6612 17.2365 29.6913 17.4294C28.7214 17.6224 27.8305 18.0986 27.1312 18.7978C26.432 19.4971 25.9557 20.388 25.7628 21.3579C25.5699 22.3278 25.6689 23.3332 26.0473 24.2468C26.4258 25.1604 27.0667 25.9413 27.8889 26.4907C28.7111 27.0401 29.6778 27.3334 30.6667 27.3334C31.9928 27.3334 33.2646 26.8066 34.2023 25.8689C35.14 24.9312 35.6667 23.6595 35.6667 22.3334ZM41.0001 30.7667C41.9111 31.6072 42.6456 32.6208 43.1607 33.7482C43.6758 34.8757 43.9611 36.0944 44.0001 37.3334V40.6667H49.0001V37.3334C49.0001 37.3334 49.0001 31.5834 41.0001 30.7667ZM39.0001 17.3334C38.4965 17.3334 37.996 17.4121 37.5167 17.5667C38.4918 18.965 39.0146 20.6287 39.0146 22.3334C39.0146 24.0381 38.4918 25.7018 37.5167 27.1C37.996 27.2546 38.4965 27.3333 39.0001 27.3334C40.3262 27.3334 41.5979 26.8066 42.5356 25.8689C43.4733 24.9312 44.0001 23.6595 44.0001 22.3334C44.0001 21.0073 43.4733 19.7355 42.5356 18.7978C41.5979 17.8602 40.3262 17.3334 39.0001 17.3334ZM21.2334 23.8667L23.1667 26.2167L15.2501 34.1334L10.6667 29.1334L12.6001 27.2L15.2501 29.8334L21.2334 23.8667Z"
      fill="#22C55E"
    />
  </svg>
);

export const AccountMulti = ({
  props,
}: {
  props?: SVGProps<SVGSVGElement>;
}) => (
  <svg
    xmlns="http://www.w3.org/2000/svg"
    width="58"
    height="58"
    viewBox="0 0 58 58"
    fill="none"
    {...props}>
    <circle cx="29" cy="29" r="29" fill="#FFF7ED" />
    <path
      d="M37.3333 34C36.1666 34 35.1805 33.5972 34.3749 32.7916C33.5694 31.9861 33.1666 31 33.1666 29.8333C33.1666 28.6666 33.5694 27.6805 34.3749 26.875C35.1805 26.0694 36.1666 25.6666 37.3333 25.6666C38.4999 25.6666 39.486 26.0694 40.2916 26.875C41.0971 27.6805 41.4999 28.6666 41.4999 29.8333C41.4999 31 41.0971 31.9861 40.2916 32.7916C39.486 33.5972 38.4999 34 37.3333 34ZM30.6666 42.3333C30.1944 42.3333 29.7988 42.1733 29.4799 41.8533C29.161 41.5333 29.001 41.1377 28.9999 40.6666V40C28.9999 39.3333 29.1738 38.7155 29.5216 38.1466C29.8694 37.5777 30.3621 37.1677 30.9999 36.9166C31.9999 36.5 33.0349 36.1877 34.1049 35.98C35.1749 35.7722 36.251 35.6677 37.3333 35.6666C38.4155 35.6655 39.4921 35.77 40.5633 35.98C41.6344 36.19 42.6688 36.5022 43.6666 36.9166C44.3055 37.1666 44.7988 37.5766 45.1466 38.1466C45.4944 38.7166 45.6677 39.3344 45.6666 40V40.6666C45.6666 41.1388 45.5066 41.535 45.1866 41.855C44.8666 42.175 44.471 42.3344 43.9999 42.3333H30.6666ZM25.6666 29C23.8333 29 22.2638 28.3472 20.9583 27.0416C19.6527 25.7361 18.9999 24.1666 18.9999 22.3333C18.9999 20.5 19.6527 18.9305 20.9583 17.625C22.2638 16.3194 23.8333 15.6666 25.6666 15.6666C27.4999 15.6666 29.0694 16.3194 30.3749 17.625C31.6805 18.9305 32.3333 20.5 32.3333 22.3333C32.3333 24.1666 31.6805 25.7361 30.3749 27.0416C29.0694 28.3472 27.4999 29 25.6666 29ZM12.3333 37.6666C12.3333 36.7222 12.5694 35.8544 13.0416 35.0633C13.5138 34.2722 14.1666 33.6677 14.9999 33.25C16.6666 32.4166 18.396 31.7777 20.1883 31.3333C21.9805 30.8888 23.8066 30.6666 25.6666 30.6666C26.6388 30.6666 27.611 30.75 28.5833 30.9166C29.5555 31.0833 30.5277 31.2777 31.4999 31.5L28.6666 34.3333C27.9721 35.0277 27.2983 35.7361 26.6449 36.4583C25.9916 37.1805 25.6655 38.0277 25.6666 39V40.625C25.6666 40.9583 25.7294 41.2711 25.8549 41.5633C25.9805 41.8555 26.1816 42.1122 26.4583 42.3333H15.6666C14.7499 42.3333 13.9655 42.0072 13.3133 41.355C12.661 40.7027 12.3344 39.9177 12.3333 39V37.6666Z"
      fill="#F97316"
    />
  </svg>
);
