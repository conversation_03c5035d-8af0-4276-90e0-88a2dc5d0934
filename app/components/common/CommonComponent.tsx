import defaultImage from '@/images/default-image.svg';
import {
  Avatar,
  AvatarFallback,
  AvatarImage,
  Tooltip,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
  Typography,
} from 'btaskee-ui';
import i18n from 'i18next';

const ServiceIconWithTooltip = ({
  service,
}: {
  service: Pick<Service, 'text' | 'icon' | 'isSubscription' | 'name'>;
}) => (
  <TooltipProvider>
    <Tooltip>
      <TooltipTrigger asChild>
        <Avatar className="w-11 h-11 rounded-[11px] object-cover">
          <AvatarImage
            className="aspect-auto"
            src={service?.icon || ''}
            alt={service.name}
          />
          <AvatarFallback>
            <img src={defaultImage} alt="Default" />
          </AvatarFallback>
        </Avatar>
      </TooltipTrigger>
      <TooltipContent>
        <Typography variant="p" affects="removePMargin">
          {`${service?.text?.[i18n.language || 'en']} ${service?.isSubscription ? '(Subscription)' : ''}`}
        </Typography>
      </TooltipContent>
    </Tooltip>
  </TooltipProvider>
);

const CalculateFakeInput = ({
  value,
  currency,
}: {
  value: number | string;
  currency: string;
}) => {
  return (
    <div className="flex items-center justify-center gap-2 px-3 h-10 rounded-md border border-input ring-offset-background focus-within:ring-1 focus-within:ring-ring focus-within:ring-offset-2 data-[disabled=true]:cursor-not-allowed data-[disabled=true]:opacity-50 bg-gray-300">
      <Typography
        variant="p"
        affects="removePMargin"
        className="flex h-full w-full rounded-md bg-transparent py-2 text-sm file:bg-transparent file:text-sm file:font-medium placeholder:text-muted-foreground shadow-none outline-none border-none focus-visible:outline-none focus-visible:border-none focus-visible:shadow-none text-gray-600">
        {value}
      </Typography>
      <span className="text-sm text-gray-500">{currency}</span>
    </div>
  );
};

export { CalculateFakeInput, ServiceIconWithTooltip };
