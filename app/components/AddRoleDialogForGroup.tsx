import { useSearchParams } from '@remix-run/react';
import type { ColumnDef } from '@tanstack/react-table';
import {
  BTaskeeTable,
  Button,
  Checkbox,
  DataTableColumnHeader,
  Dialog,
  DialogClose,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
  Separator,
  Typography,
} from 'btaskee-ui';
import { getPageSizeAndPageIndex } from 'btaskee-utils';
import { Plus, Trash2 } from 'lucide-react';
import { useMemo, useState } from 'react';
import { useTranslation } from 'react-i18next';

export default function AddRoleDialog({
  tableData,
  setDraftSelectedRoles,
  defaultValues = [],
}: {
  tableData: MustBeAny;
  setDraftSelectedRoles: MustBeAny;
  defaultValues?: MustBeAny;
}) {
  const { t } = useTranslation('user-settings');
  const [searchParams] = useSearchParams();
  const [open, setOpen] = useState<boolean>(false);

  const [selectedRoles, setSelectedRoles] = useState<Roles[]>(defaultValues);

  const [selectedCount, setSelectedCount] = useState(selectedRoles.length);
  const onCloseAndReset = () => {
    setOpen(false);
    setSelectedRoles([]);
  };

  const columns: ColumnDef<Roles>[] = useMemo(
    () => [
      {
        id: 'select',
        header: () => (
          <Checkbox
            checked={
              selectedCount === tableData?.length &&
              tableData?.every(
                (role: MustBeAny, index: number) =>
                  role?._id === selectedRoles[index]?._id,
              )
            }
            onCheckedChange={value => {
              setSelectedCount(value ? tableData?.length || 0 : 0);
              setSelectedRoles(value ? tableData : []);
            }}
            aria-label="Select all"
            className={`translate-y-[2px]`}
          />
        ),
        size: 32,
        cell: ({ row }) => (
          <Checkbox
            checked={selectedRoles.some(role => role._id === row.original._id)}
            onCheckedChange={value => {
              setSelectedCount(prev => prev + (value ? 1 : -1));
              setSelectedRoles(prev => {
                if (value) {
                  return [...prev, row.original];
                }
                return prev.filter(role => role._id !== row.original._id);
              });
            }}
            aria-label="Select row"
            className="translate-y-[2px]"
          />
        ),
        enableSorting: false,
      },
      {
        accessorKey: 'name',
        header: ({ column }) => (
          <DataTableColumnHeader column={column} title={t('ROLE_NAME')} />
        ),
        cell: ({ row }) => (
          <span className="text-gray-800">{row.getValue('name')}</span>
        ),
      },
      {
        accessorKey: 'action',
        header: ({ column }) => (
          <DataTableColumnHeader
            column={column}
            className="text-center"
            title={t('ACTION')}
          />
        ),
        cell: ({ row }) => (
          <div className="text-center">
            {selectedRoles.some(role => role._id === row.original._id) ? (
              <Button
                type="button"
                variant="ghost"
                className="text-gray-500 hover:text-gray-600 hover:bg-gray-200 gap-2 h-6 py-0.5"
                onClick={() => {
                  setSelectedCount(prev => prev - 1);
                  setSelectedRoles((prev: Roles[]) =>
                    prev.filter(user => user._id !== row.original._id),
                  );
                }}>
                <Trash2 className="w-4 h-4" />
                {t('REMOVE')}
              </Button>
            ) : (
              <Button
                type="button"
                variant="ghost"
                className="text-primary hover:text-primary hover:bg-primary-50 gap-2 h-6 py-0.5"
                onClick={() => {
                  setSelectedCount(prev => prev + 1);
                  setSelectedRoles((prev: Roles[]) => [...prev, row.original]);
                }}>
                <Plus className="w-4 h-4" />
                {t('ADD')}
              </Button>
            )}
          </div>
        ),

        enableSorting: false,
      },
    ],
    [tableData, selectedCount, selectedRoles, t],
  );

  return (
    <Dialog open={open} onOpenChange={setOpen} defaultOpen={true}>
      <DialogTrigger asChild>
        <Button className="gap-2 bg-white border-primary text-primary border hover:text-white">
          <Plus />
          {t('ADD_ROLES')}
        </Button>
      </DialogTrigger>
      <DialogContent className="max-w-5xl" onInteractOutside={onCloseAndReset}>
        <DialogHeader className="space-y-2">
          <DialogTitle className="text-2xl tracking-tighter font-semibold">
            {t('ADD_ROLES')}
          </DialogTitle>
          <Typography variant="p" className="">
            {t('ADD_ROLES_DESCRIPTION')}
          </Typography>
        </DialogHeader>
        <BTaskeeTable
          columns={columns}
          data={tableData || []}
          total={tableData?.length || 0}
          pagination={getPageSizeAndPageIndex({
            total: tableData?.length || 0,
            pageSize: Number(searchParams.get('pageSize') || 0),
            pageIndex: Number(searchParams.get('pageIndex') || 0),
          })}
          search={{
            name: 'search',
            placeholder: t('SEARCH_BY_NAME'),
            defaultValue: searchParams.get('search') || '',
          }}
        />
        <Separator />
        <div className="flex flex-col items-end gap-3">
          <div className="flex gap-4">
            <Button
              className="border-primary text-primary hover:bg-primary-foreground hover:text-primary"
              type="button"
              variant="outline"
              onClick={onCloseAndReset}>
              {t('CANCEL')}
            </Button>
            <Button
              variant="default"
              className=""
              type="button"
              onClick={() => {
                setDraftSelectedRoles(selectedRoles);
                onCloseAndReset();
              }}>
              {t('CONFIRM')}
            </Button>
          </div>
        </div>
        <DialogClose className="absolute z-10 right-4 top-4 rounded-sm w-5 h-5 cursor-default bg-white" />
      </DialogContent>
    </Dialog>
  );
}
