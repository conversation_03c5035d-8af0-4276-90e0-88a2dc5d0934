import { json } from '@remix-run/node';
import {
  isRouteErrorResponse,
  useLoaderData,
  useOutletContext,
  useRouteError,
  useSearchParams,
} from '@remix-run/react';
import type { ColumnDef } from '@tanstack/react-table';
import {
  BTaskeeTable,
  BtaskeeResponseError,
  Button,
  Checkbox,
  DataTableColumnHeader,
  Dialog,
  DialogClose,
  DialogContent,
  DialogHeader,
  DialogTitle,
  Separator,
  Typography,
  toast,
} from 'btaskee-ui';
import {
  convertSortString,
  getPageSizeAndPageIndex,
  getSkipAndLimit,
} from 'btaskee-utils';
import { Plus, Trash2 } from 'lucide-react';
import { useMemo, useState } from 'react';
import { useTranslation } from 'react-i18next';
import { hocLoader } from '~/hoc/remix';
import { getUserSession } from '~/services/helpers.server';
import {
  getListQuizzes,
  getTotalQuizzes,
} from '~/services/tasker-training.server';

export function ErrorBoundary() {
  const error = useRouteError() as { message?: string };
  const { t: tCommon } = useTranslation('common');

  if (isRouteErrorResponse(error)) {
    if (error?.message) {
      toast({ description: JSON.stringify(error.message) });
    }

    return <BtaskeeResponseError t={tCommon} errorStatus={error.status} />;
  }

  return <BtaskeeResponseError t={tCommon} errorStatus={500} />;
}

export const loader = hocLoader(async ({ request }) => {
  const url = new URL(request.url);
  const { isoCode } = await getUserSession({
    headers: request.headers,
  });

  const search = url.searchParams.get('code_or_name') || '';
  const sort = url.searchParams.get('sort') || '';
  const pageSize = Number(url.searchParams.get('pageSize')) || 10;
  const pageIndex = Number(url.searchParams.get('pageIndex')) || 0;

  const filter = {
    name: search,
    code: search,
  };

  const total = await getTotalQuizzes({ isoCode, filter });
  const { limit, skip } = getSkipAndLimit(
    getPageSizeAndPageIndex({
      total,
      pageSize,
      pageIndex,
    }),
  );
  const tableData = await getListQuizzes({
    isoCode,
    skip,
    limit,
    filter,
    sort: convertSortString({
      sortString: sort,
      defaultValue: { updatedAt: -1 },
    }),
  });

  return json({
    data: tableData || [],
    total,
  });
});

export default function AddQuizListDialog() {
  const { t } = useTranslation('quiz');
  const [searchParams] = useSearchParams();

  const loaderData = useLoaderData<LoaderTypeWithError<typeof loader>>();

  const parentContext = useOutletContext<{
    open: boolean;
    onOpenChange: (open: boolean) => void;
    onClose: () => void;
    selectedQuizzes: QuizWillBeAddedToCollection[];
    setSelectedQuizzes: (quizzes: MustBeAny) => void; //TODO: MustBeAny needs to be replaced with the correct type
  }>();

  const [selectedCount, setSelectedCount] = useState(
    parentContext.selectedQuizzes.filter(quiz => !quiz.isNewQuiz).length,
  );
  const [currentSelectedFromDialog, setCurrentSelectedFromDialog] = useState(
    parentContext.selectedQuizzes.filter(quiz => !quiz.isNewQuiz),
  );

  const columns: ColumnDef<QuizWillBeAddedToCollection>[] = useMemo(
    () => [
      {
        id: 'select',
        header: () => (
          <Checkbox
            checked={
              (selectedCount === loaderData.data?.length &&
                loaderData.data?.every(
                  (quiz, index) =>
                    quiz._id === currentSelectedFromDialog[index]._id,
                )) ||
              selectedCount === loaderData.total
            }
            onCheckedChange={value => {
              setSelectedCount(value ? loaderData.data?.length || 0 : 0);
              setCurrentSelectedFromDialog(value ? loaderData.data : []);
            }}
            aria-label="Select all"
            className={`translate-y-[2px]`}
          />
        ),
        size: 32,
        cell: ({ row }) => (
          <Checkbox
            checked={currentSelectedFromDialog.some(
              quiz => quiz._id === row.original._id,
            )}
            onCheckedChange={value => {
              setSelectedCount(prev => prev + (value ? 1 : -1));
              setCurrentSelectedFromDialog(
                (prev: QuizWillBeAddedToCollection[]) => {
                  if (value) {
                    return [...prev, row.original];
                  }
                  return prev.filter(quiz => quiz._id !== row.original._id);
                },
              );
            }}
            aria-label="Select row"
            className="translate-y-[2px]"
          />
        ),
        enableSorting: false,
      },
      {
        accessorKey: 'code',
        header: ({ column }) => (
          <DataTableColumnHeader column={column} title={t('QUIZ_CODE')} />
        ),
        size: 100,
        cell: ({ row }) => (
          <span className="text-gray-800">{row.original.code}</span>
        ),
      },
      {
        accessorKey: 'name',
        header: ({ column }) => (
          <DataTableColumnHeader column={column} title={t('QUESTION')} />
        ),
        size: 500,
        cell: ({ row }) => (
          // eslint-disable-next-line @typescript-eslint/ban-ts-comment
          //@ts-ignore
          <span className="text-gray-800">{row.original.title}</span>
        ),
        enableSorting: false,
      },
      {
        accessorKey: 'action',
        header: ({ column }) => (
          <DataTableColumnHeader
            column={column}
            className="text-center"
            title={t('ACTION')}
          />
        ),
        size: 150,
        cell: ({ row }) =>
          currentSelectedFromDialog.some(
            quiz => quiz._id === row.original._id,
          ) ? (
            <div className="w-full flex justify-center">
              <Button
                type="button"
                variant="ghost"
                className="text-gray-500 hover:text-gray-600 hover:bg-gray-200 gap-2 h-6 py-0.5"
                onClick={() => {
                  setSelectedCount(prev => prev - 1);
                  setCurrentSelectedFromDialog(
                    (prev: QuizWillBeAddedToCollection[]) =>
                      prev.filter(quiz => quiz._id !== row.original._id),
                  );
                }}>
                <Trash2 className="w-4 h-4" />
                {t('REMOVE')}
              </Button>
            </div>
          ) : (
            <div className="w-full flex justify-center">
              <Button
                type="button"
                variant="ghost"
                className="text-primary hover:text-primary hover:bg-primary-50 gap-2 h-6 py-0.5"
                onClick={() => {
                  setSelectedCount(prev => prev + 1);
                  setCurrentSelectedFromDialog(
                    (prev: QuizWillBeAddedToCollection[]) => [
                      ...prev,
                      row.original,
                    ],
                  );
                }}>
                <Plus className="w-4 h-4" />
                {t('ADD')}
              </Button>
            </div>
          ),
        enableSorting: false,
      },
    ],
    [selectedCount, loaderData, currentSelectedFromDialog, t],
  );

  return (
    <Dialog
      open={parentContext.open}
      onOpenChange={parentContext.onOpenChange}
      defaultOpen={true}>
      <DialogContent
        className="max-w-5xl max-h-[90vh] overflow-y-auto"
        onInteractOutside={parentContext.onClose}>
        <DialogHeader className="space-y-2">
          <DialogTitle className="text-2xl tracking-tighter font-semibold">
            {t('ADD_QUIZ_LIST')}
          </DialogTitle>
        </DialogHeader>
        <BTaskeeTable
          columns={columns}
          data={loaderData.data}
          total={loaderData.total}
          pagination={getPageSizeAndPageIndex({
            total: loaderData.total,
            pageSize: Number(searchParams.get('pageSize') || 0),
            pageIndex: Number(searchParams.get('pageIndex') || 0),
          })}
          search={{
            name: 'code_or_name',
            placeholder: t('SEARCH_BY_CODE_OR_NAME'),
            defaultValue: searchParams.get('code_or_name') || '',
          }}
        />
        <Separator />
        <div className="flex flex-col items-end gap-3">
          <Typography variant="p" className="text-secondary-foreground">
            {selectedCount} {t('SELECTED')}
          </Typography>
          <div className="flex gap-4">
            <Button
              className="border-primary text-primary hover:bg-primary-foreground hover:text-primary"
              type="button"
              variant="outline"
              onClick={parentContext.onClose}>
              {t('CANCEL')}
            </Button>
            <Button
              variant="default"
              className=""
              type="button"
              onClick={() => {
                parentContext.setSelectedQuizzes(
                  (prev: QuizWillBeAddedToCollection[]) => {
                    // List of quizzes that existed in the parent context but not selected from current dialog session
                    const getQuizzesRemoved = prev.filter(
                      quiz =>
                        !quiz.isNewQuiz &&
                        !currentSelectedFromDialog.some(
                          selectedQuiz => selectedQuiz._id === quiz._id,
                        ),
                    );

                    // List of quizzes that selected from current dialog session but not existed in the parent context
                    const newSelectedQuizzes = currentSelectedFromDialog
                      .filter(
                        quiz =>
                          !parentContext.selectedQuizzes.some(
                            selectedQuiz => selectedQuiz._id === quiz._id,
                          ),
                      )
                      .map((quiz, index) => ({
                        ...quiz,
                        quizOrder: prev.length + index,
                        isNewQuiz: false,
                        // eslint-disable-next-line @typescript-eslint/ban-ts-comment
                        // @ts-ignore
                        name: quiz?.title || '',
                      }));

                    return [
                      ...prev.filter(
                        quiz =>
                          !getQuizzesRemoved.some(q => q._id === quiz._id),
                      ),
                      ...newSelectedQuizzes,
                    ].sort((a, b) => a.quizOrder - b.quizOrder);
                  },
                );
                parentContext.onClose();
              }}>
              {t('CONFIRM')}
            </Button>
          </div>
        </div>
        <DialogClose className="absolute z-10 right-4 top-4 rounded-sm w-5 h-5 cursor-default bg-white" />
      </DialogContent>
    </Dialog>
  );
}
