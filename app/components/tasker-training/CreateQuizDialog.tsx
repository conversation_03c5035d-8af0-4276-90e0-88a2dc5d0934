import { zodResolver } from '@hookform/resolvers/zod';
import {
  isRouteErrorResponse,
  useOutletContext,
  useRouteError,
} from '@remix-run/react';
import { ROUTE_NAME } from 'btaskee-constants';
import {
  BtaskeeResponseError,
  Button,
  Dialog,
  DialogClose,
  DialogContent,
  DialogHeader,
  DialogTitle,
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
  Input,
  Separator,
  Switch,
  Typography,
  toast,
} from 'btaskee-ui';
import { createUID } from 'btaskee-utils';
import type { Dispatch, SetStateAction } from 'react';
import { useEffect, useState } from 'react';
import { useFieldArray, useForm } from 'react-hook-form';
import { useTranslation } from 'react-i18next';
import { z } from 'zod';
import { DashedBorderButton } from '~/components/form/DashedBorderButton';
import { AnswerCard } from '~/components/form/cards/AnswerCard';
import ImageUploaderCard from '~/components/form/cards/ImageUploaderCard';
import {
  zodAnswersSchema,
  zodCodeSchema,
  zodImageWithDescriptionSchema,
} from '~/schemas/zodSchema';

export function ErrorBoundary() {
  const error = useRouteError() as { message?: string };
  const { t: tCommon } = useTranslation('common');

  if (isRouteErrorResponse(error)) {
    if (error?.message) {
      toast({ description: JSON.stringify(error.message) });
    }

    return <BtaskeeResponseError t={tCommon} errorStatus={error.status} />;
  }

  return <BtaskeeResponseError t={tCommon} errorStatus={500} />;
}

export default function CreateNewQuizDialog() {
  const { t } = useTranslation('quiz');

  const [isShowImage, setIsShowImage] = useState(false);
  const [formData, setFormData] = useState<
    QuizWillBeAddedToCollection & { code: string }
  >();
  const [selectedCorrectAnswer, setSelectedCorrectAnswer] =
    useState<string>('');

  const resolverSchema = zodResolver(
    z.object({
      code: zodCodeSchema(t),
      name: z.string().min(3, t('MINIMUM_CHARACTERS', { number: 3 })),
      isRandomAnswer: z.boolean(),
      answers: zodAnswersSchema(t),
      image: zodImageWithDescriptionSchema(t).newImage,
      isNewQuiz: z.boolean(),
      isCreatedByForm: z.boolean(),
    }),
  );
  const form = useForm<QuizWillBeAddedToCollection>({
    resolver: resolverSchema,
    defaultValues: {
      code: '',
      name: '',
      isRandomAnswer: false,
      answers: [
        {
          _id: createUID(),
          content: '',
          isCorrect: false,
        },
        {
          _id: createUID(),
          content: '',
          isCorrect: false,
        },
      ],
      image: null,
      isNewQuiz: true,
      isCreatedByForm: true,
    },
  });

  const { control, setValue, handleSubmit } = form;

  const { fields, append, remove } = useFieldArray({
    control,
    name: 'answers',
  });

  useEffect(() => {
    if (selectedCorrectAnswer) {
      fields.forEach((field: MustBeAny, index) => {
        //TODO: MustBeAny needs to be replaced with the correct type
        if (
          field._id === selectedCorrectAnswer ||
          field.id === selectedCorrectAnswer
        ) {
          setValue(`answers.${index}.isCorrect`, true);
        } else {
          setValue(`answers.${index}.isCorrect`, false);
        }
      });
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [selectedCorrectAnswer]);

  const parentContext = useOutletContext<{
    open: boolean;
    onOpenChange: (open: boolean) => void;
    onClose: () => void;
    selectedQuizzes: (QuizWillBeAddedToCollection & { isCSVQuiz?: boolean })[];
    setSelectedQuizzes: (quizzes: MustBeAny) => void; //TODO: MustBeAny needs to be replaced with the correct type
    loaderFetcherCheckUniqueCode: MustBeAny; //TODO: MustBeAny needs to be replaced with the correct type
    setExistQuizList: Dispatch<SetStateAction<string>>;
  }>();

  const onSubmit = async (
    data: QuizWillBeAddedToCollection & {
      code: string;
    },
  ) => {
    parentContext.loaderFetcherCheckUniqueCode.load(
      `${ROUTE_NAME.CHECK_QUIZ_UNIQUE_CODE}?codes=${data.code}`,
    );
    setFormData(data);
  };

  useEffect(() => {
    if (parentContext.open) {
      form.reset();
      setIsShowImage(false);
      setSelectedCorrectAnswer('');
    } else {
      parentContext.setExistQuizList('');
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [parentContext.open]);

  useEffect(() => {
    if (formData) {
      const existingCodes =
        parentContext.loaderFetcherCheckUniqueCode?.data?.listExist;
      const existingCodeFromTableList = parentContext.selectedQuizzes.some(
        quiz => quiz.code === formData.code,
      );

      if (existingCodeFromTableList || existingCodes?.length > 0) {
        toast({ description: 'Code is already exist' });
      } else {
        parentContext.setSelectedQuizzes((prev: Quiz[]) => [
          ...parentContext.selectedQuizzes,
          {
            ...formData,
            _id: createUID(),
            quizOrder: parentContext.selectedQuizzes.length,
            isNewQuiz: true,
          },
        ]);

        parentContext.setExistQuizList('');
        parentContext.onClose();
      }
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [parentContext.loaderFetcherCheckUniqueCode?.data]);

  return (
    <Dialog
      open={parentContext.open}
      onOpenChange={parentContext.onOpenChange}
      defaultOpen={true}>
      <DialogContent className="max-w-5xl max-h-[90vh] overflow-y-auto">
        <DialogHeader className="space-y-2">
          <DialogTitle className="text-2xl tracking-tighter font-semibold">
            {t('CREATE_QUIZ')}
          </DialogTitle>
        </DialogHeader>

        <Form {...form}>
          <form
            onSubmit={handleSubmit(onSubmit)}
            encType={'multipart/form-data'}
            className={'flex flex-col gap-8'}>
            <section className={'flex flex-col gap-4'}>
              <Typography
                variant={'h4'}
                className={'text-xl tracking-tighter font-semibold'}>
                {t('QUESTION')}
              </Typography>
              <div className="space-y-8">
                <div className="grid grid-cols-2 gap-6">
                  <FormField
                    name={'code'}
                    control={control}
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel className={'text-gray-700'}>
                          {t('QUIZ_CODE')}
                        </FormLabel>
                        <FormControl>
                          <Input
                            {...field}
                            placeholder={t('ENTER_QUIZ_CODE')}
                          />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                  <FormField
                    name="name"
                    control={control}
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel className={'text-gray-700'}>
                          {t('QUESTION')}
                        </FormLabel>
                        <FormControl>
                          <Input {...field} placeholder={t('ENTER_QUESTION')} />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                </div>

                <div className="space-y-2">
                  <div className="flex justify-between items-center h-6">
                    <FormLabel className={'text-gray-700'}>
                      {t('IMAGE')}
                    </FormLabel>
                    <Typography
                      variant="p"
                      affects="removePMargin"
                      className="text-sm text-gray-400 leading-tight">
                      {t('OPTIONAL')}
                    </Typography>
                  </div>

                  {!isShowImage && (
                    <DashedBorderButton
                      label={t('ADD_IMAGE')}
                      onClick={() => {
                        setIsShowImage(true);
                        setValue('image', {
                          value: null,
                          description: '',
                        });
                      }}
                    />
                  )}

                  {isShowImage && (
                    <ImageUploaderCard
                      form={form}
                      onClose={() => {
                        setIsShowImage(false);
                        setValue('image', null);
                      }}
                    />
                  )}
                </div>
              </div>

              <Separator className="my-2" />

              <div className="flex items-center justify-between">
                <Typography
                  variant={'h4'}
                  className={'text-xl tracking-tighter font-semibold'}>
                  {t('ANSWER')}
                </Typography>

                <FormField
                  name={'isRandomAnswer'}
                  control={control}
                  render={({ field }) => (
                    <FormItem className="flex items-center gap-4">
                      <FormControl>
                        <Switch
                          checked={!!field.value}
                          onCheckedChange={field.onChange}
                        />
                      </FormControl>
                      <FormLabel className={'text-gray-700 !mt-0'}>
                        {t('RANDOM_ANSWER')}
                      </FormLabel>
                      <FormMessage />
                    </FormItem>
                  )}
                />
              </div>

              <div className="max-h-[380px] overflow-y-auto">
                <div className="grid grid-cols-3 gap-6 items-start">
                  {fields.map((field, index) => (
                    <AnswerCard
                      key={field.id}
                      fieldId={field.id}
                      control={control}
                      index={index}
                      remove={remove}
                      selectedCorrectAnswer={selectedCorrectAnswer}
                      setSelectedCorrectAnswer={setSelectedCorrectAnswer}
                    />
                  ))}

                  <DashedBorderButton
                    label={t('ADD_ANSWER')}
                    className="mt-0"
                    onClick={() =>
                      append({
                        content: '',
                        isCorrect: false,
                        _id: createUID(),
                      })
                    }
                  />
                </div>
              </div>

              <Separator />

              <div className="w-full flex flex-col items-end gap-2">
                {form.getFieldState('answers').error && (
                  <Typography
                    variant="p"
                    affects="removePMargin"
                    className="text-red-500">
                    {form.getFieldState('answers')?.error?.root?.message}
                  </Typography>
                )}
                <div className="flex gap-2 ml-auto">
                  <Button
                    type="button"
                    variant="outline"
                    className="border-primary text-primary hover:text-primary"
                    onClick={() => {
                      parentContext.setExistQuizList('');
                      parentContext.onClose();
                    }}>
                    {t('CANCEL')}
                  </Button>

                  <Button type="submit" variant={'default'}>
                    {t('SUBMIT')}
                  </Button>
                </div>
              </div>
            </section>
          </form>
        </Form>
        <DialogClose className="absolute z-10 right-4 top-4 rounded-sm w-5 h-5 cursor-default bg-white" />
      </DialogContent>
    </Dialog>
  );
}
