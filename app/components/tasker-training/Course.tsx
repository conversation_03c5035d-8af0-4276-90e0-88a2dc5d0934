import { type SerializeFrom } from '@remix-run/node';
import { CONDITION } from 'btaskee-constants';
import { useTranslation } from 'react-i18next';
import { type getNameOfCourseByIds } from '~/services/tasker-training.server';

const ConditionAndNode = ({
  condition,
  courseNames,
}: {
  condition: Course['condition'];
  courseNames: SerializeFrom<
    ReturnValueIgnorePromise<typeof getNameOfCourseByIds>
  >;
}) => {
  const { t } = useTranslation('course');

  switch (true) {
    case !!condition?.byTasker:
      return `${t(CONDITION.TASKER_STAR)} <${condition?.byTasker?.minimumStar}`;
    case !!condition?.coursesMustBeCompleted:
      return `${t(CONDITION.WHEN_COMPLETE)} - ${courseNames?.join(', ')}`;
    case !!condition?.manuallyUnblock:
      return t(CONDITION.MANUALLY_UNBLOCK);
    default:
      return t(CONDITION.NONE);
  }
};

export { ConditionAndNode };
