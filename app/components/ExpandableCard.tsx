import { cn } from 'btaskee-ui';
import type { ReactNode } from 'react';
import { useState } from 'react';

interface ExpandableCardProps {
  children: ReactNode;
  t: MustBeAny;
}

const ExpandableCard = ({ children, t }: ExpandableCardProps) => {
  const [isExpanded, setIsExpanded] = useState(false);

  const toggleExpand = () => {
    setIsExpanded(!isExpanded);
  };

  return (
    <div className="pr-4 pb-4 flex  flex-col">
      {isExpanded && <> {children}</>}
      <button
        onClick={toggleExpand}
        className="text-gray-400 flex ml-auto items-center gap-[5px] text-sm">
        {isExpanded ? t('CLOSE') : t('VIEW')}
        <svg
          className={cn(isExpanded ? '-rotate-180' : '')}
          width="16"
          height="16"
          viewBox="0 0 16 16"
          fill="none"
          xmlns="http://www.w3.org/2000/svg">
          <path
            d="M4 6L8 10L12 6"
            stroke="#A3A3A3"
            strokeWidth="1.33333"
            strokeLinecap="round"
            strokeLinejoin="round"
          />
        </svg>
      </button>
    </div>
  );
};

export default ExpandableCard;
