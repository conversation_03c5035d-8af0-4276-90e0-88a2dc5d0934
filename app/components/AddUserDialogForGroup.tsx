import { useSearchParams } from '@remix-run/react';
import type { ColumnDef } from '@tanstack/react-table';
import {
  Avatar,
  AvatarFallback,
  AvatarImage,
  BTaskeeTable,
  Button,
  Checkbox,
  DataTableColumnHeader,
  Dialog,
  DialogClose,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
  Separator,
  Typography,
} from 'btaskee-ui';
import { getPageSizeAndPageIndex } from 'btaskee-utils';
import { Plus, Trash2, UserCircle } from 'lucide-react';
import { useMemo, useState } from 'react';
import { useTranslation } from 'react-i18next';

export default function AddUserDialog({
  tableData,
  setDraftSelectedUsers,
  defaultValues = [],
}: {
  tableData: MustBeAny;
  setDraftSelectedUsers: MustBeAny;
  defaultValues?: MustBeAny;
}) {
  const { t } = useTranslation('user-settings');
  const [searchParams] = useSearchParams();

  const [openUserDialog, setOpenUserDialog] = useState<boolean>(false);
  const [selectedUsers, setSelectedUsers] = useState<Users[]>(defaultValues);
  const [selectedCount, setSelectedCount] = useState(selectedUsers.length);

  const onCloseAndReset = () => {
    setOpenUserDialog(false);
    setSelectedUsers([]);
  };

  const columns: ColumnDef<Users>[] = useMemo(
    () => [
      {
        id: 'select',
        header: () => (
          <Checkbox
            checked={
              selectedCount === tableData?.length &&
              tableData?.every(
                (user: MustBeAny, index: number) =>
                  user?._id === selectedUsers[index]?._id,
              )
            }
            onCheckedChange={value => {
              setSelectedCount(value ? tableData?.length || 0 : 0);
              setSelectedUsers(value ? tableData : []);
            }}
            aria-label="Select all"
            className={`translate-y-[2px]`}
          />
        ),
        size: 32,
        cell: ({ row }) => (
          <Checkbox
            checked={selectedUsers.some(user => user?._id === row.original._id)}
            onCheckedChange={value => {
              setSelectedCount(prev => prev + (value ? 1 : -1));
              setSelectedUsers(prev => {
                if (value) {
                  return [...prev, row.original];
                }
                return prev.filter(user => user._id !== row.original._id);
              });
            }}
            aria-label="Select row"
            className="translate-y-[2px]"
          />
        ),
        enableSorting: false,
      },
      {
        accessorKey: 'username',
        header: ({ column }) => (
          <DataTableColumnHeader column={column} title={t('USERS')} />
        ),
        cell: ({ row }) => (
          <div className="flex items-center gap-3">
            <Avatar className="h-10 w-10">
              <AvatarImage src={row.original?.avatarUrl} />
              <AvatarFallback>
                <UserCircle />
              </AvatarFallback>
            </Avatar>
            <p className="text-gray-800">{row.original?.username}</p>
          </div>
        ),
      },
      {
        accessorKey: 'action',
        header: ({ column }) => (
          <DataTableColumnHeader
            column={column}
            className="text-center"
            title={t('ACTION')}
          />
        ),
        cell: ({ row }) => (
          <div className="text-center">
            {selectedUsers.some(user => user._id === row.original._id) ? (
              <div className="w-full flex justify-center">
                <Button
                  type="button"
                  variant="ghost"
                  className="text-gray-500 hover:text-gray-600 hover:bg-gray-200 gap-2 h-6 py-0.5"
                  onClick={() => {
                    setSelectedCount(prev => prev - 1);
                    setSelectedUsers((prev: Users[]) =>
                      prev.filter(user => user._id !== row.original._id),
                    );
                  }}>
                  <Trash2 className="w-4 h-4" />
                  {t('REMOVE')}
                </Button>
              </div>
            ) : (
              <div className="w-full flex justify-center">
                <Button
                  type="button"
                  variant="ghost"
                  className="text-primary hover:text-primary hover:bg-primary-50 gap-2 h-6 py-0.5"
                  onClick={() => {
                    setSelectedCount(prev => prev + 1);
                    setSelectedUsers((prev: Users[]) => [
                      ...prev,
                      row.original,
                    ]);
                  }}>
                  <Plus className="w-4 h-4" />
                  {t('ADD')}
                </Button>
              </div>
            )}
          </div>
        ),
        enableSorting: false,
        size: 40,
      },
    ],
    [tableData, selectedCount, selectedUsers, t],
  );

  return (
    <Dialog
      open={openUserDialog}
      onOpenChange={setOpenUserDialog}
      defaultOpen={true}>
      <DialogTrigger asChild>
        <Button className="gap-2 bg-white border-primary text-primary border hover:text-white">
          <Plus />
          {t('ADD_USERS')}
        </Button>
      </DialogTrigger>
      <DialogContent className="max-w-5xl" onInteractOutside={onCloseAndReset}>
        <DialogHeader className="space-y-2">
          <DialogTitle className="text-2xl tracking-tighter font-semibold">
            {t('ADD_USERS')}
          </DialogTitle>
          <Typography variant="p" className="">
            {t('ADD_USERS_DESCRIPTION')}
          </Typography>
        </DialogHeader>
        <BTaskeeTable
          columns={columns}
          data={tableData || []}
          total={tableData?.length || 0}
          pagination={getPageSizeAndPageIndex({
            total: tableData?.length || 0,
            pageSize: Number(searchParams.get('pageSize') || 0),
            pageIndex: Number(searchParams.get('pageIndex') || 0),
          })}
          search={{
            name: 'users',
            placeholder: t('SEARCH_BY_NAME'),
            defaultValue: searchParams.get('users') || '',
          }}
        />
        <Separator />
        <div className="flex flex-col items-end gap-3">
          <div className="flex gap-4">
            <Button
              className="border-primary text-primary hover:bg-primary-foreground hover:text-primary"
              type="button"
              variant="outline"
              onClick={onCloseAndReset}>
              {t('CANCEL')}
            </Button>
            <Button
              variant="default"
              className=""
              type="button"
              onClick={() => {
                setDraftSelectedUsers(selectedUsers);
                onCloseAndReset();
              }}>
              {t('CONFIRM')}
            </Button>
          </div>
        </div>
        <DialogClose className="absolute z-10 right-4 top-4 rounded-sm w-5 h-5 cursor-default bg-white" />
      </DialogContent>
    </Dialog>
  );
}
