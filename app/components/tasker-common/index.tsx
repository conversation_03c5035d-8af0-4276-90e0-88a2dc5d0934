import type { SerializeFrom } from '@remix-run/node';
import { useSubmit } from '@remix-run/react';
import {
  IMAGE_PROFILE_TYPE,
  MESSAGE_ALL_LANGUAGE_ON_TASKER_ONBOARDING,
  TASKER_ONBOARDING_PROCESS_STATUS,
  TASKER_PROFILE_REJECT_STATUS,
} from 'btaskee-constants';
import type {
  TASKER_ONBOARDING_IMAGE_KEY,
  TASKER_PROFILE_TAB,
} from 'btaskee-constants';
import {
  Accordion,
  AccordionContent,
  AccordionItem,
  AccordionTrigger,
  Button,
  Card,
  CardContent,
  CardHeader,
  Dialog,
  DialogClose,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogTitle,
  DialogTrigger,
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  Label,
  LoadingSpinner,
  RadioGroup,
  RadioGroupItem,
  SelectBase,
  Separator,
  SingleDateTimePicker,
  StatusBadge,
  Ta<PERSON>,
  TabsList,
  TabsTrigger,
  Textarea,
  Typography,
  cn,
  useBtaskeeFormController,
  useConfirm,
} from 'btaskee-ui';
import type { BadgeProps, ButtonProps, TabsProps } from 'btaskee-ui';
import type { MappingOverallActionOnProfileDetailProps } from 'btaskee-utils';
import {
  getOverallStatusInProfile,
  getRejectActionMappingByStatus,
  getTriggerActionMappingByStatus,
  momentTz,
} from 'btaskee-utils';
import { format } from 'date-fns';
import { ImageOff, OctagonAlert, RotateCcw } from 'lucide-react';
import type { Dispatch, ReactNode, SetStateAction } from 'react';
import { useCallback, useEffect, useMemo, useState } from 'react';
import type {
  Control,
  UseFormRegister,
  UseFormResetField,
  UseFormSetValue,
} from 'react-hook-form';
import { Controller, useForm, useWatch } from 'react-hook-form';
import { useTranslation } from 'react-i18next';
import { z } from 'zod';
import { EditDocumentIcon } from '~/components/common/FigmaIcon';
import type {
  getAllOfOffices,
  getAllReasonFromSetting,
  getTaskerProfileDetail,
} from '~/services/tasker-profile.server';

export interface ConfirmUpdatingProfileFormProps {
  status: `${TASKER_ONBOARDING_PROCESS_STATUS}`;
  reason?: string;
  message?: string;
}

export interface TaskerProfileTabModelProps {
  permissions?: Array<BtaskeePermissions['key']>;
  items: typeof TASKER_PROFILE_TAB;
  onValueChange: NonNullable<TabsProps['onValueChange']>;
  value: TabsProps['value'];
}

export const BlockDescription = ({
  desc,
  variant = 'md',
  className,
}: {
  desc: {
    label: string;
    value?: string | ReactNode;
    tags?: string[];
  };
  variant?: 'sm' | 'md';
  className?: string;
}) => {
  return (
    <div className={cn('flex flex-col gap-1', className)}>
      <Typography
        variant="p"
        affects="removePMargin"
        className={`${variant === 'sm' ? 'text-xs font-medium' : 'text-sm font-normal'} text-gray-400`}>
        {desc.label}
      </Typography>
      {desc.tags ? (
        <div className="flex gap-2 flex-wrap">
          {desc.tags.map(tag => (
            <Typography
              key={tag}
              affects="removePMargin"
              variant="p"
              className="text-sm text-blue-500 bg-blue-50 py-1.5 px-3 inline rounded-md">
              {tag}
            </Typography>
          ))}
        </div>
      ) : null}
      {desc.value ? (
        <Typography
          variant="p"
          affects="removePMargin"
          className={`${variant === 'sm' ? 'text-base leading-tight font-medium' : 'text-lg font-semibold'} text-gray-600 break-words`}>
          {desc.value}
        </Typography>
      ) : null}
    </div>
  );
};

const CardProfileDescription = ({
  descriptions,
}: {
  descriptions: Array<{
    label: string;
    value?: string | ReactNode;
    tags?: string[];
    className?: string;
  }>;
}) => (
  <div className="grid grid-cols-3 gap-x-24 gap-y-5">
    {descriptions?.map(desc => (
      <BlockDescription
        key={desc.label}
        desc={desc}
        className={desc.className ?? ''}
      />
    ))}
  </div>
);

const ConfirmationDescriptions = ({
  contents,
}: {
  contents: Array<{ title: string; description?: string }>;
}) => (
  <Card className="bg-gray-100 my-4">
    <CardContent className="p-3">
      {contents.map((content, contentIndex) => (
        <div key={contentIndex}>
          <div className="flex flex-col">
            <Typography
              variant="p"
              affects="removePMargin"
              className="text-xs text-gray-400 font-normal">
              {content.title}
            </Typography>
            {content?.description ? (
              <Typography
                variant="p"
                affects="removePMargin"
                className="text-base font-normal text-gray-600">
                {content.description}
              </Typography>
            ) : null}
          </div>
          {contentIndex < contents.length - 1 ? (
            <Separator className="my-3" />
          ) : null}
        </div>
      ))}
    </CardContent>
  </Card>
);

const TaskerProfileStatus = ({
  status,
}: {
  status: `${TASKER_ONBOARDING_PROCESS_STATUS}`;
} & BadgeProps) => {
  if (!status) {
    return null;
  }

  return (
    <StatusBadge
      status={status}
      translationKey="tasker-profile"
      statusClasses={{
        [TASKER_ONBOARDING_PROCESS_STATUS.REJECTED]:
          'text-red-500 bg-red-50 rounded-md',
        [TASKER_ONBOARDING_PROCESS_STATUS.FAIL_UPDATED]:
          'text-red-500 bg-red-50 rounded-md',
        [TASKER_ONBOARDING_PROCESS_STATUS.APPROVED]:
          'text-blue bg-blue-50 rounded-md',
        [TASKER_ONBOARDING_PROCESS_STATUS.FAIL_CALLING]:
          'text-yellow-500 bg-yellow-50 rounded-md',
        [TASKER_ONBOARDING_PROCESS_STATUS.NEEDS_UPDATE]:
          'text-yellow-500 bg-yellow-50 rounded-md',
        [TASKER_ONBOARDING_PROCESS_STATUS.UPDATED]:
          'text-primary bg-primary-50 rounded-md',
        [TASKER_ONBOARDING_PROCESS_STATUS.VERIFYING]:
          'text-blue bg-blue-50 rounded-md',
        [TASKER_ONBOARDING_PROCESS_STATUS.SCHEDULED]:
          'text-primary bg-primary-50 rounded-md',
        [TASKER_ONBOARDING_PROCESS_STATUS.RESCHEDULE]:
          'text-gray bg-gray-100 rounded-md',
        [TASKER_ONBOARDING_PROCESS_STATUS.ELIMINATED]:
          'text-gray bg-gray-100 rounded-md',
        [TASKER_ONBOARDING_PROCESS_STATUS.FAIL_INTERVIEW]:
          'text-gray bg-gray-100 rounded-md',
        [TASKER_ONBOARDING_PROCESS_STATUS.RESTORED]:
          'text-gray bg-gray-100 rounded-md',
        [TASKER_ONBOARDING_PROCESS_STATUS.ACTIVE]:
          'text-secondary-foreground bg-secondary border-secondary-foreground rounded-md',
      }}
    />
  );
};

const FrontAndBackImage = ({
  images,
  alt,
}: {
  images: [string, string];
  alt: string;
}) => {
  const { t } = useTranslation('tasker-profile');

  return (
    <div className="grid grid-cols-2 gap-6 w-full bg-white p-6">
      <div className="flex flex-col justify-center align-middle bg-gray-100 w-full p-12 outline-dashed outline-[1px] outline-gray-400 rounded-md">
        <Typography
          variant="p"
          affects="removePMargin"
          className="font-medium text-lg text-center pb-6">
          {t('FRONT_SIDE')}
        </Typography>
        {images?.[0] ? (
          <a
            className="mx-auto rounded-lg overflow-hidden"
            href={images[0]}
            target="_blank"
            rel="noreferrer">
            <img src={`${images[0]}?${performance.now()}`} alt={alt} />
          </a>
        ) : (
          <div className="flex flex-col align-middle">
            <ImageOff className="w-12 h-12 text-gray-400" />
            <Typography
              variant="p"
              affects="removePMargin"
              className="text-gray-400">
              {t('NO_IMAGE')}
            </Typography>
          </div>
        )}
      </div>
      <div className="flex flex-col justify-center align-middle bg-gray-100 w-full p-12 outline-dashed outline-[1px] outline-gray-400 rounded-md">
        <Typography
          variant="p"
          affects="removePMargin"
          className="font-medium text-lg text-center pb-6">
          {t('BACK_SIDE')}
        </Typography>
        {images?.[1] ? (
          <a
            className="mx-auto rounded-lg overflow-hidden"
            href={images[1]}
            target="_blank"
            rel="noreferrer">
            <img src={`${images[1]}?${performance.now()}`} alt={alt} />
          </a>
        ) : (
          <>
            <ImageOff className="w-12 h-12 text-gray-400 mx-auto" />
            <Typography
              variant="p"
              affects="removePMargin"
              className="text-gray-400 text-center">
              {t('NO_IMAGE')}
            </Typography>
          </>
        )}
      </div>
    </div>
  );
};

const RejectImageReasonInAccordion = ({
  imageInfo,
  profileType,
}: {
  profileType: `${IMAGE_PROFILE_TYPE}`;
  imageInfo: NonNullable<
    SerializeFrom<
      ReturnValueIgnorePromise<typeof getTaskerProfileDetail>
    >['data']['identityCard']
  >;
}) => {
  const { t } = useTranslation('tasker-profile');

  if (imageInfo.status === TASKER_ONBOARDING_PROCESS_STATUS.APPROVED) {
    return null;
  }

  if (imageInfo.status === TASKER_ONBOARDING_PROCESS_STATUS.UPDATED) {
    return (
      <Typography
        variant="p"
        affects="removePMargin"
        className="text-primary text-base italic">
        {t('NEED_APPROVE_IMAGE_AGAIN_SINCE_IMAGE_UPDATED', {
          type: t(profileType),
        })}
      </Typography>
    );
  }

  const actionHistory = imageInfo?.actionHistories
    ?.filter(
      actionHistory =>
        actionHistory.reason &&
        actionHistory.newStatus === TASKER_ONBOARDING_PROCESS_STATUS.REJECTED,
    )
    ?.pop();

  if (actionHistory?.reason) {
    return (
      <Typography
        variant="p"
        affects="removePMargin"
        className="text-red-500 text-base italic">
        {actionHistory?.reason}
      </Typography>
    );
  }

  return null;
};

function ProfileImageAccordion({
  items,
  onTrigger,
  isSubmitLoading,
  profileStatus,
  localeAddress,
  disabled,
  profileType,
  reasonsFromSetting,
  setOpenWarningMissingReasonDialog,
  onReviewImageAgain,
}: {
  localeAddress: string;
  reasonsFromSetting: SerializeFrom<
    ReturnValueIgnorePromise<typeof getAllReasonFromSetting>[0]
  >[];
  isSubmitLoading: ButtonProps['disabled'];
  disabled?: ButtonProps['disabled'];
  setOpenWarningMissingReasonDialog: Dispatch<SetStateAction<boolean>>;
  profileStatus: `${TASKER_ONBOARDING_PROCESS_STATUS}`;
  onReviewImageAgain?: (value: {
    imageField: `${TASKER_ONBOARDING_IMAGE_KEY}`;
  }) => Promise<void>;
  items: Array<{
    label: string;
    isAllowUpdateStatusInImage?: boolean;
    info: SerializeFrom<
      ReturnValueIgnorePromise<typeof getTaskerProfileDetail>
    >['data']['identityCard'];
    imageField: `${TASKER_ONBOARDING_IMAGE_KEY}`;
  }>;
  profileType: `${IMAGE_PROFILE_TYPE}`;
  onTrigger: (values: ApprovalImageProfileProps) => void;
}) {
  const { t: tProfile } = useTranslation(localeAddress);

  const getIsUpdateImage = useCallback(
    (isAllowUpdateImage?: boolean) => {
      if (!isAllowUpdateImage) {
        return;
      }

      return (
        profileStatus === TASKER_ONBOARDING_PROCESS_STATUS.VERIFYING ||
        profileStatus === TASKER_ONBOARDING_PROCESS_STATUS.RESTORED ||
        profileStatus === TASKER_ONBOARDING_PROCESS_STATUS.UPDATED
      );
    },
    [profileStatus],
  );

  return (
    <Accordion
      className="rounded-md overflow-hidden border border-gray-200"
      type="single"
      defaultValue={
        items.find(
          item =>
            item.info &&
            item.info?.status !== TASKER_ONBOARDING_PROCESS_STATUS.APPROVED,
        )?.label ?? ''
      }
      collapsible>
      {items.map(item =>
        item.info?.images ? (
          <AccordionItem key={item.label} value={item.label}>
            <AccordionTrigger className="bg-gray-100 p-4">
              <div className="flex gap-3">
                <Typography
                  className="my-auto text-base text-gray-600"
                  variant="p"
                  affects="removePMargin">
                  {item.label}
                </Typography>
                {item.info.status ? (
                  <StatusBadge
                    status={item.info.status}
                    statusClasses={{
                      [TASKER_ONBOARDING_PROCESS_STATUS.REJECTED]:
                        'text-red-500 bg-red-50 rounded-md',
                      [TASKER_ONBOARDING_PROCESS_STATUS.APPROVED]:
                        'text-secondary-foreground bg-secondary rounded-md',
                      [TASKER_ONBOARDING_PROCESS_STATUS.UPDATED]:
                        'text-primary bg-primary-50 rounded-md',
                    }}
                  />
                ) : null}
                {item.info?.status ? (
                  <RejectImageReasonInAccordion
                    profileType={profileType}
                    imageInfo={item.info}
                  />
                ) : null}
              </div>
            </AccordionTrigger>
            <AccordionContent className="pb-0">
              <FrontAndBackImage images={item.info?.images} alt={item.label} />
              {(!item.info.status ||
                item.info.status === TASKER_ONBOARDING_PROCESS_STATUS.UPDATED ||
                profileType === IMAGE_PROFILE_TYPE.STAFF) &&
              getIsUpdateImage(!!item.isAllowUpdateStatusInImage) ? (
                <div className="flex gap-3 justify-end bg-white pb-6 pr-6">
                  <Button
                    disabled={isSubmitLoading || disabled}
                    onClick={() => {
                      if (item.imageField) {
                        const rejectImageReasons = reasonsFromSetting?.filter(
                          reasonFromSetting =>
                            reasonFromSetting?.type ===
                            TASKER_PROFILE_REJECT_STATUS.REJECT_DOCUMENTS,
                        );

                        if (!rejectImageReasons?.length) {
                          setOpenWarningMissingReasonDialog(true);
                        } else {
                          onTrigger({
                            title: 'REJECT_IMAGE_TITLE',
                            desc: 'REJECT_IMAGE_DESC',
                            status: TASKER_ONBOARDING_PROCESS_STATUS.REJECTED,
                            fieldName: item.imageField,
                          });
                        }
                      }
                    }}
                    className="bg-white hover:bg-white text-primary border-primary border">
                    {isSubmitLoading ? <LoadingSpinner /> : tProfile('REJECT')}
                  </Button>
                  <Button
                    disabled={isSubmitLoading || disabled}
                    onClick={() => {
                      if (item.imageField) {
                        onTrigger({
                          title: 'APPROVE_IMAGE_TITLE',
                          desc: 'APPROVE_IMAGE_DESC',
                          status: TASKER_ONBOARDING_PROCESS_STATUS.APPROVED,
                          fieldName: item.imageField,
                        });
                      }
                    }}
                    className="border">
                    {isSubmitLoading ? <LoadingSpinner /> : tProfile('APPROVE')}
                  </Button>
                </div>
              ) : null}
              {(item.info?.status ===
                TASKER_ONBOARDING_PROCESS_STATUS.APPROVED ||
                item.info?.status ===
                  TASKER_ONBOARDING_PROCESS_STATUS.REJECTED) &&
              onReviewImageAgain &&
              getIsUpdateImage(!!item.isAllowUpdateStatusInImage) ? (
                <div className="bg-white hover:bg-white flex justify-end pb-6 pr-6">
                  <Button
                    disabled={
                      isSubmitLoading ||
                      disabled ||
                      profileStatus ===
                        TASKER_ONBOARDING_PROCESS_STATUS.NEEDS_UPDATE
                    }
                    onClick={async () => {
                      await onReviewImageAgain({
                        imageField: item.imageField,
                      });
                    }}
                    className="flex items-center gap-2 bg-white hover:bg-white text-primary border-primary border">
                    {isSubmitLoading ? (
                      <LoadingSpinner />
                    ) : (
                      <>
                        <RotateCcw width={16} height={16} />
                        <Typography variant="p" affects="removePMargin">
                          {tProfile('REVIEW_AGAIN')}
                        </Typography>
                      </>
                    )}
                  </Button>
                </div>
              ) : null}
            </AccordionContent>
          </AccordionItem>
        ) : null,
      )}
    </Accordion>
  );
}

const ConfirmUpdatingProfile = ({
  reasons,
  control,
  description,
  reasonsFromSetting,
  taskerInfo,
  setValue,
}: {
  taskerInfo?: {
    taskerName: Users['name'];
    username: TaskerProfile['taskerName'];
  };
  description: string;
  setValue: UseFormSetValue<ConfirmUpdatingProfileFormProps>;
  reasons?: ConfirmUpdatingProfileFormProps['reason'][];
  control: Control<ConfirmUpdatingProfileFormProps>;
  reasonsFromSetting?: SerializeFrom<
    ReturnValueIgnorePromise<typeof getAllReasonFromSetting>[0]
  >[];
}) => {
  const { t } = useTranslation('tasker-profile');
  const [filteredReasons, setFilteredReasons] =
    useState<
      Array<
        SerializeFrom<
          ReturnValueIgnorePromise<typeof getAllReasonFromSetting>[0]
        >
      >
    >();
  const selectedStatus = useWatch({ name: 'status', control });

  useEffect(() => {
    if (reasonsFromSetting) {
      const filteredReasonsBySelectedStatus = reasonsFromSetting?.filter(
        reasonFromSetting => reasonFromSetting?.type === selectedStatus,
      );
      setFilteredReasons(filteredReasonsBySelectedStatus);
      setValue('reason', filteredReasonsBySelectedStatus?.[0]?.name);
    }
  }, [reasonsFromSetting, selectedStatus, setValue]);

  return (
    <div>
      <Typography
        variant="p"
        affects="removePMargin"
        className="text-sm font-normal">
        {description}
      </Typography>
      {taskerInfo ? (
        <ConfirmationDescriptions
          contents={[
            {
              title: t('TASKER_NAME'),
              description: taskerInfo.taskerName,
            },
          ]}
        />
      ) : null}
      {reasonsFromSetting?.length ? (
        <div className="mt-6">
          {taskerInfo ? <Separator className="my-4" /> : null}
          <Controller
            control={control}
            name="reason"
            render={({ field: { onChange, value } }) => (
              <RadioGroup value={value} onValueChange={onChange}>
                {filteredReasons?.map(reasonFromSetting => (
                  <Label
                    key={reasonFromSetting._id}
                    className={cn(
                      'flex items-center border p-4 rounded-md cursor-pointer',
                      reasonFromSetting.name === value
                        ? 'border-primary bg-primary-50'
                        : 'border-gray-300',
                    )}
                    htmlFor={reasonFromSetting.name}>
                    <RadioGroupItem
                      className="min-w-4"
                      value={reasonFromSetting.name || ''}
                      id={reasonFromSetting.name}
                    />
                    <Typography
                      affects="removePMargin"
                      className="ml-2"
                      variant="p">
                      {reasonFromSetting.name}
                    </Typography>
                  </Label>
                ))}
              </RadioGroup>
            )}
          />
        </div>
      ) : null}
      {reasons?.length ? (
        <div className="mt-6">
          {taskerInfo ? <Separator className="my-4" /> : null}
          <Controller
            control={control}
            name="reason"
            render={({ field: { onChange, value } }) => (
              <RadioGroup value={value} onValueChange={onChange}>
                {reasons.map(reason => (
                  <div
                    key={reason}
                    className={cn(
                      'flex items-center h-12 border p-4 rounded-md',
                      reason === value
                        ? 'border-primary bg-primary-50'
                        : 'border-gray-300',
                    )}>
                    <RadioGroupItem
                      className="min-w-4"
                      value={reason || ''}
                      id={reason}
                    />
                    <Label className="cursor-pointer ml-2" htmlFor={reason}>
                      {reason}
                    </Label>
                  </div>
                ))}
              </RadioGroup>
            )}
          />
        </div>
      ) : null}
    </div>
  );
};

const WarningMissingReasonConfigurationDialog = ({
  openDialog,
  setOpenDialog,
  taskerName,
}: {
  openDialog: boolean;
  taskerName: TaskerProfile['taskerName'];
  setOpenDialog: Dispatch<SetStateAction<boolean>>;
}) => {
  const { t: tTaskerProfile } = useTranslation('tasker-profile');

  return (
    <Dialog
      open={openDialog}
      onOpenChange={() => {
        setOpenDialog(true);
      }}
      defaultOpen={true}>
      <DialogContent
        onInteractOutside={() => {
          setOpenDialog(false);
        }}>
        <DialogTitle>
          {tTaskerProfile('TITLE_WARNING_MISSING_REASON_IN_CONFIGURATION')}
        </DialogTitle>
        <ConfirmationDescriptions
          contents={[
            {
              title: tTaskerProfile('TASKER_NAME'),
              description: taskerName,
            },
          ]}
        />
        <DialogDescription className="flex justify-center items-center gap-2">
          <OctagonAlert width={50} height={50} />
          <Typography
            variant="p"
            affects="removePMargin"
            className="text-yellow-500 text-base">
            {tTaskerProfile(
              'DESCRIPTION_WARNING_MISSING_REASON_IN_CONFIGURATION',
            )}
          </Typography>
        </DialogDescription>
        <DialogFooter className="flex justify-end">
          <DialogClose className="absolute z-10 right-4 top-4 rounded-sm w-5 h-5 cursor-default bg-white" />
          <Button
            onClick={() => {
              setOpenDialog(false);
            }}
            className="border-primary text-primary hover:text-primary"
            variant="outline">
            {tTaskerProfile('CANCEL')}
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
};

const TaskerProfileAction = ({
  status,
  onTriggerProfile,
  disabled,
  isSubmitLoading,
  disableApproveAction,
  profileInfo,
  control,
  setValue,
  setOpenScheduleDialog,
  reasonsFromSetting,
  setOpenWarningReasonDialog,
}: {
  setOpenScheduleDialog: Dispatch<SetStateAction<boolean>>;
  reasonsFromSetting?: SerializeFrom<
    ReturnValueIgnorePromise<typeof getAllReasonFromSetting>[0]
  >[];
  setValue: UseFormSetValue<ConfirmUpdatingProfileFormProps>;
  control: Control<ConfirmUpdatingProfileFormProps>;
  profileInfo: Pick<
    SerializeFrom<
      ReturnValueIgnorePromise<typeof getTaskerProfileDetail>
    >['data'],
    'taskerName' | 'username' | 'appointmentInfo'
  >;
  setOpenWarningReasonDialog: Dispatch<SetStateAction<boolean>>;
  status: `${TASKER_ONBOARDING_PROCESS_STATUS}`;
  disabled: ButtonProps['disabled'];
  disableApproveAction: boolean;
  isSubmitLoading: ButtonProps['disabled'];
  onTriggerProfile: (
    values: Pick<MappingOverallActionOnProfileDetailProps, 'title' | 'dialog'>,
  ) => void;
}) => {
  const { t } = useTranslation('tasker-profile');
  const triggerProfileMapping = getTriggerActionMappingByStatus(status);
  const rejectProfileMapping = getRejectActionMappingByStatus(status);
  const selectedStatus = useWatch({ name: 'status', control });

  const getIsDisableStatusOption = useCallback(
    ({ status }: { status: `${TASKER_ONBOARDING_PROCESS_STATUS}` }) =>
      (status === TASKER_ONBOARDING_PROCESS_STATUS.RESCHEDULE ||
        status === TASKER_ONBOARDING_PROCESS_STATUS.FAIL_CALLING) &&
      !profileInfo.appointmentInfo,
    [profileInfo.appointmentInfo],
  );

  const getFilteredReasons = (status: `${TASKER_ONBOARDING_PROCESS_STATUS}`) =>
    reasonsFromSetting?.filter(
      reasonFromSetting => reasonFromSetting?.type === status,
    );

  return (
    <>
      {triggerProfileMapping.title ? (
        <Button
          disabled={disabled || isSubmitLoading || disableApproveAction}
          className={cn(
            'border',
            disabled || isSubmitLoading || disableApproveAction
              ? 'bg-gray-300 text-white'
              : '',
          )}
          onClick={() => {
            setValue('status', triggerProfileMapping.singleStatusOption.status);

            if (triggerProfileMapping.singleStatusOption.message) {
              setValue(
                'message',
                JSON.stringify(
                  triggerProfileMapping.singleStatusOption.message,
                ),
              );
            }
            onTriggerProfile(triggerProfileMapping);
          }}>
          {isSubmitLoading ? (
            <LoadingSpinner />
          ) : (
            t(triggerProfileMapping.title)
          )}
        </Button>
      ) : null}
      {status === TASKER_ONBOARDING_PROCESS_STATUS.VERIFYING ? (
        <Dialog>
          <DialogTrigger>
            <Button
              disabled={disabled}
              className="bg-secondary hover:bg-secondary text-primary border-primary border">
              {isSubmitLoading ? <LoadingSpinner /> : t('REJECT_PROFILE')}
            </Button>
          </DialogTrigger>
          <DialogContent>
            <DialogTitle>{t('TITLE_CONFIRM_REJECT_PROFILE')}</DialogTitle>
            <DialogDescription>
              {t('DESCRIPTION_CONFIRM_REJECT_PROFILE')}
            </DialogDescription>
            <ConfirmationDescriptions
              contents={[
                {
                  title: t('TASKER_NAME'),
                  description: profileInfo.taskerName,
                },
              ]}
            />
            <DialogFooter>
              <DialogClose asChild>
                <Button
                  className="border-primary text-primary hover:text-primary"
                  variant="outline">
                  {t('CANCEL')}
                </Button>
              </DialogClose>
              <DialogClose asChild>
                <Button
                  variant="outline"
                  className="border-primary text-primary hover:text-primary"
                  onClick={() => {
                    const reasons = getFilteredReasons(
                      TASKER_ONBOARDING_PROCESS_STATUS.ELIMINATED,
                    );

                    setValue(
                      'message',
                      JSON.stringify({
                        title: 'MESSAGE_REJECTED_PROFILE_TITLE',
                        body: 'MESSAGE_REJECTED_PROFILE_BODY',
                      }),
                    );
                    setValue(
                      'status',
                      TASKER_ONBOARDING_PROCESS_STATUS.ELIMINATED,
                    );
                    if (!reasons?.length) {
                      setOpenWarningReasonDialog(true);
                    } else {
                      onTriggerProfile({
                        title: rejectProfileMapping.title,
                        dialog: rejectProfileMapping.dialog,
                      });
                    }
                  }}>
                  {t('ELIMINATE')}
                </Button>
              </DialogClose>
              <DialogClose asChild>
                <Button
                  onClick={() => {
                    const reasons = getFilteredReasons(
                      TASKER_ONBOARDING_PROCESS_STATUS.REJECTED,
                    );
                    setValue(
                      'status',
                      TASKER_ONBOARDING_PROCESS_STATUS.REJECTED,
                    );
                    setValue(
                      'message',
                      JSON.stringify({
                        title: 'MESSAGE_REJECTED_PROFILE_TITLE',
                        body: 'MESSAGE_REJECTED_PROFILE_BODY',
                      }),
                    );

                    if (!reasons?.length) {
                      setOpenWarningReasonDialog(true);
                    } else {
                      onTriggerProfile({
                        title: rejectProfileMapping.title,
                        dialog: rejectProfileMapping.dialog,
                      });
                    }
                  }}>
                  {t('REJECT')}
                </Button>
              </DialogClose>
            </DialogFooter>
          </DialogContent>
        </Dialog>
      ) : null}
      {rejectProfileMapping.title && rejectProfileMapping.statusOptions ? (
        <Dialog>
          <DialogTrigger asChild>
            <Button
              disabled={disabled}
              className="bg-secondary hover:bg-secondary text-primary border-primary border">
              {isSubmitLoading ? (
                <LoadingSpinner />
              ) : (
                t(rejectProfileMapping.title)
              )}
            </Button>
          </DialogTrigger>
          <DialogContent>
            <DialogTitle>{t(rejectProfileMapping.dialog?.title)}</DialogTitle>
            <DialogDescription>
              {t(rejectProfileMapping.dialog.desc)}
            </DialogDescription>
            <ConfirmationDescriptions
              contents={[
                {
                  title: t('TASKER_NAME'),
                  description: profileInfo.taskerName,
                },
              ]}
            />
            <Separator className="my-4" />
            <Controller
              control={control}
              name="status"
              rules={{ required: t('THIS_FIELD_IS_REQUIRED') }}
              render={({ field: { onChange, value } }) => (
                <RadioGroup
                  value={value}
                  onValueChange={statusValue => {
                    onChange(statusValue);

                    const statusOptionFound =
                      rejectProfileMapping?.statusOptions?.find(
                        statusOption => statusOption.status === statusValue,
                      );

                    if (statusOptionFound?.message) {
                      setValue(
                        'message',
                        JSON.stringify(statusOptionFound.message),
                      );
                    }
                  }}>
                  {rejectProfileMapping.statusOptions?.map(statusOption => (
                    <div
                      key={statusOption.status}
                      className="flex flex-col items-start">
                      <div
                        className={cn(
                          'flex items-center border-gray-300 border p-4 rounded-md w-full',
                          statusOption.status === value
                            ? 'border-primary bg-primary-50'
                            : 'border-gray-300',
                        )}>
                        <RadioGroupItem
                          disabled={getIsDisableStatusOption({
                            status: statusOption.status,
                          })}
                          className={cn(
                            'min-w-4',
                            getIsDisableStatusOption({
                              status: statusOption.status,
                            })
                              ? 'border-gray-300'
                              : '',
                          )}
                          value={statusOption.status}
                          id={statusOption.status}
                        />
                        <Label
                          className="cursor-pointer font-medium text-gray-700 ml-2"
                          htmlFor={statusOption.status}>
                          {t(statusOption.label)}
                        </Label>
                      </div>
                      {statusOption.note && statusOption.status === value ? (
                        <Typography
                          className="text-primary mt-2"
                          variant="p"
                          affects="removePMargin">
                          {t(statusOption.note)}
                        </Typography>
                      ) : null}
                    </div>
                  ))}
                </RadioGroup>
              )}
            />
            <DialogFooter>
              <DialogClose asChild>
                <Button
                  className="border-primary text-primary hover:text-primary"
                  variant="outline">
                  {t('CANCEL')}
                </Button>
              </DialogClose>
              <DialogClose asChild>
                <Button
                  onClick={() => {
                    // TODO: refactor this log which reduce using if condition
                    if (
                      selectedStatus ===
                      TASKER_ONBOARDING_PROCESS_STATUS.RESCHEDULE
                    ) {
                      setOpenScheduleDialog(true);
                    } else {
                      const reasons = getFilteredReasons(selectedStatus);
                      const rejectTypeInConfiguration = Object.values(
                        TASKER_PROFILE_REJECT_STATUS,
                      );

                      if (
                        rejectTypeInConfiguration.includes(
                          selectedStatus as TASKER_PROFILE_REJECT_STATUS,
                        ) &&
                        !reasons?.length
                      ) {
                        setOpenWarningReasonDialog(true);
                      } else {
                        onTriggerProfile({
                          title: rejectProfileMapping.title,
                          dialog: rejectProfileMapping.dialog,
                        });
                      }
                    }
                  }}>
                  {t('CONFIRM')}
                </Button>
              </DialogClose>
            </DialogFooter>
          </DialogContent>
        </Dialog>
      ) : null}
      {rejectProfileMapping.singleStatusOption?.status ? (
        <Button
          disabled={disabled}
          className="bg-secondary hover:bg-secondary text-primary border-primary border"
          onClick={() => {
            if (rejectProfileMapping.singleStatusOption?.status) {
              setValue(
                'status',
                rejectProfileMapping.singleStatusOption.status,
              );
            }

            if (rejectProfileMapping.singleStatusOption?.message) {
              setValue(
                'message',
                JSON.stringify(rejectProfileMapping.singleStatusOption.message),
              );
            }
            if (
              rejectProfileMapping.singleStatusOption?.status ===
              TASKER_ONBOARDING_PROCESS_STATUS.RESCHEDULE
            ) {
              setOpenScheduleDialog(true);
            } else {
              onTriggerProfile({
                title: rejectProfileMapping.title,
                dialog: rejectProfileMapping.dialog,
              });
            }
          }}>
          {isSubmitLoading ? <LoadingSpinner /> : t(rejectProfileMapping.title)}
        </Button>
      ) : null}
    </>
  );
};

const TaskerProfileTabModel = ({
  items,
  permissions,
  onValueChange,
  value,
}: TaskerProfileTabModelProps) => {
  const { t } = useTranslation('tasker-profile');

  return (
    <Tabs
      value={value ?? ''}
      onValueChange={onValueChange}
      className="space-y-4">
      <TabsList>
        {items.map(item => (
          <TabsTrigger
            className={cn(
              !permissions?.includes(item.permission) ? 'hidden' : '',
            )}
            key={item.tabId}
            value={item.tabId}>
            {t(item.title)}
          </TabsTrigger>
        ))}
      </TabsList>
    </Tabs>
  );
};

function NoteForm({
  register,
  control,
  resetField,
}: {
  control: Control<{ note: string }>;
  register: UseFormRegister<{ note: string }>;
  resetField: UseFormResetField<{ note: string }>;
}) {
  const { t } = useTranslation('tasker-profile');
  const submit = useSubmit();
  const confirm = useConfirm();
  const noteValueInNoteForm = useWatch({
    name: 'note',
    control,
  });

  return (
    <div className="flex flex-col mb-4">
      <Textarea
        {...register('note' as const, {
          required: t('THIS_FIELD_IS_REQUIRED'),
        })}
        value={noteValueInNoteForm}
        placeholder={t('ENTER_NOTE')}
        className="mb-4"
      />
      {noteValueInNoteForm && (
        <div className="flex justify-end">
          <Button
            onClick={async () => {
              const isConfirm = await confirm({
                title: t('NOTE_FORM_CONFIRM_TITLE'),
                body: t('NOTE_FORM_CONFIRM_DESCRIPTION'),
                cancelButton: t('CANCEL'),
                actionButton: t('CONFIRM'),
              });

              if (isConfirm) {
                const formData = new FormData();

                formData.append('note', noteValueInNoteForm);

                submit(formData, { method: 'post' });
              }
              resetField('note');
            }}>
            {t('SUBMIT')}
          </Button>
        </div>
      )}
    </div>
  );
}

function UpdateNoteDialog<TSubmit = Promise<void>>({
  noteInfo,
  onSubmit,
}: {
  onSubmit: ({ updatedNote }: { updatedNote: string }) => TSubmit;
  noteInfo: NonNullable<
    SerializeFrom<
      ReturnValueIgnorePromise<typeof getTaskerProfileDetail>
    >['data']['notes']
  >[0];
}) {
  const { t } = useTranslation('tasker-profile');
  const { register, control, resetField, getValues, setValue } = useForm<{
    updatedNote: string;
  }>({
    defaultValues: {
      updatedNote: '',
    },
  });
  const updatedNoteValueInNoteForm = useWatch({
    name: 'updatedNote',
    control,
  });

  return (
    <Dialog
      onOpenChange={isOpen => {
        if (isOpen) {
          setValue('updatedNote', noteInfo?.description);
        } else {
          resetField('updatedNote');
        }
      }}>
      <DialogTrigger asChild>
        <Button
          variant="ghost"
          className="flex gap-2 items-center text-primary hover:text-primary">
          <EditDocumentIcon />
          <Typography variant="p" affects="removePMargin">
            {t('EDIT')}
          </Typography>
        </Button>
      </DialogTrigger>
      <DialogContent>
        <DialogTitle>{t('TITLE_CONFIRM_EDITING_NOTE')}</DialogTitle>
        <ConfirmationDescriptions
          contents={[
            { title: t('NOTE_BY'), description: noteInfo?.notedBy },
            {
              title: t('NOTE_AT'),
              description: noteInfo?.notedAt
                ? format(noteInfo.notedAt, 'HH:mm - dd/MM/yyyy')
                : '',
            },
          ]}
        />
        <Textarea
          {...register('updatedNote' as const, {
            required: t('THIS_FIELD_IS_REQUIRED'),
          })}
          value={getValues('updatedNote')}
          defaultValue={noteInfo?.description || ''}
          placeholder={t('ENTER_NOTE')}
        />
        <Separator className="mt-6" />
        <DialogFooter>
          <DialogClose>
            <Button
              className="border-primary text-primary hover:text-primary"
              variant="outline">
              {t('CANCEL')}
            </Button>
          </DialogClose>
          <DialogClose
            disabled={
              !updatedNoteValueInNoteForm ||
              updatedNoteValueInNoteForm === noteInfo?.description
            }>
            <Button
              disabled={
                !updatedNoteValueInNoteForm ||
                updatedNoteValueInNoteForm === noteInfo?.description
              }
              onClick={async () => {
                await onSubmit({
                  updatedNote: getValues('updatedNote'),
                });
              }}>
              {t('CONFIRM')}
            </Button>
          </DialogClose>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
}

const UpdateScheduleDialog = ({
  openDialog,
  setOpenDialog,
  profileInfo,
  offices,
}: {
  openDialog: boolean;
  setOpenDialog: Dispatch<SetStateAction<boolean>>;
  profileInfo: SerializeFrom<
    ReturnValueIgnorePromise<typeof getTaskerProfileDetail>
  >['data'];
  offices: SerializeFrom<ReturnValueIgnorePromise<typeof getAllOfOffices>>;
}) => {
  const officeInfoMatchProfileAppointmentInfo = useMemo(
    () =>
      offices.find(
        office =>
          office.name === profileInfo.appointmentInfo?.name &&
          office.address === profileInfo.appointmentInfo?.address &&
          office.phoneNumber === profileInfo.appointmentInfo?.phoneNumber,
      ),
    [offices, profileInfo],
  );

  const { t: tTaskerProfile } = useTranslation('tasker-profile');
  // Handle null value to disable the select times picker follow the requirement
  const [workingStartDate, setWorkingStartDate] = useState<Date | null>(
    officeInfoMatchProfileAppointmentInfo?.workingStartDate
      ? momentTz(
          officeInfoMatchProfileAppointmentInfo?.workingStartDate,
        ).toDate()
      : null,
  );

  const [workingEndDate, setWorkingEndDate] = useState<Date | null>(
    officeInfoMatchProfileAppointmentInfo?.workingEndDate
      ? momentTz(officeInfoMatchProfileAppointmentInfo?.workingEndDate).toDate()
      : null,
  );

  const { form, onSubmit } =
    useBtaskeeFormController<FormUpdatingTaskerOnBoardingScheduleInfo>({
      zodRaw: {
        date: z.date().refine(
          date => {
            if (momentTz(date).isBefore(momentTz())) {
              return false;
            }

            const hoursFromWorkingStartDate = workingStartDate?.getHours() || 0;
            const hoursFromWorkingEndDate = workingEndDate?.getHours() || 0;
            const secondsFromWorkingStartDate =
              workingStartDate?.getMinutes() || 0;
            const secondsFromWorkingEndDate = workingEndDate?.getMinutes() || 0;

            if (
              date?.getHours() > hoursFromWorkingEndDate ||
              date?.getHours() < hoursFromWorkingStartDate
            ) {
              return false;
            }

            if (
              date?.getHours() === hoursFromWorkingEndDate &&
              date?.getMinutes() > secondsFromWorkingEndDate
            ) {
              return false;
            }

            if (
              date?.getHours() === hoursFromWorkingStartDate &&
              date?.getMinutes() < secondsFromWorkingStartDate
            ) {
              return false;
            }

            return true;
          },
          date => {
            if (momentTz(date).isBefore(momentTz())) {
              return {
                message: tTaskerProfile('INVALID_DATE'),
              };
            }

            return {
              message: tTaskerProfile('MUST_BE_IN_RANGE_OF_WORKING_TIME', {
                from: workingStartDate ? format(workingStartDate, 'HH:mm') : '',
                to: workingEndDate ? format(workingEndDate, 'HH:mm') : '',
              }),
            };
          },
        ),
        officeInfo: z.string().min(1, tTaskerProfile('THIS_IS_REQUIRE_FIELD')),
      },
      // eslint-disable-next-line @typescript-eslint/ban-ts-comment
      // @ts-ignore
      defaultValues: {
        date: profileInfo.appointmentInfo?.date
          ? momentTz(profileInfo.appointmentInfo?.date).seconds(0).toDate()
          : undefined,
        officeInfo: profileInfo.appointmentInfo
          ? JSON.stringify({
              name: profileInfo.appointmentInfo?.name || '',
              address: profileInfo.appointmentInfo?.address || '',
              city: profileInfo.appointmentInfo?.city || '',
              phoneNumber: profileInfo.appointmentInfo?.phoneNumber || '',
            })
          : '',
      },
      confirmParams: {
        title: tTaskerProfile('TITLE_CONFIRM_UPDATING_SCHEDULE'),
        body: tTaskerProfile('DESCRIPTION_CONFIRM_UPDATING_SCHEDULE'),
        cancelButton: tTaskerProfile('CANCEL'),
        actionButton: tTaskerProfile('CONFIRM'),
      },
      formDataProvided: ({ date, officeInfo }) => {
        setOpenDialog(false);

        const formData = new FormData();

        formData.append('officeInfo', officeInfo);
        formData.append(
          'currentProfileStatus',
          getOverallStatusInProfile({
            status: profileInfo.status,
            processStatus: profileInfo.processStatus,
            isHaveAppointmentInfo: !!profileInfo.appointmentInfo,
          }),
        );
        formData.append(
          'scheduleMessage',
          JSON.stringify({
            title:
              MESSAGE_ALL_LANGUAGE_ON_TASKER_ONBOARDING.MESSAGE_RESCHEDULE_PROFILE_TITLE,
            body: MESSAGE_ALL_LANGUAGE_ON_TASKER_ONBOARDING.MESSAGE_RESCHEDULE_PROFILE_BODY,
          }),
        );
        formData.append('date', date?.toString() || '');
        formData.append('language', profileInfo?.taskerLanguage || '');

        return formData;
      },
    });

  useEffect(() => {
    if (openDialog) {
      const officeFound = offices.find(
        office =>
          office.name === profileInfo.appointmentInfo?.name &&
          office.address === profileInfo.appointmentInfo?.address &&
          office.phoneNumber === profileInfo.appointmentInfo?.phoneNumber,
      );
      setWorkingStartDate(
        officeFound?.workingStartDate
          ? momentTz(officeFound?.workingStartDate).toDate()
          : null,
      );
      setWorkingEndDate(
        officeFound?.workingEndDate
          ? momentTz(officeFound?.workingEndDate).toDate()
          : null,
      );
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [openDialog]);

  return (
    <Dialog
      open={openDialog}
      onOpenChange={() => {
        form.reset();
        setOpenDialog(true);
      }}
      defaultOpen={true}>
      <DialogContent
        onInteractOutside={() => {
          setOpenDialog(false);
        }}>
        <DialogTitle className="text-2xl tracking-tighter font-semibold">
          {tTaskerProfile('TITLE_CONFIRM_UPDATING_SCHEDULE')}
        </DialogTitle>
        <DialogDescription>
          {tTaskerProfile('DESCRIPTION_CONFIRM_UPDATING_SCHEDULE')}
        </DialogDescription>
        <ConfirmationDescriptions
          contents={[
            {
              title: tTaskerProfile('TASKER_NAME'),
              description: profileInfo.taskerName,
            },
          ]}
        />
        <Separator className="my-4" />

        <div className="grid gap-3">
          <Form {...form}>
            <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-3">
              <FormField
                name="officeInfo"
                control={form.control}
                render={({ field }) => (
                  <FormItem>
                    <FormLabel className={'text-gray-700'}>
                      {tTaskerProfile('INTERVIEW_OFFICE')}
                    </FormLabel>
                    <FormControl>
                      <SelectBase
                        defaultValue={field.value}
                        placeholder={tTaskerProfile('CHOOSE_OFFICE')}
                        onValueChange={value => {
                          const parsedInfoValue = JSON.parse(value || '{}');
                          const officeFound = offices.find(
                            office =>
                              office.name === parsedInfoValue?.name &&
                              office.address === parsedInfoValue?.address &&
                              office.phoneNumber ===
                                parsedInfoValue?.phoneNumber,
                          );

                          setWorkingStartDate(
                            officeFound?.workingStartDate
                              ? momentTz(officeFound?.workingStartDate).toDate()
                              : null,
                          );
                          setWorkingEndDate(
                            officeFound?.workingEndDate
                              ? momentTz(officeFound?.workingEndDate).toDate()
                              : null,
                          );

                          field.onChange(value);
                        }}
                        options={offices.map(office => ({
                          label: office?.name,
                          value: JSON.stringify({
                            name: office?.name || '',
                            address: office?.address || '',
                            city: office?.city || '',
                            phoneNumber: office?.phoneNumber || '',
                          }),
                        }))}
                      />
                    </FormControl>
                  </FormItem>
                )}
              />
              {form.watch('officeInfo') ? (
                workingStartDate && workingEndDate ? (
                  <SingleDateTimePicker
                    form={form}
                    name="date"
                    label={tTaskerProfile('INTERVIEW_TIME')}
                    format="HH:mm - dd/MM/yyyy"
                    disableTimePickers={['seconds']}
                  />
                ) : (
                  <Typography variant="p" className="text-yellow-400">
                    {tTaskerProfile(
                      'WARNING_UPDATE_WORKING_RANGE_DATE_FOR_OFFICE',
                    )}
                  </Typography>
                )
              ) : null}
              <div className="flex justify-end gap-4 pt-6">
                <DialogClose className="absolute z-10 right-4 top-4 rounded-sm w-5 h-5 cursor-default bg-white" />
                <Button
                  type="button"
                  variant="outline"
                  className="border-primary text-primary hover:text-primary"
                  onClick={() => {
                    form.reset();
                    setOpenDialog(false);
                  }}>
                  {tTaskerProfile('BACK')}
                </Button>
                <Button
                  type="submit"
                  disabled={!form.watch('date')}
                  className={cn(
                    'bg-primary hover:bg-primary-600',
                    !form.watch('date') ? 'bg-gray-300 text-white' : '',
                  )}>
                  {tTaskerProfile('SUBMIT')}
                </Button>
              </div>
            </form>
          </Form>
        </div>
      </DialogContent>
    </Dialog>
  );
};

const TaskerProfileCard = ({
  children,
  title,
  extraContent,
}: {
  children: ReactNode;
  title?: string;
  extraContent?: ReactNode;
}) => (
  <Card className="bg-gray-50 mt-6">
    {title ? (
      <CardHeader>
        <div className="flex justify-between items-center">
          <Typography variant="h4">{title}</Typography>
          {extraContent ?? null}
        </div>
        <Separator className="w-[200px]" />
      </CardHeader>
    ) : null}
    <CardContent>{children}</CardContent>
  </Card>
);

export {
  TaskerProfileCard,
  UpdateNoteDialog,
  UpdateScheduleDialog,
  CardProfileDescription,
  ConfirmUpdatingProfile,
  ProfileImageAccordion,
  TaskerProfileAction,
  TaskerProfileStatus,
  TaskerProfileTabModel,
  ConfirmationDescriptions,
  NoteForm,
  WarningMissingReasonConfigurationDialog,
};
