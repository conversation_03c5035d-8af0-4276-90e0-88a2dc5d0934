import { MAXIMUM_TOOL_IMAGE_FILE_LENGTH } from 'btaskee-constants';
import {
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
  Input,
  MultiLanguageSection,
  Typography,
} from 'btaskee-ui';
import type { UseFormReturn } from 'react-hook-form';
import { useTranslation } from 'react-i18next';

import { MediaUploaderCard } from '../form/MediaUploaderCard';
import { SimpleImageUpload } from '../form/SimpleImageUpload';

interface ToolRequireFormProps {
  form: UseFormReturn<ToolRequireFormValue, MustBeAny, undefined>;
  currencyCode: string;
}

const ToolRequireForm = ({ form, currencyCode }: ToolRequireFormProps) => {
  const { t } = useTranslation('tool-require-create');
  return (
    <div className="flex justify-between gap-6">
      <MediaUploaderCard
        className="pb-4 h-fit w-[448px]"
        isShowCloseButton={false}>
        <FormField
          control={form.control}
          name="image.value"
          render={({ field: { onChange, ref, value }, fieldState }) => (
            <FormItem
              className={`flex flex-col items-center h-fit ${fieldState.error ? 'border-red-500' : 'border-gray-200'}`}>
              <FormControl>
                <SimpleImageUpload
                  fieldRef={ref}
                  avatarUrl={value}
                  cardTitle={t('UPLOAD_IMAGE')}
                  onFileChange={file => onChange(file)}
                  maxContentLength={MAXIMUM_TOOL_IMAGE_FILE_LENGTH.VALUE}
                  description={t('UPLOAD_IMAGE_REQUIRE')}
                  subDescription={t('RATIO', { ratio: '(1:1)' })}
                  ratio={1 / 1}
                  formValue={value}
                />
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />
      </MediaUploaderCard>
      <FormField
        name={'priceChar'}
        control={form.control}
        render={({ field }) => (
          <FormItem className="flex-1">
            <FormLabel>{t('PRICE')}</FormLabel>
            <FormControl>
              <Input
                {...field}
                onChange={val => {
                  // The price input field does not allow entry and displays the amount as 100,000
                  const amount = val.target.value
                    .replace(/\D/g, '')
                    .replace(/^0+/, '')
                    .replace(/\B(?=(\d{3})+(?!\d))/g, ',');
                  field.onChange(amount);
                }}
                placeholder={t('ENTER_AMOUNT')}
                endAdornment={
                  <Typography className="font-normal text-sm leading-[18px]">
                    {currencyCode}
                  </Typography>
                }
              />
            </FormControl>
            <FormMessage />
          </FormItem>
        )}
      />
      <div className="flex-1">
        <MultiLanguageSection
          childrenProps={[
            {
              name: 'text',
              label: t('TOOL_NAME'),
            },
          ]}
          form={form}>
          {[<Input key={1} placeholder={t('ENTER_TOOL_NAME')} />]}
        </MultiLanguageSection>
      </div>
    </div>
  );
};

export { ToolRequireForm };
