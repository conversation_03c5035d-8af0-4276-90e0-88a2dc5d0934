import type { SerializeFrom } from '@remix-run/node';
import type { ColumnDef } from '@tanstack/react-table';
import {
  AspectRatio,
  Button,
  Checkbox,
  DataTableBasic,
  DataTableColumnHeader,
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
  MultiLanguageText,
  Separator,
  Typography,
} from 'btaskee-ui';
import { formatNumberWithCommas } from 'btaskee-utils';
import type { TFunction } from 'i18next';
import { Plus, Trash2 } from 'lucide-react';
import type { Dispatch, SetStateAction } from 'react';
import { useMemo } from 'react';

//TODO: Implement translate in this component
export default function AddToolkitItemDialog({
  data = [],
  open,
  setOpen,
  selectedToolkitItems = [],
  setSelectedToolkitItems,
  draftSelectedToolkitItems,
  setDraftSelectedToolkitItems,
  t,
  currencySign,
}: {
  data: SerializeFrom<ToolKitItem[]>;
  open: boolean;
  setOpen: (open: boolean) => void;
  selectedToolkitItems: SerializeFrom<ToolKitItem[]>;
  setSelectedToolkitItems: Dispatch<
    SetStateAction<SerializeFrom<ToolKitItem[]>>
  >;
  draftSelectedToolkitItems: SerializeFrom<ToolKitItem[]>;
  setDraftSelectedToolkitItems: Dispatch<
    SetStateAction<SerializeFrom<ToolKitItem[]>>
  >;
  t: TFunction;
  currencySign: string;
}) {
  const columns: ColumnDef<SerializeFrom<ToolKitItem>>[] = useMemo(() => {
    return [
      {
        accessorKey: '_id',
        header: ({ table }) => (
          <Checkbox
            checked={
              table.getIsAllRowsSelected() ||
              draftSelectedToolkitItems.length === data.length
            }
            onCheckedChange={value => {
              table.toggleAllPageRowsSelected(!!value);
              if (value) {
                setDraftSelectedToolkitItems(data);
              } else {
                setDraftSelectedToolkitItems([]);
              }
            }}
            aria-label="Select all"
            className="translate-y-[2px]"
          />
        ),
        cell: ({ row }) => (
          <Checkbox
            checked={draftSelectedToolkitItems.some(
              item => item._id === row.original._id,
            )}
            onCheckedChange={value => {
              row.toggleSelected(!!value);
              const updatedRow = row.original;
              if (value) {
                setDraftSelectedToolkitItems((prev: MustBeAny) => [
                  ...prev,
                  updatedRow,
                ]);
              } else {
                setDraftSelectedToolkitItems(prev =>
                  prev.filter(item => item._id !== updatedRow._id),
                );
              }
            }}
            aria-label="Select row"
            className="translate-y-[2px]"
          />
        ),
        size: 20,
        enableSorting: false,
      },
      {
        accessorKey: 'tool',
        header: ({ column }) => (
          <DataTableColumnHeader column={column} title={t('TOOL')} />
        ),
        size: 500,
        cell: ({ row }) => (
          <div className="flex gap-2 items-center">
            <div className="min-w-10 max-w-10">
              <AspectRatio ratio={1 / 1}>
                <img
                  src={row?.original?.image || ''}
                  alt="Toolkit"
                  className="rounded-sm object-cover w-full h-full"
                />
              </AspectRatio>
            </div>
            <MultiLanguageText text={row.original.text} layout="row" />
          </div>
        ),
        enableSorting: false,
      },
      {
        accessorKey: 'price',
        header: ({ column }) => (
          <DataTableColumnHeader column={column} title={t('PRICE')} />
        ),
        size: 500,
        cell: ({ row }) => (
          <Typography variant="p" affects="removePMargin">
            {formatNumberWithCommas(row.original.price)}
            {currencySign}
          </Typography>
        ),
        enableSorting: false,
      },
      {
        accessorKey: 'action',
        header: ({ column }) => (
          <DataTableColumnHeader
            className="text-center"
            column={column}
            title={t('ACTION')}
          />
        ),
        size: 500,
        cell: ({ row }) =>
          draftSelectedToolkitItems.some(
            item => item._id === row.original._id,
          ) ? (
            <div className="w-full flex justify-center">
              <Button
                type="button"
                variant="ghost"
                className="text-gray-500 hover:text-gray-600 hover:bg-gray-200 gap-2 h-6 py-0.5"
                onClick={() => {
                  setDraftSelectedToolkitItems(prev =>
                    prev.filter(item => item._id !== row.original._id),
                  );
                }}>
                <Trash2 className="w-4 h-4" />
                {t('REMOVE')}
              </Button>
            </div>
          ) : (
            <div className="w-full flex justify-center">
              <Button
                type="button"
                variant="ghost"
                className="text-primary hover:text-primary hover:bg-primary-50 gap-2 h-6 py-0.5"
                onClick={() => {
                  setDraftSelectedToolkitItems((prev: MustBeAny) => [
                    ...prev,
                    row.original,
                  ]);
                }}>
                <Plus className="w-4 h-4" />
                {t('ADD')}
              </Button>
            </div>
          ),
        enableSorting: false,
      },
    ];
  }, [
    currencySign,
    data,
    draftSelectedToolkitItems,
    setDraftSelectedToolkitItems,
    t,
  ]);

  return (
    <Dialog open={open} onOpenChange={setOpen}>
      <DialogContent
        className="max-w-5xl max-h-[90vh] overflow-y-auto"
        onInteractOutside={() => {
          setDraftSelectedToolkitItems(selectedToolkitItems);
        }}>
        <DialogHeader className="space-y-2">
          <DialogTitle className="text-2xl tracking-tighter font-semibold">
            {t('ADD_TOOLKIT')}
          </DialogTitle>
        </DialogHeader>
        <DataTableBasic
          isManualCountSelectedRow
          columns={columns}
          data={data}
        />
        <Separator />
        <div className="flex flex-col items-end gap-3">
          <Typography variant="p" className="text-secondary-foreground">
            {draftSelectedToolkitItems.length} {t('SELECTED')}
          </Typography>

          <div className="flex gap-4">
            <Button
              className="border-primary text-primary hover:bg-primary-foreground hover:text-primary"
              type="button"
              variant="outline"
              onClick={() => {
                setDraftSelectedToolkitItems(selectedToolkitItems);
                setOpen(false);
              }}>
              {t('CANCEL')}
            </Button>
            <Button
              variant="default"
              type="button"
              onClick={() => {
                setSelectedToolkitItems(draftSelectedToolkitItems);
                setOpen(false);
              }}>
              {t('CONFIRM')}
            </Button>
          </div>
        </div>
      </DialogContent>
    </Dialog>
  );
}
