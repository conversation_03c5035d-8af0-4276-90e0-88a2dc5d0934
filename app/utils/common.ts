import { redirect } from '@remix-run/node';
import { IsoCode } from 'btaskee-constants';
// import type { PipelineStage } from 'mongo-connection';
import i18next from '~/i18next.server';
import { commitSession, getSession } from '~/services/session.server';

const finalizeAction = async ({
    request,
    flashMessage,
    destinationUrl,
}: {
    request: Request;
    flashMessage: {
        message: string;
        translationKey: string;
    };
    destinationUrl: string;
}) => {
    const session = await getSession(request.headers.get('cookie'));
    const t = await i18next.getFixedT(request, flashMessage.translationKey);
    session.flash('flashMessage', t(flashMessage.message));
    const newSession = await commitSession(session);

    return redirect(destinationUrl, {
        headers: {
            'Set-Cookie': newSession,
        },
    });
};

// TODO: move this func to utils repos
function formatPhoneNumber(phoneNumber: string) {
    if (!phoneNumber) {
        return '';
    }

    const pattern = [4, 3];
    let formattedPhoneNumber = '';
    let index = 0;

    for (let i = 0; i < phoneNumber.length; i++) {
        if (
            index < pattern.length &&
            i === pattern.slice(0, index + 1).reduce((a, b) => a + b, 0)
        ) {
            formattedPhoneNumber += ' ';
            index++;
        } else if (
            index >= pattern.length &&
            (i - pattern.reduce((a, b) => a + b, 0)) % 3 === 0 &&
            i !== 0
        ) {
            formattedPhoneNumber += ' ';
        }

        formattedPhoneNumber += phoneNumber[i];
    }

    return formattedPhoneNumber;
}

export { finalizeAction, formatPhoneNumber };

export const getCountryByIsoCode = ({ isoCode }: { isoCode: `${IsoCode}` }) => {
    if (!isoCode) {
        throw new Error('isoCode is required');
    }

    if (isoCode === IsoCode.VN) {
        return 'Vietnam';
    }

    if (isoCode === IsoCode.ID) {
        return 'Indonesia';
    }

    if (isoCode === IsoCode.TH) {
        return 'Thailand';
    }

    if (isoCode === IsoCode.MY) {
        return 'Malaysia';
    }

    throw new Error(`Not support this ${isoCode} IsoCode`);
};
