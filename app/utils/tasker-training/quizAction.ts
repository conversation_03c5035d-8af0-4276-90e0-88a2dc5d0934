import type { UploadHandler } from '@remix-run/node';
import {
    unstable_composeUploadHandlers,
    unstable_createMemoryUploadHandler,
    unstable_parseMultipartFormData,
} from '@remix-run/node';
import { ACTION_NAME, ROUTE_NAME } from 'btaskee-constants';
import type { SetInformationActionHistory } from '~/hoc/remix';
import { getUserSession } from '~/services/helpers.server';
import { createQuiz, updateQuiz } from '~/services/tasker-training.server';
import { s3UploadHandler } from '~/third-party/s3.server';
import { finalizeAction } from '~/utils/common';

interface HandleQuizActionProps {
    request: Request;
    isEdit: boolean;
    setInformationActionHistory: SetInformationActionHistory;
    quizId?: string;
}

export async function handleQuizAction({
    request,
    isEdit = false,
    setInformationActionHistory,
    quizId,
}: HandleQuizActionProps) {
    const uploadHandler: UploadHandler = unstable_composeUploadHandlers(
        async formField => {
            if (formField.name === 'image') {
                return await s3UploadHandler(formField);
            }
            return undefined;
        },
        unstable_createMemoryUploadHandler(),
    );

    const formData = await unstable_parseMultipartFormData(
        request.clone(),
        uploadHandler,
    );
    const parsedData = JSON.parse(formData.get('data')?.toString() || '{}');
    const uploadedImageUrl = formData.get('image')?.toString();

    const { isoCode, userId, username } = await getUserSession({
        headers: request.headers,
    });

    const finalData: MustBeAny = {
        ...parsedData,
        ...(isEdit
            ? { updatedByUserId: userId, updatedByUsername: username }
            : { createdByUserId: userId, createdByUsername: username }),
        ...(parsedData.image && {
            image: {
                url: uploadedImageUrl || parsedData.image.value,
                description: parsedData.image.description,
            },
        }),
    };

    let newQuiz;
    if (isEdit && quizId) {
        await updateQuiz({
            isoCode,
            quizId,
            params: finalData,
        });
        newQuiz = { _id: quizId };
    } else {
        newQuiz = await createQuiz({
            isoCode,
            params: finalData,
        });
    }

    setInformationActionHistory({
        action: isEdit ? ACTION_NAME.UPDATE_QUIZ : ACTION_NAME.CREATE_QUIZ,
    });

    return finalizeAction({
        request,
        flashMessage: {
            message: isEdit
                ? 'UPDATE_QUIZ_SUCCESSFULLY'
                : 'CREATE_QUIZ_SUCCESSFULLY',
            translationKey: 'quiz',
        },
        destinationUrl: `${ROUTE_NAME.QUIZ}/${newQuiz._id}`,
    });
}
