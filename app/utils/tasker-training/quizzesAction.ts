import type { UploadHandler } from '@remix-run/node';
import {
    unstable_composeUploadHandlers,
    unstable_createMemoryUploadHandler,
    unstable_parseMultipartFormData,
} from '@remix-run/node';
import { ACTION_NAME, ROUTE_NAME } from 'btaskee-constants';
import type { SetInformationActionHistory } from '~/hoc/remix';
import { getUserSession } from '~/services/helpers.server';
import {
    createQuizCollection,
    createQuizzes,
    updateQuizCollection,
} from '~/services/tasker-training.server';
import { s3UploadHandler } from '~/third-party/s3.server';
import { finalizeAction } from '~/utils/common';

interface handleQuizActionProps {
    request: Request;
    isEdit: boolean;
    setInformationActionHistory: SetInformationActionHistory;
}

export async function handleQuizAction({
    request,
    isEdit = false,
    setInformationActionHistory,
}: handleQuizActionProps) {
    const uploadHandler: UploadHandler = unstable_composeUploadHandlers(
        async formField => {
            if (
                formField.name === 'collection_image' ||
                (formField.name.startsWith('quizzes_') &&
                    formField.name.endsWith('_image'))
            ) {
                return await s3UploadHandler(formField);
            }

            return undefined;
        },
        unstable_createMemoryUploadHandler(),
    );

    const formData = await unstable_parseMultipartFormData(
        request.clone(),
        uploadHandler,
    );
    const parsedData = JSON.parse(formData.get('data')?.toString() || '{}');
    const uploadedCollectionImageUrl = formData
        .get('collection_image')
        ?.toString();

    const dataQuizzesWithUploadedImage = parsedData.quizzes.map(
        (quiz: QuizWillBeAddedToCollection) => {
            const uploadedQuizzesImageUrl = formData
                .get(`quizzes_${quiz._id}_image`)
                ?.toString();

            if (quiz.isCreatedByForm && uploadedQuizzesImageUrl) {
                return {
                    ...quiz,
                    image: {
                        url: uploadedQuizzesImageUrl,
                        description: quiz.image?.description || '',
                    },
                };
            }
            return quiz;
        },
    );

    const { isoCode, userId, username } = await getUserSession({
        headers: request.headers,
    });

    const dataNeedToStorage = dataQuizzesWithUploadedImage
        .filter((quiz: QuizWillBeAddedToCollection) => quiz.isNewQuiz)
        .map((quiz: QuizWillBeAddedToCollection) => {
            return {
                ...quiz,
                createdByUserId: userId,
                createdByUsername: username,
            };
        });

    const finalData = {
        ...parsedData,
        quizzes: dataQuizzesWithUploadedImage
            .sort(
                (
                    a: QuizWillBeAddedToCollection,
                    b: QuizWillBeAddedToCollection,
                ) => a.quizOrder - b.quizOrder,
            )
            .map((quiz: QuizWillBeAddedToCollection, index: number) => ({
                _id: quiz._id,
                order: index,
            })),
        ...(isEdit
            ? {
                  updatedByUserId: userId,
                  updatedByUsername: username,
              }
            : {
                  createdByUserId: userId,
                  createdByUsername: username,
              }),
        ...(uploadedCollectionImageUrl && {
            image: {
                url: uploadedCollectionImageUrl,
                description: parsedData.image?.description || '',
            },
        }),
    };

    let newQuizzes;
    const quizzesId = JSON.parse(formData.get('_id')?.toString() || '{}') || '';
    if (isEdit && quizzesId) {
        await updateQuizCollection({
            isoCode,
            params: finalData,
            _id: quizzesId._id,
        });
        newQuizzes = { _id: quizzesId._id };
    } else {
        newQuizzes = await createQuizCollection({
            isoCode,
            params: finalData,
        });
    }

    if (dataNeedToStorage.length) {
        await createQuizzes({ isoCode, params: dataNeedToStorage });
        setInformationActionHistory({
            action: ACTION_NAME.CREATE_QUIZ,
        });
    }

    return finalizeAction({
        request,
        flashMessage: {
            message: isEdit
                ? 'UPDATE_QUIZ_COLLECTION_SUCCESS'
                : 'CREATE_QUIZ_COLLECTION_SUCCESS',
            translationKey: 'quiz',
        },
        destinationUrl: `${ROUTE_NAME.QUIZ_COLLECTION}/${newQuizzes._id}`,
    });
}
