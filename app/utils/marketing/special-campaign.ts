import type { UploadHandler } from '@remix-run/node';
import {
    unstable_composeUploadHandlers,
    unstable_createMemoryUploadHandler,
    unstable_parseMultipartFormData,
} from '@remix-run/node';
import { ROUTE_NAME } from 'btaskee-constants';
import { momentTz } from 'btaskee-utils';
import type { SetInformationActionHistory } from '~/hoc/remix';
import { getUserSession } from '~/services/helpers.server';
import {
    createSpecialCampaign,
    updateSpecialCampaign,
} from '~/services/special-campaign.server';
import { s3UploadHandler } from '~/third-party/s3.server';
import { finalizeAction } from '~/utils/common';

interface HandleSpecialCampaignActionProps {
    request: Request;
    isEdit: boolean;
    setInformationActionHistory: SetInformationActionHistory;
    specialCampaignId?: string;
}

export async function handleSpecialCampaignAction({
    request,
    isEdit = false,
    setInformationActionHistory,
    specialCampaignId,
}: HandleSpecialCampaignActionProps) {
    const uploadHandler: UploadHandler = unstable_composeUploadHandlers(
        async formField => {
            if (formField.name === 'image') {
                return await s3UploadHandler(formField);
            }
            return undefined;
        },
        unstable_createMemoryUploadHandler(),
    );

    const formData = await unstable_parseMultipartFormData(
        request.clone(),
        uploadHandler,
    );
    const parsedData = JSON.parse(formData.get('data')?.toString() || '{}');
    const uploadedImageUrl = formData.get('image')?.toString();

    const { isoCode, username } = await getUserSession({
        headers: request.headers,
    });

    const { rangeDate, city, ...restData } = parsedData;

    const finalData = {
        ...restData,
        startDate: momentTz(rangeDate.from).toDate(),
        endDate: momentTz(rangeDate.to).toDate(),
        image: {
            imageUrl: uploadedImageUrl || restData.image.imageUrl,
        },
        ...(city?.length ? { city } : {}),
        ...(isEdit ? { updatedBy: username } : { createdBy: username }),
    };

    let newSpecialCampaign;
    if (isEdit && specialCampaignId) {
        await updateSpecialCampaign({
            isoCode,
            specialCampaignId,
            params: finalData,
        });
        newSpecialCampaign = { _id: specialCampaignId };
    } else {
        newSpecialCampaign = await createSpecialCampaign({
            isoCode,
            params: finalData,
        });
    }

    setInformationActionHistory({
        action: isEdit ? 'UPDATE_SPECIAL_CAMPAIGN' : 'CREATE_SPECIAL_CAMPAIGN',
    });

    return finalizeAction({
        request,
        flashMessage: {
            message: isEdit
                ? 'UPDATE_SPECIAL_CAMPAIGN_SUCCESSFULLY'
                : 'CREATE_SPECIAL_CAMPAIGN_SUCCESSFULLY',
            translationKey: 'special-campaign',
        },
        destinationUrl: `${ROUTE_NAME.SPECIAL_CAMPAIGN}/${newSpecialCampaign._id}`,
    });
}
