import { IsoCode } from 'btaskee-constants';
import { momentTz } from 'btaskee-utils';
import type { PipelineStage } from 'mongo-connection';
import { getModels } from 'schemas';
import {
    getListTaskerOnAllTaskerPage,
    getTotalTaskerOnAllTaskerPage,
    getTotalUsers,
} from '~/services/tasker-list.server';

describe('All Tasker Page', () => {
    const isoCode = IsoCode.VN;
    const CLEANING_SERVICE_ID = 'CLEANING_SERVICE_ID';
    const HOME_MOVING_SERVICE_ID = 'HOME_MOVING_SERVICE_ID';
    const MOCK_HO_CHI_MINH_CITY = 'Hồ Chí Minh';
    const MOCK_HA_NOI_CITY = 'Hà Nội';
    const ACTIVE_USER_STATUS: UserApp['status'] = 'ACTIVE';
    const IN_ACTIVE_USER_STATUS: UserApp['status'] = 'INACTIVE';
    const TASKER_USER_TYPE: UserAppType = 'TASKER';
    const FIRST_PREFIX_SEARCHING_ALL_TASKER = '090';

    const CLEANING_TASKER_ACTIVE_IN_HCM = {
        _id: 'cleaningTaskerActiveInHCMId',
        type: TASKER_USER_TYPE,
        name: 'cleaningTaskerActiveInHCMId',
        phone: `${FIRST_PREFIX_SEARCHING_ALL_TASKER}1234567`,
        status: ACTIVE_USER_STATUS,
        workingPlaces: [
            {
                city: MOCK_HO_CHI_MINH_CITY,
            },
        ],
        language: 'vi',
        createdAt: momentTz().toDate(),
        fAccountId: 'fAccountId_taskerActiveInHCMId',
    };
    const CLEANING_TASKER_ACTIVE_IN_HA_NOI = {
        _id: 'cleaningTaskerActiveInHaNoiId',
        type: TASKER_USER_TYPE,
        name: 'cleaningTaskerActiveInHaNoiId',
        phone: `${FIRST_PREFIX_SEARCHING_ALL_TASKER}1223344`,
        status: ACTIVE_USER_STATUS,
        workingPlaces: [
            {
                city: MOCK_HA_NOI_CITY,
            },
        ],
        createdAt: momentTz().toDate(),
        language: 'vi',
        fAccountId: 'fAccountId_taskerActiveInHCMId',
    };
    const HOME_MOVING_TASKER_ACTIVE_IN_HCM = {
        _id: 'taskerActiveInHCMId',
        type: TASKER_USER_TYPE,
        name: 'taskerActiveInHCMId',
        phone: `${FIRST_PREFIX_SEARCHING_ALL_TASKER}112233`,
        status: ACTIVE_USER_STATUS,
        workingPlaces: [
            {
                city: MOCK_HO_CHI_MINH_CITY,
            },
        ],
        language: 'vi',
        createdAt: momentTz().toDate(),
        fAccountId: 'fAccountId_taskerActiveInHCMId',
    };

    const CLEANING_SERVICE_CHANNEL = {
        _id: 'CLEANING_SERVICE_CHANNEL_ID',
        serviceId: CLEANING_SERVICE_ID,
        taskerList: [
            CLEANING_TASKER_ACTIVE_IN_HCM._id,
            CLEANING_TASKER_ACTIVE_IN_HA_NOI._id,
        ],
    };

    const HOME_MOVING_SERVICE_CHANNEL = {
        _id: 'HOME_MOVING_SERVICE_CHANNEL_ID',
        serviceId: HOME_MOVING_SERVICE_ID,
        taskerList: [HOME_MOVING_TASKER_ACTIVE_IN_HCM._id],
    };

    beforeAll(async () => {
        await Promise.all([
            getModels(isoCode).serviceChannel.insertMany([
                CLEANING_SERVICE_CHANNEL,
                HOME_MOVING_SERVICE_CHANNEL,
            ]),
            getModels(isoCode).users.insertMany([
                CLEANING_TASKER_ACTIVE_IN_HCM,
                CLEANING_TASKER_ACTIVE_IN_HA_NOI,
                HOME_MOVING_TASKER_ACTIVE_IN_HCM,
            ]),
        ]);
    });

    afterAll(async () => {
        await Promise.all([
            getModels(isoCode).serviceChannel.deleteMany({
                _id: {
                    $in: [
                        CLEANING_SERVICE_CHANNEL._id,
                        HOME_MOVING_SERVICE_CHANNEL._id,
                    ],
                },
            }),
            getModels(isoCode).users.deleteMany({
                _id: {
                    $in: [
                        CLEANING_TASKER_ACTIVE_IN_HCM._id,
                        CLEANING_TASKER_ACTIVE_IN_HA_NOI._id,
                        HOME_MOVING_TASKER_ACTIVE_IN_HCM._id,
                    ],
                },
            }),
        ]);
    });

    describe('getListTaskerOnAllTaskerPage', () => {
        it('Should return list tasker after filter by list services, status; search by empty string then pagination successfully', async () => {
            const MOCK_PARAMS = {
                skip: 0,
                limit: 10,
                sort: { createdAt: -1 } as PipelineStage.Sort['$sort'],
                filterValue: {
                    search: CLEANING_TASKER_ACTIVE_IN_HCM.phone,
                    services: CLEANING_SERVICE_ID,
                    status: ACTIVE_USER_STATUS,
                },
                city: [MOCK_HO_CHI_MINH_CITY, MOCK_HA_NOI_CITY],
                isoCode,
            };
            const MOCK_TASKERS_FILTER_BY_MOCK_PARAMS_ABOVE = [
                CLEANING_TASKER_ACTIVE_IN_HCM,
            ];

            const taskersFoundByQuery =
                await getListTaskerOnAllTaskerPage(MOCK_PARAMS);

            expect(taskersFoundByQuery).toHaveLength(
                MOCK_TASKERS_FILTER_BY_MOCK_PARAMS_ABOVE.length,
            );
            taskersFoundByQuery.forEach(taskerFoundByQuery => {
                expect(
                    MOCK_TASKERS_FILTER_BY_MOCK_PARAMS_ABOVE.find(
                        mockTasker => mockTasker._id === taskerFoundByQuery._id,
                    ),
                ).toBeDefined();
            });
        });
        it('Should return the empty array when search less than 3 characters, filter by list services, status and city', async () => {
            const MOCK_PARAMS = {
                skip: 0,
                limit: 10,
                sort: { createdAt: -1 } as PipelineStage.Sort['$sort'],
                filterValue: {
                    search: '',
                    services: CLEANING_SERVICE_ID,
                    status: ACTIVE_USER_STATUS,
                },
                city: [MOCK_HO_CHI_MINH_CITY, MOCK_HA_NOI_CITY],
                isoCode,
            };

            const taskersFoundByQuery =
                await getListTaskerOnAllTaskerPage(MOCK_PARAMS);

            expect(taskersFoundByQuery).toHaveLength(0);
        });
    });

    describe('getTotalTaskerOnAllTaskerPage', () => {
        it('Should get total tasker by filter by status, list services, list city; search by tasker name successfully', async () => {
            const MOCK_PARAMS = {
                filterValue: {
                    search: HOME_MOVING_TASKER_ACTIVE_IN_HCM.name,
                    services: [
                        CLEANING_SERVICE_ID,
                        HOME_MOVING_SERVICE_ID,
                    ].join(','),
                    status: [ACTIVE_USER_STATUS, IN_ACTIVE_USER_STATUS].join(
                        ',',
                    ),
                },
                isoCode,
                city: [MOCK_HO_CHI_MINH_CITY, MOCK_HA_NOI_CITY],
            };

            const MOCK_TOTAL_FILTERING_BY_MOCK_PARAMS_ABOVE = 2;

            const TOTAL_TASKER_BY_QUERY =
                await getTotalTaskerOnAllTaskerPage(MOCK_PARAMS);

            expect(TOTAL_TASKER_BY_QUERY).toEqual(
                MOCK_TOTAL_FILTERING_BY_MOCK_PARAMS_ABOVE,
            );
        });
        it('Should return 0 when search less than 3 characters, filter by list services, status and city', async () => {
            const MOCK_PARAMS = {
                filterValue: {
                    search: '',
                    services: [
                        CLEANING_SERVICE_ID,
                        HOME_MOVING_SERVICE_ID,
                    ].join(','),
                    status: [ACTIVE_USER_STATUS, IN_ACTIVE_USER_STATUS].join(
                        ',',
                    ),
                },
                isoCode,
                city: [MOCK_HO_CHI_MINH_CITY, MOCK_HA_NOI_CITY],
            };

            const TOTAL_TASKER_BY_QUERY =
                await getTotalTaskerOnAllTaskerPage(MOCK_PARAMS);

            expect(TOTAL_TASKER_BY_QUERY).toEqual(0);
        });
    });

    describe('getTotalUsers', () => {
        it('Should get total user by filtering with tasker type, status and cities of account successfully', async () => {
            const MOCK_PARAMS = {
                isoCode,
                userType: TASKER_USER_TYPE,
                userStatus: ACTIVE_USER_STATUS,
                cities: [MOCK_HO_CHI_MINH_CITY, MOCK_HA_NOI_CITY],
            };

            const MOCK_TOTAL_TASKER_FILTERING_BY_MOCK_PARAMS_ABOVE = 3;

            const TOTAL_USER_BY_QUERY = await getTotalUsers(MOCK_PARAMS);

            expect(TOTAL_USER_BY_QUERY).toEqual(
                MOCK_TOTAL_TASKER_FILTERING_BY_MOCK_PARAMS_ABOVE,
            );
        });
    });
});
