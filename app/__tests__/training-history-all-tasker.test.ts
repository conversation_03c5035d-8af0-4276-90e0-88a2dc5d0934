import { TYPE as TASKER_TRAINING_COURSE_TYPE } from 'btaskee-constants';
import { momentTz } from 'btaskee-utils';
import { type PipelineStage } from 'mongoose';
import { getModels } from 'schemas';
import { USER_STATUS, USER_TYPE } from '~/services/constants.server';
import {
    getTotalTrainingHistoryAllTasker,
    getTrainingHistoryAllTasker,
} from '~/services/training-history-all-tasker.server';

describe('training-history-all-tasker', () => {
    const isoCode = 'VN';
    const MOCK_COURSE_ID_1 = 'courseId1';
    const MOCK_COURSE_ID_2 = 'courseId2';
    const MOCK_COURSE_ID_3 = 'courseId3';

    const taskers = [
        {
            _id: 'taskerId1',
            name: 'tasker 1',
            phone: '090xxxxx1',
            language: 'vi',
            fAccountId: 'fAccountId1',
            type: USER_TYPE.TASKER,
            status: USER_STATUS.ACTIVE,
            workingPlaces: [
                {
                    city: 'H<PERSON> Ch<PERSON>',
                    district: 'Quận 1',
                },
            ],
            createdAt: momentTz().toDate(),
        },
    ];

    const TEST_COURSE = {
        _id: MOCK_COURSE_ID_1,
        title: 'Title',
        code: 'ABC',
        type: TASKER_TRAINING_COURSE_TYPE.TEST,
        status: 'ACTIVE',
        createdAt: momentTz().toDate(),
        createdByUsername: 'admin',
        createdByUserId: 'adminId',
    };

    const REVIEW_COURSE_1 = {
        _id: MOCK_COURSE_ID_2,
        title: 'Title',
        code: 'ABC',
        type: TASKER_TRAINING_COURSE_TYPE.REVIEW,
        status: 'ACTIVE',
        createdAt: momentTz().toDate(),
        createdByUsername: 'admin',
        createdByUserId: 'adminId',
    };

    const REVIEW_COURSE_2 = {
        _id: MOCK_COURSE_ID_3,
        title: 'Title',
        code: 'ABC',
        type: TASKER_TRAINING_COURSE_TYPE.REVIEW,
        status: 'ACTIVE',
        createdAt: momentTz().toDate(),
        createdByUsername: 'admin',
        createdByUserId: 'adminId',
    };

    const TEST_TRANING_COURSE_SUBMISSION = {
        _id: 'submissionId1',
        courseId: 'courseId1',
        taskerId: 'taskerId1',
        status: 'PASSED',
        changeHistories: [
            {
                key: 'UNBLOCK_TEST',
            },
        ],
        course: {
            _id: MOCK_COURSE_ID_1,
            type: TASKER_TRAINING_COURSE_TYPE.TEST,
        },
        numberOfSubmissions: 1,
        createdAt: momentTz().toDate(),
    };

    const REVIEW_TRAINING_COURSE_SUBMISSION_1 = {
        _id: 'submissionId2',
        courseId: 'courseId1',
        taskerId: 'taskerId1',
        status: 'PASSED',
        changeHistories: [
            {
                key: 'UNBLOCK_TEST',
            },
        ],
        course: {
            _id: MOCK_COURSE_ID_2,
            type: TASKER_TRAINING_COURSE_TYPE.REVIEW,
        },
        numberOfSubmissions: 1,
        createdAt: momentTz().toDate(),
    };

    const REVIEW_TRAINING_COURSE_SUBMISSION_2 = {
        _id: 'submissionId3',
        courseId: 'courseId1',
        taskerId: 'taskerId1',
        status: 'PASSED',
        changeHistories: [
            {
                key: 'UNBLOCK_TEST',
            },
        ],
        course: {
            _id: MOCK_COURSE_ID_3,
            type: TASKER_TRAINING_COURSE_TYPE.REVIEW,
        },
        numberOfSubmissions: 1,
        createdAt: momentTz().toDate(),
    };

    beforeEach(async () => {
        await getModels(isoCode).taskerTrainingCourse.insertMany([
            TEST_COURSE,
            REVIEW_COURSE_1,
            REVIEW_COURSE_2,
        ]);
        await getModels(isoCode).users.insertMany(taskers);
        await getModels(isoCode).taskerTrainingSubmission.insertMany([
            TEST_TRANING_COURSE_SUBMISSION,
            REVIEW_TRAINING_COURSE_SUBMISSION_1,
            REVIEW_TRAINING_COURSE_SUBMISSION_2,
        ]);
    });

    afterEach(async () => {
        await getModels(isoCode).taskerTrainingCourse.deleteMany({
            _id: {
                $in: [
                    TEST_COURSE._id,
                    REVIEW_COURSE_1._id,
                    REVIEW_COURSE_2._id,
                ],
            },
        });
        await getModels(isoCode).users.deleteMany({});
        await getModels(isoCode).taskerTrainingSubmission.deleteMany({
            _id: {
                $in: [
                    TEST_TRANING_COURSE_SUBMISSION._id,
                    REVIEW_TRAINING_COURSE_SUBMISSION_1._id,
                    REVIEW_TRAINING_COURSE_SUBMISSION_2._id,
                ],
            },
        });
    });

    describe('getTrainingHistoryAllTasker', () => {
        it('should return list training history of all taskers', async () => {
            const mockParams = {
                isoCode,
                filter: {
                    rangeDate: {
                        from: momentTz().subtract(1, 'days').toDate(),
                        to: momentTz().add(1, 'days').toDate(),
                    },
                    search: 'tasker',
                    status: 'PASSED',
                },
                skip: 0,
                limit: 10,
                sort: { createdAt: -1 } as PipelineStage.Sort['$sort'],
            };

            // Requirement: result only get the submissions of TEST course
            const MOCK_RESULT_TASKER_TRAINING_SUBMISSIONS = [
                TEST_TRANING_COURSE_SUBMISSION,
            ];

            const trainingSubmissionsFromQuery =
                await getTrainingHistoryAllTasker(mockParams);

            expect(trainingSubmissionsFromQuery).toHaveLength(
                MOCK_RESULT_TASKER_TRAINING_SUBMISSIONS.length,
            );
            expect(MOCK_RESULT_TASKER_TRAINING_SUBMISSIONS[0]._id).toEqual(
                TEST_TRANING_COURSE_SUBMISSION._id,
            );
        });
    });

    describe('getTotalTrainingHistoryAllTasker', () => {
        it('should return total training history of all taskers', async () => {
            const mockParams = {
                isoCode,
                filter: {
                    rangeDate: {
                        from: momentTz().subtract(1, 'days').toDate(),
                        to: momentTz().add(1, 'days').toDate(),
                    },
                    search: 'tasker',
                    status: 'PASSED',
                },
            };
            const MOCK_TOTAL_TASKER_TRAINING_SUBMISSIONS = 1;

            const totalTestTrainingSubmission =
                await getTotalTrainingHistoryAllTasker(mockParams);

            expect(totalTestTrainingSubmission).toEqual(
                MOCK_TOTAL_TASKER_TRAINING_SUBMISSIONS,
            );
        });
    });
});
