import { TYPE_LADING, TYPE_PAYMENT } from 'btaskee-constants';
import { momentTz } from 'btaskee-utils';
import type { PipelineStage } from 'mongoose';
import { getModels } from 'schemas';
import {
    confirmPaymentToolKitTransactionByTypeLading,
    getDetailPaymentToolKitTransaction,
    getPaymentToolKitTransaction,
    getTotalPaymentToolKitTransaction,
} from '~/services/payment-and-delivery.server';

describe('Payment and delivery', () => {
    const isoCode: IsoCode = 'VN';
    const currentDate = momentTz().toDate();
    const mockTaskerId_1 = 'taskerId_1';
    const mockTaskerId_2 = 'taskerId_2';
    const mockPaymentToolKitTransactionId_1 = 'paymentToolKitTransactionId_1';
    const mockTaskerToolkitLandingDetailsId_1 =
        'taskerToolkitLandingDetailsId_1';
    const mockServiceChannelId_1 = 'serviceChannelId_1';
    const mockPaymentToolKitTransaction = [
        {
            _id: mockPaymentToolKitTransactionId_1,
            taskerId: mockTaskerId_1,
            type: TYPE_PAYMENT.BNPL,
            amount: 20000,
            payment: {
                status: 'PAID',
            },
            listTool: [
                {
                    _id: 'wRXu9Z78CXFdwe8ka',
                    image: 'https://btaskee-stag.s3-ap-southeast-1.amazonaws.com/tasker/list-of-tools-require/icon/veQa7mQnDeTNdoifn',
                    price: 40000.0,
                    text: {
                        vi: 'Balo bTaskee',
                        en: 'bTaskee Baggage',
                        ko: 'bTaskee Baggage',
                        th: 'bTaskee Baggage',
                        id: 'bTaskee Baggage',
                    },
                    quantity: 3.0,
                    createdAt: currentDate,
                },
            ],
            createdAt: currentDate,
        },
    ];

    const mockTaskerToolkitLandingDetails = [
        {
            _id: mockTaskerToolkitLandingDetailsId_1,
            taskerId: mockTaskerId_1,
            type: TYPE_PAYMENT.BNPL,
            amount: 20000,
            payment: {
                status: 'PAID',
            },
            typeOfLading: 'OFFICE',
            placeOfReceipt: 'Somewhere i dont know',
            createdAt: currentDate,
            createdBy: 'user123',
        },
    ];

    const mockServiceChannel = [
        {
            _id: mockServiceChannelId_1,
            serviceId: 'serviceId_1',
            taskerList: [mockTaskerId_1, mockTaskerId_2],
        },
    ];

    const mockTasker = [
        {
            _id: mockTaskerId_1,
            name: 'Tasker 1',
            phone: '090xxxx1',
            workingPlaces: [
                {
                    country: 'VN',
                    city: 'Hồ Chí Minh',
                },
            ],
            fAccountId: 'fAccountId_1',
            language: 'en',
            status: 'ACTIVE',
            type: 'TASKER',
            createdAt: currentDate,
        },
        {
            _id: mockTaskerId_2,
            name: 'Tasker 2',
            phone: '090xxxx2',
            workingPlaces: [
                {
                    country: 'VN',
                    city: 'Hồ Chí Minh',
                },
            ],
            fAccountId: 'fAccountId_2',
            language: 'en',
            status: 'ACTIVE',
            type: 'TASKER',
            createdAt: currentDate,
        },
    ];

    beforeEach(async () => {
        await getModels(isoCode).paymentToolKitTransaction.insertMany(
            mockPaymentToolKitTransaction,
        );
        await getModels(isoCode).serviceChannel.insertMany(mockServiceChannel);
        await getModels(isoCode).users.insertMany(mockTasker);
        await getModels(isoCode).taskerToolkitLadingDetails.insertMany(
            mockTaskerToolkitLandingDetails,
        );
    });

    afterEach(async () => {
        await getModels(isoCode).paymentToolKitTransaction.deleteMany({
            _id: { $in: mockPaymentToolKitTransaction.map(item => item._id) },
        });
        await getModels(isoCode).serviceChannel.deleteMany({
            _id: { $in: mockServiceChannel.map(item => item._id) },
        });
        await getModels(isoCode).users.deleteMany({
            _id: { $in: mockTasker.map(item => item._id) },
        });
        await getModels(isoCode).taskerToolkitLadingDetails.deleteMany({
            _id: { $in: mockTaskerToolkitLandingDetails.map(item => item._id) },
        });
    });

    describe('getPaymentToolKitTransaction', () => {
        it('Should return list payment toolkit transaction correctly', async () => {
            const mockParams = {
                skip: 0,
                limit: 10,
                sort: { createdAt: -1 } as PipelineStage.Sort['$sort'],
                isoCode,
                filter: {
                    search: '1',
                    service: 'serviceId_1',
                    typeOfPayment: TYPE_PAYMENT.BNPL,
                    status: 'PAID,RECEIVED,DELIVERED',
                    rangeDate: {
                        from: momentTz(currentDate).subtract(1, 'day').toDate(),
                        to: momentTz(currentDate).add(1, 'day').toDate(),
                    },
                },
            };

            const result = await getPaymentToolKitTransaction(mockParams);

            expect(result).toBeDefined();
            expect(result.data).toEqual(
                expect.arrayContaining([
                    expect.objectContaining({
                        _id: mockPaymentToolKitTransactionId_1,
                        tasker: expect.objectContaining({
                            _id: mockTaskerId_1,
                            name: 'Tasker 1',
                            phone: '090xxxx1',
                        }),
                        type: TYPE_PAYMENT.BNPL,
                        status: expect.stringMatching(
                            /PAID|RECEIVED|DELIVERED/,
                        ),
                        amount: 20000,
                        createdAt: expect.any(Date),
                        registeredServices: expect.arrayContaining([
                            'serviceId_1',
                        ]),
                    }),
                ]),
            );
        });
    });

    describe('getTotalPaymentToolKitTransaction', () => {
        it('should return the correct total count of payment toolkit transactions', async () => {
            const mockParams = {
                isoCode,
                filter: {
                    search: 'Tasker 1',
                    service: 'serviceId_1',
                    typeOfPayment: TYPE_PAYMENT.BNPL,
                    status: 'PAID,RECEIVED,DELIVERED',
                    rangeDate: {
                        from: momentTz(currentDate).subtract(1, 'day').toDate(),
                        to: momentTz(currentDate).add(1, 'day').toDate(),
                    },
                },
            };

            const result = await getTotalPaymentToolKitTransaction(mockParams);
            expect(result).toBe(1); // We expect 1 transaction to match our criteria
        });

        it('should return 0 if no transactions match the criteria', async () => {
            const mockParams = {
                isoCode,
                filter: {
                    search: 'Nonexistent Tasker',
                    service: 'nonexistentServiceId',
                    typeOfPayment: TYPE_PAYMENT.BNPL,
                    status: 'PAID,RECEIVED,DELIVERED',
                    rangeDate: {
                        from: momentTz(currentDate).subtract(1, 'day').toDate(),
                        to: momentTz(currentDate).add(1, 'day').toDate(),
                    },
                },
            };

            const result = await getTotalPaymentToolKitTransaction(mockParams);
            expect(result).toBe(0); // We expect 0 transactions to match this incorrect criteria
        });
    });

    describe('confirmPaymentToolKitTransactionByTypeLading', () => {
        it('should create a record in taskerToolkitLadingDetails with type OFFICE', async () => {
            const params = {
                placeOfReceipt: 'Office Location A',
                amount: 5000,
                billOfLading: '', // Not needed for type OFFICE
                domesticRouting: '', // Not needed for type OFFICE
                createdBy: 'user123',
            };

            await confirmPaymentToolKitTransactionByTypeLading({
                isoCode,
                type: TYPE_LADING.OFFICE,
                taskerId: mockTaskerId_2,
                params,
                toolKitTransactionId: 'toolKitTransaction1',
            });

            const createdRecords = await getModels(
                isoCode,
            ).taskerToolkitLadingDetails.find({ taskerId: mockTaskerId_2 });

            expect(createdRecords.length).toBe(1);
            expect(createdRecords[0].typeOfLading).toBe(TYPE_LADING.OFFICE);
            expect(createdRecords[0].placeOfReceipt).toBe(
                params.placeOfReceipt,
            );
            expect(createdRecords[0].amount).toBe(params.amount);
            expect(createdRecords[0].createdBy).toBe(params.createdBy);

            // clear data inserted
            await getModels(isoCode).taskerToolkitLadingDetails.deleteOne({
                taskerId: mockTaskerId_2,
            });
        });

        it('should create a record in taskerToolkitLadingDetails with type LADING', async () => {
            const params = {
                amount: 8000,
                billOfLading: 'BL123456',
                domesticRouting: 'Route 1',
                createdBy: 'user123',
            };

            await confirmPaymentToolKitTransactionByTypeLading({
                isoCode,
                type: TYPE_LADING.LADING,
                taskerId: mockTaskerId_2,
                params,
                toolKitTransactionId: 'toolKitTransaction1',
            });

            const createdRecords = await getModels(
                isoCode,
            ).taskerToolkitLadingDetails.find({ taskerId: mockTaskerId_2 });

            expect(createdRecords.length).toBe(1);
            expect(createdRecords[0].typeOfLading).toBe(TYPE_LADING.LADING);
            expect(createdRecords[0].billOfLading).toBe(params.billOfLading);
            expect(createdRecords[0].domesticRouting).toBe(
                params.domesticRouting,
            );
            expect(createdRecords[0].amount).toBe(params.amount);
            expect(createdRecords[0].createdBy).toBe(params.createdBy);

            // clear data inserted
            await getModels(isoCode).taskerToolkitLadingDetails.deleteOne({
                taskerId: mockTaskerId_2,
            });
        });
    });

    describe('getDetailPaymentToolKitTransaction', () => {
        it('should return the correct detail for a payment toolkit transaction', async () => {
            const result = await getDetailPaymentToolKitTransaction({
                isoCode,
                paymentToolKitTransactionId: mockPaymentToolKitTransactionId_1,
            });

            expect(result.tasker._id).toBe(mockTaskerId_1);
            expect(result.tasker.name).toBe('Tasker 1');
            expect(result.tasker.phone).toBe('090xxxx1');
            expect(result.amount).toBe(20000);
            expect(result.type).toBe(TYPE_PAYMENT.BNPL);
            expect(result.listTool).toEqual([
                {
                    text: 'bTaskee Baggage',
                    quantity: 3.0,
                    price: 120000,
                },
            ]);
        });

        it('should return an empty object if no matching payment toolkit transaction is found', async () => {
            const result = await getDetailPaymentToolKitTransaction({
                isoCode,
                paymentToolKitTransactionId: 'nonExistentId',
            });

            expect(result).toStrictEqual({});
        });
    });
});
