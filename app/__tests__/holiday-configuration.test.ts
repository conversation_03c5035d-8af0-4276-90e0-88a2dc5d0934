import { IsoCode } from 'btaskee-constants';
import { momentTz } from 'btaskee-utils';
import { getModels } from 'schemas';
import {
    createHoliday,
    deleteHoliday,
    getHolidaysFromSettingSystem,
    updateHoliday,
} from '~/services/holiday-configuration.server';

describe('Holiday Configuration', () => {
    const isoCode = IsoCode.VN;
    const holidayName1 = 'Tết Nguyên Đán';
    const holidayName2 = 'Ngày Giỗ Tổ Hùng Vương';
    const holidayName3 = 'Ngày Quốc Khánh';

    const holidays = [
        {
            reason: holidayName1,
            from: momentTz('2024-01-01').toDate(),
            to: momentTz('2024-01-01').toDate(),
            isActive: true,
            isHoliday: true,
        },
        {
            reason: holidayName2,
            from: momentTz('2024-04-18').toDate(),
            to: momentTz('2024-04-18').toDate(),
            isActive: true,
            isHoliday: true,
        },
        {
            reason: holidayName3,
            from: momentTz('2024-09-02').toDate(),
            to: momentTz('2024-09-02').toDate(),
            isActive: true,
            isHoliday: true,
        },
    ];

    const settingSystem = {
        _id: 'settingSystemId',
        submitionAddressForTasker: [
            {
                offDays: holidays,
            },
        ],
    };

    beforeAll(async () => {
        await getModels(isoCode).settingSystem.create(settingSystem);
    });

    afterAll(async () => {
        await getModels(isoCode).settingSystem.deleteOne({
            _id: settingSystem._id,
        });
    });

    describe('getHolidaysFromSettingSystem', () => {
        it('Should get list holidays from settingSystem successfully', async () => {
            const search = '';

            const fetchedHolidays = await getHolidaysFromSettingSystem({
                isoCode,
                search,
            });

            expect(fetchedHolidays).toHaveLength(holidays.length);
            fetchedHolidays.forEach(holiday => {
                const matchingHoliday = holidays.find(
                    h => h.reason === holiday.reason,
                );
                expect(matchingHoliday).toBeDefined();
                expect(holiday.from).toEqual(matchingHoliday?.from);
                expect(holiday.to).toEqual(matchingHoliday?.to);
                expect(holiday.isActive).toEqual(matchingHoliday?.isActive);
                expect(holiday.isHoliday).toEqual(matchingHoliday?.isHoliday);
            });
        });
    });

    describe('createHoliday', () => {
        it('Should create a new holiday successfully', async () => {
            const newHolidayName = 'New Year';
            const username = 'testUser';
            const holidayDateRange = {
                from: new Date('2025-01-01'),
                to: new Date('2025-01-01'),
            };

            const result = await createHoliday({
                isoCode,
                username,
                holidayName: newHolidayName,
                holidayDateRange,
            });

            expect(result).toEqual({
                success: true,
                message: 'HOLIDAY_CREATED_SUCCESSFULLY',
            });

            const createdHoliday = await getModels(
                isoCode,
            ).settingSystem.findOne({
                'submitionAddressForTasker.offDays': {
                    $elemMatch: {
                        reason: newHolidayName,
                        isHoliday: true,
                    },
                },
            });

            expect(createdHoliday).toBeDefined();
        });

        it('Should throw an error when creating a holiday with an existing name', async () => {
            const existingHolidayName = holidayName1;
            const username = 'testUser';
            const holidayDateRange = {
                from: new Date('2025-01-01'),
                to: new Date('2025-01-01'),
            };

            await expect(
                createHoliday({
                    isoCode,
                    username,
                    holidayName: existingHolidayName,
                    holidayDateRange,
                }),
            ).rejects.toThrow('HOLIDAY_NAME_ALREADY_EXISTS');
        });
    });

    describe('updateHoliday', () => {
        it('Should update an existing holiday successfully', async () => {
            const previousHolidayName = holidayName1;
            const updatedHolidayName = 'Updated Tết Nguyên Đán';
            const holidayDateRange = {
                from: new Date('2025-01-01'),
                to: new Date('2025-01-03'),
            };

            const result = await updateHoliday({
                isoCode,
                previousHolidayName,
                holidayName: updatedHolidayName,
                holidayDateRange,
            });

            expect(result).toEqual({
                success: true,
                message: 'HOLIDAY_UPDATED_SUCCESSFULLY',
            });

            const updatedHoliday = await getModels(
                isoCode,
            ).settingSystem.findOne({
                'submitionAddressForTasker.offDays': {
                    $elemMatch: {
                        reason: updatedHolidayName,
                        isHoliday: true,
                    },
                },
            });

            expect(updatedHoliday).toBeDefined();
        });

        it('Should throw an error when updating a holiday with an existing name', async () => {
            const previousHolidayName = holidayName2;
            const existingHolidayName = holidayName3;
            const holidayDateRange = {
                from: new Date('2025-04-18'),
                to: new Date('2025-04-18'),
            };

            await expect(
                updateHoliday({
                    isoCode,
                    previousHolidayName,
                    holidayName: existingHolidayName,
                    holidayDateRange,
                }),
            ).rejects.toThrow('HOLIDAY_NAME_ALREADY_EXISTS');
        });
    });

    describe('deleteHoliday', () => {
        it('Should delete an existing holiday successfully', async () => {
            const holidayToDelete = holidayName2;

            const result = await deleteHoliday({
                isoCode,
                holidayName: holidayToDelete,
            });

            expect(result).toEqual({
                success: true,
                message: 'HOLIDAY_DELETED_SUCCESSFULLY',
            });

            const deletedHoliday = await getModels(
                isoCode,
            ).settingSystem.findOne({
                'submitionAddressForTasker.offDays': {
                    $elemMatch: {
                        reason: holidayToDelete,
                        isHoliday: true,
                    },
                },
            });

            expect(deletedHoliday).toBeNull();
        });

        it('Should not throw an error when deleting a non-existent holiday', async () => {
            const nonExistentHoliday = 'Non-existent Holiday';

            const result = await deleteHoliday({
                isoCode,
                holidayName: nonExistentHoliday,
            });

            expect(result).toEqual({
                success: true,
                message: 'HOLIDAY_DELETED_SUCCESSFULLY',
            });
        });
    });
});
