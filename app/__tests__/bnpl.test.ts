import { TASKER_BNPL_PROCESS_STATUS } from 'btaskee-constants';
import { momentTz } from 'btaskee-utils';
import type { PipelineStage } from 'mongoose';
import { EnumIsoCode, getModels } from 'schemas';
import {
    deductInstallmentPaymentInTaskerBNPL,
    getBNPLTransactionByTaskerId,
    getFullPayment,
    getInstallmentPayment,
    getInstallmentPaymentDetail,
    getTotalFullPayment,
    getTotalInstallmentPayment,
} from '~/services/bnpl.server';

jest.mock('~/services/utils.server', () => ({
    __esModule: true,
    statusOriginal: {
        ACTIVE: 'ACTIVE',
        REMOVED: 'REMOVED',
    },
    sendNotification: () => new Promise(resolve => resolve(true)),
}));

describe('bnpl', () => {
    const isoCode = EnumIsoCode.VN;
    const processId = [
        'process_1',
        'process_2',
        'process_3',
        'process_4',
        'process_5',
        'process_6',
        'process_7',
        'process_8',
        'process_9',
    ];

    const taskerId = [
        'tasker_1',
        'tasker_2',
        'tasker_3',
        'tasker_4',
        'tasker_5',
        'tasker_6',
        'tasker_7',
        'tasker_8',
        'tasker_9',
    ];

    const transactionId = [
        'transaction_1',
        'transaction_2',
        'transaction_3',
        'transaction_4',
        'transaction_5',
        'transaction_6',
        'transaction_7',
        'transaction_8',
        'transaction_9',
    ];

    const serviceChannel = [
        {
            _id: 'service_channel_1',
            serviceId: 'service_1',
            taskerList: taskerId,
        },
    ];

    const paymentToolKitTransactionId = [
        'payment_tool_kit_transaction_1',
        'payment_tool_kit_transaction_2',
        'payment_tool_kit_transaction_3',
        'payment_tool_kit_transaction_4',
        'payment_tool_kit_transaction_5',
        'payment_tool_kit_transaction_6',
        'payment_tool_kit_transaction_7',
        'payment_tool_kit_transaction_8',
        'payment_tool_kit_transaction_9',
    ];

    const paymentToolKitTransaction = paymentToolKitTransactionId.map(
        (id, index) => ({
            _id: id,
            type: 'ONCE_PAY',
            taskerId: taskerId[index],
            amount: 35000,
            createdAt: momentTz().add(index, 'hours').toDate(),
            payment: {
                status: 'PAID',
            },
            listTool: [
                {
                    _id: `tool_${index}`,
                    text: {
                        en: `tool_${index}`,
                    },
                    price: 35000,
                    quantity: 1,
                    createdAt: momentTz().toDate(),
                },
            ],
        }),
    );

    const process = processId.map((id, index) => ({
        _id: id,
        taskerId: taskerId[index],
        status: 'PAYING',
        amount: 35000,
        remainingAmount: 35000.0,
        moneyBNPLOnTask: 3000.0,
        expiredAt: momentTz().toDate(),
        createdAt: momentTz().add(index, 'hours').toDate(),
        firstPayMoney: 36000.0,
    }));

    const tasker = taskerId.map((id, index) => ({
        _id: id,
        name: `tasker ${index + 1}`,
        phone: `**********${index}`,
        workingPlaces: [
            {
                country: 'VN',
                city: 'Hồ Chí Minh',
            },
        ],
        language: 'vi',
        fAccountId: `fAccountId${index}`,
        status: 'ACTIVE',
        createdAt: momentTz('2023-10-01').toDate(),
    }));

    const transaction = transactionId.map((id, index) => ({
        _id: id,
        taskerId: taskerId[index],
        amount: 35000,
        createdAt: momentTz().toDate(),
    }));

    beforeEach(async () => {
        await getModels(isoCode).taskerBNPLProcess.insertMany(process);
        await getModels(isoCode).taskerBNPLTransaction.insertMany(transaction);
        await getModels(isoCode).serviceChannel.insertMany(serviceChannel);
        await getModels(isoCode).users.insertMany(tasker);
        await getModels(isoCode).paymentToolKitTransaction.insertMany(
            paymentToolKitTransaction,
        );
    });

    afterEach(async () => {
        await getModels(isoCode).taskerBNPLProcess.deleteMany();
        await getModels(isoCode).serviceChannel.deleteMany();
        await getModels(isoCode).users.deleteMany();
        await getModels(isoCode).taskerBNPLTransaction.deleteMany();
        await getModels(isoCode).paymentToolKitTransaction.deleteMany();
    });

    describe('getInstallmentPayment', () => {
        it('should return installment payment', async () => {
            const mockParams = {
                skip: 0,
                limit: 10,
                sort: { createdAt: -1 } as PipelineStage.Sort['$sort'],
                filter: {
                    search: 'tasker',
                    service: 'service_1',
                    status: 'PAYING',
                    rangeDate: {
                        from: momentTz().subtract(1, 'days').toDate(),
                        to: momentTz().add(1, 'days').toDate(),
                    },
                },
                isoCode,
            };

            const result = await getInstallmentPayment(mockParams);

            const expectationData = process
                .filter(item => item.status === 'PAYING')
                .sort((a, b) => b.createdAt.getTime() - a.createdAt.getTime());

            expect(result.data.length).toBe(expectationData.length);
            expect(result.data[0].tasker._id).toBe(expectationData[0].taskerId);
            expect(result.data[0].status).toBe(expectationData[0].status);
            expect(result.data[0].amount).toBe(expectationData[0].amount);
            expect(result.data[0].remainingAmount).toBe(
                expectationData[0].remainingAmount,
            );
            expect(result.data[0].createdAt).toEqual(
                expectationData[0].createdAt,
            );
        });
    });

    describe('getTotalInstallmentPayment', () => {
        it('should return total installment payment', async () => {
            const mockParams = {
                filter: {
                    search: 'tasker',
                    service: 'service_1',
                    status: 'PAYING',
                    rangeDate: {
                        from: momentTz().subtract(1, 'days').toDate(),
                        to: momentTz().add(1, 'days').toDate(),
                    },
                },
                isoCode,
            };

            const result = await getTotalInstallmentPayment(mockParams);

            expect(result).toBe(process.length);
        });
    });

    describe('getInstallmentPaymentDetail', () => {
        it('should return installment payment detail', async () => {
            const mockParams = {
                isoCode,
                id: process[0]._id,
            };

            const result = await getInstallmentPaymentDetail(mockParams);

            expect(result._id).toBe(process[0]._id);
            expect(result.tasker._id).toBe(process[0].taskerId);
            expect(result.createdAt).toEqual(process[0].createdAt);
        });
    });

    describe('getBNPLTransactionByTaskerId', () => {
        it('should return BNPL transaction by tasker id', async () => {
            const mockParams = {
                isoCode,
                taskerId: process[0].taskerId,
            };

            const result = await getBNPLTransactionByTaskerId(mockParams);

            expect(result[0]._id).toBe(transaction[0]._id);
            expect(result[0].amount).toBe(transaction[0].amount);
            expect(result[0].createdAt).toEqual(transaction[0].createdAt);
        });
    });

    describe('getFullPayment', () => {
        it('should return full payment', async () => {
            const mockParams = {
                isoCode,
                skip: 0,
                limit: 10,
                sort: { createdAt: -1 } as PipelineStage.Sort['$sort'],
                filter: {
                    search: 'tasker',
                    service: 'service_1',
                    city: 'Hồ Chí Minh',
                    rangeDate: {
                        from: momentTz().subtract(1, 'days').toDate(),
                        to: momentTz().add(1, 'days').toDate(),
                    },
                },
            };

            const result = await getFullPayment(mockParams);

            const expectationData = paymentToolKitTransaction
                .filter(
                    item =>
                        item.payment.status === 'PAID' &&
                        item.type === 'ONCE_PAY',
                )
                .sort((a, b) => b.createdAt.getTime() - a.createdAt.getTime());

            expect(result.data[0]._id).toBe(expectationData[0]._id);
            expect(result.data[0].amount).toBe(expectationData[0].amount);
            expect(result.data[0].createdAt).toEqual(
                expectationData[0].createdAt,
            );
        });
    });

    describe('getTotalFullPayment', () => {
        it('should return total full payment', async () => {
            const mockParams = {
                isoCode,
                filter: {
                    search: 'tasker',
                    service: 'service_1',
                    city: 'Hồ Chí Minh',
                    rangeDate: {
                        from: momentTz().subtract(1, 'days').toDate(),
                        to: momentTz().add(1, 'days').toDate(),
                    },
                },
            };

            const result = await getTotalFullPayment(mockParams);

            expect(result).toBe(paymentToolKitTransaction.length);
        });
    });
    describe('deductInstallmentPaymentInTaskerBNPL', () => {
        const MOCK_TOTAL_MAIN_ACCOUNT = 30000;
        const MOCK_REMAINING_AMOUNT_IN_TASKER_BNPL = 35000;
        const FINAL_MAIN_ACCOUNT_AFTER_DEDUCT_BNPL = -5000;

        const MOCK_FINANCIAL_ACCOUNT = {
            _id: 'mock-financial-account-id',
            FMainAccount: MOCK_TOTAL_MAIN_ACCOUNT,
            createdAt: momentTz().toDate(),
        };
        const MOCK_TASKER_HAVE_BNPL_PROCESS = {
            _id: 'mock-tasker-id',
            language: 'vi',
            status: 'ACTIVE',
            fAccountId: MOCK_FINANCIAL_ACCOUNT._id,
            createdAt: momentTz().toDate(),
        };
        const MOCK_BNPL_PROCESS = {
            _id: 'mock-tasker-bnpl-process-id',
            taskerId: MOCK_TASKER_HAVE_BNPL_PROCESS._id,
            status: 'PAYING',
            amount: 30000,
            remainingAmount: MOCK_REMAINING_AMOUNT_IN_TASKER_BNPL,
            moneyBNPLOnTask: 3000,
            expiredAt: momentTz().toDate(),
            createdAt: momentTz().toDate(),
            firstPayMoney: 36000,
        };

        const MOCK_BNPL_PROCESS_BUT_TASKER_NOT_FOUND = {
            _id: 'mock-tasker-bnpl-process-id-2',
            taskerId: 'tasker-id-not-found',
            status: 'PAYING',
            amount: 30000,
            remainingAmount: 35000,
            moneyBNPLOnTask: 3000,
            expiredAt: momentTz().toDate(),
            createdAt: momentTz().toDate(),
            firstPayMoney: 36000,
        };

        beforeEach(async () => {
            await Promise.all([
                getModels(isoCode).users.create(MOCK_TASKER_HAVE_BNPL_PROCESS),
                getModels(isoCode).financialAccount.create(
                    MOCK_FINANCIAL_ACCOUNT,
                ),
                getModels(isoCode).taskerBNPLProcess.insertMany([
                    MOCK_BNPL_PROCESS,
                    MOCK_BNPL_PROCESS_BUT_TASKER_NOT_FOUND,
                ]),
            ]);
        });
        afterEach(async () => {
            await Promise.all([
                getModels(isoCode).users.deleteOne({
                    _id: MOCK_TASKER_HAVE_BNPL_PROCESS._id,
                }),
                getModels(isoCode).financialAccount.deleteOne({
                    _id: MOCK_FINANCIAL_ACCOUNT._id,
                }),
                getModels(isoCode).taskerBNPLProcess.deleteMany({
                    _id: {
                        $in: [
                            MOCK_BNPL_PROCESS._id,
                            MOCK_BNPL_PROCESS_BUT_TASKER_NOT_FOUND._id,
                        ],
                    },
                }),
            ]);
        });

        it('Should throw error message when BNPL process not found', async () => {
            await expect(
                deductInstallmentPaymentInTaskerBNPL({
                    isoCode,
                    reason: 'new reason',
                    bnplId: 'bnplId-not-found',
                }),
            ).rejects.toThrow();
        });
        it('Should throw error message when Tasker not found in BNPL process', async () => {
            await expect(
                deductInstallmentPaymentInTaskerBNPL({
                    isoCode,
                    reason: 'new reason',
                    bnplId: MOCK_BNPL_PROCESS_BUT_TASKER_NOT_FOUND._id,
                }),
            ).rejects.toThrow();
        });
        it('Should deduct the installment payment in Tasker BNPL successfully', async () => {
            const params = {
                isoCode,
                reason: 'new reason',
                bnplId: MOCK_BNPL_PROCESS._id,
            };

            await deductInstallmentPaymentInTaskerBNPL(params);

            const [
                BNPLTransactionFound,
                BNPLProcessFound,
                FATransactionFound,
                financeAccountFound,
            ] = await Promise.all([
                getModels(isoCode).taskerBNPLTransaction.findOne({
                    taskerId: MOCK_BNPL_PROCESS.taskerId,
                    BNPLId: MOCK_BNPL_PROCESS._id,
                }),
                getModels(isoCode).taskerBNPLProcess.findById(
                    MOCK_BNPL_PROCESS._id,
                ),
                getModels(isoCode).FATransaction.findOne({
                    userId: MOCK_BNPL_PROCESS.taskerId,
                }),
                getModels(isoCode).financialAccount.findById(
                    MOCK_FINANCIAL_ACCOUNT._id,
                ),
            ]);

            expect(BNPLTransactionFound?._id).toBeDefined();
            expect(BNPLProcessFound?.remainingAmount).toEqual(0);
            expect(BNPLProcessFound?.status).toEqual(
                TASKER_BNPL_PROCESS_STATUS.DONE,
            );
            expect(FATransactionFound?._id).toBeDefined();
            expect(financeAccountFound?.FMainAccount).toEqual(
                FINAL_MAIN_ACCOUNT_AFTER_DEDUCT_BNPL,
            );

            await Promise.all([
                getModels(isoCode).taskerBNPLTransaction.deleteOne({
                    _id: BNPLTransactionFound?._id,
                }),
                getModels(isoCode).FATransaction.deleteOne({
                    _id: FATransactionFound?._id,
                }),
            ]);
        });
    });
});
