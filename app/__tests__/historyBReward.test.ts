import { CODE_TYPE } from 'btaskee-constants';
import { type PipelineStage } from 'mongo-connection';
import { getModels } from 'schemas';
import {
    getHistoryBReward,
    getTotalHistoryBReward,
} from '~/services/history-breward.server';

describe('History bReward', () => {
    const mockTaskerGiftId_1 = 'taskerGiftId_1';
    const mockTaskerGiftId_2 = 'taskerGiftId_2';
    const mockTaskerId_1 = 'taskerId_1';
    const mockTaskerId_2 = 'taskerId_2';
    const mockPointTransactionId_1 = 'pointTransactionId_1';
    const mockPointTransactionId_2 = 'pointTransactionId_2';

    const currentDate = new Date();

    const mockTaskerGifts = [
        {
            _id: mockTaskerGiftId_1,
            pointTransactionId: mockPointTransactionId_1,
            userId: mockTaskerId_1,
            title: {
                en: 'Gift 1',
                vi: 'Quà 1',
            },
            promotionCode: 'Code_1',
            used: true,
            expiredDate: new Date(
                currentDate.getTime() + 1000 * 60 * 60 * 24 * 2,
            ), // 2 days after current date
            createdAt: currentDate,
            usedAt: new Date(currentDate.getTime() + 1000 * 60 * 60 * 24 * 1), // 1 day after current date
            responsiblePerson: 'thienduy.cao',
            source: CODE_TYPE.SYSTEM,
        },
        {
            _id: mockTaskerGiftId_2,
            pointTransactionId: mockPointTransactionId_2,
            userId: mockTaskerId_2,
            title: {
                en: 'Gift 2',
                vi: 'Quà 2',
            },
            promotionCode: 'Code_2',
            used: true,
            expiredDate: new Date(
                currentDate.getTime() + 1000 * 60 * 60 * 24 * 2,
            ), // 2 days after current date
            createdAt: currentDate,
            usedAt: new Date(currentDate.getTime() + 1000 * 60 * 60 * 24 * 1), // 1 day after current date
            responsiblePerson: 'vanhoa.bui',
            reference: {
                journeySettingId: `journeySettingId_1`,
            },
        },
    ];

    const mockTaskers = [
        {
            _id: mockTaskerId_1,
            name: 'Tasker 1',
            phone: '090xxxxxx1',
            workingPlaces: [
                {
                    country: 'VN',
                    city: 'Hồ Chí Minh',
                },
            ],
            fAccountId: 'fAccountId_1',
            language: 'en',
            status: 'ACTIVE',
            type: 'TASKER',
            createdAt: currentDate,
        },
        {
            _id: mockTaskerId_2,
            name: 'Tasker 2',
            phone: '090xxxxxx2',
            workingPlaces: [
                {
                    country: 'VN',
                    city: 'Hà Nội',
                },
            ],
            fAccountId: 'fAccountId_2',
            language: 'en',
            status: 'ACTIVE',
            type: 'TASKER',
            createdAt: currentDate,
        },
    ];

    const mockTaskerPointTransactions = [
        {
            _id: mockPointTransactionId_1,
            point: 100,
        },
        {
            _id: mockPointTransactionId_2,
            point: 200,
        },
    ];

    beforeEach(async () => {
        await getModels('VN').taskerGift.insertMany(mockTaskerGifts);
        await getModels('VN').users.insertMany(mockTaskers);
        await getModels('VN').taskerPointTransaction.insertMany(
            mockTaskerPointTransactions,
        );
    });

    afterEach(async () => {
        await getModels('VN').taskerGift.deleteMany({
            _id: { $in: mockTaskerGifts.map(gift => gift._id) },
        });
        await getModels('VN').users.deleteMany({
            _id: { $in: mockTaskers.map(tasker => tasker._id) },
        });
        await getModels('VN').taskerPointTransaction.deleteMany({
            _id: {
                $in: mockTaskerPointTransactions.map(
                    taskerPointTransaction => taskerPointTransaction._id,
                ),
            },
        });
    });

    describe('getHistoryBReward', () => {
        it('should return list history bReward with filter code from SYSTEM/SYSTEM_WITH_PARTNER', async () => {
            const mockParams = {
                skip: 0,
                limit: 10,
                sort: { usedAt: -1 } as PipelineStage.Sort['$sort'],
                filter: {
                    search: 'gift',
                    rangeDate: {
                        from: new Date(
                            currentDate.getTime() - 1000 * 60 * 60 * 24 * 1,
                        ),
                        to: new Date(
                            currentDate.getTime() + 1000 * 60 * 60 * 24 * 1,
                        ),
                    },
                    city: 'Hồ Chí Minh',
                    codeType: CODE_TYPE.SYSTEM,
                },
                isoCode: 'VN',
            };

            const result = await getHistoryBReward(mockParams);

            expect(result[0]).toEqual(
                expect.objectContaining({
                    _id: mockTaskerGifts[0]._id,
                }),
            );

            expect(result[0].tasker).toEqual(
                expect.objectContaining({
                    _id: mockTaskers[0]._id,
                    name: mockTaskers[0].name,
                    phone: mockTaskers[0].phone,
                    city: mockTaskers[0].workingPlaces[0].city,
                }),
            );
        });

        it('should return list history bReward with filter code from JOURNEY', async () => {
            const mockParams = {
                skip: 0,
                limit: 10,
                sort: { usedAt: -1 } as PipelineStage.Sort['$sort'],
                filter: {
                    search: 'gift',
                    rangeDate: {
                        from: new Date(
                            currentDate.getTime() - 1000 * 60 * 60 * 24 * 1,
                        ),
                        to: new Date(
                            currentDate.getTime() + 1000 * 60 * 60 * 24 * 1,
                        ),
                    },
                    city: 'Hà Nội',
                    codeType: CODE_TYPE.JOURNEY,
                },
                isoCode: 'VN',
            };

            const result = await getHistoryBReward(mockParams);

            expect(result[0]).toEqual(
                expect.objectContaining({
                    _id: mockTaskerGifts[1]._id,
                }),
            );

            expect(result[0].tasker).toEqual(
                expect.objectContaining({
                    _id: mockTaskers[1]._id,
                    name: mockTaskers[1].name,
                    phone: mockTaskers[1].phone,
                    city: mockTaskers[1].workingPlaces[0].city,
                }),
            );
        });
    });

    describe('getTotalHistoryBReward', () => {
        it('should return total history bReward with filter code from SYSTEM/SYSTEM_WITH_PARTNER ', async () => {
            const mockParams = {
                filter: {
                    search: 'gift',
                    rangeDate: {
                        from: new Date(
                            currentDate.getTime() - 1000 * 60 * 60 * 24 * 1,
                        ),
                        to: new Date(
                            currentDate.getTime() + 1000 * 60 * 60 * 24 * 1,
                        ),
                    },
                    city: 'Hồ Chí Minh',
                    codeType: CODE_TYPE.SYSTEM,
                },
                isoCode: 'VN',
            };

            const result = await getTotalHistoryBReward(mockParams);

            expect(result).toEqual(1);
        });

        it('should return total history bReward with filter code from JOURNEY', async () => {
            const mockParams = {
                filter: {
                    search: 'gift',
                    rangeDate: {
                        from: new Date(
                            currentDate.getTime() - 1000 * 60 * 60 * 24 * 1,
                        ),
                        to: new Date(
                            currentDate.getTime() + 1000 * 60 * 60 * 24 * 1,
                        ),
                    },
                    city: 'Hà Nội',
                    codeType: CODE_TYPE.JOURNEY,
                },
                isoCode: 'VN',
            };

            const result = await getTotalHistoryBReward(mockParams);

            expect(result).toEqual(1);
        });

        it('should return total is 0 if cannot find any history bReward', async () => {
            const mockParams = {
                filter: {
                    search: 'gift',
                    rangeDate: {
                        from: new Date(
                            currentDate.getTime() - 1000 * 60 * 60 * 24 * 1,
                        ),
                        to: new Date(
                            currentDate.getTime() + 1000 * 60 * 60 * 24 * 1,
                        ),
                    },
                    city: 'Some city',
                    codeType: CODE_TYPE.JOURNEY,
                },
                isoCode: 'VN',
            };

            const result = await getTotalHistoryBReward(mockParams);

            expect(result).toEqual(0);
        });
    });
});
