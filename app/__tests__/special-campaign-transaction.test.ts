import { STATUS_SPECIAL_CAMPAIGN_TRANSACTION } from 'btaskee-constants';
import { momentTz } from 'btaskee-utils';
import { getModels } from 'schemas';
import {
    getListSpecialCampaignTransaction,
    getTotalSpecialCampaignTransaction,
} from '~/services/special-campaign-transaction.server';

describe('Special campaign transaction', () => {
    const isoCode = 'VN';

    beforeEach(async () => {
        await getModels(isoCode).taskerSpecialCampaignTransaction.insertMany([
            {
                _id: 'special-campaign-transaction-1',
                campaignId: 'special-campaign-1',
                status: STATUS_SPECIAL_CAMPAIGN_TRANSACTION.REWARDED,
                phone: '090xxxx1',
                campaignName: 'special-campaign-1',
                taskerId: 'tasker-1',
                createdAt: momentTz().add(1, 'day').toDate(),
                completedDate: momentTz().add(1, 'day').toDate(),
                rewardedDate: momentTz().add(1, 'day').toDate(),
            },
            {
                _id: 'special-campaign-transaction-2',
                campaignId: 'special-campaign-2',
                status: STATUS_SPECIAL_CAMPAIGN_TRANSACTION.COMPLETED,
                phone: '090xxxx2',
                campaignName: 'special-campaign-2',
                taskerId: 'tasker-2',
                createdAt: momentTz().toDate(),
                completedDate: momentTz().toDate(),
                rewardedDate: momentTz().toDate(),
            },
        ]);
    });

    afterEach(async () => {
        await getModels(isoCode).taskerSpecialCampaignTransaction.deleteMany({
            _id: {
                $in: [
                    'special-campaign-transaction-1',
                    'special-campaign-transaction-2',
                ],
            },
        });
    });

    describe('getListSpecialCampaignTransaction', () => {
        it('should return the list of special campaign transactions successfully', async () => {
            const specialCampaignTransactions =
                await getListSpecialCampaignTransaction({
                    isoCode,
                    skip: 0,
                    limit: 2,
                    sort: { completedDate: -1 },
                    filter: {
                        search: '090xxxx',
                        status: 'REWARDED,COMPLETED',
                        rangeDate: {
                            from: momentTz().subtract(1, 'day').toDate(),
                            to: momentTz().add(1, 'day').toDate(),
                        },
                    },
                });

            expect(specialCampaignTransactions.length).toBe(2);
            expect(specialCampaignTransactions[0].status).toBe(
                STATUS_SPECIAL_CAMPAIGN_TRANSACTION.REWARDED,
            );
            expect(specialCampaignTransactions[1].status).toBe(
                STATUS_SPECIAL_CAMPAIGN_TRANSACTION.COMPLETED,
            );
        });
    });

    describe('getTotalSpecialCampaignTransaction', () => {
        it('should return the total of special campaign transactions successfully', async () => {
            const total = await getTotalSpecialCampaignTransaction({
                isoCode,
                filter: {
                    search: '090xxxx',
                    status: 'REWARDED,COMPLETED',
                    rangeDate: {
                        from: momentTz().subtract(1, 'day').toDate(),
                        to: momentTz().add(1, 'day').toDate(),
                    },
                },
            });

            expect(total).toBe(2);
        });
    });
});
