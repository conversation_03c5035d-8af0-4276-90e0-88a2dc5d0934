import { momentTz } from 'btaskee-utils';
import { getModels } from 'schemas';
import {
    createRejectReasons,
    deleteRejectReason,
    editRejectReason,
    getListRejectReasons,
    getRejectReasonById,
    getTotalRejectReasons,
} from '~/services/reject-reason.server';

describe('Configuration Reason for Tasker Onboarding', () => {
    const isoCode: IsoCode = 'VN';
    const settingId = 'taskerOnboardingSettingId1';
    const reasonIds = [
        'reasonId1',
        'reasonId2',
        'reasonId3',
        'reasonId4',
        'reasonId5',
        'reasonId6',
        'reasonId7',
    ];
    const reasonTypes = [
        'NEEDS_UPDATE',
        'FAIL_UPDATED',
        'ELIMINATED',
        'FAIL_CALLING',
        'FAIL_INTERVIEW',
        'REJECTED',
        'REJECT_DOCUMENTS',
    ];

    const taskerOnboardingSetting = [
        {
            _id: settingId,
            reasons: reasonIds.map((id, index) => {
                return {
                    _id: id,
                    type: reasonTypes[index],
                    name: reasonTypes[index],
                    createdAt: momentTz().toDate(),
                    createdBy: 'super-user',
                };
            }),
        },
    ];

    beforeEach(async () => {
        await getModels(isoCode).taskerOnboardingSetting.insertMany(
            taskerOnboardingSetting,
        );
    });

    afterEach(async () => {
        await getModels(isoCode).taskerOnboardingSetting.deleteMany({});
    });

    describe('getListRejectReasons', () => {
        it('should return list reject reasons for tasker onboarding', async () => {
            const mockParams = {
                isoCode,
                type: reasonTypes[0],
                skip: 0,
                limit: 10,
                search: 'NEEDS_UPDATE',
            };

            const result = await getListRejectReasons(mockParams);

            expect(result).toBeDefined();
        });
    });

    describe('getTotalRejectReasons', () => {
        it('should return total  reject reasons for tasker onboarding', async () => {
            const mockParams = {
                isoCode,
                type: reasonTypes[0],
                search: 'NEEDS_UPDATE',
            };

            const result = await getTotalRejectReasons(mockParams);

            expect(result).toEqual(1);
        });
    });

    describe('createRejectReasons', () => {
        it('should create new reason successfully', async () => {
            const mockParams = {
                isoCode,
                createdBy: 'admin',
                rejectReasonsData: [
                    {
                        type: 'FAIL_CALLING',
                        name: 'new reason',
                    },
                ],
            };

            await createRejectReasons(mockParams);
            const newReason = await getModels(isoCode)
                .taskerOnboardingSetting.findOne(
                    { 'reasons.type': 'FAIL_CALLING' },
                    { 'reasons.$': 1 },
                )
                .lean();

            expect(newReason).toBeDefined();
        });
    });

    describe('editRejectReason', () => {
        it('should edit reason successfully', async () => {
            const mockParams = {
                isoCode,
                id: reasonIds[0],
                updateData: {
                    name: 'new name',
                    type: reasonTypes[0],
                },
                updatedBy: 'admin',
            };

            await editRejectReason(mockParams);

            const updatedReason = await getModels(isoCode)
                .taskerOnboardingSetting.findOne(
                    { 'reasons._id': mockParams.id },
                    { 'reasons.$': 1 },
                )
                .lean();

            expect(updatedReason?.reasons[0].name).toEqual(
                mockParams.updateData.name,
            );
        });
    });

    describe('deleteRejectReason', () => {
        it('should delete reject reason successfully', async () => {
            const mockParams = {
                isoCode,
                id: reasonIds[2],
            };

            await deleteRejectReason(mockParams);

            const deletedReason = await getModels(isoCode)
                .taskerOnboardingSetting.findOne(
                    { 'reasons._id': reasonIds[2] },
                    { 'reasons.$': 1 },
                )
                .lean();

            expect(deletedReason).toEqual(null);
        });
    });

    describe('getRejectReasonById', () => {
        it('should get reject reason detail correctly', async () => {
            const mockParams = {
                isoCode,
                id: reasonIds[0],
            };

            const result = await getRejectReasonById(mockParams);

            expect(result).toEqual(taskerOnboardingSetting[0].reasons[0]);
        });
    });
});
