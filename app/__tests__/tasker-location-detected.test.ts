import { IsoCode } from 'btaskee-constants';
import { momentTz } from 'btaskee-utils';
import type { PipelineStage } from 'mongo-connection';
import { getModels } from 'schemas';
import { USER_TYPE } from '~/services/constants.server';
import {
    getAddressOnGoogleMapBySearchingText,
    getTaskerLocationDetected,
    getTotalTaskerLocationDetected,
} from '~/services/location-detected.server';

describe('Tasker Location Detected Service', () => {
    const isoCode = IsoCode.VN;
    const MOCK_LOCATION = { lat: 10.7724073, lng: 106.6937346 };
    const MOCK_RADIUS = 1000; // Meters
    const MOCK_SEARCH_TEXT = 'Ho Chi Minh City';
    const MOCK_RANGE_DATE = {
        from: momentTz().subtract(2, 'days').toDate(),
        to: momentTz().add(2, 'days').toDate(),
    };

    const TASKER_LOCATION_200_METERS_AWAY_1 = {
        _id: 'mock_tasker_id_1',
        userId: 'mock_tasker_id_1',
        phone: '0900000001',
        name: 'Mock User 1',
        type: USER_TYPE.TASKER,
        userStatus: 'ACTIVE',
        history: [
            {
                time: momentTz().toDate(),
                lat: 10.7724073,
                lng: 106.6937346,
            },
            {
                time: new Date('2024-01-02T08:00:00.000Z'),
                lat: 10.727471386016653,
                lng: 106.6163263036413,
            },
            {
                time: new Date('2024-01-03T08:00:00.000Z'),
                lat: 10.800364773413481,
                lng: 106.65909768217173,
            },
        ],
        createdAt: new Date('2024-11-07T06:22:37.082Z'),
    };
    const TASKER_LOCATION_200_METERS_AWAY_2 = {
        _id: 'mock_tasker_id_2',
        userId: 'mock_tasker_id_2',
        phone: '0900000002',
        name: 'Mock User 2',
        type: USER_TYPE.TASKER,
        userStatus: 'ACTIVE',
        history: [
            {
                time: momentTz().toDate(),
                lat: 10.7726,
                lng: 106.69183,
            },
        ],
        createdAt: new Date('2024-11-07T06:22:37.082Z'),
    };
    const TASKER_LOCATION_OUT_OF_200_METERS_RANGE = {
        _id: 'mock_tasker_id_3',
        userId: 'mock_tasker_id_3',
        phone: '0900000003',
        name: 'Mock User 3',
        type: USER_TYPE.TASKER,
        userStatus: 'ACTIVE',
        history: [
            {
                time: momentTz().toDate(),
                lat: 11.362222,
                lng: 106.098105,
            },
        ],
        createdAt: new Date('2024-11-07T06:22:37.082Z'),
    };

    const MOCK_TOTAL_TASKER_DETECTED_IN_200_METERS_RADIUS = 2;

    beforeAll(async () => {
        await getModels(isoCode).userLocationHistory.create(
            TASKER_LOCATION_200_METERS_AWAY_1,
        );
        await getModels(isoCode).userLocationHistory.create(
            TASKER_LOCATION_200_METERS_AWAY_2,
        );
        await getModels(isoCode).userLocationHistory.create(
            TASKER_LOCATION_OUT_OF_200_METERS_RANGE,
        );
    });

    afterAll(async () => {
        await getModels(isoCode).userLocationHistory.deleteMany({
            _id: TASKER_LOCATION_200_METERS_AWAY_1._id,
        });
        await getModels(isoCode).userLocationHistory.deleteMany({
            _id: TASKER_LOCATION_200_METERS_AWAY_2._id,
        });
        await getModels(isoCode).userLocationHistory.deleteMany({
            _id: TASKER_LOCATION_OUT_OF_200_METERS_RANGE._id,
        });
    });

    describe('getAddressOnGoogleMapBySearchingText', () => {
        it('Should return emptye array when searchText is empty', async () => {
            const addresses = await getAddressOnGoogleMapBySearchingText({
                searchText: '12',
                isoCode,
            });

            const MOCK_ADDRESS_EMPTY_SEARCHING_BY_INVALID_TEXT = [];
            expect(addresses).toHaveLength(
                MOCK_ADDRESS_EMPTY_SEARCHING_BY_INVALID_TEXT.length,
            );
        });
        it('Should return address candidates when searchText length is sufficient', async () => {
            const spyFetching = jest
                .spyOn(global, 'fetch')
                .mockImplementation(async (url: MustBeAny) => {
                    if (url.includes('InvalidSearchTextThatReturnsNoResults')) {
                        return {
                            ok: true,
                            status: 200,
                            json: async () => ({ candidates: [] }),
                        } as Response;
                    }
                    return {
                        ok: true,
                        status: 200,
                        json: async () => ({
                            candidates: [
                                {
                                    formatted_address: 'Mock Address',
                                    geometry: {
                                        location: { lat: 10.0, lng: 106.0 },
                                    },
                                    name: 'Mock Place',
                                },
                            ],
                        }),
                    } as Response;
                });
            const addresses = await getAddressOnGoogleMapBySearchingText({
                searchText: MOCK_SEARCH_TEXT,
                isoCode,
            });
            expect(addresses).toHaveLength(1);

            spyFetching.mockRestore();
        });

        it('Should return empty array when searchText is too short', async () => {
            const spyFetching = jest
                .spyOn(global, 'fetch')
                .mockImplementation(async (url: MustBeAny) => {
                    if (url.includes('InvalidSearchTextThatReturnsNoResults')) {
                        return {
                            ok: true,
                            status: 200,
                            json: async () => ({ candidates: [] }),
                        } as Response;
                    }
                    return {
                        ok: true,
                        status: 200,
                        json: async () => ({
                            candidates: [
                                {
                                    formatted_address: 'Mock Address',
                                    geometry: {
                                        location: { lat: 10.0, lng: 106.0 },
                                    },
                                    name: 'Mock Place',
                                },
                            ],
                        }),
                    } as Response;
                });
            const addresses = await getAddressOnGoogleMapBySearchingText({
                searchText: TASKER_LOCATION_OUT_OF_200_METERS_RANGE.phone,
                isoCode,
            });

            const MOCK_TASKER_SEARCHING_BY_PHONE_NUMBER = [
                {
                    ...TASKER_LOCATION_OUT_OF_200_METERS_RANGE,
                    movingHistoryCount: 1,
                },
            ];

            expect(addresses).toHaveLength(
                MOCK_TASKER_SEARCHING_BY_PHONE_NUMBER.length,
            );
            spyFetching.mockRestore();
        });

        it('Should return empty array when searchText no results', async () => {
            const spyFetching = jest
                .spyOn(global, 'fetch')
                .mockImplementation(async (url: MustBeAny) => {
                    if (url.includes('InvalidSearchTextThatReturnsNoResults')) {
                        return {
                            ok: true,
                            status: 200,
                            json: async () => ({ candidates: [] }),
                        } as Response;
                    }
                    return {
                        ok: true,
                        status: 200,
                        json: async () => ({
                            candidates: [
                                {
                                    formatted_address: 'Mock Address',
                                    geometry: {
                                        location: { lat: 10.0, lng: 106.0 },
                                    },
                                    name: 'Mock Place',
                                },
                            ],
                        }),
                    } as Response;
                });
            const addresses = await getAddressOnGoogleMapBySearchingText({
                searchText: 'InvalidSearchTextThatReturnsNoResults',
                isoCode,
            });
            expect(addresses).toHaveLength(0);
            spyFetching.mockRestore();
        });

        it('Should handle invalid isoCode gracefully', async () => {
            const spyFetching = jest
                .spyOn(global, 'fetch')
                .mockImplementation(async (url: MustBeAny) => {
                    if (url.includes('InvalidSearchTextThatReturnsNoResults')) {
                        return {
                            ok: true,
                            status: 200,
                            json: async () => ({ candidates: [] }),
                        } as Response;
                    }
                    return {
                        ok: true,
                        status: 200,
                        json: async () => ({
                            candidates: [
                                {
                                    formatted_address: 'Mock Address',
                                    geometry: {
                                        location: { lat: 10.0, lng: 106.0 },
                                    },
                                    name: 'Mock Place',
                                },
                            ],
                        }),
                    } as Response;
                });
            await expect(
                getAddressOnGoogleMapBySearchingText({
                    searchText: MOCK_SEARCH_TEXT,
                    isoCode: 'INVALID_ISO_CODE' as IsoCode,
                }),
            ).rejects.toThrow('Not support this INVALID_ISO_CODE IsoCode');
            spyFetching.mockRestore();
        });
        it('Should handle special characters in searchText', async () => {
            const spyFetching = jest
                .spyOn(global, 'fetch')
                .mockImplementation(async (url: MustBeAny) => {
                    if (url.includes('InvalidSearchTextThatReturnsNoResults')) {
                        return {
                            ok: true,
                            status: 200,
                            json: async () => ({ candidates: [] }),
                        } as Response;
                    }
                    return {
                        ok: true,
                        status: 200,
                        json: async () => ({
                            candidates: [
                                {
                                    formatted_address: 'Mock Address',
                                    geometry: {
                                        location: { lat: 10.0, lng: 106.0 },
                                    },
                                    name: 'Mock Place',
                                },
                            ],
                        }),
                    } as Response;
                });
            const addresses = await getAddressOnGoogleMapBySearchingText({
                searchText: '!@#$%^&*()',
                isoCode,
            });
            expect(addresses).toHaveLength(1);
            spyFetching.mockRestore();
        });
        it('should throw error when Google Map API returns error status', async () => {
            const statusCode = 500;
            const mockStatusText = 'Internal Server Error';
            const spyFetching = jest
                .spyOn(global, 'fetch')
                .mockResolvedValueOnce({
                    ok: false,
                    status: statusCode,
                    statusText: mockStatusText,
                } as Response);

            await expect(
                getAddressOnGoogleMapBySearchingText({
                    searchText: MOCK_SEARCH_TEXT,
                    isoCode,
                }),
            ).rejects.toThrow(
                `Google Map API Error [${statusCode}]: ${mockStatusText}`,
            );

            spyFetching.mockRestore();
        });

        it('should throw error when fetch fails', async () => {
            const mockErrorText = 'Network Error';
            const spyFetching = jest
                .spyOn(global, 'fetch')
                .mockRejectedValueOnce(new Error(mockErrorText));

            await expect(
                getAddressOnGoogleMapBySearchingText({
                    searchText: MOCK_SEARCH_TEXT,
                    isoCode,
                }),
            ).rejects.toThrow(`Error: ${mockErrorText}`);

            spyFetching.mockRestore();
        });
    });

    describe('getTotalTaskerLocationDetected', () => {
        it('Should return total taskers detected within radius', async () => {
            const total = await getTotalTaskerLocationDetected({
                isoCode,
                rangeDate: MOCK_RANGE_DATE,
                location: MOCK_LOCATION,
                radius: MOCK_RADIUS,
                searchText: '',
            });
            expect(total).toBeGreaterThanOrEqual(
                MOCK_TOTAL_TASKER_DETECTED_IN_200_METERS_RADIUS,
            );
        });

        it('Should return 0 when location is missing', async () => {
            const total = await getTotalTaskerLocationDetected({
                isoCode,
                rangeDate: MOCK_RANGE_DATE,
                radius: MOCK_RADIUS,
                searchText: '',
            });
            const MOCK_TOTAL_TASKER_THAT_NOT_CHOOSE_LOCATION = 0;

            expect(total).toBe(MOCK_TOTAL_TASKER_THAT_NOT_CHOOSE_LOCATION);
        });

        it('Should return 0 when radius is invalid', async () => {
            const total = await getTotalTaskerLocationDetected({
                isoCode,
                rangeDate: MOCK_RANGE_DATE,
                location: MOCK_LOCATION,
                radius: -100,
                searchText: '',
            });

            expect(total).toBe(0);
        });

        it('Should return total taskers when searchText filters results', async () => {
            const total = await getTotalTaskerLocationDetected({
                isoCode,
                rangeDate: MOCK_RANGE_DATE,
                location: MOCK_LOCATION,
                radius: MOCK_RADIUS,
                searchText: TASKER_LOCATION_200_METERS_AWAY_2.phone,
            });

            const MOCK_TOTAL_TASKER_SEARCHING_BY_PHONE = 1;

            expect(total).toEqual(MOCK_TOTAL_TASKER_SEARCHING_BY_PHONE);
        });
    });

    describe('getTaskerLocationDetected', () => {
        it('Should return taskers detected within radius', async () => {
            const taskers = await getTaskerLocationDetected({
                isoCode,
                rangeDate: MOCK_RANGE_DATE,
                location: MOCK_LOCATION,
                radius: MOCK_RADIUS,
                sort: { time: -1 } as PipelineStage.Sort['$sort'],
                skip: 0,
                limit: 10,
                searchText: '',
            });

            const MOCK_TASKERS_DETECTED_IN_200_METERS_RADIUS = [
                { ...TASKER_LOCATION_200_METERS_AWAY_1, movingHistoryCount: 1 },
                { ...TASKER_LOCATION_200_METERS_AWAY_2, movingHistoryCount: 1 },
            ];

            expect(taskers).toHaveLength(
                MOCK_TOTAL_TASKER_DETECTED_IN_200_METERS_RADIUS,
            );
            taskers.forEach(tasker => {
                expect(
                    MOCK_TASKERS_DETECTED_IN_200_METERS_RADIUS.find(
                        mockTasker => mockTasker.userId === tasker.userId,
                    ),
                ).toBeDefined();
            });
        });

        it('Should return empty array when location is missing', async () => {
            const taskers = await getTaskerLocationDetected({
                isoCode,
                rangeDate: MOCK_RANGE_DATE,
                radius: MOCK_RADIUS,
                sort: { movingHistoryCount: -1 } as PipelineStage.Sort['$sort'],
                skip: 0,
                limit: 10,
                searchText: '',
            });

            const MOCK_TOTAL_TASKER_THAT_NOT_CHOOSE_LOCATION = 0;

            expect(taskers).toHaveLength(
                MOCK_TOTAL_TASKER_THAT_NOT_CHOOSE_LOCATION,
            );
        });

        it('Should return empty array when radius is invalidd', async () => {
            const taskers = await getTaskerLocationDetected({
                isoCode,
                rangeDate: MOCK_RANGE_DATE,
                location: MOCK_LOCATION,
                radius: -10,
                sort: { movingHistoryCount: -1 } as PipelineStage.Sort['$sort'],
                skip: 0,
                limit: 10,
                searchText: '',
            });
            const MOCK_TOTAL_TASKER_THAT_RADIUS_IS_INVALID = 0;

            expect(taskers).toHaveLength(
                MOCK_TOTAL_TASKER_THAT_RADIUS_IS_INVALID,
            );
        });

        it('Should return taskers when searchText filters results', async () => {
            const taskers = await getTaskerLocationDetected({
                isoCode,
                rangeDate: MOCK_RANGE_DATE,
                location: MOCK_LOCATION,
                radius: MOCK_RADIUS,
                sort: { movingHistoryCount: -1 } as PipelineStage.Sort['$sort'],
                skip: 0,
                limit: 10,
                searchText: 'Specific Tasker Name',
            });

            expect(taskers).toBeInstanceOf(Array);
        });

        it('Should return empty array when skip exceeds total results', async () => {
            const taskers = await getTaskerLocationDetected({
                isoCode,
                rangeDate: MOCK_RANGE_DATE,
                location: MOCK_LOCATION,
                radius: MOCK_RADIUS,
                sort: { movingHistoryCount: -1 } as PipelineStage.Sort['$sort'],
                skip: 1000000,
                limit: 10,
                searchText: '',
            });
            expect(taskers).toEqual([]);
        });
    });
});
