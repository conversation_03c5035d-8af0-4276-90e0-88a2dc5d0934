import { IsoCode } from 'btaskee-constants';
import { momentTz } from 'btaskee-utils';
import { type PipelineStage } from 'mongo-connection';
import { getModels } from 'schemas';
import {
    createToolkitSetting,
    getListToolkitItems,
    getListToolkitSetting,
    getToolkitSettingDetail,
    getTotalToolkitSetting,
    updateToolkitSetting,
} from '~/services/toolkit.server';

describe('Toolkit Setting ', () => {
    const isoCode = IsoCode.VN;
    const toolkitItemIds = [
        'toolkitItemId1',
        'toolkitItemId2',
        'toolkitItemId3',
        'toolkitItemId4',
        'toolkitItemId5',
    ];
    const serviceIds = ['serviceId1', 'serviceId2', 'serviceId3', 'serviceId4'];

    const commonToolkitSettingInfo = {
        image: 'toolkitSetting.image.com',
        discountForOncePay: 12,
        text: {
            vi: 'toolkit setting vi',
            en: 'toolkit setting en',
            ko: 'toolkit setting ko',
            th: 'toolkit setting th',
            id: 'toolkit setting id',
        },
        BNPLSetting: {
            firstPayPercent: 12,
            percentBNPLOnTask: 10,
            period: 2,
        },
        serviceIds: serviceIds,
        createdAt: new Date(),
    };

    const mockToolkitItems = toolkitItemIds.map(toolkitItemId => ({
        _id: toolkitItemId,
        image: 'image.com',
        price: 22000,
        text: {
            vi: toolkitItemId,
            en: toolkitItemId,
            ko: toolkitItemId,
            th: toolkitItemId,
            id: toolkitItemId,
        },
        createdAt: new Date(),
    }));

    const mockToolkitSettings = [
        {
            _id: 'toolkitSettingId1',
            ...commonToolkitSettingInfo,
            serviceIds: [serviceIds[1], serviceIds[0]],
            toolKitItems: [{ _id: toolkitItemIds[0], quantity: 2 }],
        },
        {
            _id: 'toolkitSettingId2',
            ...commonToolkitSettingInfo,
            toolKitItems: [
                { _id: toolkitItemIds[1], quantity: 1 },
                { _id: toolkitItemIds[2], quantity: 2 },
            ],
            serviceIds: [serviceIds[1], serviceIds[2]],
        },
        {
            _id: 'toolkitSettingId3',
            ...commonToolkitSettingInfo,
            toolKitItems: [
                { _id: toolkitItemIds[3], quantity: 1 },
                { _id: toolkitItemIds[4], quantity: 3 },
            ],
            serviceIds: [serviceIds[2], serviceIds[3]],
        },
    ];

    beforeAll(async () => {
        await getModels(isoCode).toolKitItems.insertMany(mockToolkitItems);
        await getModels(isoCode).toolKitSetting.insertMany(mockToolkitSettings);
    });
    afterAll(async () => {
        await getModels(isoCode).toolKitItems.deleteMany({
            _id: { $in: toolkitItemIds },
        });
        await getModels(isoCode).toolKitSetting.deleteMany({
            _id: {
                $in: mockToolkitSettings.map(
                    mockToolkitSetting => mockToolkitSetting._id,
                ),
            },
        });
    });
    describe('getToolkitSettingDetail', () => {
        it('Should throw error message when toolkit not found', async () => {
            await expect(
                getToolkitSettingDetail({
                    isoCode,
                    toolkitSettingId: 'idNotFound',
                }),
            ).rejects.toThrow('TOOLKIT_NOT_FOUND');
        });
        it('Should get toolkit setting detail successfully', async () => {
            const { __v, ...toolkitSetting }: MustBeAny =
                await getToolkitSettingDetail({
                    isoCode,
                    toolkitSettingId: mockToolkitSettings[1]._id,
                });

            expect({ __v, ...toolkitSetting }).toStrictEqual({
                __v,
                ...mockToolkitSettings[1],
            });
        });
    });
    describe('getTotalToolkitSetting', () => {
        it('Should get total toolkit setting successfully', async () => {
            const paramsPassingQuery = {
                isoCode,
                services: serviceIds.slice(0, 2).join(','),
                search: '',
                rangeDate: {
                    from: momentTz().subtract(2, 'day').toDate(),
                    to: momentTz().add(2, 'day').toDate(),
                },
            };

            const totalToolkitSetting =
                await getTotalToolkitSetting(paramsPassingQuery);

            const serviceIdsFormParams = paramsPassingQuery.services.split(',');

            const expectationToolkitSettings = mockToolkitSettings.filter(
                toolkitSetting =>
                    toolkitSetting.createdAt >=
                        paramsPassingQuery.rangeDate.from &&
                    toolkitSetting.createdAt <=
                        paramsPassingQuery.rangeDate.to &&
                    !!serviceIdsFormParams.find(serviceId =>
                        toolkitSetting.serviceIds.includes(serviceId),
                    ),
            );

            expect(totalToolkitSetting).toEqual(
                expectationToolkitSettings.length,
            );
        });
        it('Should get total toolkit setting when searching successfully', async () => {
            const paramsPassingQuery = {
                isoCode,
                services: serviceIds.slice(0, 2).join(','),
                search: mockToolkitSettings[1].text.en,
                rangeDate: {
                    from: momentTz().subtract(2, 'day').toDate(),
                    to: momentTz().add(2, 'day').toDate(),
                },
            };

            const totalToolkitSetting =
                await getTotalToolkitSetting(paramsPassingQuery);

            const serviceIdsFormParams = paramsPassingQuery.services.split(',');

            const expectationToolkitSettings = mockToolkitSettings.filter(
                toolkitSetting =>
                    toolkitSetting.createdAt >=
                        paramsPassingQuery.rangeDate.from &&
                    toolkitSetting.createdAt <=
                        paramsPassingQuery.rangeDate.to &&
                    !!serviceIdsFormParams.find(serviceId =>
                        toolkitSetting.serviceIds.includes(serviceId),
                    ) &&
                    toolkitSetting.text.en === paramsPassingQuery.search,
            );

            expect(totalToolkitSetting).toEqual(
                expectationToolkitSettings.length,
            );
        });
    });
    describe('getListToolkitSetting', () => {
        it('Should get list toolkit setting successfully', async () => {
            const paramsPassingQuery = {
                isoCode,
                services: serviceIds.slice(0, 2).join(','),
                search: '',
                rangeDate: {
                    from: momentTz().subtract(2, 'day').toDate(),
                    to: momentTz().add(2, 'day').toDate(),
                },
                sort: {
                    createdAt: -1,
                } as PipelineStage.Sort['$sort'],
                skip: 0,
                limit: 10,
            };

            const toolkitSettings =
                await getListToolkitSetting(paramsPassingQuery);

            const serviceIdsFormParams = paramsPassingQuery.services.split(',');
            const expectationToolkitSettings = mockToolkitSettings.filter(
                toolkitSetting =>
                    toolkitSetting.createdAt >=
                        paramsPassingQuery.rangeDate.from &&
                    toolkitSetting.createdAt <=
                        paramsPassingQuery.rangeDate.to &&
                    !!serviceIdsFormParams.find(serviceId =>
                        toolkitSetting.serviceIds.includes(serviceId),
                    ),
            );

            expect(toolkitSettings).toHaveLength(
                expectationToolkitSettings.length,
            );
            toolkitSettings.forEach(toolkit => {
                const expectationToolkitFound = expectationToolkitSettings.find(
                    toolkitSetting => toolkitSetting._id === toolkit._id,
                );
                expect(expectationToolkitFound).toBeDefined();
            });
        });
        it('Should get list toolkit setting when searching successfully', async () => {
            const paramsPassingQuery = {
                isoCode,
                services: serviceIds.slice(0, 2).join(','),
                search: mockToolkitSettings[0].text.vi,
                rangeDate: {
                    from: momentTz().subtract(2, 'day').toDate(),
                    to: momentTz().add(2, 'day').toDate(),
                },
                sort: {
                    createdAt: -1,
                } as PipelineStage.Sort['$sort'],
                skip: 0,
                limit: 10,
            };

            const toolkitSettings =
                await getListToolkitSetting(paramsPassingQuery);

            const serviceIdsFormParams = paramsPassingQuery.services.split(',');
            const expectationToolkitSettings = mockToolkitSettings.filter(
                toolkitSetting =>
                    toolkitSetting.createdAt >=
                        paramsPassingQuery.rangeDate.from &&
                    toolkitSetting.createdAt <=
                        paramsPassingQuery.rangeDate.to &&
                    !!serviceIdsFormParams.find(serviceId =>
                        toolkitSetting.serviceIds.includes(serviceId),
                    ) &&
                    toolkitSetting.text.vi === paramsPassingQuery.search,
            );

            expect(toolkitSettings).toHaveLength(
                expectationToolkitSettings.length,
            );
            toolkitSettings.forEach(toolkit => {
                const expectationToolkitFound = expectationToolkitSettings.find(
                    toolkitSetting => toolkitSetting._id === toolkit._id,
                );
                expect(expectationToolkitFound).toBeDefined();
            });
        });
    });

    describe('createToolkitSetting', () => {
        it('Should create toolkit setting successfully', async () => {
            const paramsPassingQuery = {
                isoCode,
                toolkitSetting: {
                    image: 'image.com',
                    discountForOncePay: 20,
                    text: {
                        vi: 'text vi',
                        en: 'text en',
                        ko: 'text ko',
                        th: 'text th',
                        id: 'text id',
                    },
                    BNPLSetting: {
                        firstPayPercent: 20,
                        percentBNPLOnTask: 20,
                        period: 20,
                    },
                    toolKitItems: [
                        {
                            _id: toolkitItemIds[1],
                            quantity: 20,
                        },
                    ],
                    serviceIds: [serviceIds[0], serviceIds[2]],
                },
            };

            const toolkitItemId =
                await createToolkitSetting(paramsPassingQuery);

            const toolkitFound = await getModels(
                isoCode,
            ).toolKitSetting.findById(toolkitItemId?._id);

            expect(toolkitFound).toBeDefined();

            await getModels(isoCode).toolKitSetting.findByIdAndDelete(
                toolkitFound?._id,
            );
        });
    });
    describe('updateToolkitSetting', () => {
        it('Should throw error message when toolkit setting not found', async () => {
            await expect(
                updateToolkitSetting({
                    isoCode,
                    toolkitSettingId: 'idNotFound',
                    updateInfos: { image: 'updated-image.com' },
                }),
            ).rejects.toThrow('TOOLKIT_NOT_FOUND');
        });
        it('Should update toolkit setting successfully', async () => {
            const paramsPassingQuery = {
                isoCode,
                toolkitSettingId: mockToolkitSettings[1]._id,
                updateInfos: { image: 'updated-image.com' },
            };
            await updateToolkitSetting(paramsPassingQuery);

            const toolkitSettingFound = await getModels(
                isoCode,
            ).toolKitSetting.findById(paramsPassingQuery.toolkitSettingId);

            expect(toolkitSettingFound?.image).toEqual(
                paramsPassingQuery.updateInfos.image,
            );
        });
    });
    describe('getListToolkitItems', () => {
        it('Should get list toolkit items successfully', async () => {
            const toolkitItems = await getListToolkitItems({ isoCode });

            expect(toolkitItems).toHaveLength(mockToolkitItems.length);

            toolkitItems.forEach(toolkitItem => {
                const expectationToolkitItem = mockToolkitItems.find(
                    mockToolkitItem => mockToolkitItem._id === toolkitItem._id,
                );

                expect(expectationToolkitItem).toBeDefined();
            });
        });
    });
});
