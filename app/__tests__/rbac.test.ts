import { statusOriginal } from 'btaskee-constants';
import { momentTz } from 'btaskee-utils';
import GroupsModel from '~/services/model/groups.server';
import PermissionsModel from '~/services/model/permissions.server';
import RolesModel from '~/services/model/roles.servers';
import UsersModel from '~/services/model/users.server';
import {
    addMultiUserToGroup,
    createGroup,
    deleteGroup,
    getAllChildrenGroupOfUser,
    getAllPermissions,
    getGroupDetail,
    getGroupPermissions,
    getGroupsOfUser,
    getListMemberAddingToGroup,
    getPermissionsCreatedByGroupId,
    getTotalMemberAddingToGroup,
    getUserPermissionIdsGlobal,
    getUserPermissionsGlobal,
    updateGroupInformation,
    updatePermission,
    verifyManager,
    verifySuperUser,
    verifyUserInGroup,
} from '~/services/role-base-access-control.server';

function mockResponseThrowError() {
    const errorText = 'response return error message';

    const spy = jest
        .spyOn(global, 'Response')
        .mockImplementation(() => new Error(errorText) as MustBeAny);

    function restore() {
        spy.mockRestore();
    }
    return { restore, errorText };
}

const mockRecordCommonField = {
    createdAt: new Date('2024-03-24T00:00:00.000Z'),
    _id: `new-role-id${Date.now()}`,
    status: 'ACTIVE',
};

jest.mock('~/services/helpers.server', () => ({
    __esModule: true,
    getUserId: () => 'root',
}));

jest.mock('~/services/helpers.server', () => ({
    __esModule: true,
    getUserId: () => 'root',
}));

jest.mock('~/services/constants.server', () => ({
    __esModule: true,
    statusOriginal: {
        ACTIVE: 'ACTIVE',
        REMOVED: 'REMOVED',
    },
    newRecordCommonField: () => mockRecordCommonField,
}));

jest.mock('~/third-party/mail.server', () => ({
    __esModule: true,
    sendEmail: jest.fn(),
}));

describe('Role base access control', () => {
    const rootId = 'root';
    const managerId = 'manager';
    const leaderId = 'leader';
    const employeeId = 'employee';

    const userId = 'user-1';
    const userId2 = 'user-2';
    const groupId = 'group-1';

    const mockPermission: Array<BtaskeePermissions> = [
        {
            _id: rootId,
            key: rootId,
            name: 'ROOT',
            description: 'ROOT',
            module: 'admin',
            'slug-module': 'root',
        },
        {
            _id: managerId,
            key: managerId,
            name: 'MANAGER',
            description: 'MANAGER',
            module: 'admin',
            'slug-module': 'admin',
        },
        {
            _id: leaderId,
            key: leaderId,
            name: 'LEADER',
            module: 'admin',
            description: 'LEADER',
            'slug-module': 'leader',
        },
        {
            _id: employeeId,
            key: employeeId,
            name: 'EMPLOYEE',
            description: 'EMPLOYEE',
            module: 'admin',
            'slug-module': 'employee',
        },
    ];

    beforeEach(async () => {
        await UsersModel.insertMany([
            {
                _id: userId,
                email: '<EMAIL>',
                username: 'Test 1',
                createdAt: momentTz().toDate(),
                status: 'ACTIVE',
                cities: ['Hồ Chí Minh'],
                isoCode: 'VN',
            },
            {
                _id: userId2,
                email: '<EMAIL>',
                username: 'Test 1',
                createdAt: momentTz().toDate(),
                status: 'ACTIVE',
                cities: ['Hồ Chí Minh'],
                isoCode: 'VN',
            },
        ]);

        await GroupsModel.create({
            _id: groupId,
            users: [
                { _id: rootId, status: 'ACTIVE', addedAt: momentTz().toDate() },
            ],
            name: 'groupName1',
            description: 'groupName1 desc',
            genealogy: [],
            hierarchy: 1,
            createdAt: new Date('2023-12-01'),
            status: 'ACTIVE',
            isoCode: 'VN',
            createdBy: rootId,
        });

        await PermissionsModel.insertMany(mockPermission);
        await UsersModel.insertMany([
            {
                _id: rootId,
                email: '<EMAIL>',
                username: 'Root user',
                createdAt: new Date(),
                status: 'ACTIVE',
                cities: ['Hồ Chí Minh'],
                services: {
                    password: {
                        bcrypt: 'bcrypt',
                    },
                },
                verification: {
                    code: '123456',
                    token: 'xxxxx',
                    expired: new Date('2024-02-01'),
                },
                resetPassword: {
                    token: 'resetXxxx',
                    expired: new Date('2024-02-01'),
                },
                isoCode: 'VN',
            },
            {
                _id: managerId,
                email: '<EMAIL>',
                username: 'Manager user',
                createdAt: new Date(),
                status: 'ACTIVE',
                cities: ['Hà Nội'],
                isoCode: 'VN',
            },
            {
                _id: leaderId,
                email: '<EMAIL>',
                username: 'Leader user',
                createdAt: new Date('2024-03-25'),
                status: 'ACTIVE',
                cities: ['Đà Nẵng'],
                isoCode: 'VN',
            },
            {
                _id: employeeId,
                email: '<EMAIL>',
                username: 'Employee user',
                createdAt: new Date('2024-04-01'),
                status: 'INACTIVE',
                cities: ['Huế'],
                isoCode: 'VN',
            },
        ]);

        await GroupsModel.insertMany([
            {
                _id: rootId,
                users: [
                    {
                        _id: rootId,
                        status: 'ACTIVE',
                        addedAt: momentTz().toDate(),
                    },
                ],
                // roleAssignedIds: [rootId],
                name: 'root',
                description: 'root group desc',
                hierarchy: 1,
                createdAt: new Date(),
                status: 'ACTIVE',
                createdBy: rootId,
            },
            {
                _id: managerId,
                users: [
                    {
                        _id: managerId,
                        status: 'ACTIVE',
                        addedAt: momentTz().toDate(),
                    },
                ],
                // roleAssignedIds: [managerId],
                name: 'manager group',
                description: 'manager group desc',
                genealogy: ['genealogy1'],
                hierarchy: 2,
                createdAt: new Date('2024-02-01'),
                status: 'ACTIVE',
                createdBy: managerId,
            },
            {
                _id: leaderId,
                users: [
                    {
                        _id: leaderId,
                        status: 'ACTIVE',
                        addedAt: momentTz().toDate(),
                    },
                ],
                name: 'leader',
                description: 'leader group desc',
                genealogy: ['genealogy1'],
                hierarchy: 3,
                createdAt: new Date('2024-01-01'),
                status: 'ACTIVE',
                createdBy: managerId,
            },
            {
                _id: employeeId,
                users: [
                    {
                        _id: employeeId,
                        status: 'ACTIVE',
                        addedAt: momentTz().toDate(),
                    },
                ],
                name: 'employee',
                description: 'employee group desc',
                genealogy: ['genealogy1'],
                hierarchy: 4,
                createdAt: new Date('2024-02-05'),
                status: 'ACTIVE',
                createdBy: managerId,
            },
        ]);
    });

    afterEach(async () => {
        await UsersModel.deleteMany({ _id: { $in: [userId, userId2] } });
        await GroupsModel.deleteOne({ _id: groupId });
        await UsersModel.deleteMany({
            _id: {
                $in: [rootId, leaderId, managerId, employeeId],
            },
        });
        await GroupsModel.deleteMany({
            _id: { $in: [rootId, leaderId, managerId, employeeId] },
        });
        await PermissionsModel.deleteMany({
            _id: { $in: [rootId, leaderId, managerId, employeeId] },
        });
    });

    describe('getGroupsOfUser', () => {
        const userId = 'testUser';
        const groupId = 'testGroup';

        beforeEach(async () => {
            await GroupsModel.create({
                _id: groupId,
                users: [
                    {
                        _id: userId,
                        status: 'ACTIVE',
                        addedAt: momentTz().toDate(),
                    },
                ],
                name: 'Test Group',
                description: 'Test Group Description',
                genealogy: ['genealogy1'],
                hierarchy: 2,
                createdAt: new Date(),
                status: 'ACTIVE',
                createdBy: 'root',
            });
        });

        afterEach(async () => {
            await GroupsModel.deleteOne({ _id: groupId });
        });

        it('Should return groups of a user', async () => {
            const projection = { _id: 1, name: 1 };
            const groups = await getGroupsOfUser({ projection, userId });
            expect(groups).toHaveLength(1);
            expect(groups[0]._id).toEqual(groupId);
            expect(groups[0].name).toEqual('Test Group');
        });

        it('Should return empty array if user is not part of any group', async () => {
            const projection = { _id: 1, name: 1 };
            const groups = await getGroupsOfUser({
                projection,
                userId: 'nonexistentUser',
            });
            expect(groups).toHaveLength(0);
        });
    });

    describe('createGroup', () => {
        it('Should createGroup successfully', async () => {
            const mockParams = {
                name: 'test',
                description: 'testing',
                parentId: groupId,
                userId: rootId,
                status: 'ACTIVE',
            };

            await createGroup(mockParams);
            const newGroupInserted = await GroupsModel.findOne({
                name: mockParams.name,
            });
            expect(newGroupInserted?.description).toBe(mockParams.description);

            await GroupsModel.deleteOne({ name: mockParams.name });
        });

        it('Should throw error if parent group not found', async () => {
            const groupData: Pick<
                GroupsV2,
                'name' | 'description' | 'roleAssignedIds' | 'status'
            > & { parentId: string; userId: Users['_id'] } = {
                name: 'Test Group',
                description: 'This is a test group',
                parentId: 'nonexistentParentId',
                userId: rootId,
                status: 'ACTIVE',
                roleAssignedIds: ['roleId1'],
            };

            await expect(createGroup(groupData)).rejects.toThrow(
                'PARENT_GROUP_NOT_FOUND',
            );
        });
    });

    describe('getGroupDetail', () => {
        const groupId1 = 'groupId1-detail';
        const groupUserId = 'groupUserId-detail';
        const userId1 = 'userId1';
        const userId2 = 'userId2';
        const genealogyId1 = 'genealogyId1-detail';
        const groups = [
            {
                _id: groupId1,
                users: [
                    {
                        _id: userId1,
                        status: 'ACTIVE',
                        addedAt: momentTz().toDate(),
                    },
                ],
                name: 'group-1',
                genealogy: [genealogyId1],
                description: 'groupName1 desc',
                hierarchy: 2,
                createdAt: new Date('2023-12-01'),
                status: 'ACTIVE',
                createdBy: userId1,
            },
            {
                _id: genealogyId1,
                users: [
                    {
                        _id: userId1,
                        status: 'ACTIVE',
                        addedAt: momentTz().toDate(),
                    },
                ],
                name: 'groups-2',
                description: 'groupName2 desc',
                hierarchy: 2,
                createdAt: new Date('2023-11-01'),
                status: 'ACTIVE',
                createdBy: userId1,
            },
            {
                _id: groupUserId,
                users: [
                    {
                        _id: userId2,
                        status: 'ACTIVE',
                        addedAt: momentTz().toDate(),
                    },
                ],
                name: 'groups-3',
                description: 'groupName3 desc',
                hierarchy: 2,
                createdAt: new Date('2023-11-01'),
                status: 'ACTIVE',
                createdBy: userId2,
            },
        ];

        const user = {
            _id: userId1,
            username: 'userName1',
            email: '<EMAIL>',
            createdAt: new Date('2024-01-01'),
            status: 'ACTIVE',
            cities: ['Hồ Chí Minh'],
            isoCode: 'VN',
        };

        const user2 = {
            _id: userId2,
            username: 'userName2',
            email: '<EMAIL>',
            createdAt: new Date('2024-01-01'),
            status: 'ACTIVE',
            cities: ['Hồ Chí Minh'],
            isoCode: 'VN',
        };

        beforeEach(async () => {
            groups.forEach(async group => {
                await GroupsModel.create(group);
            });
            await UsersModel.create(user);
            await UsersModel.create(user2);
        });
        afterEach(async () => {
            groups.forEach(async group => {
                await GroupsModel.deleteOne(group);
            });
            await UsersModel.deleteOne(user);
            await UsersModel.deleteOne(user2);
        });
        it('Should return group detail if user is root', async () => {
            const groupDetail = await getGroupDetail({
                isSuperUser: true,
                userId: userId1,
                groupId: groupId1,
                projection: { _id: 1 },
            });

            expect(groupDetail).toBeDefined();
            expect(groupDetail._id).toEqual(groupId1);
        });
        it('Should return group detail if user is parent of group', async () => {
            const groupDetail = await getGroupDetail({
                isSuperUser: false,
                userId: userId1,
                groupId: groupId1,
                projection: { _id: 1 },
            });

            expect(groupDetail).toBeDefined();
            expect(groupDetail._id).toEqual(groupId1);
        });
        it('Should return group detail for a group user', async () => {
            const groupDetail = await getGroupDetail({
                isSuperUser: false,
                userId: userId2,
                groupId: groupUserId,
                projection: { _id: 1 },
            });

            expect(groupDetail).toBeDefined();
            expect(groupDetail._id).toEqual(groupUserId);
        });
        it('Should throw an error if group does not exist and user is root', async () => {
            const { errorText, restore } = mockResponseThrowError();
            await expect(
                getGroupDetail({
                    isSuperUser: false,
                    userId,
                    groupId: 'nonexistentGroupId',
                    projection: { _id: 1 },
                }),
            ).rejects.toThrow(errorText);
            restore();
        });

        it('Should throw an error if group does not exist and user is parent of group', async () => {
            const { errorText, restore } = mockResponseThrowError();
            await expect(
                getGroupDetail({
                    isSuperUser: true,
                    userId,
                    groupId: 'nonexistentGroupId',
                    projection: { _id: 1 },
                }),
            ).rejects.toThrow(errorText);
            restore();
        });

        it('Should throw an error if group does not exist and user is group user', async () => {
            const { errorText, restore } = mockResponseThrowError();
            await expect(
                getGroupDetail({
                    isSuperUser: false,
                    userId: userId2,
                    groupId: 'nonexistentGroupId',
                    projection: { _id: 1 },
                }),
            ).rejects.toThrow(errorText);
            restore();
        });
    });

    describe('getGroupPermissions', () => {
        const group = {
            _id: 'groupId1-permission',
            status: 'ACTIVE',
            name: 'groupName1-permission',
            description: 'groupName1 desc',
            roleAssignedIds: ['anotherRoleId'],
            users: [
                {
                    _id: 'userId1',
                    status: 'ACTIVE',
                    addedAt: momentTz().toDate(),
                },
            ],
            genealogy: ['root'],
            hierarchy: 12,
            createdAt: new Date('2024-02-01'),
            createdBy: 'root',
        };

        beforeEach(async () => {
            await GroupsModel.create(group);
        });

        afterEach(async () => {
            await GroupsModel.deleteOne({ _id: group._id });
        });

        it('Should get entire permission when found root permission in a group correctly', async () => {
            const result = await getGroupPermissions({
                groupId: group._id,
                isSuperUser: true,
                isManager: true,
            });

            expect(result).toHaveLength(3);
        });
        it('Should get entire permission within a group correctly', async () => {
            const result = await getGroupPermissions({
                groupId: group._id,
                isSuperUser: true,
                isManager: false,
            });

            expect(result).toHaveLength(
                mockPermission.filter(permission => permission._id !== rootId)
                    .length,
            );
        });

        it('Should return empty array if group does not exist', async () => {
            const result = await getGroupPermissions({
                groupId: 'anotherGroupId',
                isSuperUser: false,
                isManager: false,
            });

            expect(result).toEqual([]);
        });

        it('Should return empty array if group does not have any permission', async () => {
            const result = await getGroupPermissions({
                groupId: group._id,
                isSuperUser: false,
                isManager: false,
            });

            expect(result).toEqual([]);
        });
    });

    describe('verifyUserInGroup', () => {
        it('Should verify a user in a group successfully', async () => {
            const isVerified = await verifyUserInGroup({
                userId: managerId,
                groupId: managerId,
            });

            expect(isVerified).toBe(true);
        });
    });

    describe('getAllPermissions', () => {
        it('Should get all permission by projection request successfully', async () => {
            const entirePermission = await getAllPermissions();

            expect(entirePermission).toHaveLength(mockPermission.length);
            entirePermission.forEach((permission, index) => {
                expect(permission._id).toEqual(mockPermission[index]._id);
            });
        });
    });

    describe('deleteGroup', () => {
        const group = {
            _id: 'groupId1-deleted',
            status: 'ACTIVE',
            name: 'groupName1-deleted',
            description: 'groupName1 desc',
            users: [
                {
                    _id: 'userId1',
                    status: 'ACTIVE',
                    addedAt: momentTz().toDate(),
                },
            ],
            hierarchy: 12,
            createdAt: new Date('2024-02-01'),
            genealogy: [],
            createdBy: 'userId1',
        };
        const groupChildren = {
            _id: 'groupId2-deleted',
            status: 'ACTIVE',
            name: 'groupName2-deleted',
            description: 'groupName1 desc',
            users: [
                {
                    _id: 'userId1',
                    status: 'ACTIVE',
                    addedAt: momentTz().toDate(),
                },
            ],
            hierarchy: 12,
            createdAt: new Date('2024-02-01'),
            genealogy: [group._id],
            createdBy: 'root',
        };

        beforeEach(async () => {
            await GroupsModel.create(group);
            await GroupsModel.create(groupChildren);
        });

        afterEach(async () => {
            await GroupsModel.deleteOne({ _id: group._id });
            await GroupsModel.deleteOne({ _id: groupChildren._id });
        });

        it('Should delete group by id successfully', async () => {
            await deleteGroup({ groupId: group._id });

            const deletedGroupFound = await GroupsModel.findOne({
                _id: group._id,
            });

            expect(deletedGroupFound?.status).toEqual('REMOVED');
        });
    });

    describe('verifySuperUser', () => {
        it('Should verify super user correctly', async () => {
            const isSuperUser = await verifySuperUser(rootId);

            expect(isSuperUser).toBe(false);
        });
    });

    describe('verifyManager', () => {
        it('Should verify manager correctly', async () => {
            const isManager = await verifyManager(managerId);

            expect(isManager).toBe(false);
        });
    });

    describe('getUserPermissionIdsGlobal', () => {
        it('Should get all permission ids ignore root if user has root or manager permission', async () => {
            const permissions = await getUserPermissionIdsGlobal(rootId);

            expect(permissions.sort()).toEqual([]);

            const permissionsManager =
                await getUserPermissionIdsGlobal(managerId);

            expect(permissionsManager.sort()).toEqual([]);
        });
    });

    describe('getUserPermissionsGlobal', () => {
        it('Should get all permission object ignore root if user has root or manager permission', async () => {
            const permissions = await getUserPermissionsGlobal(rootId);

            expect(
                permissions.sort().map(permission => permission.key),
            ).toEqual([]);

            const permissionsManager =
                await getUserPermissionsGlobal(managerId);

            expect(
                permissionsManager.sort().map(permission => permission.key),
            ).toEqual([]);
        });
    });

    describe('getPermissionsCreatedByGroupId', () => {
        it('Should return permissions for a given group id', async () => {
            const result = await getPermissionsCreatedByGroupId({
                groupId: managerId,
            });

            expect(result).toEqual([]);
        });

        it('Should return an empty array if no group is found', async () => {
            const groupId = 'anotherGroupId';
            const result = await getPermissionsCreatedByGroupId({ groupId });

            expect(result).toEqual([]);
        });
    });

    describe('getAllChildrenGroupOfUser', () => {
        const group = {
            _id: 'groupId1-11',
            genealogy: ['genealogyId'],
            name: 'groupName1-11',
            description: 'groupName1 desc',
            users: [
                {
                    _id: 'userId1',
                    status: 'ACTIVE',
                    addedAt: momentTz().toDate(),
                },
            ],
            hierarchy: 10,
            createdAt: new Date('2024-02-01'),
            status: 'ACTIVE',
            createdBy: 'root',
        };

        const childrenGroup = {
            _id: 'groupId2-22',
            genealogy: ['groupId1-11'],
            name: 'groupName2-22',
            description: 'groupName2 desc',
            users: [
                {
                    _id: 'userId1',
                    status: 'ACTIVE',
                    addedAt: momentTz().toDate(),
                },
            ],
            hierarchy: 10,
            createdAt: new Date('2024-03-01'),
            status: 'ACTIVE',
            createdBy: 'root',
        };

        beforeEach(async () => {
            await GroupsModel.create(group);
            await GroupsModel.create(childrenGroup);
        });

        afterEach(async () => {
            await GroupsModel.deleteOne({ _id: group._id });
            await GroupsModel.deleteOne({ _id: childrenGroup._id });
        });

        it('Should return all children group of a user', async () => {
            const result = await getAllChildrenGroupOfUser('userId1');

            expect(result).toHaveLength(1);
            expect(result[0]._id).toEqual(childrenGroup._id);
        });

        it('Should return an empty array if no children group is found', async () => {
            const result = await getAllChildrenGroupOfUser('anotherUserId');

            expect(result).toEqual([]);
        });
    });

    describe('updatePermission', () => {
        it('Should update permission successfully', async () => {
            const mockParams = {
                permissionId: employeeId,
                params: {
                    name: 'New Permission Name',
                    description: 'Updated description for the permission',
                },
            };

            await updatePermission(mockParams);

            const result = await PermissionsModel.findOne({
                _id: mockParams.permissionId,
            }).lean();

            expect(result?.name).toEqual(mockParams.params.name);
            expect(result?.description).toEqual(mockParams.params.description);
        });

        it('Should throw an error if permission is not found', async () => {
            const mockParams = {
                permissionId: 'nonExistentPermissionId',
                params: {
                    name: 'New Permission Name',
                    description: 'Updated description for the permission',
                },
            };

            await expect(updatePermission(mockParams)).rejects.toThrow(
                'PERMISSION_NOT_FOUND',
            );
        });
    });

    describe('updateGroupInformation', () => {
        const mockGroupId = 'group-test';
        const mockRoleId = 'role-test';

        beforeEach(async () => {
            await GroupsModel.create({
                _id: mockGroupId,
                name: 'Test',
                description: 'Test',
                hierarchy: 1,
                createdAt: new Date(),
                status: 'ACTIVE',
                createdBy: 'superuser',
            });
            await RolesModel.create({
                _id: mockRoleId,
                permissions: [managerId],
                name: 'Manager role',
                description: 'Manager description',
                createdAt: new Date('2024-03-01'),
                status: 'ACTIVE',
                slug: 'm-a-n-a-g-e-r',
                createdBy: 'superuser',
            });
        });

        afterEach(async () => {
            await GroupsModel.deleteOne({ _id: mockGroupId });
            await RolesModel.deleteOne({ _id: mockRoleId });
        });

        it('Should update group successfully', async () => {
            const mockParams = {
                groupId: mockGroupId,
                status: 'ACTIVE',
                name: 'updated',
                description: 'updated',
            };
            await updateGroupInformation(mockParams);

            const group = await GroupsModel.findOne({ _id: mockGroupId });
            expect(group?.name).toEqual('updated');
        });
    });
    describe('addMultiUserToGroup', () => {
        it('Throw error message when group not found', async () => {
            await expect(
                addMultiUserToGroup({
                    userIds: [employeeId],
                    groupId: 'group-id-not-found',
                }),
            ).rejects.toThrow();
        });
        it('Should throw error when user not found', async () => {
            await expect(
                addMultiUserToGroup({
                    userIds: ['user-id-not-found'],
                    groupId: groupId,
                }),
            ).rejects.toThrow();
        });
        it('Should add multi user to group successfully', async () => {
            await addMultiUserToGroup({ userIds: [userId2], groupId });

            const groupFound = await GroupsModel.findById(groupId);
            const userFound = groupFound?.users?.find(
                user => user._id === userId2,
            );

            expect(userFound?._id).toEqual(userId2);
            expect(userFound?.addedAt).toBeDefined();
            expect(userFound?.status).toEqual(statusOriginal.ACTIVE);
        });
    });
    describe('getTotalMemberAddingToGroup', () => {
        const MOCK_TOTAL = 4;

        it('Should get total member adding to ground successfully', async () => {
            const total = await getTotalMemberAddingToGroup({
                isoCode: 'VN',
                filter: {},
                groupId,
            });

            expect(total).toEqual(MOCK_TOTAL);
        });
    });
    describe('getListMemberAddingToGroup', () => {
        const MOCK_TOTAL = 4;

        it('Should get list member adding to group successfully', async () => {
            const listMember = await getListMemberAddingToGroup({
                isoCode: 'VN',
                filter: {},
                groupId,
                skip: 0,
                limit: 10,
            });

            const leaderUserFound = listMember.find(
                member => member._id === rootId,
            );

            expect(listMember).toHaveLength(MOCK_TOTAL);
            expect(leaderUserFound).toBeUndefined();
        });
    });
});
