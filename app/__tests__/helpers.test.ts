import { IsoCode } from 'btaskee-constants';
import { momentTz } from 'btaskee-utils';
import { getModels } from 'schemas';
import { getAllLevelsOfJourney } from '~/services/helpers.server';

describe('Helper function', () => {
    describe('getAllLevelsOfJourney', () => {
        const isoCode = IsoCode.VN;
        const mockJourneySetting = {
            _id: 'x64f00cc8287ab0d3c9e341dd',
            status: 'ACTIVE',
            createdAt: momentTz().toDate(),
            levels: [
                {
                    name: 'LV1',
                    title: {
                        vi: 'Cấp 1',
                        en: 'Level 1',
                        th: 'Level 1',
                        id: 'Level 1',
                    },
                    text: {
                        vi: 'Ong Non',
                        en: 'Young Bee',
                        th: 'Young Bee',
                        id: 'Young Bee',
                    },
                },
                {
                    name: 'LV2',
                    title: {
                        vi: 'Cấp 2',
                        en: 'Level 2',
                        th: 'Level 2',
                        id: 'Level 2',
                    },
                    text: {
                        vi: 'Ong Trưởng Thành',
                        en: 'Mature Bee',
                        th: 'Mature Bee',
                        id: 'Mature Bee',
                    },
                },
                {
                    name: 'LV3',
                    title: {
                        vi: 'Cấp 3',
                        en: 'Level 3',
                        th: 'Level 3',
                        id: 'Level 3',
                    },
                    text: {
                        vi: 'Ong Thợ',
                        en: 'Worker Bee',
                        th: 'Worker Bee',
                        id: 'Worker Bee',
                    },
                    reward: [
                        {
                            name: 'BPOINT',
                            text: {
                                vi: 'Tăng **20%** khi tích điểm bPoint',
                                en: 'Increase by **20%** when accumulating bPoints',
                                th: 'Increase by **20%** when accumulating bPoints',
                                id: 'Increase by **20%** when accumulating bPoints',
                            },
                            point: {
                                percentage: 0.2,
                            },
                        },
                    ],
                },
                {
                    name: 'LV4',
                    title: {
                        vi: 'Cấp 4',
                        en: 'Level 4',
                        th: 'Level 4',
                        id: 'Level 4',
                    },
                    text: {
                        vi: 'Ong Chiến Binh',
                        en: 'Warrior Bee',
                        th: 'Warrior Bee',
                        id: 'Warrior Bee',
                    },
                    reward: [
                        {
                            name: 'BPOINT',
                            text: {
                                vi: 'Tăng **30%** khi tích điểm bPoint',
                                en: 'Increase by **30%** when accumulating bPoints',
                                th: 'Increase by **30%** when accumulating bPoints',
                                id: 'Increase by **30%** when accumulating bPoints',
                            },
                            point: {
                                percentage: 0.3,
                            },
                        },
                    ],
                },
                {
                    name: 'LV5',
                    title: {
                        vi: 'Cấp 5',
                        en: 'Level 5',
                        th: 'Level 5',
                        id: 'Level 5',
                    },
                    text: {
                        vi: 'Ong Chúa',
                        en: 'Queen Bee',
                        th: 'Queen Bee',
                        id: 'Queen Bee',
                    },
                },
            ],
            cityName: 'Hồ Chí Minh',
        };

        beforeEach(async () => {
            await getModels(isoCode).journeySetting.insertMany([
                mockJourneySetting,
            ]);
        });

        afterEach(async () => {
            await getModels(isoCode).journeySetting.deleteMany();
        });

        it('Should return correct data', async () => {
            const allLevelsOfJourney = await getAllLevelsOfJourney({ isoCode });

            expect(allLevelsOfJourney).toHaveLength(
                mockJourneySetting.levels.length,
            );
        });
    });
});
