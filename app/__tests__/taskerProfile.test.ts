import {
    BTASKEE_LANGUAGE,
    TASKER_ONBOARDING_IMAGE_KEY,
    TASKER_ONBOARDING_PROCESS_STATUS,
} from 'btaskee-constants';
import { type PipelineStage } from 'mongo-connection';
import { EnumIsoCode, getModels } from 'schemas';
import type {
    GettingProfileProps,
    GettingProfileWithPagination,
} from '~/services/tasker-profile.server';
import {
    getAllReasonFromSetting,
    getEmployeeProfile,
    getEmployeeProfileDetail,
    getTaskerProfile,
    getTaskerProfileDetail,
    getTotalEmployeeProfile,
    getTotalTaskerProfile,
    rejectEmployeeProfile,
    resetImageStatusInTaskerOrSupplierProfile,
    storeNoteInStaffProfile,
    storeNoteIntoProfile,
    updateNoteByIndex,
    updateNoteInStaffProfileByIndex,
    updateScheduleInfoInTaskerOrSupplierProfile,
    updateStatusOnEmployeeProfileByAPI,
    updateStatusOnTaskerProfile,
} from '~/services/tasker-profile.server';

jest.mock('~/services/utils.server', () => ({
    __esModule: true,
    statusOriginal: {
        ACTIVE: 'ACTIVE',
        REMOVED: 'REMOVED',
    },
    fetchAPI: () => new Promise(resolve => resolve(true)),
    sendNotification: () => new Promise(resolve => resolve(true)),
    getImageStatusKeyByImageFieldInTaskerOnboarding: () => 'portraitStatus',
}));

const mockMomentDate = new Date('2024-01-01');

jest.mock('btaskee-utils', () => ({
    __esModule: true,
    momentTz: () => {
        return { toDate: () => mockMomentDate };
    },
}));

function mockResponseThrowError() {
    const errorText = 'response return error message';

    const spy = jest
        .spyOn(global, 'Response')
        .mockImplementation(() => new Error(errorText) as MustBeAny);

    function restore() {
        spy.mockRestore();
    }
    return { restore, errorText };
}

describe('Tasker profile', () => {
    const isoCode = EnumIsoCode.VN;
    const mockTaskerId1 = 'mockTaskerId1';
    const mockTaskerId2 = 'mockTaskerId2';
    const mockTaskerId3 = 'mockTaskerId3';

    const mockTaskerName1 = 'tasker name 1';
    const mockTaskerName2 = 'tasker name 2';

    const mockProfileId1 = 'mockProfileId1';
    const mockProfileId2 = 'mockProfileId2';
    const mockProfileId3 = 'mockProfileId3';
    const mockProfileId4 = 'mockProfileId4';
    const mockProfileId5 = 'mockProfileId5';
    const mockProfileId6 = 'mockProfileId6';
    const mockProfileId7 = 'mockProfileId7';

    const mockServiceChannelId1 = 'mockServiceChannelId1';
    const mockServiceChannelId2 = 'mockServiceChannelId2';

    const mockServiceId1 = 'mockServiceId1';
    const mockServiceId2 = 'mockServiceId2';

    const employeeStatuses = [
        TASKER_ONBOARDING_PROCESS_STATUS.VERIFYING,
        TASKER_ONBOARDING_PROCESS_STATUS.APPROVED,
        TASKER_ONBOARDING_PROCESS_STATUS.REJECTED,
        TASKER_ONBOARDING_PROCESS_STATUS.ELIMINATED,
    ];

    const employeeProfileIds = [
        'employeeProfileId1',
        'employeeProfileId2',
        'employeeProfileId3',
        'employeeProfileId4',
    ];
    const companyIds = [
        'employeeProfileId1',
        'employeeProfileId2',
        'employeeProfileId3',
        'employeeProfileId4',
    ];
    const mockWorkingPlaces = [
        {
            city: 'Hồ Chí Minh',
        },
    ];

    const employeeProfiles = employeeProfileIds.map(
        (employeeProfileId, index) => ({
            _id: employeeProfileId,
            idNumber: `${employeeProfileId}_idNumber`,
            name: `${employeeProfileId}_name`,
            phone: `${employeeProfileId}_phone`,
            gender: `${employeeProfileId}_gender`,
            identityCard: {
                images: [
                    `${employeeProfileId}_identityCard_image1`,
                    `${employeeProfileId}_identityCard_image2`,
                ],
                status: TASKER_ONBOARDING_PROCESS_STATUS.REJECTED,
            },
            portrait: {
                images: [
                    `${employeeProfileId}_portrait_image1`,
                    `${employeeProfileId}_portrait_image2`,
                ],
            },
            dob: new Date('2023-10-01'),
            companyId: companyIds[index],
            status: employeeStatuses[index],
            createdAt: new Date('2023-10-01'),
        }),
    );

    const userCompanies = companyIds.map(companyId => ({
        _id: companyId,
        isoCode: isoCode,
        status: 'ACTIVE',
        name: `${companyId}_name`,
        username: `${companyId}_username`,
        fAccountId: `${companyId}_fAccountId`,
        language: 'vi',
        createdAt: new Date('2023-10-01'),
    }));

    const taskerProfiles = [
        {
            _id: mockProfileId1,
            createdAt: new Date('2024-04-15'),
            taskerId: mockTaskerId1,
            taskerName: 'tasker name 1',
            taskerPhone: '091xxxx',
            taskerIdNumber: '1111',
            confirmationConduct: {
                images: ['image1', 'image2'],
            },
            identityCard: {
                message: ['image 3', 'image 4'],
            },
            appointmentInfo: {
                address: 'Quan 2, Ho Chi Minh',
                city: 'Ho Chi Minh',
                name: 'Van phone quan 2',
                phoneNumber: '09111xx',
                date: new Date('2024-04-15'),
            },
            processStatus: TASKER_ONBOARDING_PROCESS_STATUS.APPROVED,
            status: TASKER_ONBOARDING_PROCESS_STATUS.APPROVED,
        },
        {
            _id: mockProfileId2,
            createdAt: new Date('2024-04-10'),
            taskerId: mockTaskerId2,
            taskerName: 'tasker name 2',
            taskerPhone: '092xxxx',
            taskerIdNumber: '2222',
            confirmationConduct: {
                images: ['image1', 'image2'],
            },
            appointmentInfo: {
                address: 'Quan 2, Ho Chi Minh',
                city: 'Ho Chi Minh',
                name: 'Van phone quan 2',
                phoneNumber: '09111xx',
                date: new Date('2024-04-15'),
            },
            processStatus: TASKER_ONBOARDING_PROCESS_STATUS.APPROVED,
            status: TASKER_ONBOARDING_PROCESS_STATUS.APPROVED,
        },
        {
            _id: mockProfileId3,
            taskerId: mockTaskerId3,
            taskerPhone: '093xxxx',
            taskerName: 'tasker name 3',
            processStatus: TASKER_ONBOARDING_PROCESS_STATUS.APPROVED,
            status: TASKER_ONBOARDING_PROCESS_STATUS.APPROVED,
            appointmentInfo: {
                address: 'Quan 2, Ho Chi Minh',
                city: 'Ho Chi Minh',
                name: 'Van phone quan 2',
                phoneNumber: '09111xx',
                date: new Date('2024-04-15'),
            },
            taskerIdNumber: '3333',
            createdAt: new Date('2024-04-05'),
        },
        {
            _id: mockProfileId4,
            taskerId: mockTaskerId3,
            taskerPhone: '093xxxx',
            taskerName: 'tasker name 3',
            processStatus: TASKER_ONBOARDING_PROCESS_STATUS.RESTORED,
            appointmentInfo: {
                address: 'Quan 2, Ho Chi Minh',
                city: 'Ho Chi Minh',
                name: 'Van phone quan 2',
                phoneNumber: '09111xx',
                date: new Date('2024-04-15'),
            },
            taskerIdNumber: '3333',
            createdAt: new Date('2024-04-05'),
        },
        {
            _id: mockProfileId5,
            taskerId: mockTaskerId3,
            taskerPhone: '093xxxx',
            taskerName: 'tasker name 3',
            isPartner: true,
            processStatus: TASKER_ONBOARDING_PROCESS_STATUS.REJECTED,
            status: TASKER_ONBOARDING_PROCESS_STATUS.REJECTED,
            appointmentInfo: {
                address: 'Quan 2, Ho Chi Minh',
                city: 'Ho Chi Minh',
                name: 'Van phone quan 2',
                phoneNumber: '09111xx',
                date: new Date('2024-04-05'),
            },
            taskerIdNumber: '3333',
            createdAt: new Date('2024-06-05'),
        },
        {
            _id: mockProfileId6,
            taskerId: mockTaskerId3,
            taskerPhone: '093xxxx',
            taskerName: 'tasker name 3',
            isPartner: true,
            appointmentInfo: {
                address: 'Quan 2, Ho Chi Minh',
                city: 'Ho Chi Minh',
                name: 'Van phone quan 2',
                phoneNumber: '09111xx',
                date: new Date('2024-04-05'),
            },
            taskerIdNumber: '3333',
            createdAt: new Date('2024-06-05'),
        },
        {
            _id: mockProfileId7,
            taskerId: mockTaskerId3,
            taskerPhone: '093xxxx',
            taskerName: 'tasker name 3',
            isPartner: true,
            appointmentInfo: {
                address: 'Quan 2, Ho Chi Minh',
                city: 'Ho Chi Minh',
                name: 'Van phone quan 2',
                phoneNumber: '09111xx',
                date: new Date('2024-04-05'),
            },
            taskerIdNumber: '3333',
            createdAt: new Date('2024-06-05'),
        },
    ];

    const taskers = [
        {
            _id: mockTaskerId1,
            isoCode: 'VN',
            status: 'ACTIVE',
            name: mockTaskerName1,
            username: 'tasker name 1',
            fAccountId: 'fAccountId1',
            workingPlaces: mockWorkingPlaces,
            language: 'vi',
            createdAt: new Date('2023-10-01'),
        },
        {
            _id: mockTaskerId2,
            isoCode: 'VN',
            status: 'ACTIVE',
            name: mockTaskerName2,
            workingPlaces: mockWorkingPlaces,
            username: 'username 2',
            fAccountId: 'fAccountId2',
            language: 'vi',
            createdAt: new Date('2023-10-01'),
        },
        {
            _id: mockTaskerId3,
            isoCode: 'VN',
            status: 'ACTIVE',
            name: mockTaskerName2,
            workingPlaces: mockWorkingPlaces,
            username: 'username 3',
            fAccountId: 'fAccountId3',
            language: 'vi',
            createdAt: new Date('2023-10-01'),
        },
    ];

    const servicesChannels = [
        {
            _id: mockServiceChannelId1,
            serviceId: mockServiceId1,
            taskerList: [mockTaskerId1, mockTaskerId2, mockTaskerId3],
        },
        {
            _id: mockServiceChannelId2,
            serviceId: mockServiceId2,
            taskerList: [mockTaskerId1],
        },
    ];

    const services = [
        {
            _id: mockServiceId1,
            name: 'CLEANING',
            icon: 'icon1.com',
            status: 'ACTIVE',
            text: {
                vi: 'cleaning',
                en: 'cleaning',
                ko: 'cleaning',
                th: 'cleaning',
                id: 'cleaning',
            },
        },
        {
            _id: mockServiceId2,
            name: 'LAUNDRY',
            icon: 'icon1.com',
            status: 'ACTIVE',
            text: {
                vi: 'laundry',
                en: 'laundry',
                ko: 'laundry',
                th: 'laundry',
                id: 'laundry',
            },
        },
    ];

    beforeAll(async () => {
        await getModels(isoCode).taskerProfile.insertMany(taskerProfiles);
        await getModels(isoCode).serviceChannel.insertMany(servicesChannels);
        await getModels(isoCode).service.insertMany(services);
        await getModels(isoCode).users.insertMany(taskers);
        await getModels(isoCode).employeeProfile.insertMany(employeeProfiles);
        await getModels(isoCode).users.insertMany(userCompanies);
    });

    afterAll(async () => {
        await getModels(isoCode).users.deleteMany({
            _id: { $in: userCompanies.map(user => user._id) },
        });
        await getModels(isoCode).employeeProfile.deleteMany({
            _id: { $in: employeeProfiles.map(profile => profile._id) },
        });
        await getModels(isoCode).taskerProfile.deleteMany({
            _id: { $in: taskerProfiles.map(profile => profile._id) },
        });
        await getModels(isoCode).serviceChannel.deleteMany({
            _id: {
                $in: servicesChannels.map(serviceChannel => serviceChannel._id),
            },
        });
        await getModels(isoCode).service.deleteMany({
            _id: { $in: services.map(service => service._id) },
        });
        await getModels(isoCode).users.deleteMany({
            _id: { $in: taskers.map(tasker => tasker._id) },
        });
    });

    function getOverallStatusInProfileToServer({
        processStatus,
        status,
        isHaveAppointmentInfo,
    }: {
        processStatus?: TaskerProfile['processStatus'];
        status?: TaskerProfile['status'];
        isHaveAppointmentInfo: boolean;
    }) {
        if (!status && !processStatus) {
            return TASKER_ONBOARDING_PROCESS_STATUS.VERIFYING;
        }

        if (status === TASKER_ONBOARDING_PROCESS_STATUS.UPDATED) {
            return TASKER_ONBOARDING_PROCESS_STATUS.UPDATED;
        }

        if (
            status === TASKER_ONBOARDING_PROCESS_STATUS.APPROVED &&
            isHaveAppointmentInfo &&
            (!processStatus ||
                processStatus === TASKER_ONBOARDING_PROCESS_STATUS.APPROVED)
        ) {
            return TASKER_ONBOARDING_PROCESS_STATUS.SCHEDULED;
        }

        return processStatus || status;
    }

    describe('getTotalTaskerProfile', () => {
        const getExpectationProfiles = (
            params: Omit<GettingProfileProps, 'isoCode'>,
        ) =>
            taskerProfiles.filter(
                profile =>
                    params.status.includes(
                        // eslint-disable-next-line @typescript-eslint/ban-ts-comment
                        // @ts-ignore
                        getOverallStatusInProfileToServer({
                            status: profile.status,
                            processStatus: profile.processStatus,
                            isHaveAppointmentInfo: !!profile.appointmentInfo,
                        }),
                    ) &&
                    profile.createdAt >= params.createdAt.from &&
                    profile.createdAt <= params.createdAt.to &&
                    (params.isPartner
                        ? profile.isPartner
                        : !profile.isPartner) &&
                    (params.searchText
                        ? new RegExp(params.searchText, 'i').test(
                              profile.taskerName,
                          ) ||
                          new RegExp(params.searchText, 'i').test(
                              profile.taskerPhone,
                          )
                        : true),
            );
        it('Should get total tasker profile with entire filtering successfully', async () => {
            const params = {
                searchText: '',
                status: [TASKER_ONBOARDING_PROCESS_STATUS.SCHEDULED],
                createdAt: {
                    from: new Date('2024-04-01'),
                    to: new Date('2024-09-01'),
                },
                cities: mockWorkingPlaces.map(
                    workingPlace => workingPlace.city,
                ),
                isoCode: isoCode,
                isPartner: false,
            };
            const total = await getTotalTaskerProfile(params);

            expect(total).toEqual(getExpectationProfiles(params).length);
        });
        it('Should get total by searching tasker name of profile', async () => {
            const params = {
                searchText: mockTaskerName2,
                status: [TASKER_ONBOARDING_PROCESS_STATUS.SCHEDULED],
                createdAt: {
                    from: new Date('2024-04-01'),
                    to: new Date('2024-09-01'),
                },
                cities: mockWorkingPlaces.map(
                    workingPlace => workingPlace.city,
                ),
                isoCode: isoCode,
            };
            const total = await getTotalTaskerProfile(params);
            const MOCK_TOTAL_SEARCHING_BY_TASKER_NAME_ON_PROFILE = 2;

            expect(total).toEqual(
                MOCK_TOTAL_SEARCHING_BY_TASKER_NAME_ON_PROFILE,
            );
        });
        it('Should get total partner profile successfully', async () => {
            const params = {
                searchText: '',
                status: [TASKER_ONBOARDING_PROCESS_STATUS.REJECTED],
                isPartner: true,
                createdAt: {
                    from: new Date('2024-04-01'),
                    to: new Date('2024-09-01'),
                },
                cities: mockWorkingPlaces.map(
                    workingPlace => workingPlace.city,
                ),
                isoCode: isoCode,
            };
            const total = await getTotalTaskerProfile(params);

            expect(total).toEqual(getExpectationProfiles(params).length);
        });
        it('Should get total verifying partner profile successfully', async () => {
            const params = {
                searchText: '',
                status: [TASKER_ONBOARDING_PROCESS_STATUS.VERIFYING],
                isPartner: true,
                createdAt: {
                    from: new Date('2024-04-01'),
                    to: new Date('2024-09-01'),
                },
                cities: mockWorkingPlaces.map(
                    workingPlace => workingPlace.city,
                ),
                isoCode: isoCode,
            };
            const total = await getTotalTaskerProfile(params);

            expect(total).toEqual(getExpectationProfiles(params).length);
        });
    });
    describe('getTaskerProfileDetail', () => {
        it('Should get tasker profile detail successfully', async () => {
            const profileDetail = await getTaskerProfileDetail({
                isoCode,
                cities: mockWorkingPlaces.map(
                    workingPlace => workingPlace.city,
                ),
                profileId: mockProfileId2,
            });

            expect(profileDetail.data._id).toBeDefined();
        });
        it('Should throw an error if profile not found', async () => {
            const { errorText, restore } = mockResponseThrowError();

            await expect(
                getTaskerProfileDetail({
                    profileId: 'nonexistentGroupId',
                    cities: mockWorkingPlaces.map(
                        workingPlace => workingPlace.city,
                    ),
                    isoCode,
                }),
            ).rejects.toThrow(errorText);
            restore();
        });
    });
    describe('updateStatusOnTaskerProfile', () => {
        const getUpDatedStatus = ({
            status,
        }: {
            status: `${TASKER_ONBOARDING_PROCESS_STATUS}`;
        }) => {
            if (status === TASKER_ONBOARDING_PROCESS_STATUS.ACTIVE) {
                return {
                    status: TASKER_ONBOARDING_PROCESS_STATUS.APPROVED,
                    processStatus: TASKER_ONBOARDING_PROCESS_STATUS.ACTIVE,
                };
            }

            if (status === TASKER_ONBOARDING_PROCESS_STATUS.RESCHEDULE) {
                return {
                    status: TASKER_ONBOARDING_PROCESS_STATUS.APPROVED,
                    processStatus: TASKER_ONBOARDING_PROCESS_STATUS.RESCHEDULE,
                };
            }

            if (status === TASKER_ONBOARDING_PROCESS_STATUS.FAIL_CALLING) {
                return {
                    status: TASKER_ONBOARDING_PROCESS_STATUS.REJECTED,
                    processStatus:
                        TASKER_ONBOARDING_PROCESS_STATUS.FAIL_CALLING,
                };
            }

            if (status === TASKER_ONBOARDING_PROCESS_STATUS.FAIL_INTERVIEW) {
                return {
                    status: TASKER_ONBOARDING_PROCESS_STATUS.REJECTED,
                    processStatus:
                        TASKER_ONBOARDING_PROCESS_STATUS.FAIL_INTERVIEW,
                };
            }

            if (status === TASKER_ONBOARDING_PROCESS_STATUS.RESTORED) {
                return {
                    processStatus: TASKER_ONBOARDING_PROCESS_STATUS.RESTORED,
                };
            }

            return {
                status,
                processStatus: status,
            };
        };
        it('Should throw error message when profile not found', async () => {
            const { restore, errorText } = mockResponseThrowError();
            const params = {
                profileId: 'profileIdNotFound',
                isoCode,
                status: TASKER_ONBOARDING_PROCESS_STATUS.ACTIVE,
                userId: 'accountId',
                language: BTASKEE_LANGUAGE.VI,
                username: 'username',
            };

            await expect(updateStatusOnTaskerProfile(params)).rejects.toThrow(
                errorText,
            );
            restore();
        });
        it('Should update tasker profile status successfully', async () => {
            const params = {
                profileId: mockProfileId4,
                isoCode,
                status: TASKER_ONBOARDING_PROCESS_STATUS.ACTIVE,
                userId: 'accountId',
                language: BTASKEE_LANGUAGE.VI,
                reason: 'reason 1',
                username: 'username',
                message: {
                    title: {
                        vi: 'active profile title',
                        en: 'active profile title',
                        ko: 'active profile title',
                        th: 'active profile title',
                        id: 'active profile title',
                    },
                    body: {
                        vi: 'active profile body',
                        en: 'active profile body',
                        ko: 'active profile body',
                        th: 'active profile body',
                        id: 'active profile body',
                    },
                },
            };

            await updateStatusOnTaskerProfile(params);

            const profileFound = await getModels(isoCode).taskerProfile.findOne(
                {
                    _id: mockProfileId4,
                },
            );

            expect(profileFound?.status).toEqual(
                getUpDatedStatus({ status: params.status }).status,
            );
            expect(profileFound?.processStatus).toEqual(
                getUpDatedStatus({ status: params.status }).processStatus,
            );
            expect(profileFound?.actionHistories?.[0]).toBeDefined();
        });
        it('Should update image status of tasker profile successfully', async () => {
            const params = {
                profileId: mockProfileId2,
                isoCode,
                status: TASKER_ONBOARDING_PROCESS_STATUS.REJECTED,
                userId: 'accountId',
                language: BTASKEE_LANGUAGE.VI,
                fieldName: TASKER_ONBOARDING_IMAGE_KEY.IDENTITY_CARD,
                reason: 'reason 1',
                username: 'username',
            };

            await updateStatusOnTaskerProfile(params);

            const profileFound = await getModels(isoCode).taskerProfile.findOne(
                {
                    _id: mockProfileId2,
                },
            );

            expect(
                profileFound?.identityCard?.actionHistories?.[0].reason,
            ).toStrictEqual(params.reason);
        });
    });

    describe('getTaskerProfile', () => {
        const getExpectationProfiles = (
            params: Omit<GettingProfileWithPagination, 'isoCode'>,
        ) => {
            const filteredProfiles = taskerProfiles.filter(
                profile =>
                    params.status.includes(
                        // eslint-disable-next-line @typescript-eslint/ban-ts-comment
                        // @ts-ignore
                        getOverallStatusInProfileToServer({
                            status: profile.status,
                            processStatus: profile.processStatus,
                            isHaveAppointmentInfo: !!profile.appointmentInfo,
                        }),
                    ) &&
                    profile.createdAt >= params.createdAt.from &&
                    profile.createdAt <= params.createdAt.to &&
                    (params.isPartner
                        ? profile.isPartner
                        : !profile.isPartner) &&
                    (params.searchText
                        ? new RegExp(params.searchText, 'i').test(
                              profile.taskerName,
                          ) ||
                          new RegExp(params.searchText, 'i').test(
                              profile.taskerPhone,
                          )
                        : true),
            );

            const sortedProfiles = filteredProfiles.reduce(
                (acc: MustBeAny, profile) => {
                    const index = acc.findIndex(
                        (item: MustBeAny) =>
                            new Date(item.createdAt) >
                            new Date(profile.createdAt),
                    );

                    if (index === -1) {
                        acc.push(profile);
                    } else {
                        acc.splice(index, 0, profile);
                    }

                    return acc;
                },
                [],
            );

            const skippedAndLimitedProfiles = sortedProfiles.slice(
                params.skip,
                params.skip + params.limit,
            );
            return skippedAndLimitedProfiles;
        };

        it('Should get tasker profile successfully', async () => {
            const params = {
                status: [
                    TASKER_ONBOARDING_PROCESS_STATUS.APPROVED,
                    TASKER_ONBOARDING_PROCESS_STATUS.SCHEDULED,
                ],
                createdAt: {
                    from: new Date('2023-01-01'),
                    to: new Date('2024-12-12'),
                },
                searchText: '',
                sort: {
                    createdAt: -1,
                } as PipelineStage.Sort['$sort'],
                skip: 0,
                cities: mockWorkingPlaces.map(
                    workingPlace => workingPlace.city,
                ),
                limit: 10,
                isoCode: isoCode,
            };

            const taskerProfiles = await getTaskerProfile(params);
            expect(taskerProfiles).toHaveLength(
                getExpectationProfiles(params).length,
            );

            getExpectationProfiles(params).forEach((profile: MustBeAny) => {
                expect(
                    taskerProfiles.find(
                        taskerProfile => taskerProfile._id === profile._id,
                    ),
                ).toBeDefined();
            });
        });
        it('Should get partner profile successfully', async () => {
            const params = {
                status: [TASKER_ONBOARDING_PROCESS_STATUS.APPROVED],
                createdAt: {
                    from: new Date('2023-01-01'),
                    to: new Date('2024-12-12'),
                },
                sort: {
                    createdAt: -1,
                } as PipelineStage.Sort['$sort'],
                searchText: '',
                isPartner: true,
                skip: 0,
                limit: 10,
                cities: mockWorkingPlaces.map(
                    workingPlace => workingPlace.city,
                ),
                isoCode: isoCode,
            };

            const partnerProfiles = await getTaskerProfile(params);

            expect(partnerProfiles).toHaveLength(
                getExpectationProfiles(params).length,
            );

            getExpectationProfiles(params).forEach((profile: MustBeAny) => {
                expect(
                    partnerProfiles.find(
                        partnerProfile => partnerProfile._id === profile._id,
                    ),
                ).toBeDefined();
            });
        });
    });
    describe('getEmployeeProfileDetail', () => {
        it('Should throw error message when profile not found', async () => {
            const { restore, errorText } = mockResponseThrowError();
            await expect(
                getEmployeeProfileDetail({
                    isoCode,
                    employeeProfileId: 'employeeProfileId_not_found',
                }),
            ).rejects.toThrow(errorText);
            restore();
        });
        it('Should get employee profile detail successfully', async () => {
            const params = {
                employeeProfileId: employeeProfileIds[2],
                isoCode,
            };

            const employeeProfile = await getEmployeeProfileDetail(params);

            const expectationProfile = employeeProfiles.find(
                profile => profile._id === params.employeeProfileId,
            );

            expect(employeeProfile.data._id).toEqual(expectationProfile?._id);
        });
    });
    describe('updateStatusOnEmployeeProfileByAPI', () => {
        it('Should throw error message when profile not found', async () => {
            const { restore, errorText } = mockResponseThrowError();
            const params = {
                profileId: 'profileId_not_found',
                status: TASKER_ONBOARDING_PROCESS_STATUS.APPROVED,
                userId: 'userId',
                fieldName: TASKER_ONBOARDING_IMAGE_KEY.IDENTITY_CARD,
                isoCode,
                username: 'username',
                reason: 'reason',
            };

            await expect(
                updateStatusOnEmployeeProfileByAPI(params),
            ).rejects.toThrow(errorText);
            restore();
        });
        it('Should update employee profile status successfully', async () => {
            const params = {
                profileId: employeeProfileIds[1],
                status: TASKER_ONBOARDING_PROCESS_STATUS.APPROVED,
                userId: 'userId',
                fieldName: TASKER_ONBOARDING_IMAGE_KEY.IDENTITY_CARD,
                isoCode,
                username: 'username',
                reason: 'reason',
            };

            await updateStatusOnEmployeeProfileByAPI(params);

            const employeeProfileFound = await getModels(
                isoCode,
            ).employeeProfile.findById(params.profileId);

            expect(employeeProfileFound?.actionHistories).toBeDefined();
            expect(
                employeeProfileFound?.actionHistories?.length,
            ).toBeGreaterThan(0);
        });
    });
    describe('getTotalEmployeeProfile', () => {
        const getExpectationProfiles = (
            params: Omit<GettingProfileProps, 'isPartner'>,
        ) =>
            employeeProfiles.filter(
                employeeProfile =>
                    params.status.includes(employeeProfile.status) &&
                    employeeProfile.createdAt >= params.createdAt.from &&
                    employeeProfile.createdAt <= params.createdAt.to &&
                    (params.searchText
                        ? new RegExp(params.searchText, 'i').test(
                              employeeProfile.name,
                          ) ||
                          new RegExp(params.searchText, 'i').test(
                              employeeProfile.phone,
                          )
                        : true),
            );
        it('Should get total employee profile successfully', async () => {
            const params = {
                searchText: '',
                createdAt: {
                    from: new Date('2020-01-01'),
                    to: new Date('2024-01-01'),
                },
                status: [
                    TASKER_ONBOARDING_PROCESS_STATUS.VERIFYING,
                    TASKER_ONBOARDING_PROCESS_STATUS.APPROVED,
                ],
                cities: mockWorkingPlaces.map(
                    workingPlace => workingPlace.city,
                ),
                isoCode,
            };

            const total = await getTotalEmployeeProfile(params);

            expect(total).toEqual(getExpectationProfiles(params).length);
        });
    });
    describe('getEmployeeProfile', () => {
        const getExpectationProfiles = (
            params: Omit<GettingProfileWithPagination, 'isoCode'>,
        ) => {
            const filteredProfiles = employeeProfiles.filter(
                employeeProfile =>
                    params.status.includes(employeeProfile.status) &&
                    employeeProfile.createdAt >= params.createdAt.from &&
                    employeeProfile.createdAt <= params.createdAt.to &&
                    (params.searchText
                        ? new RegExp(params.searchText, 'i').test(
                              employeeProfile.name,
                          ) ||
                          new RegExp(params.searchText, 'i').test(
                              employeeProfile.phone,
                          )
                        : true),
            );

            const sortedProfiles = filteredProfiles.reduce(
                (acc: MustBeAny, profile) => {
                    const index = acc.findIndex(
                        (item: MustBeAny) =>
                            new Date(item.createdAt) >
                            new Date(profile.createdAt),
                    );

                    if (index === -1) {
                        acc.push(profile);
                    } else {
                        acc.splice(index, 0, profile);
                    }

                    return acc;
                },
                [],
            );

            const skippedAndLimitedProfiles = sortedProfiles.slice(
                params.skip,
                params.skip + params.limit,
            );
            return skippedAndLimitedProfiles;
        };
        it('Should get list employee profile successfully', async () => {
            const params = {
                searchText: '',
                createdAt: {
                    from: new Date('2020-01-01'),
                    to: new Date('2024-01-01'),
                },
                status: [
                    TASKER_ONBOARDING_PROCESS_STATUS.VERIFYING,
                    TASKER_ONBOARDING_PROCESS_STATUS.APPROVED,
                ],
                isoCode,
                cities: mockWorkingPlaces.map(
                    workingPlace => workingPlace.city,
                ),
                sort: {
                    createdAt: -1,
                } as PipelineStage.Sort['$sort'],
                skip: 0,
                limit: 10,
            };
            const employeeProfiles = await getEmployeeProfile(params);

            const expectationProfiles = getExpectationProfiles(params);

            expect(employeeProfiles.length).toEqual(expectationProfiles.length);
            employeeProfiles.forEach(employeeProfile => {
                expect(
                    expectationProfiles.find(
                        (profile: MustBeAny) =>
                            profile._id === employeeProfile._id,
                    ),
                ).toBeDefined();
            });
        });
    });
    describe('rejectEmployeeProfile', () => {
        it('Should throw error message when employee profile not found', async () => {
            const { errorText, restore } = mockResponseThrowError();
            const params = {
                profileId: 'profileId-not-found',
                userId: 'userId',
                isoCode,
                reason: 'reason',
                username: 'username',
            };
            await expect(rejectEmployeeProfile(params)).rejects.toThrow(
                errorText,
            );
            restore();
        });

        it('Should reject employee profile successfully', async () => {
            const params = {
                profileId: employeeProfileIds[0],
                userId: 'userId',
                isoCode,
                reason: 'reason',
                username: 'username',
            };

            await rejectEmployeeProfile(params);

            const profileFound = await getModels(
                isoCode,
            ).employeeProfile.findById(params.profileId);

            expect(profileFound?.status).toEqual(
                TASKER_ONBOARDING_PROCESS_STATUS.REJECTED,
            );
            expect(profileFound?.actionHistories).toBeDefined();
            expect(profileFound?.actionHistories?.length).toBeGreaterThan(0);
        });
    });
    describe('storeNoteIntoProfile', () => {
        it('Should throw error message when employee profile not found', async () => {
            const { errorText, restore } = mockResponseThrowError();
            const params = {
                profileId: 'profileId-not-found',
                userId: 'userId',
                isoCode,
                note: 'note',
                username: 'username',
            };
            await expect(storeNoteIntoProfile(params)).rejects.toThrow(
                errorText,
            );
            restore();
        });
        it('Should store note in profile successfully', async () => {
            const params = {
                profileId: taskerProfiles[1]._id,
                userId: 'userId',
                isoCode,
                note: 'note',
                username: 'username',
            };

            await storeNoteIntoProfile(params);

            const profileFound = await getModels(
                isoCode,
            ).taskerProfile.findById(params.profileId);

            expect(profileFound?.notes).toBeDefined();
            expect(profileFound?.notes?.[0]?.description).toEqual(params.note);
        });
    });
    describe('updateNoteByIndex', () => {
        it('Should throw error message when employee profile not found', async () => {
            const { errorText, restore } = mockResponseThrowError();
            const params = {
                profileId: 'profileId-not-found',
                userId: 'userId',
                isoCode,
                note: 'updated note',
                username: 'username',
                noteIndex: 0,
            };
            await expect(updateNoteByIndex(params)).rejects.toThrow(errorText);
            restore();
        });
        it('Should store note in profile successfully', async () => {
            const params = {
                profileId: taskerProfiles[1]._id,
                userId: 'userId',
                isoCode,
                note: 'note',
                username: 'username',
                noteIndex: 0,
            };

            await updateNoteByIndex(params);

            const profileFound = await getModels(
                isoCode,
            ).taskerProfile.findById(params.profileId);

            expect(profileFound?.notes).toBeDefined();
            expect(
                profileFound?.notes?.[params.noteIndex]?.description,
            ).toEqual(params.note);
        });
    });
    describe('getAllReasonFromSetting', () => {
        const taskerOnboardingSetting = {
            _id: 'taskerOnboardingSettingId1',
            reasons: [
                {
                    _id: 'reason1',
                    type: TASKER_ONBOARDING_PROCESS_STATUS.REJECTED,
                    name: 'reject reason',
                },
                {
                    _id: 'reason2',
                    type: TASKER_ONBOARDING_PROCESS_STATUS.ELIMINATED,
                    name: 'eliminate reason',
                },
            ],
        };
        beforeEach(async () => {
            await getModels(isoCode).taskerOnboardingSetting.create(
                taskerOnboardingSetting,
            );
        });
        afterEach(async () => {
            await getModels(isoCode).taskerOnboardingSetting.deleteOne({
                _id: taskerOnboardingSetting._id,
            });
        });
        it('Should get all reason from setting successfully', async () => {
            const reasons = await getAllReasonFromSetting({ isoCode });

            expect(reasons).toHaveLength(
                taskerOnboardingSetting.reasons.length,
            );
        });
    });
    describe('resetImageStatusInTaskerOrSupplierProfile', () => {
        it('Should reset image status in tasker profile successfully', async () => {
            const params = {
                isoCode,
                profileId: taskerProfiles[1]._id,
                imageField: TASKER_ONBOARDING_IMAGE_KEY.IDENTITY_CARD,
                userId: 'userId',
                reason: 'restore image',
                username: 'username',
            };
            await resetImageStatusInTaskerOrSupplierProfile(params);

            const profileFound = await getModels(
                isoCode,
            ).taskerProfile.findById(params.profileId);

            expect(profileFound?.identityCard?.status).toBeUndefined();
            expect(
                profileFound?.identityCard?.actionHistories?.find(
                    history => history.reason === params.reason,
                )?.reason,
            ).toBeDefined();
        });
    });
    describe('updateScheduleInfoInTaskerOrSupplierProfile', () => {
        it('Should update schedule info in tasker or supplier profile successfully', async () => {
            const params = {
                isoCode,
                profileId: taskerProfiles[2]._id,
                date: new Date('2024-01-01'),
                officeInfo: {
                    address: 'updated address',
                    name: 'updated name',
                    city: 'updated city',
                    phoneNumber: 'updated phoneNumber',
                },
            };
            await updateScheduleInfoInTaskerOrSupplierProfile(params);

            const profileFound = await getModels(
                isoCode,
            ).taskerProfile.findById(params.profileId);

            expect(profileFound?.appointmentInfo?.address).toEqual(
                params.officeInfo.address,
            );
            expect(profileFound?.appointmentInfo?.name).toEqual(
                params.officeInfo.name,
            );
            expect(profileFound?.appointmentInfo?.city).toEqual(
                params.officeInfo.city,
            );
            expect(profileFound?.appointmentInfo?.phoneNumber).toEqual(
                params.officeInfo.phoneNumber,
            );
            expect(profileFound?.appointmentInfo?.date).toEqual(params.date);
        });
    });
    describe('storeNoteInStaffProfile', () => {
        it('Should store note in profile successfully', async () => {
            const params = {
                profileId: employeeProfileIds[1],
                userId: 'userId',
                isoCode,
                note: 'note',
                username: 'username',
            };

            await storeNoteInStaffProfile(params);

            const profileFound = await getModels(
                isoCode,
            ).employeeProfile.findById(params.profileId);

            expect(profileFound?.notes).toBeDefined();
            expect(profileFound?.notes?.[0]?.description).toEqual(params.note);
        });
    });
    describe('updateNoteInStaffProfileByIndex', () => {
        it('Should throw error message when employee profile not found', async () => {
            const { errorText, restore } = mockResponseThrowError();
            const params = {
                profileId: 'profileId-not-found',
                userId: 'userId',
                isoCode,
                note: 'updated note',
                username: 'username',
                noteIndex: 0,
            };
            await expect(
                updateNoteInStaffProfileByIndex(params),
            ).rejects.toThrow(errorText);
            restore();
        });
        it('Should store note in profile successfully', async () => {
            const params = {
                profileId: employeeProfileIds[1],
                userId: 'userId',
                isoCode,
                note: 'note',
                username: 'username',
                noteIndex: 0,
            };

            await updateNoteInStaffProfileByIndex(params);

            const profileFound = await getModels(
                isoCode,
            ).employeeProfile.findById(params.profileId);

            expect(profileFound?.notes).toBeDefined();
            expect(
                profileFound?.notes?.[params.noteIndex]?.description,
            ).toEqual(params.note);
        });
    });
});
