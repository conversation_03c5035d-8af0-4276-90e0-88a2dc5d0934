import { IsoCode, OFFICE_STATUS_IN_CONFIGURATION } from 'btaskee-constants';
import { momentTz } from 'btaskee-utils';
import { getModels } from 'schemas';
import {
    createOffice,
    deleteOfficeByIndex,
    getListOfficesFromSettingSystem,
    getOfficeDetailByIndexInSetting,
    getTotalOfficesFromSettingSystem,
    updateOffice,
} from '~/services/offices-configuration.server';

describe('Offices Configuration', () => {
    const isoCode = IsoCode.VN;
    const officeNames = [
        'Văn phòng Hà Nội',
        'Văn phòng HCM',
        'Văn phòng Đà Nẵng',
    ];

    const submissionAddresses = officeNames.map(officeName => ({
        name: officeName,
        address: `${officeName}_address`,
        status: OFFICE_STATUS_IN_CONFIGURATION.ACTIVE,
        workingStartDate: momentTz('2024-05-01').toDate(),
        workingEndDate: momentTz('2024-05-05').toDate(),
        city: 'Test City',
        phoneNumber: '1234567890',
        workingDays: [1, 2, 3, 4, 5],
        offDays: [],
        createdAt: momentTz().toDate(),
        createdBy: 'testUser',
    }));

    const settingSystem = {
        _id: 'settingSystemId',
        submitionAddressForTasker: submissionAddresses,
    };

    beforeEach(async () => {
        await getModels(isoCode).settingSystem.create(settingSystem);
    });

    afterEach(async () => {
        await getModels(isoCode).settingSystem.deleteMany({});
    });

    describe('getListOfficesFromSettingSystem', () => {
        it('Should get list offices from settingSystem successfully', async () => {
            const params = {
                isoCode,
                filter: {
                    search: '',
                    statuses: Object.values(
                        OFFICE_STATUS_IN_CONFIGURATION,
                    ).join(','),
                },
                skip: 0,
                limit: 10,
            };

            const offices = await getListOfficesFromSettingSystem(params);

            expect(offices).toHaveLength(submissionAddresses.length);
            offices.forEach((office, index) => {
                expect(office.name).toBe(submissionAddresses[index].name);
                expect(office.address).toBe(submissionAddresses[index].address);
                expect(office.status).toBe(submissionAddresses[index].status);
                expect(office.workingStartDate).toEqual(
                    submissionAddresses[index].workingStartDate,
                );
                expect(office.workingEndDate).toEqual(
                    submissionAddresses[index].workingEndDate,
                );
                expect(office.city).toBe(submissionAddresses[index].city);
            });
        });
    });

    describe('getTotalOfficesFromSettingSystem', () => {
        it('Should get total offices from settingSystem successfully', async () => {
            const filter = {
                search: '',
                statuses: Object.values(OFFICE_STATUS_IN_CONFIGURATION).join(
                    ',',
                ),
            };

            const total = await getTotalOfficesFromSettingSystem({
                isoCode,
                filter,
            });

            expect(total).toEqual(submissionAddresses.length);
        });
    });

    describe('getOfficeDetailByIndexInSetting', () => {
        it('Should get office detail by index successfully', async () => {
            const indexOffice = 0;
            const officeDetail = await getOfficeDetailByIndexInSetting({
                isoCode,
                indexOffice,
            });

            expect(officeDetail).toEqual(
                expect.objectContaining({
                    name: submissionAddresses[indexOffice].name,
                    address: submissionAddresses[indexOffice].address,
                    status: submissionAddresses[indexOffice].status,
                    workingStartDate:
                        submissionAddresses[indexOffice].workingStartDate,
                    workingEndDate:
                        submissionAddresses[indexOffice].workingEndDate,
                    city: submissionAddresses[indexOffice].city,
                    phoneNumber: submissionAddresses[indexOffice].phoneNumber,
                    workingDays: submissionAddresses[indexOffice].workingDays,
                    offDays: submissionAddresses[indexOffice].offDays,
                }),
            );
        });

        it('Should throw error when office not found', async () => {
            const indexOffice = 999;
            await expect(
                getOfficeDetailByIndexInSetting({ isoCode, indexOffice }),
            ).rejects.toThrow('SUBMISSION_ADDRESS_NOT_FOUND');
        });
    });

    describe('createOffice', () => {
        it('Should create a new office successfully', async () => {
            const newOffice = {
                name: 'New Office',
                address: 'New Address',
                status: OFFICE_STATUS_IN_CONFIGURATION.ACTIVE,
                city: 'New City',
                phoneNumber: '9876543210',
                workingStartDate: momentTz('2024-06-01').toDate(),
                workingEndDate: momentTz('2024-06-30').toDate(),
                workingDays: [1, 2, 3, 4, 5],
                offDays: [],
                offDaysForOffice: [],
            };

            const result = await createOffice({
                isoCode,
                username: 'testUser',
                ...newOffice,
            });

            expect(result).toEqual({ msg: 'CREATE_OFFICE_SUCCESSFULLY' });

            const updatedSettingSystem = await getModels(isoCode)
                .settingSystem.findOne({})
                .lean();
            const createdOffice =
                updatedSettingSystem?.submitionAddressForTasker[
                    submissionAddresses.length
                ];

            expect(createdOffice).toBeDefined();
            expect(createdOffice?.name).toBe(newOffice.name);
            expect(createdOffice?.address).toBe(newOffice.address);
            expect(createdOffice?.status).toBe(newOffice.status);
        });
    });

    describe('deleteOfficeByIndex', () => {
        it('Should delete an office successfully', async () => {
            const officeIndex = 0;
            const result = await deleteOfficeByIndex({ isoCode, officeIndex });

            expect(result).toEqual({ msg: 'DELETE_OFFICE_SUCCESSFULLY' });

            const updatedSettingSystem = await getModels(isoCode)
                .settingSystem.findOne({})
                .lean();
            expect(
                updatedSettingSystem?.submitionAddressForTasker,
            ).toHaveLength(submissionAddresses.length - 1);
            expect(
                updatedSettingSystem?.submitionAddressForTasker[0].name,
            ).not.toBe(submissionAddresses[0].name);
        });
    });

    describe('updateOffice', () => {
        it('Should update an office successfully', async () => {
            const officeIndex = 1;
            const updateInfo = {
                name: 'Updated Office',
                address: 'Updated Address',
                status: OFFICE_STATUS_IN_CONFIGURATION.INACTIVE,
                city: 'Updated City',
                phoneNumber: '9876543210',
                workingStartDate: momentTz('2024-07-01').toDate(),
                workingEndDate: momentTz('2024-07-31').toDate(),
                workingDays: [1, 2, 3, 4, 5],
                offDays: [],
                createdBy: 'updatedUser',
                offDaysForOffice: [],
            };

            const result = await updateOffice({
                isoCode,
                officeIndex,
                updateInfo,
            });

            expect(result).toEqual({ msg: 'UPDATE_OFFICE_SUCCESSFULLY' });

            const updatedSettingSystem = await getModels(isoCode)
                .settingSystem.findOne({})
                .lean();
            const updatedOffice =
                updatedSettingSystem?.submitionAddressForTasker[officeIndex];

            expect(updatedOffice).toBeDefined();
            expect(updatedOffice?.name).toBe(updateInfo.name);
            expect(updatedOffice?.address).toBe(updateInfo.address);
            expect(updatedOffice?.status).toBe(updateInfo.status);
            expect(updatedOffice?.city).toBe(updateInfo.city);
            expect(updatedOffice?.phoneNumber).toBe(updateInfo.phoneNumber);
            expect(updatedOffice?.workingStartDate).toEqual(
                updateInfo.workingStartDate,
            );
            expect(updatedOffice?.workingEndDate).toEqual(
                updateInfo.workingEndDate,
            );
            expect(updatedOffice?.workingDays).toEqual(updateInfo.workingDays);
            expect(updatedOffice?.createdBy).toBe(updateInfo.createdBy);
        });
    });
});
