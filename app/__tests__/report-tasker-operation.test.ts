import { TASK_STATUS } from 'btaskee-constants';
import { type IsoCode } from 'btaskee-constants';
import { momentTz } from 'btaskee-utils';
import { getModels } from 'schemas';
import {
    SERVICE_TEXT_IN_REPORT_TASKER_OPERATION,
    USER_STATUS,
    USER_TYPE,
} from '~/services/constants.server';
import { getReportTaskerOperation } from '~/services/report-tasker-operation.server';

describe('Report - Tasker operation', () => {
    const mockServiceId = 'cleaningServiceId';
    const mockTaskerId1 = 'mockTaskerId1';
    const mockTaskerId2 = 'mockTaskerId2';
    const mockTaskerId3 = 'mockTaskerId3';
    const mockCity1 = 'Hồ Chí Minh';
    const mockCity2 = 'Hà Nội';
    const mockDistrict1 = 'Quận 1';
    const mockDistrict2 = 'Đống Đa';
    const mockIsoCode: `${IsoCode}` = 'VN';
    const tasks = [
        {
            _id: 'mockTaskId1',
            taskPlace: {
                city: mockCity1,
                district: mockDistrict1,
            },
            acceptedTasker: [{ taskerId: mockTaskerId1 }],
            date: new Date(),
            status: TASK_STATUS.CONFIRMED,
            serviceText: {
                vi: 'Dọn dẹp nhà',
                en: 'Cleaning',
            },
        },
        {
            _id: 'mockTaskId2',
            taskPlace: {
                city: mockCity2,
                district: mockDistrict2,
            },
            date: new Date(),
            acceptedTasker: [{ taskerId: mockTaskerId2 }],
            serviceText: {
                vi: 'Dọn dẹp nhà',
                en: 'Cleaning',
            },
            status: TASK_STATUS.DONE,
        },
        {
            _id: 'mockTaskId3',
            taskPlace: {
                city: mockCity2,
                district: mockDistrict2,
            },
            isPremium: true,
            date: new Date(),
            acceptedTasker: [{ taskerId: mockTaskerId3 }],
            serviceText: {
                vi: 'Dọn dẹp nhà',
                en: 'Cleaning',
            },
            status: TASK_STATUS.CONFIRMED,
        },
    ];
    const taskers = [
        {
            _id: mockTaskerId1,
            language: 'vi',
            fAccountId: 'fAccountId1',
            type: USER_TYPE.TASKER,
            status: USER_STATUS.ACTIVE,
            workingPlaces: [
                {
                    city: mockCity1,
                    district: mockDistrict1,
                },
            ],
        },
        {
            _id: mockTaskerId2,
            language: 'vi',
            fAccountId: 'fAccountId2',
            type: USER_TYPE.TASKER,
            status: USER_STATUS.ACTIVE,
            workingPlaces: [
                {
                    city: mockCity2,
                    district: mockDistrict2,
                },
            ],
        },
        {
            _id: mockTaskerId3,
            language: 'vi',
            fAccountId: 'fAccountId3',
            type: USER_TYPE.TASKER,
            status: USER_STATUS.ACTIVE,
            isPremiumTasker: true,
            workingPlaces: [
                {
                    city: mockCity2,
                    district: mockDistrict2,
                },
            ],
        },
    ];

    const service = {
        _id: mockServiceId,
        text: {
            vi: 'Dọn dẹp nhà',
            en: 'Cleaning',
        },
        icon: 'icon.com',
        status: 'ACTIVE',
        name: 'CLEANING',
    };

    const serviceChannel = {
        _id: 'serviceChannelId',
        serviceId: mockServiceId,
        taskerList: [mockTaskerId1, mockTaskerId2, mockTaskerId3],
    };

    beforeAll(async () => {
        await getModels(mockIsoCode).task.insertMany(tasks);
        await getModels(mockIsoCode).users.insertMany(
            taskers.map(tasker => ({ ...tasker, createdAt: new Date() })),
        );
        await getModels(mockIsoCode).service.create(service);
        await getModels(mockIsoCode).serviceChannel.create(serviceChannel);
    });
    afterAll(async () => {
        await getModels(mockIsoCode).task.deleteMany({
            _id: { $in: tasks.map(task => task._id) },
        });
        await getModels(mockIsoCode).users.deleteMany({
            _id: { $in: taskers.map(tasker => tasker._id) },
        });
        await getModels(mockIsoCode).service.deleteOne({ _id: service._id });
        await getModels(mockIsoCode).serviceChannel.deleteOne({
            _id: serviceChannel._id,
        });
    });

    it('Should get normal tasker statistic, normal task statistic successfully', async () => {
        const params = {
            isoCode: mockIsoCode,
            rangeDate: {
                from: new Date('2024-01-01'),
                to: momentTz().add(2, 'day').toDate(),
            },
            cities: 'Hồ Chí Minh,Hà Nội',
            serviceText: 'Cleaning',
        };

        const result = await getReportTaskerOperation(params);
        expect(result.ratioTasker).toHaveLength(2);
        expect(result.ratioTasker[0]).toStrictEqual({
            label: 'TASKER_WORKING',
            value: 3,
        });
        expect(result.ratioTasker[1]).toStrictEqual({
            label: 'TASKER_NOT_WORKING',
            value: 0,
        });
        expect(result.totalTasker).toEqual(3);
        expect(result.ratioTask).toHaveLength(2);
        expect(result.ratioTaskOfDistrict).toHaveLength(2);
    });
    it('Should get premium tasker statistic, premium task statistic successfully', async () => {
        const params = {
            isoCode: mockIsoCode,
            rangeDate: {
                from: new Date('2024-01-01'),
                to: momentTz().add(2, 'day').toDate(),
            },
            cities: 'Hồ Chí Minh,Hà Nội',
            serviceText:
                SERVICE_TEXT_IN_REPORT_TASKER_OPERATION.PREMIUM_SERVICE,
        };

        const result = await getReportTaskerOperation(params);

        expect(result.ratioTasker).toHaveLength(2);
        expect(result.ratioTasker[0]).toStrictEqual({
            label: 'TASKER_WORKING',
            value: 0,
        });
        expect(result.ratioTasker[1]).toStrictEqual({
            label: 'TASKER_NOT_WORKING',
            value: 1,
        });
        expect(result.totalTasker).toEqual(1);
        expect(result.ratioTask).toHaveLength(0);
        expect(result.ratioTaskOfDistrict).toHaveLength(0);
    });
});
