import {
    STATUS,
    TYPE_REWARD_ACCOUNT_SPECIAL_CAMPAIGN,
    TYPE_REWARD_SPECIAL_CAMPAIGN,
    TYPE_SPECIAL_CAMPAIGN,
} from 'btaskee-constants';
import { momentTz } from 'btaskee-utils';
import { getModels } from 'schemas';
import {
    createSpecialCampaign,
    getListSpecialCampaign,
    getSpecialCampaignDetail,
    getTotalSpecialCampaign,
    updateSpecialCampaign,
} from '~/services/special-campaign.server';

describe('Special campaign', () => {
    const isoCode = 'VN';

    const mockSpecialCampaign1 = {
        _id: 'special-campaign-1',
        name: 'special-campaign-1',
        image: { imageUrl: 'https://example.com/image.jpg' },
        type: 'REFERRAL_CAMPAIGN',
        status: 'ACTIVE',
        value: 10,
        startDate: momentTz().toDate(),
        endDate: momentTz().add(1, 'day').toDate(),
        text: {
            vi: {
                name: 'Tên Chiến Dịch Đặc Biệt',
                description: '<PERSON><PERSON> tả chiến dịch đặc biệt',
            },
            en: {
                name: 'Tên Chiến Dịch Đặc Biệt',
                description: 'Mô tả chiến dịch đặc biệt',
            },
            ko: {
                name: 'Tên Chiến Dịch Đặc Biệt',
                description: 'Mô tả chiến dịch đặc biệt',
            },
            th: {
                name: 'Tên Chiến Dịch Đặc Biệt',
                description: 'Mô tả chiến dịch đặc biệt',
            },
            id: {
                name: 'Tên Chiến Dịch Đặc Biệt',
                description: 'Mô tả chiến dịch đặc biệt',
            },
        },
        rewards: [
            {
                type: 'MONEY',
                amount: 100000,
            },
        ],
        city: ['Hồ Chí Minh'],
        createdAt: momentTz().toDate(),
    };

    beforeEach(async () => {
        await getModels(isoCode).taskerSpecialCampaign.insertMany([
            mockSpecialCampaign1,
        ]);
    });

    afterEach(async () => {
        await getModels(isoCode).taskerSpecialCampaign.deleteMany({
            _id: mockSpecialCampaign1._id,
        });
    });

    describe('getListSpecialCampaign', () => {
        it('should return the list of special campaigns successfully', async () => {
            const specialCampaigns = await getListSpecialCampaign({
                isoCode,
                limit: 1,
                skip: 0,
                sort: {
                    updatedAt: -1,
                },
                filter: {
                    search: mockSpecialCampaign1.name,
                    type: mockSpecialCampaign1.type,
                    status: mockSpecialCampaign1.status,
                    rangeDate: {
                        from: momentTz().subtract(1, 'day').toDate(),
                        to: momentTz().add(1, 'day').toDate(),
                    },
                },
            });

            expect(specialCampaigns).toHaveLength(1);
            expect(specialCampaigns[0].name).toEqual(mockSpecialCampaign1.name);
            expect(specialCampaigns[0].status).toEqual(
                mockSpecialCampaign1.status,
            );
            expect(specialCampaigns[0].type).toEqual(mockSpecialCampaign1.type);
        });
        it('should initialize $and when filtering by date range after other filters', async () => {
            const specialCampaigns = await getListSpecialCampaign({
                isoCode,
                limit: 2,
                skip: 0,
                sort: {
                    updatedAt: -1,
                },
                filter: {
                    search: '', // no search, so $and won't be initialized first
                    type: mockSpecialCampaign1.type,
                    status: mockSpecialCampaign1.status,
                    rangeDate: {
                        from: momentTz().subtract(1, 'day').toDate(),
                        to: momentTz().add(1, 'day').toDate(),
                    },
                },
            });

            // The query should still work, meaning $and was properly initialized
            expect(specialCampaigns).toHaveLength(1);
            expect(specialCampaigns[0].type).toEqual(mockSpecialCampaign1.type);
            expect(specialCampaigns[0].status).toEqual(
                mockSpecialCampaign1.status,
            );
        });
    });

    describe('getTotalSpecialCampaign', () => {
        it('should return the total number of special campaigns successfully', async () => {
            const total = await getTotalSpecialCampaign({
                isoCode,
                filter: {
                    search: mockSpecialCampaign1.name,
                    type: mockSpecialCampaign1.type,
                    status: mockSpecialCampaign1.status,
                    rangeDate: {
                        from: momentTz().subtract(1, 'day').toDate(),
                        to: momentTz().add(1, 'day').toDate(),
                    },
                },
            });

            expect(total).toEqual(1);
        });
    });

    describe('getSpecialCampaignDetail', () => {
        it('should return the detail of special campaign successfully', async () => {
            const specialCampaign = await getSpecialCampaignDetail({
                isoCode,
                specialCampaignId: mockSpecialCampaign1._id,
            });

            expect(specialCampaign?.name).toEqual(mockSpecialCampaign1.name);
            expect(specialCampaign?.status).toEqual(
                mockSpecialCampaign1.status,
            );
            expect(specialCampaign?.type).toEqual(mockSpecialCampaign1.type);
        });
    });

    describe('createSpecialCampaign', () => {
        it('should create a new special campaign successfully', async () => {
            const specialCampaign = await createSpecialCampaign({
                isoCode,
                params: {
                    name: 'special-campaign-2',
                    image: {
                        imageUrl: 'https://example.com/image.jpg',
                        thumbnailUrl: 'https://example.com/thumbnail.jpg',
                    },
                    type: 'REFERRAL_CAMPAIGN',
                    status: 'ACTIVE',
                    value: 0,
                    startDate: momentTz().toDate(),
                    endDate: momentTz().add(1, 'day').toDate(),
                    text: {
                        vi: {
                            name: '',
                            description: '',
                        },
                        en: {
                            name: '',
                            description: '',
                        },
                        ko: {
                            name: '',
                            description: '',
                        },
                        th: {
                            name: '',
                            description: '',
                        },
                        id: {
                            name: '',
                            description: '',
                        },
                        ms: {
                            name: '',
                            description: '',
                        },
                    },
                    rewards: [
                        {
                            type: 'MONEY',
                            amount: 100000,
                            applyAccountType: 'M',
                            taskerJourneyLevels: [],
                            minRateTask: 0,
                            applyForServices: [],
                        },
                    ],
                    city: ['Hồ Chí Minh'],
                    createdBy: 'thienduy.cao',
                },
            });

            const expectedSpecialCampaign = await getModels(
                isoCode,
            ).taskerSpecialCampaign.findById(specialCampaign._id);

            expect(specialCampaign?.name).toEqual(
                expectedSpecialCampaign?.name,
            );
            expect(specialCampaign?.status).toEqual(
                expectedSpecialCampaign?.status,
            );
            expect(specialCampaign?.type).toEqual(
                expectedSpecialCampaign?.type,
            );

            // clear data inserted
            await getModels(isoCode).taskerSpecialCampaign.deleteOne({
                _id: specialCampaign._id,
            });
        });
    });

    describe('updateSpecialCampaign', () => {
        it('should update a special campaign successfully', async () => {
            const mockUpdateParams = {
                image: {
                    imageUrl: 'https://example.com/image.jpg',
                    thumbnailUrl: 'https://example.com/thumbnail.jpg',
                },
                type: TYPE_SPECIAL_CAMPAIGN.REFERRAL_CAMPAIGN,
                status: STATUS.INACTIVE,
                createdBy: 'thienduy.cao',
                name: '',
                startDate: momentTz().toDate(),
                endDate: momentTz().add(1, 'day').toDate(),
                value: 0,
                text: {
                    vi: {
                        name: '',
                        description: '',
                    },
                    en: {
                        name: '',
                        description: '',
                    },
                    ko: {
                        name: '',
                        description: '',
                    },
                    th: {
                        name: '',
                        description: '',
                    },
                    id: {
                        name: '',
                        description: '',
                    },
                    ms: {
                        name: '',
                        description: '',
                    },
                },
                rewards: [
                    {
                        type: TYPE_REWARD_SPECIAL_CAMPAIGN.MONEY,
                        amount: 100000,
                        applyAccountType:
                            TYPE_REWARD_ACCOUNT_SPECIAL_CAMPAIGN.MAIN_ACCOUNT,
                        taskerJourneyLevels: [],
                        minRateTask: 0,
                        applyForServices: [],
                    },
                ],
                city: ['Hồ Chí Minh'],
            };

            await updateSpecialCampaign({
                isoCode,
                specialCampaignId: mockSpecialCampaign1._id,
                params: mockUpdateParams,
            });

            const expectedSpecialCampaign = await getModels(
                isoCode,
            ).taskerSpecialCampaign.findById(mockSpecialCampaign1._id);

            expect(expectedSpecialCampaign?.status).toEqual(
                mockUpdateParams.status,
            );
            expect(expectedSpecialCampaign?.type).toEqual(
                mockUpdateParams.type,
            );
            expect(expectedSpecialCampaign?.city).toEqual(
                mockUpdateParams.city,
            );
        });

        it('Should remove city field when city is empty', async () => {
            const mockUpdateParams = {
                image: {
                    imageUrl: 'https://example.com/image.jpg',
                    thumbnailUrl: 'https://example.com/thumbnail.jpg',
                },
                type: TYPE_SPECIAL_CAMPAIGN.REFERRAL_CAMPAIGN,
                status: STATUS.INACTIVE,
                createdBy: 'thienduy.cao',
                name: '',
                startDate: momentTz().toDate(),
                endDate: momentTz().add(1, 'day').toDate(),
                value: 0,
                text: {
                    vi: {
                        name: '',
                        description: '',
                    },
                    en: {
                        name: '',
                        description: '',
                    },
                    ko: {
                        name: '',
                        description: '',
                    },
                    th: {
                        name: '',
                        description: '',
                    },
                    id: {
                        name: '',
                        description: '',
                    },
                    ms: {
                        name: '',
                        description: '',
                    },
                },
                rewards: [
                    {
                        type: TYPE_REWARD_SPECIAL_CAMPAIGN.MONEY,
                        amount: 100000,
                        applyAccountType:
                            TYPE_REWARD_ACCOUNT_SPECIAL_CAMPAIGN.MAIN_ACCOUNT,
                        taskerJourneyLevels: [],
                        minRateTask: 0,
                        applyForServices: [],
                    },
                ],
            };

            await updateSpecialCampaign({
                isoCode,
                specialCampaignId: mockSpecialCampaign1._id,
                params: mockUpdateParams,
            });

            const expectedSpecialCampaign = await getModels(
                isoCode,
            ).taskerSpecialCampaign.findById(mockSpecialCampaign1._id);

            expect(expectedSpecialCampaign?.status).toEqual(
                mockUpdateParams.status,
            );
            expect(expectedSpecialCampaign?.type).toEqual(
                mockUpdateParams.type,
            );
            expect(expectedSpecialCampaign?.city).toEqual(undefined);
        });
    });
});
