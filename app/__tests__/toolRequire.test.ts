import { IsoCode } from 'btaskee-constants';
import { type PipelineStage } from 'mongo-connection';
import { getModels } from 'schemas';
import {
    createToolItem,
    deleteToolItem,
    getListToolItems,
    getToolItemDetail,
    getTotalToolItems,
    updateToolItem,
} from '~/services/tool-require.server';

describe('Toolkit item management', () => {
    const isoCode = IsoCode.VN;
    const toolkitItemIds = [
        'toolkitItemId1',
        'toolkitItemId2',
        'toolkitItemId3',
        'toolkitItemId4',
        'toolkitItemId5',
    ];

    const commonToolkitSettingInfo = {
        image: 'toolkitSetting.image.com',
        discountForOncePay: 12,
        text: {
            vi: 'toolkit setting vi',
            en: 'toolkit setting en',
            ko: 'toolkit setting ko',
            th: 'toolkit setting th',
            id: 'toolkit setting id',
        },
        BNPLSetting: {
            firstPayPercent: 12,
            percentBNPLOnTask: 10,
            period: 2,
        },
        serviceIds: ['serviceId1', 'serviceId2'],
        createdAt: new Date(),
    };

    const mockToolkitItems = toolkitItemIds.map(toolkitItemId => ({
        _id: toolkitItemId,
        image: 'image.com',
        price: 22000,
        text: {
            vi: toolkitItemId,
            en: toolkitItemId,
            ko: toolkitItemId,
            th: toolkitItemId,
            id: toolkitItemId,
        },
        createdAt: new Date(),
    }));

    const mockToolkitSettings = [
        {
            _id: 'toolkitSettingId1',
            ...commonToolkitSettingInfo,
            toolKitItems: [{ _id: toolkitItemIds[0], quantity: 2 }],
        },
        {
            _id: 'toolkitSettingId2',
            ...commonToolkitSettingInfo,
            toolKitItems: [
                { _id: toolkitItemIds[1], quantity: 1 },
                { _id: toolkitItemIds[2], quantity: 2 },
            ],
        },
        {
            _id: 'toolkitSettingId3',
            ...commonToolkitSettingInfo,
            toolKitItems: [
                { _id: toolkitItemIds[3], quantity: 1 },
                { _id: toolkitItemIds[4], quantity: 3 },
            ],
        },
    ];

    beforeAll(async () => {
        await getModels(isoCode).toolKitItems.insertMany(mockToolkitItems);
        await getModels(isoCode).toolKitSetting.insertMany(mockToolkitSettings);
    });
    afterAll(async () => {
        await getModels(isoCode).toolKitItems.deleteMany({
            _id: { $in: toolkitItemIds },
        });
        await getModels(isoCode).toolKitSetting.deleteMany({
            _id: {
                $in: mockToolkitSettings.map(
                    mockToolkitSetting => mockToolkitSetting._id,
                ),
            },
        });
    });

    describe('createToolkitItem', () => {
        const params = {
            toolkitItem: {
                image: 'created-toolkit-image.com',
                price: 12000,
                text: {
                    vi: 'created toolkit',
                    en: 'created toolkit',
                    ko: 'created toolkit',
                    th: 'created toolkit',
                    id: 'created toolkit',
                },
            },
            isoCode,
        };

        afterEach(async () => {
            await getModels(isoCode).toolKitItems.findOneAndDelete(
                params.toolkitItem,
            );
        });

        it('Should create toolkit item successfully', async () => {
            await createToolItem(params);

            const toolkitItemFoundAfterCreate = await getModels(isoCode)
                .toolKitItems.findOne(params.toolkitItem)
                .lean<ToolKitItem>();

            expect(toolkitItemFoundAfterCreate?._id).toBeDefined();
            expect(toolkitItemFoundAfterCreate?.createdAt).toBeDefined();
            expect(toolkitItemFoundAfterCreate?.image).toEqual(
                params.toolkitItem.image,
            );
            expect(toolkitItemFoundAfterCreate?.text).toEqual(
                params.toolkitItem.text,
            );
            expect(toolkitItemFoundAfterCreate?.price).toEqual(
                params.toolkitItem.price,
            );
        });
    });
    describe('updateToolkitItem', () => {
        it('Should throw error message if price is 0', async () => {
            await expect(
                updateToolItem({
                    isoCode,
                    toolkitItemId: toolkitItemIds[1],
                    updateInfo: {
                        price: 0,
                    },
                }),
            ).rejects.toThrow('PRICE_MUST_BE_MORE_THAN_0');
        });
        it('Should update toolkit item successfully', async () => {
            const params = {
                isoCode,
                toolkitItemId: toolkitItemIds[1],
                updateInfo: {
                    text: {
                        vi: 'updated vi',
                        en: 'updated vi',
                        ko: 'updated vi',
                        th: 'updated vi',
                        id: 'updated vi',
                    },
                    image: 'updated-image.com',
                },
            };

            await updateToolItem(params);

            const toolkitItemFoundAfterUpdate = await getModels(isoCode)
                .toolKitItems.findOne({ _id: params.toolkitItemId })
                .lean<ToolKitItem>();

            expect(toolkitItemFoundAfterUpdate?.text).toEqual(
                params.updateInfo.text,
            );
            expect(toolkitItemFoundAfterUpdate?.image).toEqual(
                params.updateInfo.image,
            );
            expect(toolkitItemFoundAfterUpdate?.updatedAt).toBeDefined();
        });
    });
    describe('getTotalToolkitItems', () => {
        it('Should get total toolkitItems successfully', async () => {
            const newDate = new Date();
            const params = {
                isoCode,
                search: '',
                rangeDate: {
                    from: new Date(new Date().setDate(newDate.getDate() - 2)),
                    to: new Date(new Date().setDate(newDate.getDate() + 2)),
                },
            };
            const totalToolkitItems = await getTotalToolItems(params);

            const expectationToolkitItems = mockToolkitItems.filter(
                mockToolkitItem =>
                    mockToolkitItem.createdAt >= params.rangeDate.from &&
                    mockToolkitItem.createdAt <= params.rangeDate.to,
            );

            expect(totalToolkitItems).toEqual(expectationToolkitItems.length);
        });
        it('Should get total toolkitItems when search by toolkit name successfully', async () => {
            const newDate = new Date();
            const params = {
                isoCode,
                search: mockToolkitItems[3].text.en,
                rangeDate: {
                    from: new Date(new Date().setDate(newDate.getDate() - 2)),
                    to: new Date(new Date().setDate(newDate.getDate() + 2)),
                },
            };
            const totalToolkitItems = await getTotalToolItems(params);

            const expectationToolkitItems = mockToolkitItems.filter(
                mockToolkitItem =>
                    mockToolkitItem.createdAt >= params.rangeDate.from &&
                    mockToolkitItem.createdAt <= params.rangeDate.to &&
                    mockToolkitItem.text.en === params.search,
            );

            expect(totalToolkitItems).toEqual(expectationToolkitItems.length);
        });
    });
    describe('getListToolkitItems', () => {
        it('Should get list toolkitItems successfully', async () => {
            const newDate = new Date();
            const params = {
                isoCode,
                search: '',
                rangeDate: {
                    from: new Date(new Date().setDate(newDate.getDate() - 2)),
                    to: new Date(new Date().setDate(newDate.getDate() + 2)),
                },
                sort: {
                    createdAt: -1,
                } as PipelineStage.Sort['$sort'],
                skip: 0,
                limit: 10,
            };
            const toolkitItems = await getListToolItems(params);

            const expectationToolkitItems = mockToolkitItems
                .filter(
                    mockToolkitItem =>
                        mockToolkitItem.createdAt >= params.rangeDate.from &&
                        mockToolkitItem.createdAt <= params.rangeDate.to,
                )
                .slice(params.skip, params.limit)
                .map(mockToolkitItem => mockToolkitItem._id);

            expect(toolkitItems).toHaveLength(expectationToolkitItems.length);
            toolkitItems.forEach(toolkitItem => {
                expect(expectationToolkitItems).toContain(toolkitItem._id);
            });
        });
        it('Should get list toolkitItems after searching successfully', async () => {
            const newDate = new Date();
            const params = {
                isoCode,
                search: mockToolkitItems[2].text.vi,
                rangeDate: {
                    from: new Date(new Date().setDate(newDate.getDate() - 2)),
                    to: new Date(new Date().setDate(newDate.getDate() + 2)),
                },
                sort: {
                    createdAt: -1,
                } as PipelineStage.Sort['$sort'],
                skip: 0,
                limit: 10,
            };
            const toolkitItems = await getListToolItems(params);

            const expectationToolkitItems = mockToolkitItems
                .filter(
                    mockToolkitItem =>
                        mockToolkitItem.createdAt >= params.rangeDate.from &&
                        mockToolkitItem.createdAt <= params.rangeDate.to &&
                        mockToolkitItem.text.vi === params.search,
                )
                .slice(params.skip, params.limit)
                .map(mockToolkitItem => mockToolkitItem._id);

            expect(toolkitItems).toHaveLength(expectationToolkitItems.length);
            toolkitItems.forEach(toolkitItem => {
                expect(expectationToolkitItems).toContain(toolkitItem._id);
            });
        });
    });
    describe('deleteToolkitItem', () => {
        it('Should delete toolkit item successfully', async () => {
            const params = {
                isoCode,
                toolkitItemId: toolkitItemIds[2],
            };

            await deleteToolItem(params);

            const toolkitItemFoundAfterDelete = await getModels(isoCode)
                .toolKitItems.findOne({ _id: params.toolkitItemId })
                .lean<ToolKitItem>();
            const toolkitSettingFoundByToolkitItemId = await getModels(isoCode)
                .toolKitSetting.findOne({
                    'toolKitItems._id': params.toolkitItemId,
                })
                .lean<ToolkitSetting>();

            expect(toolkitItemFoundAfterDelete).toBeNull();
            expect(toolkitSettingFoundByToolkitItemId).toBeNull();
        });
    });
    describe('getToolkitItemDetail', () => {
        it('Should throw error message when toolkit item not found', async () => {
            await expect(
                getToolItemDetail({
                    isoCode,
                    toolkitItemId: 'toolkitIdNotFound',
                }),
            ).rejects.toThrow('TOOLKIT_ITEM_NOT_FOUND');
        });
        it('Should get toolkit item successfully', async () => {
            const paramsPassingQuery = {
                isoCode,
                toolkitItemId: toolkitItemIds[1],
            };
            const toolkitFoundByQuery =
                await getToolItemDetail(paramsPassingQuery);

            const expectationToolkitItem = mockToolkitItems.find(
                toolkitItem =>
                    toolkitItem._id === paramsPassingQuery.toolkitItemId,
            );

            expect(toolkitFoundByQuery._id).toEqual(
                expectationToolkitItem?._id,
            );
        });
    });
});
