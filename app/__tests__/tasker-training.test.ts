import {
    COURSE_COMPLETION_IN_TRAINING,
    COURSE_VISIBILITY_IN_TRAINING,
    STATUS,
    SUBMISSION_STATUS_IN_TRAINING,
} from 'btaskee-constants';
import { momentTz } from 'btaskee-utils';
import { type PipelineStage } from 'mongo-connection';
import { EnumIsoCode, getModels } from 'schemas';
import {
    FILTER_OPTION_IN_QUIZ,
    USER_TYPE,
    newRecordCommonField,
} from '~/services/constants.server';
import {
    createCourse,
    createQuiz,
    createQuizCollection,
    createQuizzes,
    getAllCourses,
    getCourseDetail,
    getListQuizDetailSortByOrder,
    getListQuizzes,
    getListQuizzesDetailSortByOrder,
    getListTrainingHistory,
    getNameOfCourseByIds,
    getQuizCollectionDetail,
    getQuizDetail,
    getServicesByTaskerId,
    getTotalQuizzes,
    updateCourse,
    updateQuiz,
    updateQuizCollection,
} from '~/services/tasker-training.server';

const mockUpdatedAt = new Date();

jest.mock('~/services/utils.server', () => ({
    __esModule: true,
    fetchAPI: () => new Promise(resolve => resolve(true)),
}));

jest.mock('btaskee-utils', () => ({
    __esModule: true,
    momentTz: () => {
        return { toDate: () => mockUpdatedAt };
    },
}));

describe('Tasker training feature', () => {
    const mockQuizIds = [
        'quiz_1',
        'quiz_2',
        'quiz_3',
        'quiz_4',
        'quiz_5',
        'quiz_6',
        'quiz_7',
        'quiz_8',
        'quiz_9',
        'quiz_10',
        'quiz_11',
        'quiz_12',
    ];
    const mockQuizCollectionIds = [
        'quiz_collection_1',
        'quiz_collection_2',
        'quiz_collection_3',
        'quiz_collection_4',
        'quiz_collection_5',
        'quiz_collection_6',
        'quiz_collection_7',
        'quiz_collection_8',
        'quiz_collection_9',
        'quiz_collection_10',
        'quiz_collection_11',
        'quiz_collection_12',
    ];
    const mockCourseIds = [
        'course_1_special',
        'course_2',
        'course_3',
        'course_4',
        'course_5',
        'course_6',
        'course_7',
        'course_8',
        'course_9',
        'course_10',
        'course_11',
        'course_12',
    ];
    const mockQuizId1 = 'quiz_1';
    const mockQuizId2 = 'quiz_2';
    const mockQuizzes = [
        {
            _id: mockQuizId1,
            createdAt: momentTz('2024-01-02').toDate(),
            updatedAt: momentTz('2024-01-02').toDate(),
            title: 'title 1',
            code: 'code 1',
            description: 'description 1',
            isTesting: true,
            createdByUserId: 'mock-user-id',
            createdByUsername: 'mock-username',
            isRandomAnswer: false,
            image: {
                url: '',
                description: 'image',
            },
            answers: [
                {
                    content: 'something',
                    isCorrect: true,
                },
            ],
        },
        {
            _id: mockQuizId2,
            createdAt: momentTz('2024-01-01').toDate(),
            updatedAt: momentTz('2024-01-01').toDate(),
            title: 'title 2',
            code: 'code 2',
            description: 'description 2',
            isTesting: true,
            createdByUserId: 'mock-user-id',
            createdByUsername: 'mock-username',
            isRandomAnswer: false,
            image: {
                url: '',
                description: 'image',
            },
            answers: [
                {
                    content: 'something',
                    isCorrect: true,
                },
            ],
        },
    ];
    const mockQuizCollections = mockQuizCollectionIds.map(id => ({
        _id: id,
        createdAt: new Date(),
        title: id,
        code: id,
        description: id,
        isTesting: true,
        createdByUserId: 'mock-user-id',
        createdByUsername: 'mock-username',
        timeToCompleteByMinutes: 120,
        numberOfDisplayQuizzes: 1,
        isRandomQuizzes: false,
        image: {
            url: '',
            description: 'image',
        },
        video: {
            url: '',
            description: 'image',
        },
        quizzes: mockQuizIds.map((id, index) => ({
            _id: id,
            order: index,
        })),
    }));
    const mockCourses = mockCourseIds.map(id => ({
        _id: id,
        status: 'ACTIVE',
        createdAt: new Date(),
        title: id,
        code: id,
        description: id,
        isTesting: true,
        createdByUserId: 'mock-user-id',
        createdByUsername: 'mock-username',
        type: 'Course basic',
        timeToCompleteByMinutes: 120,
        maximumNumberOfRetries: 3,
        percentageToPass: 0.5,
        deadlineIn: 100,
        isRecommended: false,
        relatedServices: [],
        quizCollections: mockQuizCollectionIds.map((id, index) => ({
            _id: id,
            order: index,
        })),
        condition: {
            byTasker: {
                minimumStar: 4,
            },
        },
        displayPosition: '?',
    }));

    const isoCode = EnumIsoCode.VN;

    beforeAll(async () => {
        await getModels(isoCode).taskerTrainingQuiz.insertMany(mockQuizzes);
        await getModels(isoCode).taskerTrainingQuizCollection.insertMany(
            mockQuizCollections,
        );
        await getModels(isoCode).taskerTrainingCourse.insertMany(mockCourses);
    });

    afterAll(async () => {
        await getModels(isoCode).taskerTrainingQuiz.deleteMany({
            _id: { $in: mockQuizzes.map(quiz => quiz._id) },
        });
        await getModels(isoCode).taskerTrainingQuizCollection.deleteMany({
            _id: { $in: mockQuizCollectionIds },
        });
        await getModels(isoCode).taskerTrainingCourse.deleteMany({
            _id: { $in: mockCourseIds },
        });
    });

    describe('getListQuizzes', () => {
        it('Should return quizzes correctly', async () => {
            const params = {
                isoCode,
                filter: {
                    name: '',
                    code: '',
                    updatedAt: {
                        from: momentTz('2024-01-01').toDate(),
                        to: momentTz('2024-01-02').toDate(),
                    },
                },
                sort: {
                    updatedAt: -1,
                } as PipelineStage.Sort['$sort'],
                skip: 0,
                limit: 10,
            };

            const listQuizzes = await getListQuizzes(params);

            expect(listQuizzes).toHaveLength(mockQuizzes.length);
            listQuizzes.forEach(quiz => {
                expect(
                    mockQuizzes.find(
                        expectationQuiz => expectationQuiz._id === quiz._id,
                    ),
                ).toBeTruthy();
            });
        });
        it('Should search quiz by name and return data correctly', async () => {
            const params = {
                isoCode,
                filter: {
                    name: mockQuizzes[0].title,
                    code: '',
                    updatedAt: {
                        from: momentTz('2024-01-01').toDate(),
                        to: momentTz('2024-01-02').toDate(),
                    },
                    filters: {
                        image: [FILTER_OPTION_IN_QUIZ.YES].join(','),
                        randomAnswer: [FILTER_OPTION_IN_QUIZ.NO].join(','),
                    },
                },
                sort: {
                    updatedAt: -1,
                } as PipelineStage.Sort['$sort'],
                skip: 0,
                limit: 11,
            };

            const listQuizzes = await getListQuizzes(params);

            expect(listQuizzes[0]._id).toEqual(mockQuizId1);
        });
    });

    describe('getTotalQuizzes', () => {
        it('Should return total quizzes correctly', async () => {
            const newDate = new Date();
            const params = {
                isoCode,
                filter: {
                    updatedAt: {
                        from: new Date(newDate.setDate(newDate.getDate() - 2)),
                        to: new Date(newDate.setDate(newDate.getDate() + 2)),
                    },
                    filters: {
                        image: [FILTER_OPTION_IN_QUIZ.YES].join(','),
                        randomAnswer: [FILTER_OPTION_IN_QUIZ.NO].join(','),
                    },
                },
            };

            const quizzes = await getTotalQuizzes(params);

            const expectationQuizzes = mockQuizzes.filter(
                mockQuiz =>
                    !!mockQuiz.image &&
                    !mockQuiz.isRandomAnswer &&
                    (mockQuiz.updatedAt
                        ? mockQuiz.updatedAt >= params.filter.updatedAt.from &&
                          mockQuiz.updatedAt <= params.filter.updatedAt.to
                        : mockQuiz.createdAt >= params.filter.updatedAt.from &&
                          mockQuiz.createdAt <= params.filter.updatedAt.to),
            );

            expect(quizzes).toEqual(expectationQuizzes.length);
        });
    });

    describe('getAllCourseExcludeCourseId', () => {
        it('Should return all courses successfully', async () => {
            const courses = await getAllCourses({
                isoCode,
                projection: {
                    _id: 1,
                },
            });

            expect(courses).toHaveLength(mockCourseIds.length);
            expect(courses[0]._id).toEqual(mockCourseIds[0]);
        });

        it('Should return all courses excluding the course with the given courseId', async () => {
            const courses = await getAllCourses({
                isoCode,
                projection: {
                    _id: 1,
                },
                courseId: mockCourseIds[0],
            });

            expect(courses).toHaveLength(mockCourseIds.length - 1);
        });
    });

    describe('createQuiz', () => {
        it('Should create a quiz correctly', async () => {
            const mockQuizName = 'quiz-one';
            await createQuiz({
                params: {
                    name: mockQuizName,
                    code: 'id',
                    description: 'id',
                    isTesting: true,
                    createdByUserId: 'mock-user-id',
                    createdByUsername: 'mock-username',
                    isRandomAnswer: false,
                    image: {
                        url: '',
                        description: 'image',
                    },
                    answers: [
                        {
                            _id: 'answer-1',
                            content: 'something',
                            isCorrect: true,
                        },
                    ],
                },
                isoCode,
            });
            const newQuiz = await getModels(isoCode).taskerTrainingQuiz.findOne(
                {
                    title: mockQuizName,
                },
            );

            expect(newQuiz).toBeDefined();

            // clear data inserted
            await getModels(isoCode).taskerTrainingQuiz.deleteOne({
                title: mockQuizName,
            });
        });
    });

    describe('createQuizCollection', () => {
        it('Should create a quiz collection correctly', async () => {
            const mockQuizCollectionName = 'quiz-col-one';
            await createQuizCollection({
                params: {
                    name: mockQuizCollectionName,
                    code: 'id',
                    description: 'id',
                    isTesting: true,
                    createdByUserId: 'mock-user-id',
                    createdByUsername: 'mock-username',
                    timeToCompleteByMinutes: 120,
                    numberOfDisplayQuizzes: 1,
                    isRandomQuizzes: false,
                    image: {
                        url: '',
                        description: 'image',
                    },
                    video: {
                        url: '',
                        description: 'image',
                    },
                    quizzes: mockQuizIds.map((id, index) => ({
                        _id: id,
                        order: index,
                    })),
                },
                isoCode,
            });
            const newQuizCollection = await getModels(
                isoCode,
            ).taskerTrainingQuizCollection.findOne({
                title: mockQuizCollectionName,
            });

            expect(newQuizCollection).toBeDefined();

            // clear data inserted
            await getModels(isoCode).taskerTrainingQuizCollection.deleteOne({
                title: mockQuizCollectionName,
            });
        });
    });

    describe('createQuizzes', () => {
        it('Should create quizzes correctly', async () => {
            const mockQuizName = 'quiz-one';
            await createQuizzes({
                params: [
                    {
                        ...newRecordCommonField(),
                        name: mockQuizName,
                        code: 'id',
                        description: 'id',
                        isTesting: true,
                        createdByUserId: 'mock-user-id',
                        createdByUsername: 'mock-username',
                        isRandomAnswer: false,
                        image: {
                            url: '',
                            description: 'image',
                        },
                        answers: [
                            {
                                _id: 'answer-1',
                                content: 'something',
                                isCorrect: true,
                            },
                        ],
                    },
                ],
                isoCode,
            });
            const newQuiz = await getModels(isoCode).taskerTrainingQuiz.findOne(
                {
                    title: mockQuizName,
                },
            );

            expect(newQuiz).toBeDefined();

            // clear data inserted
            await getModels(isoCode).taskerTrainingQuiz.deleteOne({
                title: mockQuizName,
            });
        });
    });

    describe('createCourse', () => {
        const mockTaskerId = 'tasker-done-course';
        const mockServiceId = 'service-1';
        const mockCourseId = 'course-1';

        beforeEach(async () => {
            await getModels(isoCode).taskerTrainingSubmission.create({
                _id: 'submission-1',
                status: SUBMISSION_STATUS_IN_TRAINING.PASSED,
                taskerId: mockTaskerId,
                course: {
                    _id: mockCourseId,
                },
            });

            await getModels(isoCode).users.create({
                _id: mockTaskerId,
                name: 'tasker done course',
                workingPlaces: [{ country: 'VN', city: 'Hồ Chí Minh' }],
                type: USER_TYPE.TASKER,
                language: 'vi',
                fAccountId: 'fAccountId1',
                status: 'ACTIVE',
                createdAt: momentTz('2023-10-01').toDate(),
            });

            await getModels(isoCode).serviceChannel.create({
                _id: 'service-channel-1',
                serviceId: mockServiceId,
                taskerList: [mockTaskerId],
            });
        });

        afterEach(async () => {
            await getModels(isoCode).taskerTrainingSubmission.deleteOne({
                _id: 'submission-1',
            });
            await getModels(isoCode).serviceChannel.deleteOne({
                _id: 'service-channel-1',
            });
            await getModels(isoCode).users.deleteOne({
                _id: mockTaskerId,
            });
        });

        it('Should create a course correctly with condition must be completed a course is PASSED by another tasker', async () => {
            const courseCreated = await createCourse({
                params: {
                    title: 'course-create',
                    type: 'TEST',
                    condition: {
                        coursesMustBeCompleted: {
                            courseIds: [mockCourseId],
                        },
                    },
                    createdByUsername: 'mock-username',
                    createdByUserId: 'mock-user-id',
                    status: STATUS.ACTIVE,
                    code: 'mock-code',
                    deadlineIn: 50,
                    displayPosition: '?',
                    relatedServices: [
                        {
                            _id: mockServiceId,
                            name: 'Service 1',
                        },
                    ],
                    maximumNumberOfRetries: 0,
                    timeToCompleteByMinutes: 0,
                    percentageToPass: 0,
                    quizCollections: mockQuizCollectionIds.map((id, index) => ({
                        _id: id,
                        order: index,
                    })),
                    cities: ['Hồ Chí Minh'],
                },
                isoCode,
            });

            const courseStartDate = await getModels(isoCode)
                .taskerTrainingCourseStartDate.findOne({
                    courseId: courseCreated._id,
                    taskerId: mockTaskerId,
                })
                .lean();

            expect(courseStartDate).toBeDefined();
            expect(courseStartDate?.courseId).toEqual(courseCreated._id);
            expect(courseStartDate?.deadlineIn).toEqual(50);

            // clear data inserted
            await getModels(isoCode).taskerTrainingCourse.deleteOne({
                _id: courseCreated._id,
            });
            await getModels(isoCode).taskerTrainingCourseStartDate.deleteOne({
                courseId: courseStartDate?._id,
            });
        });

        it('Should create a course correctly', async () => {
            const mockCourseName = 'course-one';
            await createCourse({
                params: {
                    status: 'ACTIVE',
                    title: mockCourseName,
                    code: 'id',
                    isTesting: true,
                    createdByUserId: 'mock-user-id',
                    createdByUsername: 'mock-username',
                    type: 'Course basic',
                    timeToCompleteByMinutes: 120,
                    maximumNumberOfRetries: 3,
                    percentageToPass: 0.5,
                    deadlineIn: 100,
                    relatedServices: [],
                    quizCollections: mockQuizCollectionIds.map((id, index) => ({
                        _id: id,
                        order: index,
                    })),
                    condition: {
                        byTasker: {
                            minimumStar: 4,
                        },
                    },
                    displayPosition: '?',
                    cities: ['city-1'],
                },
                isoCode,
            });
            const newCourse = await getModels(
                isoCode,
            ).taskerTrainingCourse.findOne({
                title: mockCourseName,
            });

            expect(newCourse).toBeDefined();
            expect(newCourse?.cities).toEqual(['city-1']);

            // clear data inserted
            await getModels(isoCode).taskerTrainingCourse.deleteOne({
                title: mockCourseName,
            });
        });
    });

    describe('getQuizDetail', () => {
        it('Should return quiz detail correctly', async () => {
            const quiz = await getQuizDetail({
                isoCode,
                _id: mockQuizIds[0],
            });

            expect(quiz).toBeDefined();
        });
    });

    describe('getQuizCollectionDetail', () => {
        it('Should return quiz collection detail correctly', async () => {
            const quizCollection = await getQuizCollectionDetail({
                isoCode,
                _id: mockQuizCollectionIds[0],
            });

            expect(quizCollection).toBeDefined();
        });
    });

    describe('getCourseDetail', () => {
        it('Should return course detail correctly', async () => {
            const course = await getCourseDetail({
                isoCode,
                _id: mockCourseIds[0],
            });

            expect(course).toBeDefined();
        });
    });

    describe('updateQuiz', () => {
        it('Should update quiz correctly', async () => {
            const mockNameUpdated = 'updated-name';

            await updateQuiz({
                isoCode,
                quizId: mockQuizIds[0],
                params: {
                    name: mockNameUpdated,
                },
            });
            const quiz = await getModels(isoCode).taskerTrainingQuiz.findOne({
                title: mockNameUpdated,
            });

            expect(quiz).toBeDefined();
            expect(quiz?.updatedAt).toEqual(mockUpdatedAt);
        });
    });

    describe('updateQuizCollection', () => {
        it('Should update quiz collection correctly', async () => {
            const mockNameUpdated = 'updated-name';

            await updateQuizCollection({
                isoCode,
                _id: mockQuizCollectionIds[0],
                params: {
                    name: mockNameUpdated,
                },
            });
            const quizCollection = await getModels(
                isoCode,
            ).taskerTrainingQuizCollection.findOne({
                title: mockNameUpdated,
            });

            expect(quizCollection).toBeDefined();
            expect(quizCollection?.updatedAt).toEqual(mockUpdatedAt);
        });
    });

    describe('updateCourse', () => {
        const mockTaskerId = 'tasker-done-course';
        const mockTaskerId_2 = 'tasker-done-course-2';
        const mockServiceId = 'service-1';
        const mockCourseIdUpdate = 'course-update';
        const mockCourseStartDate = momentTz(
            '2024-10-23T08:00:00.000+07:00',
        ).toDate();

        beforeEach(async () => {
            await getModels(isoCode).taskerTrainingSubmission.create({
                _id: 'submission-1',
                status: SUBMISSION_STATUS_IN_TRAINING.PASSED,
                taskerId: mockTaskerId,
                course: {
                    _id: 'course-2',
                },
            });
            await getModels(isoCode).taskerTrainingSubmission.create({
                _id: 'submission-2',
                status: SUBMISSION_STATUS_IN_TRAINING.PASSED,
                taskerId: mockTaskerId_2,
                course: {
                    _id: mockCourseIdUpdate,
                },
            });
            await getModels(isoCode).taskerTrainingSubmission.create({
                _id: 'submission-3',
                status: SUBMISSION_STATUS_IN_TRAINING.PASSED,
                taskerId: mockTaskerId_2,
                course: {
                    _id: 'course-2',
                },
            });
            await getModels(isoCode).taskerTrainingCourseStartDate.create({
                _id: 'course-start-date-1',
                courseId: mockCourseIdUpdate,
                taskerId: mockTaskerId_2,
                startDate: mockCourseStartDate,
            });
            await getModels(isoCode).taskerTrainingCourseStartDate.create({
                _id: 'course-start-date-2',
                courseId: mockCourseIdUpdate,
                taskerId: mockTaskerId,
                startDate: momentTz('2024-10-23T08:00:00.000+07:00').toDate(),
                deadlineIn: 50,
            });
            await getModels(isoCode).taskerTrainingCourse.create({
                _id: mockCourseIdUpdate,
                title: 'course-update',
                createdAt: momentTz('2024-10-23T08:00:00.000+07:00').toDate(),
                type: 'TEST',
                condition: {
                    coursesMustBeCompleted: {
                        courseIds: ['course-1'],
                    },
                },
                relatedServices: [
                    {
                        _id: mockServiceId,
                        name: 'Service 1',
                    },
                ],
                deadlineIn: 50,
                createdByUsername: 'mock-username',
                createdByUserId: 'mock-user-id',
                status: 'ACTIVE',
                code: 'mock-code-2',
                cities: ['Hồ Chí Minh'],
            });
            await getModels(isoCode).serviceChannel.create({
                _id: 'service-channel-1',
                serviceId: mockServiceId,
                taskerList: [mockTaskerId, mockTaskerId_2],
            });
            await getModels(isoCode).users.create({
                _id: mockTaskerId,
                name: 'tasker done course',
                workingPlaces: [{ country: 'VN', city: 'Hồ Chí Minh' }],
                type: USER_TYPE.TASKER,
                language: 'vi',
                fAccountId: 'fAccountId1',
                status: 'ACTIVE',
                createdAt: momentTz('2023-10-01').toDate(),
            });
            await getModels(isoCode).users.create({
                _id: mockTaskerId_2,
                name: 'tasker done course 2',
                workingPlaces: [{ country: 'VN', city: 'Hồ Chí Minh' }],
                type: USER_TYPE.TASKER,
                language: 'vi',
                fAccountId: 'fAccountId2',
                status: 'ACTIVE',
                createdAt: momentTz('2023-10-01').toDate(),
            });
        });

        afterEach(async () => {
            await getModels(isoCode).taskerTrainingSubmission.deleteOne({
                _id: 'submission-1',
            });
            await getModels(isoCode).taskerTrainingSubmission.deleteOne({
                _id: 'submission-2',
            });
            await getModels(isoCode).taskerTrainingSubmission.deleteOne({
                _id: 'submission-3',
            });
            await getModels(isoCode).taskerTrainingCourse.deleteOne({
                _id: mockCourseIdUpdate,
            });
            await getModels(isoCode).serviceChannel.deleteOne({
                _id: 'service-channel-1',
            });
            await getModels(isoCode).users.deleteOne({
                _id: mockTaskerId,
            });
            await getModels(isoCode).users.deleteOne({
                _id: mockTaskerId_2,
            });
            await getModels(isoCode).taskerTrainingCourseStartDate.deleteOne({
                _id: 'course-start-date-1',
            });
            await getModels(isoCode).taskerTrainingCourseStartDate.deleteOne({
                _id: 'course-start-date-2',
            });
        });

        it('Should update course with condition must be completed another course', async () => {
            await updateCourse({
                isoCode,
                _id: mockCourseIdUpdate,
                params: {
                    condition: {
                        coursesMustBeCompleted: {
                            courseIds: ['course-2'],
                        },
                    },
                    deadlineIn: 100,
                    cities: ['Hồ Chí Minh'],
                    relatedServices: [
                        {
                            _id: mockServiceId,
                            name: 'Service 1',
                        },
                    ],
                },
            });

            const courseStartDate = await getModels(isoCode)
                .taskerTrainingCourseStartDate.findOne({
                    courseId: mockCourseIdUpdate,
                    taskerId: mockTaskerId,
                })
                .lean();

            expect(courseStartDate).toBeDefined();
            expect(courseStartDate?.courseId).toEqual(mockCourseIdUpdate);
            expect(courseStartDate?.deadlineIn).toEqual(100);
            // Course start date must be updated to current date
            expect(courseStartDate?.startDate).toEqual(momentTz().toDate());
        });

        it('Should update course with condition must be completed another course and that Tasker has already completed the course updated', async () => {
            await updateCourse({
                isoCode,
                _id: mockCourseIdUpdate,
                params: {
                    condition: {
                        coursesMustBeCompleted: {
                            courseIds: ['course-2'],
                        },
                    },
                    cities: ['Hồ Chí Minh'],
                    relatedServices: [
                        {
                            _id: mockServiceId,
                            name: 'Service 1',
                        },
                    ],
                },
            });

            const courseStartDate = await getModels(
                isoCode,
            ).taskerTrainingCourseStartDate.findOne({
                courseId: mockCourseIdUpdate,
                taskerId: mockTaskerId_2,
            });

            // Course start date must be not updated if tasker has already completed the course
            expect(courseStartDate).toBeDefined();
            expect(courseStartDate?.courseId).toEqual(mockCourseIdUpdate);
            expect(courseStartDate?.startDate).toEqual(mockCourseStartDate);
        });

        it('Should update course correctly', async () => {
            const mockNameUpdated = 'updated-name';

            await updateCourse({
                isoCode,
                _id: mockCourseIds[0],
                params: {
                    title: mockNameUpdated,
                    cities: ['city-1'],
                },
            });
            const course = await getModels(
                isoCode,
            ).taskerTrainingCourse.findOne({
                title: mockNameUpdated,
            });

            expect(course).toBeDefined();
            expect(course?.updatedAt).toEqual(mockUpdatedAt);
            expect(course?.cities).toEqual(['city-1']);
        });
    });

    describe('getListQuizDetailSortByOrder', () => {
        it('Should return quiz details and sort correctly', async () => {
            const quizzes = await getListQuizDetailSortByOrder({
                isoCode,
                quizzes: mockQuizIds.map((id, index) => ({
                    _id: id,
                    order: index,
                })),
            });

            expect(quizzes).toBeDefined();
        });
    });

    describe('getListQuizzesDetailSortByOrder', () => {
        it('Should return quiz collection detail and sort correctly', async () => {
            const quizCollection = await getListQuizzesDetailSortByOrder({
                isoCode,
                quizCollection: mockQuizCollectionIds.map((id, index) => ({
                    _id: id,
                    order: index,
                })),
            });

            expect(quizCollection).toBeDefined();
        });
    });
    describe('getServicesByTaskerId', () => {
        const taskerIds = ['mockTaskerId1', 'mockTaskerId2', 'mockTaskerId3'];
        const servicesIds = ['serviceId1', 'serviceId2', 'serviceId3'];
        const serviceInfo = {
            name: 'Service Name',
            text: {
                vi: 'VN Service',
                th: 'TH Service',
            },
            icon: 'icon.com',
        };

        const taskers = taskerIds.map(taskerId => ({
            _id: taskerId,
            fAccountId: 'fAccountId',
            language: 'vi',
            status: 'ACTIVE',
            type: USER_TYPE.TASKER,
            createdAt: new Date(),
        }));

        const serviceChannels = [
            {
                _id: 'channel1',
                serviceId: servicesIds[0],
                taskerList: [taskerIds[0], taskerIds[1]],
            },
            {
                _id: 'channel2',
                serviceId: servicesIds[1],
                taskerList: [taskerIds[0], taskerIds[2]],
            },
            {
                _id: 'channel3',
                serviceId: servicesIds[2],
                taskerList: [taskerIds[1], taskerIds[2]],
            },
        ];

        const services = servicesIds.map((serviceId, index) => ({
            _id: serviceId,
            name: `${serviceInfo.name}_${index}`,
            text: serviceInfo.text,
            icon: serviceInfo.icon,
            status: 'ACTIVE',
        }));

        beforeEach(async () => {
            await getModels(isoCode).users.insertMany(taskers);
            await getModels(isoCode).serviceChannel.insertMany(serviceChannels);
            await getModels(isoCode).service.insertMany(services);
        });

        afterEach(async () => {
            await getModels(isoCode).users.deleteMany({
                _id: taskers.map(tasker => tasker._id),
            });
            await getModels(isoCode).serviceChannel.deleteMany({
                _id: serviceChannels.map(serviceChannel => serviceChannel._id),
            });
            await getModels(isoCode).service.deleteMany({
                _id: services.map(service => service._id),
            });
        });
        it('Should get services by tasker id successfully', async () => {
            const params = {
                isoCode,
                taskerId: taskerIds[1],
            };

            const services = await getServicesByTaskerId(params);

            const serviceChannelFoundByTaskerId = serviceChannels.filter(
                serviceChannel =>
                    !!serviceChannel.taskerList.includes(params.taskerId),
            );

            const servicesIdsFromFoundChannels =
                serviceChannelFoundByTaskerId.map(channel => channel.serviceId);

            const expectationServices = services.filter(service =>
                servicesIdsFromFoundChannels.includes(service._id),
            );

            expect(services).toHaveLength(expectationServices.length);
            services.forEach(service => {
                expect(expectationServices).toContain(service);
            });
        });
    });
    describe('getListTrainingHistory', () => {
        it('Should get list training histories successfully', async () => {
            const params = {
                isoCode,
                taskerId: 'taskerId1',
                filter: {
                    rangeDate: {
                        from: new Date('2024-01-01'),
                        to: new Date('2024-05-01'),
                    },
                    visibilities: [COURSE_VISIBILITY_IN_TRAINING.BLOCKED],
                    completions: [COURSE_COMPLETION_IN_TRAINING.IN_PROGRESS],
                    statuses: [SUBMISSION_STATUS_IN_TRAINING.PASSED],
                },
                sort: {
                    updatedAt: 1,
                } as PipelineStage.Sort['$sort'],
                pageSize: 10,
                pageIndex: 1,
            };

            const trainingHistoriesFetchingAPI =
                await getListTrainingHistory(params);

            expect(trainingHistoriesFetchingAPI).toBeTruthy();
        });
    });
    describe('getNameOfCourseByIds', () => {
        it('Should return list name of course correctly', async () => {
            const nameOfCourses = await getNameOfCourseByIds({
                isoCode,
                courseIds: mockCourseIds,
            });

            expect(nameOfCourses).toBeDefined();
        });
    });
});
