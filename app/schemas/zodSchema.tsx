import {
  MAXIM<PERSON>_CODE_LENGTH,
  MAXIMUM_IMAGE_FILE_LENGTH_SPECIAL_CAMPAIGN,
  MAXIMUM_IMAGE_FILE_LENGTH_TRAINING,
} from 'btaskee-constants';
import { isContainsSingleURL, validateAlphanumeric } from 'btaskee-utils';
import type { TFunction } from 'i18next';
import { z } from 'zod';

const zodImageWithDescriptionSchema = (t: TFunction<string, undefined>) => {
  return {
    newImage: z
      .object({
        value: z
          .custom(file => file instanceof File, {
            message: t('IMAGE_IS_REQUIRED'),
          })
          .refine(
            file =>
              file instanceof File &&
              file.size <= MAXIMUM_IMAGE_FILE_LENGTH_TRAINING.VALUE,
            {
              message: t('FILE_SIZE_TOO_LARGE', {
                size: MAXIMUM_IMAGE_FILE_LENGTH_TRAINING.DISPLAY_TEXT,
              }),
            },
          ),
        description: z.string().min(3, t('MINIMUM_CHARACTERS', { number: 3 })),
      })
      .nullable(),
    updateImage: z
      .object({
        value: z
          .custom(file => file instanceof File, {
            message: t('IMAGE_IS_REQUIRED'),
          })
          .refine(
            file =>
              file instanceof File &&
              file.size <= MAXIMUM_IMAGE_FILE_LENGTH_TRAINING.VALUE,
            {
              message: t('FILE_SIZE_TOO_LARGE', {
                size: MAXIMUM_IMAGE_FILE_LENGTH_TRAINING.DISPLAY_TEXT,
              }),
            },
          )
          .or(z.string().url()),
        description: z.string().min(3, t('MINIMUM_CHARACTERS', { number: 3 })),
      })
      .nullable(),
  };
};

const zodAnswersSchema = (t: TFunction<string, undefined>) => {
  return z
    .array(
      z.object({
        _id: z.string(),
        content: z.string().min(1, t('MINIMUM_CHARACTERS', { number: 1 })),
        isCorrect: z.boolean(),
      }),
    )
    .min(2, t('MINIMUM_ANSWER', { number: 2 }))
    .refine(answers => answers?.some(answer => answer.isCorrect), {
      message: t('AT_LEAST_ONE_CORRECT_ANSWER'),
    });
};

const zodCodeSchema = (t: TFunction<string, undefined>) => {
  return z
    .string()
    .min(3, t('MINIMUM_CHARACTERS', { number: 3 }))
    .max(
      MAXIMUM_CODE_LENGTH,
      t('MAXIMUM_CHARACTERS', { number: MAXIMUM_CODE_LENGTH }),
    )
    .refine(val => validateAlphanumeric(val), {
      message: t('MUST_NOT_CONTAIN_SPACES_OR_SPECIAL_CHARACTER'),
    });
};

const zodVideoWithDescriptionSchema = (t: TFunction<string, undefined>) => {
  return z
    .object({
      url: z
        .string()
        .url(t('MUST_BE_A_VALID_URL'))
        .refine(val => isContainsSingleURL(val), {
          message: t('MUST_BE_A_VALID_URL'),
        }),
      // description: z.array(z.any()).nonempty({
      //   message: t('DESCRIPTION_MUST_NOT_BE_EMPTY'),
      // }),
      description: z.string().min(3, t('MINIMUM_CHARACTERS', { number: 3 })),
    })
    .nullable();
};

const zodTimeToCompleteByMinutesSchema = (t: TFunction<string, undefined>) => {
  return z
    .object({
      hours: z.number(),
      minutes: z.number(),
    })
    .refine(data => data.hours > 0 || data.minutes > 0, {
      message: t('MIN_MINUTE_TO_COMPLETE', { time: 1 }),
    });
};

const zodSpecialCampaignFormSchema = (t: TFunction<string, undefined>) => {
  return {
    rangeDate: z.object({
      from: z.date(),
      to: z.date(),
    }),
    name: z.string().min(1, t('REQUIRED')),
    image: z.object({
      imageUrl: z
        .custom(file => file instanceof File, {
          message: t('IMAGE_IS_REQUIRED'),
        })
        .refine(
          file =>
            file instanceof File &&
            file.size <= MAXIMUM_IMAGE_FILE_LENGTH_SPECIAL_CAMPAIGN.VALUE,
          {
            message: t('FILE_SIZE_TOO_LARGE', {
              size: MAXIMUM_IMAGE_FILE_LENGTH_SPECIAL_CAMPAIGN.DISPLAY_TEXT,
            }),
          },
        )
        .or(z.string().url()),
    }),
    type: z.string().min(1, t('REQUIRED')),
    status: z.string().min(1, t('REQUIRED')),
    value: z.coerce.number().min(1, t('VALUE_MUST_BE_GREATER_THAN_ZERO')),
    text: z.record(
      z.object({
        name: z.string().min(1, t('REQUIRED')),
        description: z.string().min(1, t('REQUIRED')),
      }),
    ),
    rewards: z.array(
      z.object({
        type: z.string().min(1, t('REQUIRED')),
        amount: z.coerce.number().min(1, t('VALUE_MUST_BE_GREATER_THAN_ZERO')),
        applyAccountType: z.string().optional(),
        taskerJourneyLevels: z.array(z.string()).optional(),
        minRateTask: z.coerce.number().optional(),
        applyForServices: z.array(z.string()).optional(),
      }),
    ),
    city: z.array(z.string()).optional(),
  };
};

export {
  zodAnswersSchema,
  zodCodeSchema,
  zodImageWithDescriptionSchema,
  zodSpecialCampaignFormSchema,
  zodTimeToCompleteByMinutesSchema,
  zodVideoWithDescriptionSchema,
};
