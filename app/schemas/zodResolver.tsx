import { zodResolver } from '@hookform/resolvers/zod';
import type { TFunction } from 'i18next';
import { z } from 'zod';
import {
  zodCodeSchema,
  zodImageWithDescriptionSchema,
  zodTimeToCompleteByMinutesSchema,
  zodVideoWithDescriptionSchema,
} from '~/schemas/zodSchema';

export const zodQuizzesFormResolver = ({
  t,
  isEdit = false,
  totalSelectedQuizzes = 0,
}: {
  t: TFunction<string, undefined>;
  isEdit: boolean;
  totalSelectedQuizzes: number;
}) => {
  const imageSchema = zodImageWithDescriptionSchema(t);
  return zodResolver(
    z.object({
      code: zodCodeSchema(t),
      name: z.string().min(3, t('MINIMUM_CHARACTERS', { number: 3 })),
      description: z.string().min(3, t('MINIMUM_CHARACTERS', { number: 3 })),
      numberOfDisplayQuizzes: z.coerce
        .number()
        .min(1, t('MUST_HAVE_AT_LEAST_ONE_QUIZ'))
        .max(
          totalSelectedQuizzes,
          t('MAXIMUM_OF_QUESTIONS', { number: totalSelectedQuizzes }),
        ),
      isRandomQuizzes: z.boolean().optional(),
      timeToCompleteByMinutes: zodTimeToCompleteByMinutesSchema(t),
      image: isEdit ? imageSchema.updateImage : imageSchema.newImage,
      video: zodVideoWithDescriptionSchema(t),
      quizzes: z.array(z.any()).min(1, t('MUST_HAVE_AT_LEAST_ONE_QUIZ')),
      csvUpload: z.array(z.any()).nullable(),
    }),
  );
};
