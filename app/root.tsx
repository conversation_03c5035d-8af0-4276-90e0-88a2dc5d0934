import type {
  ActionFunctionArgs,
  LinksFunction,
  LoaderFunctionArgs,
} from '@remix-run/node';
import { json, redirect } from '@remix-run/node';
import {
  Links,
  LiveReload,
  Meta,
  Outlet,
  Scripts,
  ScrollRestoration,
  useLoaderData,
  useNavigate,
  useRouteError,
} from '@remix-run/react';
import { ROUTE_NAME } from 'btaskee-constants';
import {
  AlertDialogProvider,
  BtaskeeResponseError,
  Button,
  LoadingGlobal,
  ToasterBase,
} from 'btaskee-ui';
import { HomeIcon } from 'lucide-react';
import { useTranslation } from 'react-i18next';
import { useChangeLanguage } from 'remix-i18next/react';
import { type ErrorResponse, hocAction } from '~/hoc/remix';
import { getUserProfile, setUserLanguage } from '~/services/settings.server';

import { getUserId } from './services/helpers.server';
import styles from './tailwind.css';

export const action = hocAction(async ({ request }: ActionFunctionArgs) => {
  const formData = await request.clone().formData();
  const {
    language,
    name,
    redirect: redirectPath,
  } = Object.fromEntries(formData);

  if (name === 'changeLanguage' && typeof language === 'string') {
    const userId = await getUserId({ request });
    await setUserLanguage({ language, userId });

    return redirect(`${redirectPath}`);
  }

  return null;
});

export const loader = async ({ request }: LoaderFunctionArgs) => {
  const userId = await getUserId({ request });
  const userProfile = await getUserProfile(userId);

  return json({ locale: userProfile?.language });
};

export const links: LinksFunction = () => {
  return [
    {
      rel: 'stylesheet',
      href: styles,
    },
    {
      rel: 'stylesheet',
      href: 'https://fonts.googleapis.com/css2?family=Inter:wght@400;500;600;700&display=swap',
    },
  ];
};

export const handle = { i18n: 'common' };

export function ErrorBoundary() {
  const navigate = useNavigate();
  const { t: tCommon } = useTranslation('common');

  const error = useRouteError() as ErrorResponse;

  if (!error || !error.status || !error.statusText) {
    return null;
  }

  return (
    <html lang="en">
      <head>
        <title>bTaskee Tasker - Oh no!</title>
        <Meta />
        <Links />
      </head>
      <body>
        <div className="flex flex-col items-center justify-center h-screen bg-white">
          <BtaskeeResponseError t={tCommon} errorStatus={error.status} />
          <Button
            className="mt-10 gap-2 items-center"
            onClick={() => {
              navigate(-1);
            }}>
            <HomeIcon /> {tCommon('GO_BACK')}
          </Button>
        </div>
        <Scripts />
      </body>
    </html>
  );
}

export default function App() {
  const loaderData = useLoaderData<typeof loader>();
  useChangeLanguage(loaderData?.locale || 'en');

  return (
    <html lang={loaderData?.locale}>
      <head>
        <title>bTaskee Tasker</title>
        <meta charSet="utf-8" />
        <meta name="viewport" content="width=device-width, initial-scale=1" />
        <Meta />
        <Links />
      </head>
      <body className="font-sans">
        <LoadingGlobal />
        <AlertDialogProvider>
          <Outlet />
        </AlertDialogProvider>
        <ScrollRestoration
          getKey={location => {
            const paths = [ROUTE_NAME.CREATE_QUIZ_COLLECTION];
            return paths.includes(location.pathname)
              ? //Restore by pathname to keep the users scroll position
                location.pathname
              : // everything else by location like the browser
                location.key;
          }}
        />
        <Scripts />
        <ToasterBase />
        <LiveReload />
      </body>
    </html>
  );
}
