import {
    type LoaderFunctionArgs,
    type Serialize<PERSON>rom,
    json,
} from '@remix-run/node';
import { useOutletContext } from '@remix-run/react';
import {
    PERMISSIONS,
    TASKER_ONBOARDING_PROCESS_STATUS,
    TASKER_PROFILE_TAB,
    res403,
} from 'btaskee-constants';
import {
    DEFAULT_RANGE_DATE_CURRENT_DAY,
    convertSortString,
    getPageSizeAndPageIndex,
    getSkipAndLimit,
    getValuesFromSearchParams,
} from 'btaskee-utils';
import { getCitiesByUserId, getUserSession } from '~/services/helpers.server';
import { verifyManager } from '~/services/role-base-access-control.server';
import {
    getTaskerProfile,
    getTotalTaskerProfile,
} from '~/services/tasker-profile.server';

export const willBecomeTaskerOnboardingProfileLoader = async (
    { request }: LoaderFunctionArgs,
    { permissionsPassed }: { permissionsPassed: BtaskeePermissions['key'][] },
) => {
    const { isoCode, isSuperUser, userId } = await getUserSession({
        headers: request.headers,
    });
    const isManager = await verifyManager(userId);
    const userCities = await getCitiesByUserId({
        userId,
        isManager: isManager || isSuperUser,
    });

    const url = new URL(request.url);
    const [
        { tabId, createdAt, status, searchText, sort: sortString, cities },
        { pageSize, pageIndex },
    ] = getValuesFromSearchParams(url.searchParams, {
        keysString: [
            'tabId',
            'createdAt',
            'status',
            'searchText',
            'sort',
            'cities',
        ],
        keysNumber: ['pageSize', 'pageIndex'],
    });

    const statusFilterValues = Object.values(
        TASKER_ONBOARDING_PROCESS_STATUS,
    )?.filter(profileStatus => status.split(',')?.includes(profileStatus));

    const tabsFound: typeof TASKER_PROFILE_TAB = [];

    permissionsPassed.forEach(permissionPassed => {
        const tabFound = TASKER_PROFILE_TAB.find(
            tab => tab.permission === permissionPassed,
        );

        if (tabFound) {
            tabsFound.push(tabFound);
        }
    });

    const profileFoundByTabId =
        tabsFound.find(tab => tab.tabId === tabId) || tabsFound[0];

    const filteredValue = {
        status: statusFilterValues?.length
            ? statusFilterValues
            : profileFoundByTabId.status,
        createdAt: DEFAULT_RANGE_DATE_CURRENT_DAY(createdAt),
        searchText,
        cities: cities ? cities.split(',') : userCities,
        isoCode,
    };

    const permissionsFound = [];

    [
        PERMISSIONS.READ_VERIFYING_STEP_ON_TASKER_ONBOARDING,
        PERMISSIONS.READ_APPROVED_STEP_ON_TASKER_ONBOARDING,
        PERMISSIONS.READ_ELIMINATED_STEP_ON_TASKER_ONBOARDING,
        PERMISSIONS.READ_REJECTED_STEP_ON_TASKER_ONBOARDING,
    ].forEach(permission => {
        if (permissionsPassed.includes(permission)) {
            permissionsFound.push(permission);
        }
    });

    if (!permissionsFound.length) {
        throw new Response(null, res403);
    }

    const totalProfile = await getTotalTaskerProfile(filteredValue);

    const taskerProfiles = await getTaskerProfile({
        ...filteredValue,
        ...getSkipAndLimit(
            getPageSizeAndPageIndex({
                total: totalProfile,
                pageSize,
                pageIndex,
            }),
        ),
        sort: convertSortString({
            sortString,
            defaultValue: { updatedAt: -1 },
        }),
    });

    return json({
        taskerProfiles,
        totalProfile,
        userCities,
        filteredValue,
        tabId: profileFoundByTabId.tabId,
        status: profileFoundByTabId.status,
        permissions: permissionsPassed,
    });
};

export const useOutletGetTaskerOnboardingProfile = () => {
    return useOutletContext<
        SerializeFrom<typeof willBecomeTaskerOnboardingProfileLoader>
    >();
};
