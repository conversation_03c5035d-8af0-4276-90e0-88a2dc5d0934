import { json } from '@remix-run/node';
import type { LoaderFunctionArgs, SerializeFrom } from '@remix-run/node';
import { useOutletContext } from '@remix-run/react';
import { hoc404 } from '~/hoc/remix';
import { getCitiesByUserId, getUserSession } from '~/services/helpers.server';
import { getOfficeDetailByIndexInSetting } from '~/services/offices-configuration.server';
import { getSession } from '~/services/session.server';

export const willBecomeOfficesConfigurationDetailLoader = async ({
    request,
    params,
}: LoaderFunctionArgs) => {
    const { isoCode, userId, isSuperUser } = await getUserSession({
        headers: request.headers,
    });

    const [officeDetail, userCities, session] = await Promise.all([
        hoc404(() =>
            getOfficeDetailByIndexInSetting({
                isoCode,
                indexOffice: Number(params.indexOffice),
            }),
        ),
        getCitiesByUserId({
            userId,
            isManager: isSuperUser,
        }),
        getSession(request.headers.get('cookie')),
    ]);

    const flashMessage = session.get('flashMessage') || '';

    return json({
        userCities,
        indexOffice: Number(params.indexOffice),
        flashMessage,
        ...officeDetail,
    });
};

export const useOutletOfficeConfigurationDetail = () =>
    useOutletContext<
        SerializeFrom<typeof willBecomeOfficesConfigurationDetailLoader>
    >();
