import {
    type LoaderFunctionArgs,
    type Serialize<PERSON>rom,
    json,
} from '@remix-run/node';
import { useOutletContext } from '@remix-run/react';
import {
    DEFAULT_RANGE_DATE_CURRENT_DAY,
    convertSortString,
    getPageSizeAndPageIndex,
    getSkipAndLimit,
    getValuesFromSearchParams,
} from 'btaskee-utils';
import { getUserSession } from '~/services/helpers.server';
import {
    getTotalTrainingHistoryAllTasker,
    getTrainingHistoryAllTasker,
} from '~/services/training-history-all-tasker.server';

export const willBecomeOverallTrainingHistoryLoader = async ({
    request,
}: LoaderFunctionArgs) => {
    const { isoCode } = await getUserSession({ headers: request.headers });
    const url = new URL(request.url);

    const [
        { sort: sortString, status, createdAt: rangeDate, search },
        { pageSize, pageIndex },
    ] = getValuesFromSearchParams(url.searchParams, {
        keysString: ['sort', 'status', 'createdAt', 'search'],
        keysNumber: ['pageSize', 'pageIndex'],
    });

    const filteredValue = {
        search,
        status,
        rangeDate: DEFAULT_RANGE_DATE_CURRENT_DAY(rangeDate),
    };

    const totalTrainingHistory = await getTotalTrainingHistoryAllTasker({
        isoCode,
        filter: filteredValue,
    });

    const trainingHistories = await getTrainingHistoryAllTasker({
        isoCode,
        filter: filteredValue,
        ...getSkipAndLimit(
            getPageSizeAndPageIndex({
                total: totalTrainingHistory,
                pageSize,
                pageIndex,
            }),
        ),
        sort: convertSortString({
            sortString,
            defaultValue: { createdAt: -1 },
        }),
    });

    return json({
        trainingHistories,
        filteredValue,
        totalTrainingHistory,
    });
};

export const useOutletOverallTrainingHistoryProfile = () => {
    return useOutletContext<
        SerializeFrom<typeof willBecomeOverallTrainingHistoryLoader>
    >();
};
