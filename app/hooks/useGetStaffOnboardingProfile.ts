import {
    type LoaderFunctionArgs,
    type Serialize<PERSON>rom,
    json,
} from '@remix-run/node';
import { useOutletContext } from '@remix-run/react';
import { STAFF_TAB, TASKER_ONBOARDING_PROCESS_STATUS } from 'btaskee-constants';
import {
    DEFAULT_RANGE_DATE_CURRENT_DAY,
    convertSortString,
    getPageSizeAndPageIndex,
    getSkipAndLimit,
    getValuesFromSearchParams,
} from 'btaskee-utils';
import { getCitiesByUserId, getUserSession } from '~/services/helpers.server';
import { verifyManager } from '~/services/role-base-access-control.server';
import {
    getEmployeeProfile,
    getTotalEmployeeProfile,
} from '~/services/tasker-profile.server';

export const willBecomeStaffOnboardingProfileLoader = async (
    { request }: LoaderFunctionArgs,
    { permissionsPassed }: { permissionsPassed: BtaskeePermissions['key'][] },
) => {
    const { isoCode, isSuperUser, userId } = await getUserSession({
        headers: request.headers,
    });
    const url = new URL(request.url);

    const [
        { tabId, createdAt, status, searchText, sort: sortString },
        { pageSize, pageIndex },
    ] = getValuesFromSearchParams(url.searchParams, {
        keysString: ['tabId', 'createdAt', 'status', 'searchText', 'sort'],
        keysNumber: ['pageSize', 'pageIndex'],
    });
    const isManager = await verifyManager(userId);
    const cities = await getCitiesByUserId({
        userId,
        isManager: isManager || isSuperUser,
    });

    const statusFilterValues = Object.values(
        TASKER_ONBOARDING_PROCESS_STATUS,
    )?.filter(profileStatus => status.split(',')?.includes(profileStatus));

    const tabsFound: typeof STAFF_TAB = [];

    permissionsPassed.forEach(permissionPassed => {
        const tabFound = STAFF_TAB.find(
            tab => tab.permission === permissionPassed,
        );

        if (tabFound) {
            tabsFound.push(tabFound);
        }
    });

    const profileFoundByTabId =
        tabsFound.find(tab => tab.tabId === tabId) || tabsFound[0];

    const filterValue = {
        status: statusFilterValues?.length
            ? statusFilterValues
            : profileFoundByTabId.status?.slice(0, 1),
        createdAt: DEFAULT_RANGE_DATE_CURRENT_DAY(createdAt),
        searchText,
        cities,
        isoCode,
    };

    const total = await getTotalEmployeeProfile(filterValue);

    const data = await getEmployeeProfile({
        ...filterValue,
        ...getSkipAndLimit(
            getPageSizeAndPageIndex({
                total,
                pageSize,
                pageIndex,
            }),
        ),
        sort: convertSortString({
            sortString,
            defaultValue: { updatedAt: -1 },
        }),
    });

    return json({
        data,
        total,
        filterValue,
        tabId: profileFoundByTabId.tabId,
        status: profileFoundByTabId.status,
        permissions: permissionsPassed,
    });
};

export const useOutletGetStaffOnboardingProfile = () => {
    return useOutletContext<
        SerializeFrom<typeof willBecomeStaffOnboardingProfileLoader>
    >();
};
