import {
    type LoaderFunctionArgs,
    type Serialize<PERSON>rom,
    json,
} from '@remix-run/node';
import { useOutletContext } from '@remix-run/react';
import {
    DEFAULT_RANGE_DATE_CURRENT_DAY,
    convertSortString,
    getPageSizeAndPageIndex,
    getSkipAndLimit,
    getValuesFromSearchParams,
} from 'btaskee-utils';
import { getUserSession } from '~/services/helpers.server';
import {
    getAddressOnGoogleMapBySearchingText,
    getTaskerLocationDetected,
    getTotalTaskerLocationDetected,
} from '~/services/location-detected.server';

export const willBecomeLoaderTaskerLocationDetected = async ({
    request,
}: LoaderFunctionArgs) => {
    const url = new URL(request.url);
    const { isoCode } = await getUserSession(request);

    const DEFAULT_RADIUS_FILTERING_TASKER = 200;
    const [
        { sort: sortString, rangeDate, address, geoCoordinates, searchText },
        { pageSize, pageIndex, radius },
    ] = getValuesFromSearchParams(url.searchParams, {
        keysString: [
            'sort',
            'rangeDate',
            'searchText',
            'address',
            'geoCoordinates',
        ],
        keysNumber: ['pageSize', 'pageIndex', 'radius'],
    });

    const filterValues = {
        rangeDate: DEFAULT_RANGE_DATE_CURRENT_DAY(rangeDate ? rangeDate : ''),
        location: geoCoordinates ? JSON.parse(geoCoordinates) : undefined,
        radius: radius || DEFAULT_RADIUS_FILTERING_TASKER,
        searchText,
    };

    const [locations, totalTasker] = await Promise.all([
        getAddressOnGoogleMapBySearchingText({
            isoCode,
            searchText: address,
        }),
        getTotalTaskerLocationDetected({
            isoCode,
            ...filterValues,
        }),
    ]);

    const { limit, skip } = getSkipAndLimit(
        getPageSizeAndPageIndex({
            total: totalTasker,
            pageSize,
            pageIndex,
        }),
    );

    const taskers = await getTaskerLocationDetected({
        isoCode,
        ...filterValues,
        sort: convertSortString({
            sortString,
            defaultValue: { updatedAt: -1 },
        }),
        skip,
        limit,
    });

    return json({
        locations,
        taskers,
        totalTasker,
        filterValues,
        searchText,
        address,
    });
};

export const useOutletTaskerLocationDetected = () => {
    return useOutletContext<
        SerializeFrom<typeof willBecomeLoaderTaskerLocationDetected>
    >();
};
