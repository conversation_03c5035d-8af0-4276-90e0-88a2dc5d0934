import {
    type LoaderFunctionArgs,
    type Serialize<PERSON>rom,
    json,
} from '@remix-run/node';
import { useOutletContext } from '@remix-run/react';
import {
    DEFAULT_RANGE_DATE_CURRENT_DAY,
    convertSortString,
    defaultPageSize,
    findClosest,
    getValuesFromSearchParams,
} from 'btaskee-utils';
import { getUserSession } from '~/services/helpers.server';
import { getServices } from '~/services/service.server';
import type { ListTrainingHistory } from '~/services/tasker-training.server';
import {
    getListTrainingHistory,
    getServicesByTaskerId,
} from '~/services/tasker-training.server';

export const willBecomeTaskerTrainingHistoryLoader = async ({
    request,
    params,
}: LoaderFunctionArgs) => {
    const { isoCode } = await getUserSession({
        headers: request.headers,
    });
    const url = new URL(request.url);

    const [
        {
            sort: sortString,
            status,
            updatedAt: rangeDate,
            search,
            completion,
            visibility,
            services: filteredServices,
        },
        { pageSize, pageIndex },
    ] = getValuesFromSearchParams(url.searchParams, {
        keysString: [
            'sort',
            'status',
            'updatedAt',
            'search',
            'completion',
            'visibility',
            'services',
        ],
        keysNumber: ['pageSize', 'pageIndex'],
    });

    const pageSizeVerified = findClosest({
        arr: [...defaultPageSize],
        valueChecking: pageSize,
        defaultValue: defaultPageSize[0],
    });
    const DEFAULT_PAGE_INDEX_FROM_API = 0;

    const filterCourse: Omit<ListTrainingHistory, 'sort' | 'skip' | 'limit'> = {
        isoCode,
        taskerId: params?.id || '',
        pageSize: pageSize || pageSizeVerified,
        pageIndex: pageIndex || DEFAULT_PAGE_INDEX_FROM_API,
        filter: {
            ...(rangeDate
                ? { rangeDate: DEFAULT_RANGE_DATE_CURRENT_DAY(rangeDate) }
                : {}),
        },
    };

    if (search) {
        filterCourse.filter.search = search;
    }

    if (visibility) {
        filterCourse.filter.visibilities = visibility.split(',');
    }

    if (completion) {
        filterCourse.filter.completions = completion.split(',');
    }

    if (status) {
        filterCourse.filter.statuses = status.split(',');
    }

    if (filteredServices) {
        filterCourse.filter.services = filteredServices.split(',');
    }

    const courses = await getListTrainingHistory({
        ...filterCourse,
        sort: convertSortString({
            sortString,
            defaultValue: { updatedAt: -1 },
        }),
    });

    const taskerServices = await getServicesByTaskerId({
        isoCode,
        taskerId: params?.id || '',
    });

    const services = await getServices({
        isoCode,
        projection: { name: 1, text: 1, icon: 1, isSubscription: 1 },
    });

    return json({
        total: courses.total || 0,
        data: courses.data || [],
        taskerId: params?.id || '',
        filterCourse,
        services,
        taskerServices,
    });
};

export const useOutletGetTaskerTrainingHistoryProfile = () => {
    return useOutletContext<
        SerializeFrom<typeof willBecomeTaskerTrainingHistoryLoader>
    >();
};
