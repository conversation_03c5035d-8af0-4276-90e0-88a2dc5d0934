import type { LoaderFunctionArgs } from '@remix-run/node';
import { json } from '@remix-run/node';
import { useOutletContext } from '@remix-run/react';
import {
    DEFAULT_RANGE_DATE_CURRENT_DAY,
    convertSortString,
    getPageSizeAndPageIndex,
    getSkipAndLimit,
    getValuesFromSearchParams,
} from 'btaskee-utils';
import { getUserSession } from '~/services/helpers.server';
import {
    getListSpecialCampaign,
    getTotalSpecialCampaign,
} from '~/services/special-campaign.server';

export const willBecomeSpecialCampaignLoader = async ({
    request,
}: LoaderFunctionArgs) => {
    const { isoCode } = await getUserSession({ headers: request.headers });
    const url = new URL(request.url);

    const [
        { sort: sortString, status, updatedAt: rangeDate, search, type },
        { pageSize, pageIndex },
    ] = getValuesFromSearchParams(url.searchParams, {
        keysString: ['sort', 'status', 'updatedAt', 'search', 'type'],
        keysNumber: ['pageSize', 'pageIndex'],
    });

    const filterValue = {
        search,
        type,
        status,
        rangeDate: DEFAULT_RANGE_DATE_CURRENT_DAY(rangeDate),
    };

    const total = await getTotalSpecialCampaign({
        isoCode,
        filter: filterValue,
    });

    const { limit, skip } = getSkipAndLimit(
        getPageSizeAndPageIndex({
            total,
            pageSize,
            pageIndex,
        }),
    );

    const specialCampaigns = await getListSpecialCampaign({
        isoCode,
        filter: filterValue,
        skip,
        limit,
        sort: convertSortString({
            sortString,
            defaultValue: { updatedAt: -1 },
        }),
    });

    return json({
        total,
        filterValue,
        specialCampaigns,
    });
};

export const useOutletSpecialCampaign = () => {
    return useOutletContext<typeof willBecomeSpecialCampaignLoader>();
};
