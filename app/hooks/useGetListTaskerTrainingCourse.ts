import {
    type LoaderFunctionArgs,
    type Serialize<PERSON>rom,
    json,
} from '@remix-run/node';
import { useOutletContext } from '@remix-run/react';
import {
    DEFAULT_RANGE_DATE_CURRENT_DAY,
    convertSortString,
    getPageSizeAndPageIndex,
    getSkipAndLimit,
    getValuesFromSearchParams,
} from 'btaskee-utils';
import { getUserSession } from '~/services/helpers.server';
import { getServices } from '~/services/service.server';
import {
    getListCourses,
    getTotalCourses,
} from '~/services/tasker-training.server';

export const willBecomeTaskerTrainingCourseLoader = async ({
    request,
}: LoaderFunctionArgs) => {
    const { isoCode } = await getUserSession({ headers: request.headers });
    const url = new URL(request.url);

    const [
        {
            sort: sortString,
            status,
            updatedAt: rangeDate,
            search,
            type,
            service,
        },
        { pageSize, pageIndex },
    ] = getValuesFromSearchParams(url.searchParams, {
        keysString: [
            'sort',
            'status',
            'updatedAt',
            'search',
            'type',
            'service',
        ],
        keysNumber: ['pageSize', 'pageIndex'],
    });

    const filterValue = {
        search,
        type,
        status,
        service,
        ...(rangeDate
            ? { rangeDate: DEFAULT_RANGE_DATE_CURRENT_DAY(rangeDate) }
            : {}),
    };

    const [total, services] = await Promise.all([
        getTotalCourses({
            isoCode,
            filter: filterValue,
        }),
        getServices({
            isoCode,
            projection: { name: 1, text: 1, icon: 1, isSubscription: 1 },
        }),
    ]);

    const { limit, skip } = getSkipAndLimit(
        getPageSizeAndPageIndex({
            total,
            pageSize,
            pageIndex,
        }),
    );

    const courses = await getListCourses({
        isoCode,
        filter: filterValue,
        skip,
        limit,
        sort: convertSortString({
            sortString,
            defaultValue: { updatedAt: -1 },
        }),
        projection: {
            code: 1,
            title: 1,
            type: 1,
            status: 1,
            relatedServices: 1,
            updatedByUsername: 1,
            updatedAt: 1,
            createdByUsername: 1,
        },
    });

    return json({
        total,
        filterValue,
        courses,
        services,
    });
};

export const useOutletTaskerTrainingCourse = () => {
    return useOutletContext<
        SerializeFrom<typeof willBecomeTaskerTrainingCourseLoader>
    >();
};
