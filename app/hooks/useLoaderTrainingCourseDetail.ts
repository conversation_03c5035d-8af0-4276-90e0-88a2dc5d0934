import {
    type LoaderFunctionArgs,
    type Serialize<PERSON>rom,
    json,
} from '@remix-run/node';
import { useOutletContext } from '@remix-run/react';
import { hoc404 } from '~/hoc/remix';
import { getUserSession } from '~/services/helpers.server';
import { getServices } from '~/services/service.server';
import {
    getCourseDetail,
    getListQuizzesDetailSortByOrder,
    getNameOfCourseByIds,
} from '~/services/tasker-training.server';

export const willBecomeTaskerTrainingCourseDetailLoader = async ({
    request,
    params,
}: LoaderFunctionArgs) => {
    const { isoCode } = await getUserSession({
        headers: request.headers,
    });

    const course = await hoc404(() =>
        getCourseDetail({ isoCode, _id: params?.id || '' }),
    );

    const [courseNames, quizCollection, services] = await Promise.all([
        getNameOfCourseByIds({
            isoCode,
            courseIds:
                course?.condition?.coursesMustBeCompleted?.courseIds || [],
        }),
        getListQuizzesDetailSortByOrder({
            isoCode,
            quizCollection: course?.quizCollections || [],
        }),
        getServices({
            isoCode,
            projection: { name: 1, text: 1, icon: 1, isSubscription: 1 },
        }),
    ]);

    return json({
        data: course,
        quizCollection,
        services,
        courseNames,
        courseId: course?._id || '',
    });
};

export const useOutletTaskerTrainingCourseDetail = () => {
    return useOutletContext<
        SerializeFrom<typeof willBecomeTaskerTrainingCourseDetailLoader>
    >();
};
