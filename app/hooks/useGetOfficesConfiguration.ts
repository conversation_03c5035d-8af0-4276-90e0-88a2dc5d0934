import {
    type LoaderFunctionArgs,
    type Serialize<PERSON>rom,
    json,
} from '@remix-run/node';
import { useOutletContext } from '@remix-run/react';
import {
    getPageSizeAndPageIndex,
    getSkipAndLimit,
    getValuesFromSearchParams,
} from 'btaskee-utils';
import { getCitiesByUserId, getUserSession } from '~/services/helpers.server';
import { getHolidaysFromSettingSystem } from '~/services/holiday-configuration.server';
import {
    getListOfficesFromSettingSystem,
    getTotalOfficesFromSettingSystem,
} from '~/services/offices-configuration.server';
import { getSession } from '~/services/session.server';

export const willBecomeOfficesConfigurationLoader = async ({
    request,
}: LoaderFunctionArgs) => {
    const { isoCode, userId, isSuperUser } = await getUserSession({
        headers: request.headers,
    });
    const url = new URL(request.url);

    const [{ searchText, statuses }, { pageSize, pageIndex }] =
        getValuesFromSearchParams(url.searchParams, {
            keysString: ['searchText', 'statuses'],
            keysNumber: ['pageSize', 'pageIndex'],
        });

    const filterValue = {
        search: searchText,
        statuses,
    };

    const total = await getTotalOfficesFromSettingSystem({
        isoCode,
        filter: filterValue,
    });

    const { skip, limit } = getSkipAndLimit(
        getPageSizeAndPageIndex({
            total,
            pageSize,
            pageIndex,
        }),
    );

    const [offices, userCities, session, holidays] = await Promise.all([
        getListOfficesFromSettingSystem({
            isoCode,
            filter: filterValue,
            limit,
            skip,
        }),
        getCitiesByUserId({
            userId,
            isManager: isSuperUser,
        }),
        getSession(request.headers.get('cookie')),
        getHolidaysFromSettingSystem({
            isoCode,
        }),
    ]);

    const flashMessage = session.get('flashMessage') || '';

    return json({
        offices,
        total,
        userCities,
        filterValue,
        flashMessage,
        holidays,
    });
};

export const useOutletOfficesConfiguration = () => {
    return useOutletContext<
        SerializeFrom<typeof willBecomeOfficesConfigurationLoader>
    >();
};
