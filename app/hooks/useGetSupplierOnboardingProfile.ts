import {
    type LoaderFunctionArgs,
    type Serialize<PERSON>rom,
    json,
} from '@remix-run/node';
import { useOutletContext } from '@remix-run/react';
import {
    SUPPLIER_PROFILE_TAB,
    TASKER_ONBOARDING_PROCESS_STATUS,
} from 'btaskee-constants';
import {
    DEFAULT_RANGE_DATE_CURRENT_DAY,
    convertSortString,
    getPageSizeAndPageIndex,
    getSkipAndLimit,
    getValuesFromSearchParams,
} from 'btaskee-utils';
import { getCitiesByUserId, getUserSession } from '~/services/helpers.server';
import { verifyManager } from '~/services/role-base-access-control.server';
import {
    getTaskerProfile,
    getTotalTaskerProfile,
} from '~/services/tasker-profile.server';

export const willBecomeSupplierOnboardingProfileLoader = async (
    { request }: LoaderFunctionArgs,
    { permissionsPassed }: { permissionsPassed: BtaskeePermissions['key'][] },
) => {
    const { isoCode, isSuperUser, userId } = await getUserSession({
        headers: request.headers,
    });
    const isManager = await verifyManager(userId);
    const cities = await getCitiesByUserId({
        userId,
        isManager: isManager || isSuperUser,
    });

    const url = new URL(request.url);

    const [
        { tabId, createdAt, status, searchText, sort: sortString },
        { pageSize, pageIndex },
    ] = getValuesFromSearchParams(url.searchParams, {
        keysString: ['tabId', 'createdAt', 'status', 'searchText', 'sort'],
        keysNumber: ['pageSize', 'pageIndex'],
    });

    const statusFilterValues = Object.values(
        TASKER_ONBOARDING_PROCESS_STATUS,
    )?.filter(profileStatus => status.split(',')?.includes(profileStatus));

    const tabsFound: typeof SUPPLIER_PROFILE_TAB = [];

    permissionsPassed.forEach(permissionPassed => {
        const tabFound = SUPPLIER_PROFILE_TAB.find(
            tab => tab.permission === permissionPassed,
        );

        if (tabFound) {
            tabsFound.push(tabFound);
        }
    });

    const profileFoundByTabId =
        tabsFound.find(tab => tab.tabId === tabId) || tabsFound[0];

    const filteredValue = {
        status: statusFilterValues?.length
            ? statusFilterValues
            : profileFoundByTabId.status?.slice(0, 1),
        createdAt: DEFAULT_RANGE_DATE_CURRENT_DAY(createdAt),
        searchText,
        isoCode,
        cities,
        isPartner: true,
    };

    const totalProfile = await getTotalTaskerProfile(filteredValue);

    const supplierProfiles = await getTaskerProfile({
        ...filteredValue,
        ...getSkipAndLimit(
            getPageSizeAndPageIndex({
                total: totalProfile,
                pageSize,
                pageIndex,
            }),
        ),
        sort: convertSortString({
            sortString,
            defaultValue: { updatedAt: -1 },
        }),
    });

    return json({
        supplierProfiles,
        totalProfile,
        filteredValue,
        tabId: profileFoundByTabId.tabId,
        status: profileFoundByTabId.status,
        permissions: permissionsPassed,
    });
};

export const useOutletGetSupplierOnboardingProfile = () => {
    return useOutletContext<
        SerializeFrom<typeof willBecomeSupplierOnboardingProfileLoader>
    >();
};
