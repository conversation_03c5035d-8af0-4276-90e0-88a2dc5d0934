import type { DialogProps } from '@radix-ui/react-dialog';
import type { LoaderFunctionArgs, SerializeFrom } from '@remix-run/node';
import { json } from '@remix-run/node';
import { useLoaderData, useOutletContext } from '@remix-run/react';
import { hoc404 } from '~/hoc/remix';
import { getUserSession } from '~/services/helpers.server';
import { getGroupDetail } from '~/services/role-base-access-control.server';

export const willBecomeLoader = async ({
    params,
    request,
}: LoaderFunctionArgs) => {
    const groupId = params.id || '';
    const { userId, isSuperUser } = await getUserSession({
        headers: request.headers,
    });

    const group = await hoc404(() =>
        getGroupDetail<GroupDetail['group']>({
            isSuperUser,
            userId,
            groupId,
            projection: {
                name: 1,
            },
        }),
    );

    return json({
        group,
    });
};

export const useGetGroupDetail = () => {
    return useOutletContext<SerializeFrom<typeof willBecomeLoader>>();
};

export const useOutletGroupDetail = () => {
    return useLoaderData<typeof willBecomeLoader>();
};

export const useOutletCreateAndAddMemberIntoGroup = () => {
    return useOutletContext<{
        openDialogAddAndCreateMember: boolean;
        onOpenChange: NonNullable<DialogProps['onOpenChange']>;
        onClose: () => void;
        groupName: GroupDetail['group']['name'];
    }>();
};
