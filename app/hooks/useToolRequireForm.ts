import { zodResolver } from '@hookform/resolvers/zod';
import { TOOL_REQUIRE_TEXT_DEFAULT_VALUE } from 'btaskee-constants';
import { useForm } from 'react-hook-form';
import { useTranslation } from 'react-i18next';
import { z } from 'zod';

export const useToolRequireForm = (initialData?: ToolDetail) => {
    const { t } = useTranslation('tool-require-create');

    const FormSchema = z.object({
        image: z
            .object({
                value: z.custom(file => !!file, {
                    message: t('MUST_BE_A_FILE'),
                }),
            })
            .nullable(),
        priceChar: z
            .string()
            .min(1, t('PRICE_REQUIRE'))
            .refine(
                val => {
                    return val !== '0';
                },
                {
                    message: t('PRICE_MUST_BE_MORE_THAN_0'),
                },
            ),
        text: z.object({
            vi: z.string().min(1, t('NAME_REQUIRE', { lang: t('VIETNAMESE') })),
            en: z.string().min(1, t('NAME_REQUIRE', { lang: t('ENGLISH') })),
            ko: z.string().min(1, t('NAME_REQUIRE', { lang: t('KOREAN') })),
            th: z.string().min(1, t('NAME_REQUIRE', { lang: t('THAILAND') })),
            id: z.string().min(1, t('NAME_REQUIRE', { lang: t('INDONESIAN') })),
        }),
    });

    // TODO: refactor the schema to make it easier to define default values and easier to read
    const form = useForm<ToolRequireFormValue>({
        resolver: zodResolver(FormSchema),
        defaultValues: {
            image: {
                value: initialData?.image || '',
            },
            priceChar:
                // change price from number (100000) to string with format: 100,000
                initialData?.price
                    ?.toString()
                    .replace(/\B(?=(\d{3})+(?!\d))/g, ',') || '',
            text: initialData?.text || TOOL_REQUIRE_TEXT_DEFAULT_VALUE,
        },
    });

    return form;
};
