import {
    type LoaderFunctionArgs,
    type Serialize<PERSON><PERSON>,
    json,
} from '@remix-run/node';
import { useOutletContext } from '@remix-run/react';
import {
    PERMISSIONS,
    TASKER_ONBOARDING_PROCESS_STATUS,
    TASKER_PROFILE_TAB_ID,
    res403,
} from 'btaskee-constants';
import {
    DEFAULT_RANGE_DATE_CURRENT_DAY,
    convertSortString,
    getPageSizeAndPageIndex,
    getSkipAndLimit,
    getValuesFromSearchParams,
} from 'btaskee-utils';
import { getCitiesByUserId, getUserSession } from '~/services/helpers.server';
import { verifyManager } from '~/services/role-base-access-control.server';
import {
    getTaskerProfile,
    getTotalTaskerProfile,
    type GettingProfileProps,
} from '~/services/tasker-profile.server';

// Local tab configuration for identity verification system
const IDENTITY_VERIFICATION_TABS = [
    {
        title: "VERIFY_IDENTITY_INFORMATION_FOR_TASKER",
        tabId: TASKER_PROFILE_TAB_ID.VERIFYING,
        permission: PERMISSIONS.READ_VERIFY_IDENTITY_INFORMATION_FOR_TASKER,
        status: [
            TASKER_ONBOARDING_PROCESS_STATUS.VERIFYING,
            TASKER_ONBOARDING_PROCESS_STATUS.NEEDS_UPDATE,
            TASKER_ONBOARDING_PROCESS_STATUS.UPDATED,
        ]
    },
    {
        title: "APPROVED_IDENTITY_INFORMATION_FOR_TASKER",
        tabId: TASKER_PROFILE_TAB_ID.APPROVED,
        permission: PERMISSIONS.READ_APPROVED_IDENTITY_INFORMATION_FOR_TASKER,
        status: [TASKER_ONBOARDING_PROCESS_STATUS.APPROVED]
    }
];

export const willBecomeIdentityInformationLoader = async (
    { request }: LoaderFunctionArgs,
    { permissionsPassed }: { permissionsPassed: BtaskeePermissions['key'][] },
) => {
    const { isoCode, isSuperUser, userId } = await getUserSession({
        headers: request.headers,
    });
    const url = new URL(request.url);
    const [
        { tabId, createdAt, status, searchText, sort: sortString, cities },
        { pageSize, pageIndex },
    ] = getValuesFromSearchParams(url.searchParams, {
        keysString: ['tabId', 'createdAt', 'status', 'searchText', 'sort', 'cities'],
        keysNumber: ['pageSize', 'pageIndex'],
    });
    const isManager = await verifyManager(userId);
    const userCities = await getCitiesByUserId({
        userId,
        isManager: isManager || isSuperUser,
    });

    const permissionsFound = [];
    [
        PERMISSIONS.READ_VERIFY_IDENTITY_INFORMATION_FOR_TASKER,
        PERMISSIONS.READ_APPROVED_IDENTITY_INFORMATION_FOR_TASKER,
    ].forEach(permission => {
        if (permissionsPassed.includes(permission)) {
            permissionsFound.push(permission);
        }
    });

    if (!permissionsFound.length) {
        throw new Response(null, res403);
    }

    // Find the tab configuration based on tabId or default to first tab
    const profileFoundByTabId = IDENTITY_VERIFICATION_TABS.find(
        tab => tab.tabId === tabId
    ) || IDENTITY_VERIFICATION_TABS[0];

    // Parse status filter values
    const statusFilterValues = status ? status.split(',') : [];

    const filteredValue = {
        status: (statusFilterValues?.length
            ? statusFilterValues
            : profileFoundByTabId.status) as TASKER_ONBOARDING_PROCESS_STATUS[],
        createdAt: DEFAULT_RANGE_DATE_CURRENT_DAY(createdAt),
        searchText,
        cities: cities ? cities.split(',') : userCities,
        isoCode,
        hasIdentityCard: true, // Filter for identity verification
    };

    const total = await getTotalTaskerProfile(filteredValue);

    const data = await getTaskerProfile({
        ...filteredValue,
        ...getSkipAndLimit(
            getPageSizeAndPageIndex({
                total,
                pageSize,
                pageIndex,
            }),
        ),
        sort: convertSortString({
            sortString,
            defaultValue: { updatedAt: -1 },
        }),
    });

    return json({
        data,
        total,
        filterValue: filteredValue,
        tabId: profileFoundByTabId.tabId,
        status: profileFoundByTabId.status,
        userCities,
        permissions: permissionsPassed,
    });
};

export const useIdentityInformation = () => {
    return useOutletContext<
        SerializeFrom<typeof willBecomeIdentityInformationLoader>
    >();
};

