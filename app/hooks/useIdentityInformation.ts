import {
    type LoaderFunctionArgs,
    type Serialize<PERSON>rom,
    json,
} from '@remix-run/node';
import { useOutletContext } from '@remix-run/react';
import {
    PERMISSIONS,
    TASKER_ONBOARDING_PROCESS_STATUS,
    res403,
} from 'btaskee-constants';
import {
    getValuesFromSearchParams,
} from 'btaskee-utils';
import { getCitiesByUserId, getUserSession } from '~/services/helpers.server';
import { verifyManager } from '~/services/role-base-access-control.server';

export const willBecomeIdentityInformationLoader = async (
    { request }: LoaderFunctionArgs,
    { permissionsPassed }: { permissionsPassed: BtaskeePermissions['key'][] },
) => {
    const { isSuperUser, userId } = await getUserSession({
        headers: request.headers,
    });
    const url = new URL(request.url);
    const [
        { tabId, createdAt, status, searchText, sort: sortString },
        { pageSize, pageIndex },
    ] = getValuesFromSearchParams(url.searchParams, {
        keysString: ['tabId', 'createdAt', 'status', 'searchText', 'sort'],
        keysNumber: ['pageSize', 'pageIndex'],
    });
    const isManager = await verifyManager(userId);
    const userCities = await getCitiesByUserId({
        userId,
        isManager: isManager || isSuperUser,
    });

    const permissionsFound = [];

    const filteredValue = {
        status: statusFilterValues?.length
            ? statusFilterValues
            : profileFoundByTabId.status,
        createdAt: DEFAULT_RANGE_DATE_CURRENT_DAY(createdAt),
        searchText,
        cities: cities ? cities.split(',') : userCities,
        isoCode,
    };
    [
        PERMISSIONS.READ_VERIFY_IDENTITY_INFORMATION_FOR_TASKER,
        PERMISSIONS.READ_APPROVED_IDENTITY_INFORMATION_FOR_TASKER,
    ].forEach(permission => {
        if (permissionsPassed.includes(permission)) {
            permissionsFound.push(permission);
        }
    });

    if (!permissionsFound.length) {
        throw new Response(null, res403);
    }

    return json({
        userCities,
        status: [
            TASKER_ONBOARDING_PROCESS_STATUS.VERIFYING,
            TASKER_ONBOARDING_PROCESS_STATUS.APPROVED,
        ],
        permissions: permissionsPassed,
    });
};

export const useIdentityInformation = () => {
    return useOutletContext<
        SerializeFrom<typeof willBecomeIdentityInformationLoader>
    >();
};

