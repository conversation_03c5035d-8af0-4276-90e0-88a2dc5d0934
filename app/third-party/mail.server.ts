import { getEnvMailServer } from 'btaskee-dotenv';
import nodemailer from 'nodemailer';
import type Mail from 'nodemailer/lib/mailer';

export async function sendEmail({
    to,
    from,
    text,
    subject,
    html,
}: Mail.Options) {
    const dotenv = getEnvMailServer();
    try {
        const transporter = nodemailer.createTransport({
            host: dotenv.MAIL_HOST,
            secure: true,
            auth: {
                user: dotenv.MAIL_SERVER_USERNAME,
                pass: dotenv.MAIL_SERVER_PASSWORD,
            },
        });
        await transporter.sendMail({
            to,
            from,
            text,
            html,
            subject,
        });
    } catch (error) {
        throw new Error('EMAIL_SERVICE_ERROR');
    }
}
