import { S3 } from '@aws-sdk/client-s3';
import { Upload } from '@aws-sdk/lib-storage';
import {
    type UploadHandlerPart,
    writeAsyncIterableToWritable,
} from '@remix-run/node';
import { getEnvS3 } from 'btaskee-dotenv';
import { PassThrough } from 'stream';

export const s3UploadHandler = async ({
    data,
    filename,
    contentType,
}: UploadHandlerPart) => {
    const writable = new PassThrough();
    await writeAsyncIterableToWritable(data, writable);

    const file = await new Upload({
        client: new S3({
            credentials: {
                accessKeyId: getEnvS3().STORAGE_ACCESS_KEY,
                secretAccessKey: getEnvS3().STORAGE_SECRET,
            },
            region: getEnvS3().STORAGE_REGION,
        }),
        params: {
            Bucket: getEnvS3().STORAGE_BUCKET,
            Key: `${getEnvS3().STORAGE_PATH}/tasker/${Date.now()}_${filename || ''}`,
            ACL: 'public-read',
            Body: writable,
        },
    }).done();

    return file.Location;
};
