{"type": "module", "name": "remixrun-boilerplate", "private": true, "sideEffects": false, "scripts": {"build": "remix build", "dev": "remix dev -c \"node server.mjs\"", "lint": "eslint --cache --cache-location ./node_modules/.cache/eslint .", "deploy": "remix-serve ./build/index.js", "type:check": "tsc", "prettier": "prettier --write .", "cspell": "cspell \"**\"", "prepare": "husky", "test:local": "jest --config=./jest.config.dev.ts --detect<PERSON><PERSON>Handles", "test:ci": "jest --config=./jest.config.ci.ts", "lint-staged": "npx lint-staged --allow-empty"}, "dependencies": {"@ariakit/react": "0.4.15", "@radix-ui/react-toolbar": "1.1.0", "@types/bcryptjs": "2.4.6", "bcrypt": "5.1.1", "@aws-sdk/client-s3": "3.701.0", "@aws-sdk/lib-storage": "3.701.0", "@hookform/error-message": "2.0.1", "@hookform/resolvers": "3.9.1", "@radix-ui/react-accordion": "1.2.1", "@radix-ui/react-alert-dialog": "1.1.2", "@radix-ui/react-aspect-ratio": "1.1.0", "@radix-ui/react-avatar": "1.1.1", "@radix-ui/react-checkbox": "1.1.2", "@radix-ui/react-collapsible": "1.1.1", "@radix-ui/react-context-menu": "2.2.2", "@radix-ui/react-dialog": "1.1.2", "@radix-ui/react-dropdown-menu": "2.1.2", "@radix-ui/react-hover-card": "1.1.2", "@radix-ui/react-icons": "1.3.2", "@radix-ui/react-label": "2.1.0", "@radix-ui/react-menubar": "1.1.2", "@radix-ui/react-navigation-menu": "1.2.1", "@radix-ui/react-popover": "1.1.2", "@radix-ui/react-progress": "1.1.0", "@radix-ui/react-radio-group": "1.2.1", "@radix-ui/react-scroll-area": "1.2.1", "@radix-ui/react-select": "2.1.2", "@radix-ui/react-separator": "1.1.0", "@radix-ui/react-slider": "1.2.1", "@radix-ui/react-slot": "1.1.0", "@radix-ui/react-switch": "1.1.1", "@radix-ui/react-tabs": "1.1.1", "@radix-ui/react-toast": "1.2.2", "@radix-ui/react-toggle": "1.1.0", "@radix-ui/react-toggle-group": "1.1.0", "@radix-ui/react-tooltip": "1.1.4", "@remix-run/express": "2.15.0", "@remix-run/node": "2.15.0", "@remix-run/react": "2.15.0", "@remix-run/serve": "2.15.0", "@tanstack/react-table": "8.20.5", "@types/nodemailer": "6.4.17", "@types/papaparse": "5.3.15", "@types/recharts": "1.8.29", "@types/uuid": "9.0.8", "bcryptjs": "2.4.3", "btaskee-constants": "git+ssh://**************:btaskee/btaskee-ops/constants.git#e09d5a7cec8826915ce90045cb4e2aa2af4e5a1f", "btaskee-dotenv": "git+ssh://**************:btaskee/btaskee-ops/dotenv.git#v1.0.7", "btaskee-hooks": "git+ssh://**************:btaskee/btaskee-ops/hooks.git#v1.0.5", "btaskee-logger": "git+ssh://**************:btaskee/btaskee-ops/logger.git#v1.0.2", "btaskee-ui": "git+ssh://**************:btaskee/btaskee-ops/ui-components.git#v1.3.64", "btaskee-types": "git+ssh://**************:btaskee/btaskee-ops/types.git#5c17cf59c27d1dd2132bc37d1c55cc31989281c9", "btaskee-utils": "git+ssh://**************:btaskee/btaskee-ops/utils.git#v1.0.25", "schemas": "git+ssh://**************:btaskee/btaskee-ops/schemas.git#v1.0.71", "mongo-connection": "git+ssh://**************:btaskee/btaskee-ops/mongo-connection.git#v1.0.1", "react-dnd": "16.0.1", "react-dnd-html5-backend": "16.0.1", "react-lite-youtube-embed": "2.4.0", "class-variance-authority": "0.7.1", "clsx": "2.1.1", "cmdk": "0.2.1", "date-fns": "4.1.0", "dotenv": "16.4.5", "embla-carousel-react": "8.5.1", "express": "4.21.1", "express-prom-bundle": "7.0.2", "i18next": "24.0.2", "i18next-browser-languagedetector": "8.0.0", "i18next-fs-backend": "2.6.0", "i18next-http-backend": "3.0.1", "isbot": "5.1.17", "lodash": "4.17.21", "lucide-react": "0.418.0", "moment": "2.30.1", "moment-timezone": "0.5.46", "mongodb": "6.11.0", "mongoose": "8.8.3", "next-themes": "0.4.3", "nodemailer": "6.9.16", "papaparse": "5.4.1", "prom-client": "15.1.3", "react": "18.3.1", "react-day-picker": "8.10.1", "react-dom": "18.3.1", "react-hook-form": "7.53.2", "react-i18next": "14.1.3", "react-resizable-panels": "2.1.7", "recharts": "2.13.3", "remix-auth": "3.7.0", "remix-auth-form": "1.5.0", "remix-i18next": "6.4.1", "sonner": "1.7.0", "styled-components": "6.1.13", "tailwind-merge": "2.5.5", "tailwindcss-animate": "1.0.7", "uuid": "9.0.1", "vaul": "0.9.0", "winston": "3.17.0", "winston-loggly-bulk": "3.3.2", "zod": "3.23.8", "zustand": "4.5.5"}, "devDependencies": {"@babel/core": "7.26.0", "@babel/preset-env": "7.26.0", "@babel/preset-typescript": "7.26.0", "@commitlint/cli": "19.6.0", "@commitlint/config-conventional": "19.6.0", "@remix-run/dev": "2.15.0", "@remix-run/eslint-config": "2.15.0", "@remix-run/testing": "2.15.0", "@semantic-release/changelog": "6.0.3", "@semantic-release/commit-analyzer": "13.0.0", "@semantic-release/git": "10.0.1", "@semantic-release/gitlab": "13.1.0", "@semantic-release/release-notes-generator": "14.0.1", "@testing-library/jest-dom": "6.6.3", "@testing-library/react": "14.3.1", "@trivago/prettier-plugin-sort-imports": "4.3.0", "@types/express": "4.17.21", "@types/jest": "29.5.14", "@types/lodash": "4.17.13", "@types/react": "18.3.12", "@types/react-dom": "18.3.1", "@types/styled-components": "5.1.34", "@types/winston-loggly-bulk": "3.0.6", "autoprefixer": "10.4.20", "babel-jest": "29.7.0", "babel-plugin-module-resolver": "5.0.2", "cross-env": "7.0.3", "cspell": "8.16.1", "eslint": "8.57.1", "eslint-import-resolver-typescript": "3.6.3", "eslint-plugin-import": "2.31.0", "eslint-plugin-jsx-a11y": "6.10.2", "eslint-plugin-react": "7.37.2", "eslint-plugin-react-hooks": "4.6.2", "husky": "9.1.7", "jest": "29.7.0", "prettier": "3.4.1", "semantic-release": "24.2.0", "tailwindcss": "3.4.15", "ts-jest": "29.2.5", "ts-node": "10.9.2", "typescript": "5.7.2"}, "engines": {"node": ">=18.0.0"}}