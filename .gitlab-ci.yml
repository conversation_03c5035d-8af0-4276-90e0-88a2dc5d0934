default:
  image: node:latest

stages:
  - bot-review
  - install
  - code standard
  - test
  - release
  - release-docker

Install dependencies:
  stage: install
  rules:
    - if: $CI_PIPELINE_SOURCE == "merge_request_event"
  script:
    - 'which ssh-agent || ( apt-get update -y && apt-get install openssh-client -y )'
    - eval $(ssh-agent -s)
    - echo "$SSH_PRIVATE_KEY" | tr -d '\r' | ssh-add -
    - mkdir -p ~/.ssh
    - chmod 700 ~/.ssh
    - ssh-keyscan gitlab.com >> ~/.ssh/known_hosts
    - chmod 644 ~/.ssh/known_hosts
    - SHA_SUM=$(npm view pnpm@10.1.0 dist.shasum)
    - corepack enable pnpm
    - corepack install -g pnpm@10.1.0+sha1.$SHA_SUM
    - pnpm install --force
  cache:
    key: node_modules_cache
    paths:
      - node_modules/

Bot review code:
  stage: bot-review
  image: semgrep/semgrep
  script:
    - |
      VERSION_RESPONSE=$(curl --header "PRIVATE-TOKEN: $GITLAB_TOKEN" "https://gitlab.com/api/v4/projects/$CI_PROJECT_ID/merge_requests/$CI_MERGE_REQUEST_IID/versions")

      HEAD_COMMIT_SHA=$(echo "$VERSION_RESPONSE" | jq -r '.[0].head_commit_sha')
      BASE_COMMIT_SHA=$(echo "$VERSION_RESPONSE" | jq -r '.[0].base_commit_sha')
      START_COMMIT_SHA=$(echo "$VERSION_RESPONSE" | jq -r '.[0].start_commit_sha')

      git fetch origin $CI_MERGE_REQUEST_TARGET_BRANCH_NAME
      CHANGED_FILES=$(git diff --name-only $START_COMMIT_SHA $HEAD_COMMIT_SHA)
      if [ -z "$CHANGED_FILES" ]; then
        echo "No changed files detected."
        exit 0
      fi

      semgrep scan --config .semgrep/.semgrep-js.yml --config ./semgrep/.semgrep-ts.yml $CHANGED_FILES --json --output=result.json || true

      FINDINGS=$(jq -c '.results[]?' result.json)
      FINDINGS_COUNT=$(jq '.results | length' result.json)

      if [ "$FINDINGS_COUNT" -gt 0 ]; then
        echo "$FINDINGS" | while read -r FINDING; do
          FILE=$(echo "$FINDING" | jq -r '.path')
          END_LINE=$(echo "$FINDING" | jq -r '.end.line')
          RULE=$(echo "$FINDING" | jq -r '.check_id')
          MESSAGE=$(echo "$FINDING" | jq -r '.extra.message')
          EXTRA_LINES=$(echo "$FINDING" | jq -r '.extra.lines')

          COMMENT="**bTaskee rule**: \`$RULE\`\n\n$MESSAGE\n\n\`\`\`\n$EXTRA_LINES\n\`\`\`"
          curl --header "PRIVATE-TOKEN: $GITLAB_TOKEN" \
               --data-urlencode "body=$COMMENT" \
               --data-urlencode "position[new_path]=$FILE" \
               --data-urlencode "position[old_path]=$FILE" \
               --data-urlencode "position[position_type]=text" \
               --data-urlencode "position[new_line]=$END_LINE" \
               --data-urlencode "position[base_sha]=$BASE_COMMIT_SHA" \
               --data-urlencode "position[start_sha]=$START_COMMIT_SHA" \
               --data-urlencode "position[head_sha]=$HEAD_COMMIT_SHA" \
               "https://gitlab.com/api/v4/projects/$CI_PROJECT_ID/merge_requests/$CI_MERGE_REQUEST_IID/discussions"
        done

        echo "Semgrep found issues, posting comment to PR, failing pipeline."
        exit 1
      else
        echo "No issues found, continuing pipeline."
      fi
  rules:
    - if: $CI_PIPELINE_SOURCE == "merge_request_event"
  variables:
    SEMGREP_APP_TOKEN: $SEMGREP_APP_TOKEN

Typescript checker:
  rules:
    - if: $CI_PIPELINE_SOURCE == "merge_request_event"
  stage: code standard
  script:
    - npm run type:check
  cache:
    key: node_modules_cache
    paths:
      - node_modules/

Lint:
  rules:
    - if: $CI_PIPELINE_SOURCE == "merge_request_event"
  stage: code standard
  script:
    - npm run lint
  cache:
    key: node_modules_cache
    paths:
      - node_modules/

Spell checker:
  rules:
    - if: $CI_PIPELINE_SOURCE == "merge_request_event"
  stage: code standard
  script:
    - npm run cspell
  cache:
    key: node_modules_cache
    paths:
      - node_modules/

Unit testing:
  rules:
    - if: $CI_PIPELINE_SOURCE == "merge_request_event"
  stage: test
  services:
    - mongo:latest
  script:
    - npm run test:ci
  artifacts:
    reports:
      coverage_report:
        coverage_format: cobertura
        path: coverage/coverage.txt
  cache:
    key: node_modules_cache
    paths:
      - node_modules/

.release:
  stage: release
  script:
    - 'which ssh-agent || ( apt-get update -y && apt-get install openssh-client -y )'
    - eval $(ssh-agent -s)
    - echo "$SSH_PRIVATE_KEY" | tr -d '\r' | ssh-add -
    - mkdir -p ~/.ssh
    - chmod 700 ~/.ssh
    - ssh-keyscan gitlab.com >> ~/.ssh/known_hosts
    - chmod 644 ~/.ssh/known_hosts
    - npm install pnpm -g
    - pnpm install --force
    - npx semantic-release --branches main,$CI_COMMIT_REF_NAME --no-ci
    - test -e .VERSION || (echo $(git describe --abbrev=0 --tags | tr -d v) > .VERSION && touch .NORELEASE)
    - echo "Determined Version $(cat .VERSION)"
  artifacts:
    paths:
      - .VERSION
      - .NORELEASE
    expire_in: 1 week
  only:
    - main
  variables:
    GITLAB_TOKEN: $GITLAB_TOKEN

Push docker image:
  image: docker:24.0.6
  stage: release-docker
  only:
    - main
  services:
    - docker:24.0.6-dind
    - mongo:latest
  variables:
    APP_MODE: prod
    HUB: btaskeehub
    REPO: btaskee-ops
  script:
    - echo $DOCKER_TOKEN | docker login --username $DOCKER_USERNAME --password-stdin
    # - VERSION=$(cat .VERSION)
    # - TAG="tasker-ops-$VERSION"
    - TAG="tasker-ops-1.3.4"
    - export DOCKER_BUILDKIT=1
    - echo "$SSH_PRIVATE_KEY" > id_rsa
    - >
      DOCKER_BUILDKIT=1 docker buildx build
      --secret id=ssh_key,src=./id_rsa
      --no-cache
      -t $HUB/$REPO:$TAG .
    - docker push $HUB/$REPO:$TAG
  # dependencies:
    # - release

Staging - Push docker image:
  image: docker:24.0.6
  stage: release-docker
  only:
    - staging
  services:
    - docker:24.0.6-dind
    - mongo:latest
  variables:
    APP_MODE: prod
    HUB: btaskeehub
    REPO: ops-staging
  script:
    - echo $DOCKER_TOKEN | docker login --username $DOCKER_USERNAME --password-stdin
    - TAG="tasker-ops"
    - export DOCKER_BUILDKIT=1
    - echo "$SSH_PRIVATE_KEY" > id_rsa
    - >
      DOCKER_BUILDKIT=1 docker buildx build
      --secret id=ssh_key,src=./id_rsa
      --no-cache
      --provenance=true
      --sbom=true 
      -t $HUB/$REPO:$TAG .
    - docker push $HUB/$REPO:$TAG