{
    "include": ["remix.env.d.ts", "**/*.ts", "**/*.tsx"],
    "compilerOptions": {
        "lib": ["DOM", "DOM.Iterable", "ES2022"],
        "isolatedModules": true,
        "esModuleInterop": true,
        "jsx": "react-jsx",
        "moduleResolution": "Bundler",
        "resolveJsonModule": true,
        "target": "ES2022",
        "strict": true,
        "allowJs": true,
        "skipLibCheck": true,
        "forceConsistentCasingInFileNames": true,
        "baseUrl": ".",
        "paths": {
            "~/*": ["./app/*"]
        },
        "noImplicitAny": true,
        "strictNullChecks": true,
        "exactOptionalPropertyTypes": true,

        // Remix takes care of building everything in `remix build`.
        "noEmit": true
    },
    "exclude": ["node_modules", "build", "public", "coverage"]
}
