# First
# chmod +x seeding-data.sh

# MongoDB connection string
MONGO_URI="mongodb://127.0.0.1:27017/db-name"

mongo "$MONGO_URI" --eval '
const superUserId = "'"$(uuidgen)"'"
const superRoleId = "'"$(uuidgen)"'"
const superGroupId = "'"$(uuidgen)"'"
const superPermissionId = "'"$(uuidgen)"'"

const usersData = [
  { 
    "_id": superUserId,
    "isoCode": "VN",
    "username": "superuser",
    "email": "<EMAIL>",
    "status": "ACTIVE"
  },
];
const rolesData = [
  {
    "_id": superRoleId,
    "permissions": [superPermissionId],
    "name": "Role: Super user",
    "status": "ACTIVE"
  }
]
const groupsData = [
  {
    "_id": superGroupId,
    "name": "Group: Super user",
    "roleAssignedIds": [superRoleId],
    "users": [
      {_id: superUserId, status: "ACTIVE", addedAt: new Date()}
    ],
    "genealogy": [],
    "status": "ACTIVE",
    "hierarchy": 1
  }
]
const permissionsData = [
  {
    "_id": superPermissionId,
    "key": "root",
    "description": "This is super user, all powers",
    "module": "system",
    "name": "Super user"
  },
  {
    "_id": "'"$(uuidgen)"'",
    "key": "manager",
    "description": "This is manager, can access data all cities of each country, view action history of Team, and manage member",
    "module": "system",
    "name": "Permission: Manager"
  },
  {
    "_id": "'"$(uuidgen)"'",
    "key": "write/role-management",
    "description": "Groups management feature: Write roles (create - update - remove)",
    "module": "system",
    "name": "Groups management feature: Write roles (create - update)"
  },
  {
    "_id": "'"$(uuidgen)"'",
    "key": "read/role-management",
    "description": "Groups management feature: Read roles",
    "module": "system",
    "name": "Groups management feature: Read roles"
  },
  {
    "_id": "'"$(uuidgen)"'",
    "key": "write/group-management",
    "description": "Groups management feature: Write children groups (create - update - remove)",
    "module": "system",
    "name": "Groups management feature: Write children groups (create - update - remove)"
  },
  {
    "_id": "'"$(uuidgen)"'",
    "key": "read/group-management",
    "description": "Groups management feature: Read children groups",
    "module": "system",
    "name": "Groups management feature: Read children groups"
  }
]

db.users.insertMany(usersData);
db.roles.insertMany(rolesData);
db.groups.insertMany(groupsData);
db.permissions.insertMany(permissionsData);
'

echo "Data init successfully."