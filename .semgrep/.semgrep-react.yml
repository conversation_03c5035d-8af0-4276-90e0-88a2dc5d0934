rules:

  - id: calling-set-state-on-current-state
    patterns:
      - pattern: $Y($X);
      - pattern-inside: |
          const [$X, $Y] = useState(...);
          ...
    message: Calling setState on the current state is always a no-op. Did you mean
      to change the state like $Y(!$X) instead?
    languages:
      - javascript
    severity: ERROR
    metadata:
      technology:
        - react
      category: correctness
      license: Semgrep Rules License v1.0. For more details, visit
        semgrep.dev/legal/rules-license

  - id: react-markdown-insecure-html
    message: Overwriting `transformLinkUri` or `transformImageUri` to something
      insecure, or turning `allowDangerousHtml` on, or turning `escapeHtml` off,
      will open the code up to XSS vectors.
    metadata:
      cwe:
        - "CWE-79: Improper Neutralization of Input During Web Page Generation
          ('Cross-site Scripting')"
      owasp:
        - A07:2017 - Cross-Site Scripting (XSS)
        - A03:2021 - Injection
      references:
        - https://www.npmjs.com/package/react-markdown#security
      category: security
      technology:
        - react
      cwe2022-top25: true
      cwe2021-top25: true
      subcategory:
        - audit
      likelihood: LOW
      impact: LOW
      confidence: LOW
      license: Semgrep Rules License v1.0. For more details, visit
        semgrep.dev/legal/rules-license
      vulnerability_class:
        - Cross-Site-Scripting (XSS)
    languages:
      - typescript
      - javascript
    severity: WARNING
    patterns:
      - pattern-either:
          - pattern-inside: |
              $X = require('react-markdown/with-html');
              ...
          - pattern-inside: |
              $X = require('react-markdown');
              ...
          - pattern-inside: |
              import 'react-markdown/with-html';
              ...
          - pattern-inside: |
              import 'react-markdown';
              ...
      - pattern-either:
          - pattern: |
              <$EL allowDangerousHtml />
          - pattern: |
              <$EL escapeHtml={false} />
          - pattern: |
              <$EL transformLinkUri=... />
          - pattern: |
              <$EL transformImageUri=... />

rules:
  - id: react-insecure-request
    message: Unencrypted request over HTTP detected.
    metadata:
      vulnerability: Insecure Transport
      owasp:
        - A03:2017 - Sensitive Data Exposure
        - A02:2021 - Cryptographic Failures
      cwe:
        - "CWE-319: Cleartext Transmission of Sensitive Information"
      references:
        - https://www.npmjs.com/package/axios
      category: security
      technology:
        - react
      subcategory:
        - vuln
      likelihood: LOW
      impact: MEDIUM
      confidence: MEDIUM
      license: Semgrep Rules License v1.0. For more details, visit
        semgrep.dev/legal/rules-license
      vulnerability_class:
        - Mishandled Sensitive Information
    languages:
      - typescript
      - javascript
    severity: ERROR
    patterns:
      - pattern-either:
          - patterns:
              - pattern-either:
                  - pattern-inside: |
                      import $AXIOS from 'axios';
                      ...
                      $AXIOS.$METHOD(...)
                  - pattern-inside: |
                      $AXIOS = require('axios');
                      ...
                      $AXIOS.$METHOD(...)
              - pattern: $AXIOS.$VERB("$URL",...)
              - metavariable-regex:
                  metavariable: $VERB
                  regex: ^(get|post|delete|head|patch|put|options)
          - patterns:
              - pattern-either:
                  - pattern-inside: |
                      import $AXIOS from 'axios';
                      ...
                      $AXIOS(...)
                  - pattern-inside: |
                      $AXIOS = require('axios');
                      ...
                      $AXIOS(...)
              - pattern-either:
                  - pattern: '$AXIOS({url: "$URL"}, ...)'
                  - pattern: |
                      $OPTS = {url: "$URL"}
                      ...
                      $AXIOS($OPTS, ...)
          - pattern: fetch("$URL", ...)
      - metavariable-regex:
          metavariable: $URL
          regex: ^([Hh][Tt][Tt][Pp]:\/\/(?!localhost).*)

  - id: react-dangerouslysetinnerhtml
    message: Detection of dangerouslySetInnerHTML from non-constant definition. This
      can inadvertently expose users to cross-site scripting (XSS) attacks if
      this comes from user-provided input. If you have to use
      dangerouslySetInnerHTML, consider using a sanitization library such as
      DOMPurify to sanitize your HTML.
    metadata:
      cwe:
        - "CWE-79: Improper Neutralization of Input During Web Page Generation
          ('Cross-site Scripting')"
      owasp:
        - A07:2017 - Cross-Site Scripting (XSS)
        - A03:2021 - Injection
      references:
        - https://react.dev/reference/react-dom/components/common#dangerously-setting-the-inner-html
      category: security
      confidence: MEDIUM
      technology:
        - react
      cwe2022-top25: true
      cwe2021-top25: true
      subcategory:
        - vuln
      likelihood: MEDIUM
      impact: MEDIUM
      license: Semgrep Rules License v1.0. For more details, visit
        semgrep.dev/legal/rules-license
      vulnerability_class:
        - Cross-Site-Scripting (XSS)
    languages:
      - typescript
      - javascript
    severity: WARNING
    mode: taint
    pattern-sources:
      - patterns:
          - pattern-either:
              - pattern-inside: |
                  function ...({..., $X, ...}) { ... }
              - pattern-inside: |
                  function ...(..., $X, ...) { ... }
          - focus-metavariable: $X
          - pattern-not-inside: |
              $F. ... .$SANITIZEUNC(...)
    pattern-sinks:
      - patterns:
          - focus-metavariable: $X
          - pattern-either:
              - pattern: |
                  {...,dangerouslySetInnerHTML: {__html: $X},...}
              - pattern: |
                  <$Y ... dangerouslySetInnerHTML={{__html: $X}} />
          - pattern-not: |
              <$Y ... dangerouslySetInnerHTML={{__html: "..."}} />
          - pattern-not: |
              {...,dangerouslySetInnerHTML:{__html: "..."},...}
          - metavariable-pattern:
              patterns:
                - pattern-not: |
                    {...}
              metavariable: $X
          - pattern-not: |
              <... {__html: "..."} ...>
          - pattern-not: |
              <... {__html: `...`} ...>
    pattern-sanitizers:
      - patterns:
          - pattern-either:
              - pattern-inside: |
                  import $S from "underscore.string"
                  ...
              - pattern-inside: |
                  import * as $S from "underscore.string"
                  ...
              - pattern-inside: |
                  import $S from "underscore.string"
                  ...
              - pattern-inside: |
                  $S = require("underscore.string")
                  ...
          - pattern-either:
              - pattern: $S.escapeHTML(...)
      - patterns:
          - pattern-either:
              - pattern-inside: |
                  import $S from "dompurify"
                  ...
              - pattern-inside: |
                  import { ..., $S,... } from "dompurify"
                  ...
              - pattern-inside: |
                  import * as $S from "dompurify"
                  ...
              - pattern-inside: |
                  $S = require("dompurify")
                  ...
              - pattern-inside: |
                  import $S from "isomorphic-dompurify"
                  ...
              - pattern-inside: |
                  import * as $S from "isomorphic-dompurify"
                  ...
              - pattern-inside: |
                  $S = require("isomorphic-dompurify")
                  ...
          - pattern-either:
              - patterns:
                  - pattern-inside: |
                      $VALUE = $S(...)
                      ...
                  - pattern: $VALUE.sanitize(...)
              - patterns:
                  - pattern-inside: |
                      $VALUE = $S.sanitize
                      ...
                  - pattern: $S(...)
              - pattern: $S.sanitize(...)
              - pattern: $S(...)
      - patterns:
          - pattern-either:
              - pattern-inside: |
                  import $S from 'xss';
                  ...
              - pattern-inside: |
                  import * as $S from 'xss';
                  ...
              - pattern-inside: |
                  $S = require("xss")
                  ...
          - pattern: $S(...)
      - patterns:
          - pattern-either:
              - pattern-inside: |
                  import $S from 'sanitize-html';
                  ...
              - pattern-inside: |
                  import * as $S from "sanitize-html";
                  ...
              - pattern-inside: |
                  $S = require("sanitize-html")
                  ...
          - pattern: $S(...)
      - patterns:
          - pattern-either:
              - pattern-inside: |
                  $S = new Remarkable()
                  ...
          - pattern: $S.render(...)

  - id: i18next-key-format
    patterns:
      - pattern-either:
          - patterns:
              - pattern-either:
                  - pattern: t('$KEY')
                  - pattern: t('$KEY', $OPTIONS)
                  - pattern: t([$DYNAMIC_KEY, '$KEY'])
                  - pattern: t([$DYNAMIC_KEY, '$KEY'], $OPTIONS)
              - metavariable-regex:
                  metavariable: $KEY
                  regex: (?!^[a-z0-9-]+\.[a-z0-9-]+\.[a-zA-Z0-9_.-]+$)
          - patterns:
              - pattern-either:
                  - pattern: t([$DYNAMIC_KEY, '$KEY'])
                  - pattern: t([$DYNAMIC_KEY, '$KEY'], $OPTIONS)
              - metavariable-regex:
                  metavariable: $DYNAMIC_KEY
                  regex: (?!^[`][a-z0-9-]+[.][a-z0-9-]+[.]\S+$)
          - patterns:
              - pattern-either:
                  - pattern: $I18NEXT.t('$KEY')
                  - pattern: $I18NEXT.t('$KEY', $OPTIONS)
                  - pattern: $I18NEXT.t([$DYNAMIC_KEY, '$KEY'])
                  - pattern: $I18NEXT.t([$DYNAMIC_KEY, '$KEY'], $OPTIONS)
              - metavariable-regex:
                  metavariable: $I18NEXT
                  regex: (^i18n|i18next$)
              - metavariable-regex:
                  metavariable: $KEY
                  regex: (?!^[a-z0-9-]+\.[a-z0-9-]+\.[a-zA-Z0-9_.-]+$)
          - patterns:
              - pattern-either:
                  - pattern: $I18NEXT.t([$DYNAMIC_KEY, '$KEY'])
                  - pattern: $I18NEXT.t([$DYNAMIC_KEY, '$KEY'], $OPTIONS)
              - metavariable-regex:
                  metavariable: $I18NEXT
                  regex: (^(i18n|i18next)$)
              - metavariable-regex:
                  metavariable: $DYNAMIC_KEY
                  regex: (?!^[`][a-z0-9-]+[.][a-z0-9-]+[.]\S+$)
    message: Translation key '$KEY' should match format 'MODULE.FEATURE.*'
    languages:
      - typescript
      - javascript
    severity: WARNING
    metadata:
      category: portability
      technology:
        - react
        - mui
        - i18next
      references:
        - https://www.notion.so/hendyirawan/Internationalization-Localization-Policy-318c21674e5f44c48d6f136a6eb2e024
        - https://mui.com/
        - https://react.i18next.com/
      license: Semgrep Rules License v1.0. For more details, visit
        semgrep.dev/legal/rules-license

  - id: jsx-label-not-i18n
    patterns:
      - pattern-either:
          - pattern: <TextField ... label="$MESSAGE" ... />
          - pattern: <Tab ... label="$MESSAGE" ... />
      - metavariable-regex:
          metavariable: $MESSAGE
          regex: (.*[a-zA-Z]+.*)
      - pattern-not: <$ELEMENT ... label="" ... />
      - pattern-not: <$ELEMENT ... label={t($KEY, ...)} ... />
    message: "JSX Component label not internationalized: '$MESSAGE'"
    languages:
      - typescript
      - javascript
    severity: WARNING
    metadata:
      category: portability
      technology:
        - react
        - mui
        - i18next
      references:
        - https://www.notion.so/hendyirawan/Internationalization-Localization-Policy-318c21674e5f44c48d6f136a6eb2e024
        - https://mui.com/
        - https://react.i18next.com/
      license: Semgrep Rules License v1.0. For more details, visit
        semgrep.dev/legal/rules-license

  - id: jsx-not-internationalized
    patterns:
      - pattern: <$ELEMENT>$MESSAGE</$ELEMENT>
      - metavariable-regex:
          metavariable: $MESSAGE
          regex: ([A-Za-z\n ]+[A-Za-z]+[A-Za-z\n ]+)
      - pattern-not: <$ELEMENT>t('$KEY', ...)</$ELEMENT>
    message: "JSX element not internationalized: '$MESSAGE'.  You should support
      different languages in your website or app with internationalization.
      Instead, use packages such as `i18next` in order to internationalize your
      elements."
    languages:
      - typescript
      - javascript
    severity: WARNING
    metadata:
      category: portability
      technology:
        - react
        - mui
        - i18next
      references:
        - https://www.notion.so/hendyirawan/Internationalization-Localization-Policy-318c21674e5f44c48d6f136a6eb2e024
        - https://mui.com/
        - https://react.i18next.com/
      license: Semgrep Rules License v1.0. For more details, visit
        semgrep.dev/legal/rules-license

  - id: react-legacy-component
    patterns:
      - pattern: |
          $METHOD(...) {
            ...
          }
      - metavariable-regex:
          metavariable: $METHOD
          regex: componentWillMount|componentWillReceiveProps|componentWillUpdate
    message: Legacy component lifecycle was detected - $METHOD.
    languages:
      - typescript
      - javascript
    severity: WARNING
    metadata:
      category: best-practice
      technology:
        - react
      license: Semgrep Rules License v1.0. For more details, visit
        semgrep.dev/legal/rules-license

  - id: react-props-spreading
    patterns:
      - pattern: <$X {...$PROPS} />
      - focus-metavariable: $PROPS
    message: It's best practice to explicitly pass props to an HTML component rather
      than use the spread operator. The spread operator risks passing invalid
      HTML props to an HTML element, which can cause console warnings or worse,
      give malicious actors a way to inject unexpected attributes.
    languages:
      - typescript
      - javascript
    severity: WARNING
    metadata:
      source-rule-url: https://github.com/yannickcr/eslint-plugin-react/blob/master/docs/rules/jsx-props-no-spreading.md
      references:
        - https://github.com/yannickcr/eslint-plugin-react/blob/master/docs/rules/jsx-props-no-spreading.md
      category: best-practice
      technology:
        - react
      license: Semgrep Rules License v1.0. For more details, visit
        semgrep.dev/legal/rules-license
