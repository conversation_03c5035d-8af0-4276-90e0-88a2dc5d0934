rules:
    - id: mongo-client-bad-auth
      languages:
          - go
          - ts
          - js
      severity: ERROR
      message:
          Warning MONGODB-CR was deprecated with the release of MongoDB 3.6 and
          is no longer supported by MongoDB 4.0 (see
          https://api.mongodb.com/python/current/examples/authentication.html for
          details).
      pattern: pymongo.MongoClient(..., authMechanism='MONGODB-CR')
      fix-regex:
          regex: MONGODB-CR
          replacement: SCRAM-SHA-256
      metadata:
          cwe:
              - 'CWE-477: Use of Obsolete Function'
          category: security
          technology:
              - pymongo
          references:
              - https://cwe.mitre.org/data/definitions/477.html
          subcategory:
              - vuln
          likelihood: LOW
          impact: LOW
          confidence: MEDIUM
          license: Commons Clause License Condition v1.0[LGPL-2.1-only]
          vulnerability_class:
              - Dangerous Method or Function

    - id: mongo-hostname-verification-disabled
      message: Found MongoDB client with SSL hostname verification disabled
      languages: [java, kotlin]
      severity: WARNING
      metadata:
          category: security
          subcategory: [audit]
          technology: [java, kotlin, mongodb]
          cwe: 'CWE-295: Improper Certificate Validation'
          confidence: HIGH
          likelihood: HIGH
          impact: HIGH
          references:
              - https://www.mongodb.com/docs/drivers/java/sync/current/fundamentals/connection/tls/#disable-hostname-verification
      pattern: $SETTINGS.invalidHostNameAllowed(true)
