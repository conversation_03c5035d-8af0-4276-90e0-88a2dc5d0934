rules:
  - id: detect-insecure-websocket
    pattern-regex: ws:\/\/
    languages: [regex]
    message: Insecure WebSocket Detected. WebSocket Secure (wss) should be used for all WebSocket connections.
    metadata:
      asvs:
        section: 'V13: API and Web Service Verification Requirements'
        control_id: 13.5.1 Insecure WebSocket
        control_url: https://github.com/OWASP/ASVS/blob/master/4.0/en/0x21-V13-API.md#v135-websocket-security-requirements
        version: '4'
      category: security
    severity: ERROR

  - id: detected-npm-registry-auth-token
    pattern-regex: _authToken=(\w{8})-(\w{4})-(\w{4})-(\w{4})-(\w{12})$
    languages: [regex]
    message: NPM registry authentication token detected
    paths:
      include:
      - '*npmrc*'
    severity: ERROR
    metadata:
      category: security
      technology:
      - secrets

  - id: detected-mailgun-api-key
    pattern-regex: key-[0-9a-zA-Z]{32}
    languages: [regex]
    message: Mailgun API Key detected
    severity: ERROR
    metadata:
      source-rule-url: https://github.com/dxa4481/truffleHogRegexes/blob/master/truffleHogRegexes/regexes.json
      category: security
      technology:
      - secrets

  - id: detected-private-key
    pattern-regex: (?i)-{5}begin( [dr]sa| ec| openssh| encrypted)? private key-{5}
    languages: [regex]
    message: Private Key detected
    severity: ERROR
    metadata:
      source-rule-url: https://github.com/grab/secret-scanner/blob/master/scanner/signatures/pattern.go
      category: security
      technology:
      - secrets

  - id: detected-sauce-token
    pattern-regex: |-
      (?i)sauce.{0,50}(\\\"|'|`)?[0-9a-f-]{36}(\\\"|'|`)?
    languages: [regex]
    message: Sauce Token detected
    severity: ERROR
    metadata:
      source-rule-url: https://github.com/grab/secret-scanner/blob/master/scanner/signatures/pattern.go
      category: security
      technology:
      - secrets

- id: detected-ssh-password
  pattern-regex: |-
    sshpass -p.*['|\\\"]
  languages: [regex]
  message: SSH Password detected
  severity: ERROR
  metadata:
    source-rule-url: https://github.com/grab/secret-scanner/blob/master/scanner/signatures/pattern.go
    category: security
    technology:
    - secrets

  - id: all-origins-allowed
    patterns:
    - pattern-inside: cors_rule { ... }
    - pattern: allowed_origins = ["*"]
    languages:
    - generic
    paths:
      include:
      - '*.tf'
    severity: WARNING
    message: CORS rule on bucket permits any origin
    metadata:
      references:
      - https://registry.terraform.io/providers/hashicorp/aws/latest/docs/resources/s3_bucket#using-cors
      category: security
      technology:
      - terraform

  - id: s3-public-read-bucket
    patterns:
    - pattern: acl = "public-read"
    - pattern-not-inside: '{ ... website { ... } ... }'
    languages:
    - generic
    paths:
      include:
      - '*.tf'
    severity: WARNING
    message: S3 bucket with public read access detected.
    metadata:
      references:
      - https://registry.terraform.io/providers/hashicorp/aws/latest/docs/resources/s3_bucket#acl
      - https://docs.aws.amazon.com/AmazonS3/latest/dev/acl-overview.html#canned-acl
      category: security
      technology:
      - terraform