rules:
    - id: channel-guarded-with-mutex
      pattern-either:
          - pattern: |
                $MUX.Lock()
                $VALUE <- $CHANNEL
                $MUX.Unlock()
          - pattern: |
                $MUX.Lock()
                $VALUE = <- $CHANNEL
                $MUX.Unlock()
      message: Detected a channel guarded with a mutex. Channels already have an
          internal mutex, so this is unnecessary. Remove the mutex. See
          https://hackmongo.com/page/golang-antipatterns/#guarded-channel for more
          information.
      languages:
          - go
      severity: WARNING
      metadata:
          category: best-practice
          technology:
              - go
          license: Semgrep Rules License v1.0. For more details, visit
              semgrep.dev/legal/rules-license

    - id: hidden-goroutine
      patterns:
          - pattern-not: |
                func $FUNC(...) {
                  go func() {
                    ...
                  }(...)
                  $MORE
                }
          - pattern: |
                func $FUNC(...) {
                  go func() {
                    ...
                  }(...)
                }
      message: Detected a hidden goroutine. Function invocations are expected to
          synchronous, and this function will execute asynchronously because all it
          does is call a goroutine. Instead, remove the internal goroutine and call
          the function using 'go'.
      languages:
          - go
      severity: WARNING
      metadata:
          category: best-practice
          technology:
              - go
          license: Semgrep Rules License v1.0. For more details, visit
              semgrep.dev/legal/rules-license

    - id: exported_loop_pointer
      message:
          '`$VALUE` is a loop pointer that may be exported from the loop. This
          pointer is shared between loop iterations, so the exported reference will
          always point to the last loop value, which is likely unintentional. To
          fix, copy the pointer to a new pointer within the loop.'
      metadata:
          references:
              - https://github.com/kyoh86/looppointer
          category: correctness
          technology:
              - go
          license: Semgrep Rules License v1.0. For more details, visit
              semgrep.dev/legal/rules-license
      severity: WARNING
      languages:
          - go
      pattern-either:
          - pattern: |
                for _, $VALUE := range $SOURCE {
                  <... &($VALUE) ...>
                }
          - pattern: |
                for _, $VALUE := range $SOURCE {
                  <... func() { <... &$VALUE ...> } ...>
                }
          - pattern: |
                for _, $VALUE := range $SOURCE {
                  <... $ANYTHING(..., <... &$VALUE ...>, ...) ...>
                }

    - id: hardcoded-eq-true-or-false
      message:
          Detected useless if statement. 'if (True)' and 'if (False)' always
          result in the same behavior, and therefore is not necessary in the code.
          Remove the 'if (False)' expression completely or just the 'if (True)'
          comparison depending on which expression is in the code.
      languages:
          - go
      severity: INFO
      pattern-either:
          - pattern: if (true) { ... }
          - pattern: if (false) { ... }
      metadata:
          category: correctness
          technology:
              - go
          license: Semgrep Rules License v1.0. For more details, visit
              semgrep.dev/legal/rules-license

    - id: useless-if-body
      pattern: |
          if ($X) {
              $S
          } else {
              $S
          }
      message:
          Detected identical statements in the if body and the else body of an
          if-statement. This will lead to the same code being executed no matter
          what the if-expression evaluates to. Instead, remove the if statement.
      languages:
          - go
      severity: WARNING
      metadata:
          category: maintainability
          technology:
              - go
          license: Semgrep Rules License v1.0. For more details, visit
              semgrep.dev/legal/rules-license

    - id: useless-if-conditional
      message: Detected an if block that checks for the same condition on both
          branches (`$X`). The second condition check is useless as it is the same
          as the first, and therefore can be removed from the code,
      languages:
          - go
      severity: WARNING
      pattern: |
          if ($X) {
              ...
          } else if ($X) {
              ...
          }
      metadata:
          category: maintainability
          technology:
              - go
          license: Semgrep Rules License v1.0. For more details, visit
              semgrep.dev/legal/rules-license
