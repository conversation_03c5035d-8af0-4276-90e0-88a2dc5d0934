rules:
    - id: missing-template-string-indicator
      patterns:
          - pattern-inside: |
                `...`
          - pattern: $STR
          - metavariable-regex:
                metavariable: $STR
                regex: .*[^$]+{[^{}]*}.*
      languages:
          - javascript
          - typescript
      message:
          This looks like a JavaScript template string. Are you missing a '$' in
          front of '{...}'?
      severity: INFO
      metadata:
          category: correctness
          technology:
              - js
          license: Semgrep Rules License v1.0. For more details, visit
              semgrep.dev/legal/rules-license

    - id: eqeq-is-bad
      patterns:
          - pattern-not-inside: assert(...)
          - pattern-either:
                - pattern: $X == $X
                - pattern: $X != $X
          - pattern-not: 1 == 1
      message:
          Detected a useless comparison operation `$X == $X` or `$X != $X`. This
          operation is always true. If testing for floating point NaN, use
          `math.isnan`, or `cmath.isnan` if the number is complex.
      languages:
          - javascript
          - typescript
      severity: INFO
      metadata:
          category: correctness
          technology:
              - javascript
          license: Semgrep Rules License v1.0. For more details, visit
              semgrep.dev/legal/rules-license

    - id: useless-assignment
      patterns:
          - pattern: |
                $X = $Y;
                $X = $Z;
          - pattern-not: |
                $X = $Y;
                $X = <... $X ...>;
      message: '`$X` is assigned twice; the first assignment is useless'
      languages:
          - javascript
          - typescript
      severity: INFO
      metadata:
          category: correctness
          technology:
              - javascript
          license: Semgrep Rules License v1.0. For more details, visit
              semgrep.dev/legal/rules-license
