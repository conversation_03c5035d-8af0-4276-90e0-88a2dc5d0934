rules:
    - id: moment-deprecated
      pattern: |
          import 'moment'
      message: Moment is a legacy project in maintenance mode. Consider using
          libraries that are actively supported, e.g. `dayjs`.
      languages:
          - typescript
          - javascript
      severity: ERROR
      metadata:
          category: best-practice
          technology:
              - moment
              - dayjs
          references:
              - https://momentjs.com/docs/#/-project-status/
              - https://day.js.org/
          license: Semgrep Rules License v1.0. For more details, visit
              semgrep.dev/legal/rules-license

    - id: useless-ternary
      pattern: |
          $CONDITION ? $ANS : $ANS
      message:
          It looks like no matter how $CONDITION is evaluated, this expression
          returns $ANS. This is probably a copy-paste error.
      languages:
          - typescript
          - javascript
      metadata:
          category: correctness
          technology:
              - react
          license: Semgrep Rules License v1.0. For more details, visit
              semgrep.dev/legal/rules-license
      severity: ERROR

    - id: cors-regex-wildcard
      message: "Unescaped '.' character in CORS domain regex $CORS: $PATTERN"
      metadata:
          cwe:
              - 'CWE-183: Permissive List of Allowed Inputs'
          category: security
          technology:
              - cors
          owasp:
              - A04:2021 - Insecure Design
          references:
              - https://owasp.org/Top10/A04_2021-Insecure_Design
          subcategory:
              - audit
          likelihood: LOW
          impact: LOW
          confidence: LOW
          license: Semgrep Rules License v1.0. For more details, visit
              semgrep.dev/legal/rules-license
          vulnerability_class:
              - Improper Validation
      languages:
          - ts
      severity: WARNING
      patterns:
          - pattern-either:
                - pattern: $CORS = [...,/$PATTERN/,...]
                - pattern: $CORS = /$PATTERN/
          - focus-metavariable: $PATTERN
          - metavariable-regex:
                metavariable: $PATTERN
                regex: .+?(?<!\\).\..+(?<!\\)\..+
          - metavariable-regex:
                metavariable: $CORS
                regex: (?i)cors
