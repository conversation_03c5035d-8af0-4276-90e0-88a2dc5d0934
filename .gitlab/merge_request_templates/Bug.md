Bug Fixes **ONLY**. NO NEW FEATURES ACCEPTED!

## Issues link

## List packages need be merged first

## Please check if the PR fulfills these requirements

### By owner

- [ ] Write all information under
- [ ] Demo: old behaviors, new behaviors, test coverages
- [ ] The commit message follows our guidelines
- [ ] Clarity: Is the code easy to understand for others? Can someone unfamiliar with the project follow the logic?
- [ ] Naming Conventions: Are variables, functions, classes, and files named meaningfully and consistently?
- [ ] Comments: Are comments used appropriately to explain complex or non-obvious logic? Is there an overuse of comments where the code itself should be clearer?
- [ ] Code Length: Are functions and methods kept short and to the point? Is any piece of code doing more than one thing and could it be refactored?
- [ ] Correctness: Does the code meet the requirements and specifications? Does it implement the intended business logic?
- [ ] Error Handling: Is error handling consistent and meaningful (e.g., try-catch blocks, logging)? Are errors gracefully handled or returned to the user in a meaningful way?
- [ ] Edge Cases: Are all edge cases, boundary conditions, and potential errors handled appropriately?
- [ ] Test Readability: Are the tests clear, with meaningful assertions and good coverage of edge cases?
- [ ] Duplication: Are there any sections of code that are duplicated? If so, can this be refactored into reusable functions or modules?

### By reviewer 1: replace_by_reviewer_name

- [ ] Do you understand all information under ?
- [ ] Demo: the video demo is clearly ?
- [ ] The commit message follows our guidelines
- [ ] Clarity: Is the code easy to understand for others? Can someone unfamiliar with the project follow the logic?
- [ ] Naming Conventions: Are variables, functions, classes, and files named meaningfully and consistently?
- [ ] Comments: Are comments used appropriately to explain complex or non-obvious logic? Is there an overuse of comments where the code itself should be clearer?
- [ ] Code Length: Are functions and methods kept short and to the point? Is any piece of code doing more than one thing and could it be refactored?
- [ ] Correctness: Does the code meet the requirements and specifications? Does it implement the intended business logic?
- [ ] Error Handling: Is error handling consistent and meaningful (e.g., try-catch blocks, logging)? Are errors gracefully handled or returned to the user in a meaningful way?
- [ ] Edge Cases: Are all edge cases, boundary conditions, and potential errors handled appropriately?
- [ ] Test Readability: Are the tests clear, with meaningful assertions and good coverage of edge cases?
- [ ] Duplication: Are there any sections of code that are duplicated? If so, can this be refactored into reusable functions or modules?

### By reviewer 2: replace_by_reviewer_name

- [ ] Do you understand all information under ?
- [ ] Demo: the video demo is clearly ?
- [ ] The commit message follows our guidelines
- [ ] Clarity: Is the code easy to understand for others? Can someone unfamiliar with the project follow the logic?
- [ ] Naming Conventions: Are variables, functions, classes, and files named meaningfully and consistently?
- [ ] Comments: Are comments used appropriately to explain complex or non-obvious logic? Is there an overuse of comments where the code itself should be clearer?
- [ ] Code Length: Are functions and methods kept short and to the point? Is any piece of code doing more than one thing and could it be refactored?
- [ ] Correctness: Does the code meet the requirements and specifications? Does it implement the intended business logic?
- [ ] Error Handling: Is error handling consistent and meaningful (e.g., try-catch blocks, logging)? Are errors gracefully handled or returned to the user in a meaningful way?
- [ ] Edge Cases: Are all edge cases, boundary conditions, and potential errors handled appropriately?
- [ ] Test Readability: Are the tests clear, with meaningful assertions and good coverage of edge cases?
- [ ] Duplication: Are there any sections of code that are duplicated? If so, can this be refactored into reusable functions or modules?

## Bug information

- **What wrong?**<br>

- **Why is born?**<br>

- **Scope affects?**<br>

## Talk about your code changes

## Is there anything in this PR that needs careful consideration?

- [ ] No
- [ ] Yes (describe more information)

## Does this PR introduce a breaking change?

- [ ] No
- [ ] Yes (describe more information)

- **If no, why do you think this PR not breaking current codebase?**<br>

## Old behavior (bugs demo)

## Bugs fixed

## Screenshot coverage
