const Configuration = {
    /*
     * Resolve and load @commitlint/config-conventional from node_modules.
     * Referenced packages must be installed
     */
    extends: ['@commitlint/config-conventional'],
    rules: {
        'type-enum': [
            2,
            'always',
            [
                'build',
                'chore',
                'ci',
                'docs',
                'feat',
                'fix',
                'perf',
                'refactor',
                'revert',
                'style',
                'test',
                'draft',
            ],
        ],
        'subject-case': [
            2,
            'always',
            [
                'lower-case', // default
                'upper-case', // UPPERCASE
                'camel-case', // camelCase
                'kebab-case', // kebab-case
                'pascal-case', // PascalCase
                'sentence-case', // Sentence case
                'snake-case', // snake_case
                'start-case', // Start Case
            ],
        ],
        'subject-min-length': [2, 'always', 13],
        'body-max-length': [0, 'always'],
        'body-max-line-length': [0, 'always'],
        'footer-max-length': [0, 'always'],
        'footer-max-line-length': [0, 'always'],
        'header-max-length': [0, 'always'],
        'scope-max-length': [0, 'always'],
        'subject-max-length': [0, 'always'],
        'type-max-length': [0, 'always'],
    },
};

module.exports = Configuration;
