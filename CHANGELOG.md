## [1.3.4](https://gitlab.com/btaskee/btaskee-ops/tasker-ops/compare/v1.3.3...v1.3.4) (2024-11-14)

### Bug Fixes

- city of tasker profile on tasker onboarding ([fd4cefb](https://gitlab.com/btaskee/btaskee-ops/tasker-ops/commit/fd4cefb6ccfef74463be2cf507f2ab9d395e9a13))

### Features

- config manual semantic release ([aae5152](https://gitlab.com/btaskee/btaskee-ops/tasker-ops/commit/aae5152ad4dee0e73025ca2c71edcb7e2855f883))
- filter by cities on tasker onboarding ([8da20a2](https://gitlab.com/btaskee/btaskee-ops/tasker-ops/commit/8da20a2d13ee8fd899886f536bd2551359d3595a))

# [1.3.3](https://gitlab.com/btaskee/btaskee-ops/tasker-ops/compare/v1.3.2...v1.3.3) (2024-11-11)

### Bug Fixes

- adding field unique sort ([087233f](https://gitlab.com/btaskee/btaskee-ops/tasker-ops/commit/087233f))
- allow manager have full permission like root user ([fe98d92](https://gitlab.com/btaskee/btaskee-ops/tasker-ops/commit/fe98d92))
- can not submit edit office when existed off days is in the past ([4ba2fef](https://gitlab.com/btaskee/btaskee-ops/tasker-ops/commit/4ba2fef))
- city of tasker profile on tasker onboarding ([fd4cefb](https://gitlab.com/btaskee/btaskee-ops/tasker-ops/commit/fd4cefb))
- do not allow user login if account inactive ([6d77de2](https://gitlab.com/btaskee/btaskee-ops/tasker-ops/commit/6d77de2))
- fix toolkit payment method query ([77954bb](https://gitlab.com/btaskee/btaskee-ops/tasker-ops/commit/77954bb))
- remove prop feature flag and call it from global store ([7897bdc](https://gitlab.com/btaskee/btaskee-ops/tasker-ops/commit/7897bdc))
- require field cities and reinstall pnpm ([1833bcc](https://gitlab.com/btaskee/btaskee-ops/tasker-ops/commit/1833bcc))
- style displaying total on btakee table ([cdb6e46](https://gitlab.com/btaskee/btaskee-ops/tasker-ops/commit/cdb6e46))
- update tag version updateing notification message, add more sending notification ([fc1e317](https://gitlab.com/btaskee/btaskee-ops/tasker-ops/commit/fc1e317))

### Features

- add logic when create and update course to another information collection ([4211568](https://gitlab.com/btaskee/btaskee-ops/tasker-ops/commit/4211568))
- add number of quiz collection into table of view quiz collection course ([9894dbe](https://gitlab.com/btaskee/btaskee-ops/tasker-ops/commit/9894dbe))
- add support for applying courses to all cities ([9922c05](https://gitlab.com/btaskee/btaskee-ops/tasker-ops/commit/9922c05))
- adding field city special campaign ([4556f08](https://gitlab.com/btaskee/btaskee-ops/tasker-ops/commit/4556f08))
- clone new rbac from mkt repo ([d939148](https://gitlab.com/btaskee/btaskee-ops/tasker-ops/commit/d939148))
- config holidays and off days for office ([3f63681](https://gitlab.com/btaskee/btaskee-ops/tasker-ops/commit/3f63681))
- config manual semantic release ([aae5152](https://gitlab.com/btaskee/btaskee-ops/tasker-ops/commit/aae5152))
- filter by cities on tasker onboarding ([8da20a2](https://gitlab.com/btaskee/btaskee-ops/tasker-ops/commit/8da20a2))
- new logic to support go send noti when create and update course ([be8dff4](https://gitlab.com/btaskee/btaskee-ops/tasker-ops/commit/be8dff4))
- special campaign and special campaign transaction ([05e7aae](https://gitlab.com/btaskee/btaskee-ops/tasker-ops/commit/05e7aae))
- tasker location detected page ([ef753d4](https://gitlab.com/btaskee/btaskee-ops/tasker-ops/commit/ef753d4))
- wrap new logic query function with feature flag ([2f5306e](https://gitlab.com/btaskee/btaskee-ops/tasker-ops/commit/2f5306e))

# [1.3.2](https://gitlab.com/btaskee/btaskee-ops/tasker-ops/compare/v1.3.1...v1.3.2) (2024-10-20)

### Features

- update schema tag version updating collection ([d3535b0c](https://gitlab.com/btaskee/btaskee-ops/tasker-ops/-/merge_requests/128/diffs?commit_id=d3535b0ce193ddbe59c0791cd65a517eed9601b6))

# [1.3.1](https://gitlab.com/btaskee/btaskee-ops/tasker-ops/compare/v1.3.0...v1.3.1) (2024-10-18)

### Bug Fixes

- check size null training history all tasker ([db982ff](https://gitlab.com/btaskee/btaskee-ops/tasker-ops/commit/db982ffbf8938fb88884b341c33e6cf16b8c8191))
- cspell versions increased ([885e5b4](https://gitlab.com/btaskee/btaskee-ops/tasker-ops/commit/885e5b41a3e52e36eee5814a73194b4bfe1eae71))
- filter with user cities, fix query getting total on all tasker page ([2e7a560](https://gitlab.com/btaskee/btaskee-ops/tasker-ops/commit/2e7a5606508fb75adf61a38111b30b9c597408d2))
- fix quizzes detail ui, and action quizzes ([8b8ea74](https://gitlab.com/btaskee/btaskee-ops/tasker-ops/commit/8b8ea748bc9b34340cdedbb473546d6b160122db))
- remmove hoc404 not need ([4aeac1f](https://gitlab.com/btaskee/btaskee-ops/tasker-ops/commit/4aeac1fa009cd5961b568934e78e02b1f0870214))

### Features

- add new fields for quiz collection ([1156d1c](https://gitlab.com/btaskee/btaskee-ops/tasker-ops/commit/1156d1ccf9ad4291c61da6c928134ad277d1683e))
- apply env sandbox ([0ada4af](https://gitlab.com/btaskee/btaskee-ops/tasker-ops/commit/0ada4af64c964ab9b1c5e50302dfc2188ba283af))

# [1.3.0](https://gitlab.com/btaskee/btaskee-ops/tasker-ops/compare/v1.2.0...v1.3.0) (2024-09-30)

### Bug Fixes

- api indo ưith prefix -id into -indo ([7130252](https://gitlab.com/btaskee/btaskee-ops/tasker-ops/commit/7130252f790f114b1a522b14a9e2be53f543d84f))
- api indo ưith prefix -id into -indo ([4fa71da](https://gitlab.com/btaskee/btaskee-ops/tasker-ops/commit/4fa71da751f8625d703d0242ab5d078d031a91d0))
- api indo ưith prefix -id into -indo ([35e0719](https://gitlab.com/btaskee/btaskee-ops/tasker-ops/commit/35e071986fa548688a2563b0828d4924e404a28c))
- pnpm lock ([5d5d144](https://gitlab.com/btaskee/btaskee-ops/tasker-ops/commit/5d5d144a35d5819ed8a0751b46bcd27d3d140737))
- size number of submission and remove button create quiz at list training history ([815637e](https://gitlab.com/btaskee/btaskee-ops/tasker-ops/commit/815637e8a32bc5385980ab270ad2b32ba652ee09))
- size number of submission and remove button create quiz at list training history ([2dfd28a](https://gitlab.com/btaskee/btaskee-ops/tasker-ops/commit/2dfd28a0cb2f0b0b8b946463f3efd0a84d03d975))
- style of date in training history of all tasker and course ([726212a](https://gitlab.com/btaskee/btaskee-ops/tasker-ops/commit/726212a3dd4b88d40c291c6957e7155cdb7abb78))

### Features

- implement admin tasker list page ([c022731](https://gitlab.com/btaskee/btaskee-ops/tasker-ops/commit/c02273177093f25a54740d928f003d9c731c2dda))
- implement reject reason page ([ba41b3a](https://gitlab.com/btaskee/btaskee-ops/tasker-ops/commit/ba41b3a68ee317b970fafc5f3e9d2475b9f9c292))
- implement tasker detail page ([5f52b17](https://gitlab.com/btaskee/btaskee-ops/tasker-ops/commit/5f52b17a49591427be7abf1ae39496149d596d45))
- improvement video desc ui page quiz collection detail ([025c95d](https://gitlab.com/btaskee/btaskee-ops/tasker-ops/commit/025c95d07133b34c6e268d813381531f7f164919))
- list training history for all tasker ([8d07b62](https://gitlab.com/btaskee/btaskee-ops/tasker-ops/commit/8d07b627ef2618ca497563a59089f175dda8f649))
- not filder date range table pages tasker training ([331fbd8](https://gitlab.com/btaskee/btaskee-ops/tasker-ops/commit/331fbd8b8591ba61546edeb694cad8c2bec3972d))
- storage percentage at decimal instead of numbers ([f0e720a](https://gitlab.com/btaskee/btaskee-ops/tasker-ops/commit/f0e720af48eed37504a8f6f14919a76c2729c1b0))

# [1.2.0](https://gitlab.com/btaskee/btaskee-ops/tasker-ops/compare/v1.1.2...v1.2.0) (2024-09-13)

### Bug Fixes

- add api name after fix conflict ([d35293e](https://gitlab.com/btaskee/btaskee-ops/tasker-ops/commit/d35293e2c8498f15929a244224a9250f7654f40e))
- change collection field name ([2e2acfd](https://gitlab.com/btaskee/btaskee-ops/tasker-ops/commit/2e2acfd741ca84d8e04ca7ba06b4b9fde9c68e76))
- change collection field name ([7252681](https://gitlab.com/btaskee/btaskee-ops/tasker-ops/commit/7252681b619ef8cc084ca4068bc01d992fddcc63))
- change collection field name ([1c14cbf](https://gitlab.com/btaskee/btaskee-ops/tasker-ops/commit/1c14cbfde453f5e3b6709dc48f42db32cbd3ea82))
- change name to title ([b620c89](https://gitlab.com/btaskee/btaskee-ops/tasker-ops/commit/b620c89cc9d0528f778afe97394b75ad371c5041))
- ci fail by caching node modules more times ([d00a00e](https://gitlab.com/btaskee/btaskee-ops/tasker-ops/commit/d00a00ec1319cd260368af1a7016186dd9ac82a7))
- ci fail by caching node modules more times ([a899d63](https://gitlab.com/btaskee/btaskee-ops/tasker-ops/commit/a899d63c9fa9be2f8397b877e4ed59fab24f085f))
- ci fail by caching node modules more times ([34f1769](https://gitlab.com/btaskee/btaskee-ops/tasker-ops/commit/34f176911bfbe4780465223df4c5b1fdc288358f))
- ci fail by caching node modules more times ([bada26f](https://gitlab.com/btaskee/btaskee-ops/tasker-ops/commit/bada26f039e62f797522ab38e4db8b4677a1fa6c))
- ci fail by caching node modules more times ([eb5e4a6](https://gitlab.com/btaskee/btaskee-ops/tasker-ops/commit/eb5e4a6b238d49215d6009d50cd72247b7dadd35))
- course tasker training ([ad8384b](https://gitlab.com/btaskee/btaskee-ops/tasker-ops/commit/ad8384b05e1fa3e7482e6c2896e02761575df632))
- course tasker training ([383c20d](https://gitlab.com/btaskee/btaskee-ops/tasker-ops/commit/383c20dd3941a29f260c05318b8e4bb0773cbb8d))
- feedback qc improvement ([ef9df23](https://gitlab.com/btaskee/btaskee-ops/tasker-ops/commit/ef9df230b006cc0ddab4059d0778adf43c818881))
- fix issue missing code ([843e456](https://gitlab.com/btaskee/btaskee-ops/tasker-ops/commit/843e45608c84faa52606e59f4ffbee759118f79a))
- fix query update quiz and quizzes ([8b63fd7](https://gitlab.com/btaskee/btaskee-ops/tasker-ops/commit/8b63fd7799496c3332f698a6a6429c8450643d7f))
- fix quizz collection issue ([44ea5e2](https://gitlab.com/btaskee/btaskee-ops/tasker-ops/commit/44ea5e217aca880c2628ae07f4b9a0340b7508ac))
- fix some ui error ([dbee10e](https://gitlab.com/btaskee/btaskee-ops/tasker-ops/commit/dbee10ed978c74fc5a0435a523eb60b8f844eb56))
- fix table select all make confuse, remove slate and plate ([deaf1a7](https://gitlab.com/btaskee/btaskee-ops/tasker-ops/commit/deaf1a793a28cc1f4cdacb676be46aa419321c20))
- fix ui layout diff w figma ([403915d](https://gitlab.com/btaskee/btaskee-ops/tasker-ops/commit/403915d608266f6727d6cf8d82333e243a414d75))
- format ammount ([295dd91](https://gitlab.com/btaskee/btaskee-ops/tasker-ops/commit/295dd912e28115a1ce44ed04a596f7ae7497fde9))
- get services in training ([dc34cad](https://gitlab.com/btaskee/btaskee-ops/tasker-ops/commit/dc34cad59bc2a8612b5d43f6310dec374c25827f))
- get total of course list query ([31e60bc](https://gitlab.com/btaskee/btaskee-ops/tasker-ops/commit/31e60bc43f8a1a4c5aaee75fda54f14910825fbc))
- get wrong attemp count ([f6505e6](https://gitlab.com/btaskee/btaskee-ops/tasker-ops/commit/f6505e6b4ff1162f488cf14cd548f23278ddb43a))
- hot fix some issue mistake ([5036fb8](https://gitlab.com/btaskee/btaskee-ops/tasker-ops/commit/5036fb8aa55710215e10b09489121e4ffa71f83f))
- missing check unique on update quiz and quizzes ([2af200d](https://gitlab.com/btaskee/btaskee-ops/tasker-ops/commit/2af200ddc9fa43886d6880e9ae8e75ecb9c7a2cb))
- missing locale translation ([203a766](https://gitlab.com/btaskee/btaskee-ops/tasker-ops/commit/203a7664de2de6ab1c24270bf97072e54055030c))
- not allow choose itself course and unset condition with value is none ([752f33b](https://gitlab.com/btaskee/btaskee-ops/tasker-ops/commit/752f33b649868512af6d067d8729ca84e4738edc))
- pnpm and lint rules work error with peers deps ([11536a6](https://gitlab.com/btaskee/btaskee-ops/tasker-ops/commit/11536a6ced5080566f66316a382430ba018bd56a))
- pnpm and lint rules work error with peers deps ([74c27eb](https://gitlab.com/btaskee/btaskee-ops/tasker-ops/commit/74c27eb44a60f965fd164a3a8a7d5b6983a7e7f9))
- pnpm and lint rules work error with peers deps ([b8d0e59](https://gitlab.com/btaskee/btaskee-ops/tasker-ops/commit/b8d0e596b457466d861752aa859416e1841b6120))
- pnpm and lint rules work error with peers deps ([5cffb1b](https://gitlab.com/btaskee/btaskee-ops/tasker-ops/commit/5cffb1bf49958b4d4a493bfbe5ecc01ed9926b0c))
- pnpm and lint rules work error with peers deps ([7cb0ce7](https://gitlab.com/btaskee/btaskee-ops/tasker-ops/commit/7cb0ce7859213dde3fd961d582acf8c2f05c268a))
- pnpm and lint rules work error with peers deps ([adfcfd1](https://gitlab.com/btaskee/btaskee-ops/tasker-ops/commit/adfcfd1e89107a74c67b154924d20c7681591d99))
- pnpm and lint rules work error with peers deps ([d5e07fd](https://gitlab.com/btaskee/btaskee-ops/tasker-ops/commit/d5e07fd1741136bd0a2f0ca19c7c8b142c3b6fb4))
- pnpm and lint rules work error with peers deps ([39461fa](https://gitlab.com/btaskee/btaskee-ops/tasker-ops/commit/39461fab3143baa2da7484d6d31fdbc8a9f6d454))
- pnpm and lint rules work error with peers deps ([d483b61](https://gitlab.com/btaskee/btaskee-ops/tasker-ops/commit/d483b6171a936f933c50c916c86818bf5b2fbbab))
- pnpm and lint rules work error with peers deps ([5928ae7](https://gitlab.com/btaskee/btaskee-ops/tasker-ops/commit/5928ae79940f5b8129ea97033d1c7895f536f979))
- taskerTrainingQuizCollections ([c7c23e9](https://gitlab.com/btaskee/btaskee-ops/tasker-ops/commit/c7c23e98840157286dcdb0c089846512e22a6e72))
- type and todo course ([0a0404d](https://gitlab.com/btaskee/btaskee-ops/tasker-ops/commit/0a0404d8d2d478452ebd15b315095a30de33af5d))
- type error at unit test ([c492890](https://gitlab.com/btaskee/btaskee-ops/tasker-ops/commit/c492890febcc580d2661d363c5d0b7f98741d99e))
- type of quizzes ([ac1c30e](https://gitlab.com/btaskee/btaskee-ops/tasker-ops/commit/ac1c30ea611caa0a7077dc5c0b40848ae9b97872))
- type of title and name ([f0d78e4](https://gitlab.com/btaskee/btaskee-ops/tasker-ops/commit/f0d78e4e7dea557677685e56981cb73cc01db42e))
- type of title and name ([fbe04fe](https://gitlab.com/btaskee/btaskee-ops/tasker-ops/commit/fbe04fe3efb1025655651a39c4a67dfcd7593849))
- type of title and name ([4b35f70](https://gitlab.com/btaskee/btaskee-ops/tasker-ops/commit/4b35f70e73c9f8d6c79e05c8642ef843e12644d6))
- update course type in tasker training ([c2502c6](https://gitlab.com/btaskee/btaskee-ops/tasker-ops/commit/c2502c6bbbd75bc4bb280022974997899e61dcb0))
- update get match value func clearly ([3ae30c8](https://gitlab.com/btaskee/btaskee-ops/tasker-ops/commit/3ae30c8b6917f49f32d508da974cb354f99693de))
- update time of quiz collection at the same time update course include quiz collection ([71c8c52](https://gitlab.com/btaskee/btaskee-ops/tasker-ops/commit/71c8c52bde8f50e7e5a968539409fa4b43699e81))
- width of tool name in table ([80e5bbe](https://gitlab.com/btaskee/btaskee-ops/tasker-ops/commit/80e5bbe684b41f19ea8cd4a2db730a3875cb734c))

### Features

- basic querying for tasker training ([334214f](https://gitlab.com/btaskee/btaskee-ops/tasker-ops/commit/334214f2750ddaedfd26605ad844f099b74d16c1))
- course with two screen view list and create ([50de5ac](https://gitlab.com/btaskee/btaskee-ops/tasker-ops/commit/50de5acea9f47d5f5170f196e338525dbf6c93e1))
- course with two screen view list and create ([442abec](https://gitlab.com/btaskee/btaskee-ops/tasker-ops/commit/442abec3f01383b872cb9276f93ed5a9d2474d1c))
- course with two screen view list and create ([2e98617](https://gitlab.com/btaskee/btaskee-ops/tasker-ops/commit/2e986176b2e92148763a517113fe33a3791bf55c))
- create course form controller ([e6f83a2](https://gitlab.com/btaskee/btaskee-ops/tasker-ops/commit/e6f83a216f35b04374aae4e9419e829a14db3f5f))
- create schema and query for payment and delivery page ([d82bef4](https://gitlab.com/btaskee/btaskee-ops/tasker-ops/commit/d82bef45096b2ccb59b17ef7178279b172bffff9))
- detail screen course ([64d6009](https://gitlab.com/btaskee/btaskee-ops/tasker-ops/commit/64d60094ae7b7f0e4f2b2cda4f997d5dd105c0df))
- fix type of quizzes ([f5bc899](https://gitlab.com/btaskee/btaskee-ops/tasker-ops/commit/f5bc8993a15a27a1c51c6434063f6484bfe673cb))
- full feature create quiz collection ([6fc0b64](https://gitlab.com/btaskee/btaskee-ops/tasker-ops/commit/6fc0b649c107279ed574070ebc107e6c3be51bbc))
- handle remove quizzes in dialog ([1eda14a](https://gitlab.com/btaskee/btaskee-ops/tasker-ops/commit/1eda14a344735b863d695f985b4ac6eb1f847bc6))
- handle remove quizzes in dialog ([eaa239c](https://gitlab.com/btaskee/btaskee-ops/tasker-ops/commit/eaa239c8f6acdae110d63a460f492ca56fc4cde5))
- implement btaskee profile configuration pages ([575641b](https://gitlab.com/btaskee/btaskee-ops/tasker-ops/commit/575641bffd45e717e2475b275522aee0c6579f4c))
- implement configuration personal page manangement ([8fe4b7c](https://gitlab.com/btaskee/btaskee-ops/tasker-ops/commit/8fe4b7c99282987ba53b1ff42cb4f07e5dc99caa))
- implement order management page ([7a896ea](https://gitlab.com/btaskee/btaskee-ops/tasker-ops/commit/7a896ea4259d6c4119501ded89b7c8221e701d13))
- implement order management page ([e848d37](https://gitlab.com/btaskee/btaskee-ops/tasker-ops/commit/e848d375545c30018a2caabd0e1747a181939efe))
- implement order management pages ([1853586](https://gitlab.com/btaskee/btaskee-ops/tasker-ops/commit/1853586a364c84979370df0c53befb304412cf27))
- implement order management pages ([09f677a](https://gitlab.com/btaskee/btaskee-ops/tasker-ops/commit/09f677ab78d5c81f05d736b307c6107e2100a51e))
- implement order management pages ([6e5ca55](https://gitlab.com/btaskee/btaskee-ops/tasker-ops/commit/6e5ca55a70b15c358d61d1a8292c5974671b1968))
- implement tool require pages ([10c189b](https://gitlab.com/btaskee/btaskee-ops/tasker-ops/commit/10c189b43162bf6e262063c1fd14d06460b3087e))
- implement ui for update personal page management page ([29f33f5](https://gitlab.com/btaskee/btaskee-ops/tasker-ops/commit/29f33f59e699670aecdc8ed9c3628fddab7f2a15))
- import csv with validation ([48b9d5a](https://gitlab.com/btaskee/btaskee-ops/tasker-ops/commit/48b9d5a12ad0e11d637b14a1dc17e5d87e924d4e))
- installment and full payment ([70a8d8f](https://gitlab.com/btaskee/btaskee-ops/tasker-ops/commit/70a8d8fa35caddaa9ac6818696b01a30e7b72f13))
- installment and full payment ([d9a97ca](https://gitlab.com/btaskee/btaskee-ops/tasker-ops/commit/d9a97ca9cf78ff9f5b9cc1e26ec5622ed7faac21))
- installment and full payment ([adcc1ad](https://gitlab.com/btaskee/btaskee-ops/tasker-ops/commit/adcc1ad39d0a6e8559d3dbe138f72bcd6f2cc667))
- list training courses view ([743cd33](https://gitlab.com/btaskee/btaskee-ops/tasker-ops/commit/743cd336d54437a370d45f2128cba77d6049d1dc))
- migrate from npm into pnpm ([b1dc982](https://gitlab.com/btaskee/btaskee-ops/tasker-ops/commit/b1dc9829a4cc32d7e2da500aa235ca4d0bd3bbb2))
- screen detail course ([7726854](https://gitlab.com/btaskee/btaskee-ops/tasker-ops/commit/7726854fe9693bb09ad575f7208c3c8f4e288ca3))
- screen edit course ([d47ae5a](https://gitlab.com/btaskee/btaskee-ops/tasker-ops/commit/d47ae5a1f01b01daccfed4d2621c51cb8eb2dd5c))
- taske training quiz create view edit ([d048535](https://gitlab.com/btaskee/btaskee-ops/tasker-ops/commit/d048535a1b3abf2abbb6f45886e7da3ebdec9aaf))
- tasker training create quiz ([56e14f0](https://gitlab.com/btaskee/btaskee-ops/tasker-ops/commit/56e14f0c17fa6ed4972f56d020ab3caf8e524fd2))
- tasker training history ([1868c0e](https://gitlab.com/btaskee/btaskee-ops/tasker-ops/commit/1868c0eafe554cdb118db39389f0c1d1968e65d8))
- toolkit management ([bacb299](https://gitlab.com/btaskee/btaskee-ops/tasker-ops/commit/bacb2992e060a26600c522764d612b6de1f0d637))
- ui of tasker training history detail ([df2ab9b](https://gitlab.com/btaskee/btaskee-ops/tasker-ops/commit/df2ab9b2617427a916ca038fe7f8111087127168))
- unique code quiz and quiz collection ([00b8169](https://gitlab.com/btaskee/btaskee-ops/tasker-ops/commit/00b81695f3ad5764dff9d449b41a64a1d065beb9))
- update behavior with form action button ([3c7e4b9](https://gitlab.com/btaskee/btaskee-ops/tasker-ops/commit/3c7e4b98c0a37fcf3d6dafb817e07c9c852e27ec))
- update field needed in training submission ([b8b122d](https://gitlab.com/btaskee/btaskee-ops/tasker-ops/commit/b8b122d88eef7c8c5ac87b7fcceaece3963d587a))
- view list quiz collection ([156aca6](https://gitlab.com/btaskee/btaskee-ops/tasker-ops/commit/156aca6b1be1b40667cf227c8e2267bf366fdda8))

## [1.1.2](https://gitlab.com/btaskee/btaskee-ops/tasker-ops/compare/v1.1.1...v1.1.2) (2024-08-16)

### Bug Fixes

- create new page for add new user, fix ui ([f33e5b3](https://gitlab.com/btaskee/btaskee-ops/tasker-ops/commit/f33e5b329bdfc63ba93ce4f693b4efcbfbd8e5ef))

## [1.1.1](https://gitlab.com/btaskee/btaskee-ops/tasker-ops/compare/v1.1.0...v1.1.1) (2024-08-15)

### Bug Fixes

- change login background and title ([bb17e5c](https://gitlab.com/btaskee/btaskee-ops/tasker-ops/commit/bb17e5cfc6d2de8110d5c497768b693886f26e9b))
- improve ui for rbac, tree view for group management ([77c2c49](https://gitlab.com/btaskee/btaskee-ops/tasker-ops/commit/77c2c49b77fa9ac7bbb0a3014f134e58a5e1e430))
- update handle ispartner in tasker profile ([c8dcc66](https://gitlab.com/btaskee/btaskee-ops/tasker-ops/commit/c8dcc66299e647b7a3f6929f52ea3f0943754aa8))

# [1.1.0](https://gitlab.com/btaskee/btaskee-ops/tasker-ops/compare/v1.0.1...v1.1.0) (2024-07-22)

### Bug Fixes

- format schema ([965e665](https://gitlab.com/btaskee/btaskee-ops/tasker-ops/commit/965e665218f35232db42b2d4b3e15be41e160782))
- multi language pattern ([2dbbe41](https://gitlab.com/btaskee/btaskee-ops/tasker-ops/commit/2dbbe41eb12fc8b9534c1ce86968feb9145b2ea6))
- remove import type from bundle ([537003d](https://gitlab.com/btaskee/btaskee-ops/tasker-ops/commit/537003da4caa3639abc8ab805636483b67ed9751))
- update pack lock ([2a57596](https://gitlab.com/btaskee/btaskee-ops/tasker-ops/commit/2a575963c5d27396e57439a79c8628bced0800f1))
- update package lock ([7ee96b2](https://gitlab.com/btaskee/btaskee-ops/tasker-ops/commit/7ee96b29506e46f0f631ee16273c9807fea76dfb))
- update package log ([a3316bc](https://gitlab.com/btaskee/btaskee-ops/tasker-ops/commit/a3316bcd934cff6f6c5c05905b633c9fa2c77a3b))

### Features

- auth flow repsonsive ([003e3bc](https://gitlab.com/btaskee/btaskee-ops/tasker-ops/commit/003e3bc87549dde54e3611ff78c2d605756f7a34))
- history breward page ([491d6bb](https://gitlab.com/btaskee/btaskee-ops/tasker-ops/commit/491d6bbe4b7433ae173d67c2498a0d80dd80708e))
- remove import btaskee type ([bd9ba2d](https://gitlab.com/btaskee/btaskee-ops/tasker-ops/commit/bd9ba2df468b38f402ba320bda7621932d25c7ad))
- report tasker operation ([6e2c572](https://gitlab.com/btaskee/btaskee-ops/tasker-ops/commit/6e2c57264da990391655d61f43f6a233922dd097))
- tasker operation query ([01ee00c](https://gitlab.com/btaskee/btaskee-ops/tasker-ops/commit/01ee00c2c9b40ebe7935375365b364dea0cf1fe6))
- training journey, tasker incentive ([e97123a](https://gitlab.com/btaskee/btaskee-ops/tasker-ops/commit/e97123a002a29f92d56ff379f1ca4c221a2428d0))
- training tasker, tasker toolkit lading details, things to know schema ([f2a4d49](https://gitlab.com/btaskee/btaskee-ops/tasker-ops/commit/f2a4d492e2fca5db4d9491ffcb0353893f54fc9d))
- unit testing, implement chart ui ([a93da7e](https://gitlab.com/btaskee/btaskee-ops/tasker-ops/commit/a93da7e285e27a80acb0f58489ef2ecd31b8203b))

## [1.0.1](https://gitlab.com/btaskee/btaskee-ops/tasker-ops/compare/v1.0.0...v1.0.1) (2024-07-01)

### Bug Fixes

- docker file with btaskee package ([5bdf0d9](https://gitlab.com/btaskee/btaskee-ops/tasker-ops/commit/5bdf0d93d521915a2b3560b691b63066aed7fca0))
- docker file with btaskee package ([f78d1f8](https://gitlab.com/btaskee/btaskee-ops/tasker-ops/commit/f78d1f8cea6b6a8a317284547218d80ab7abd0ef))

# 1.0.0 (2024-07-01)

### Bug Fixes

- add permission for employee profile ([c5930a2](https://gitlab.com/btaskee/btaskee-ops/tasker-ops/commit/c5930a2ef9a447e75d62ffbf5b325ac05ad066fb))
- add translation for cancel button, clean importting translation ([9dc7331](https://gitlab.com/btaskee/btaskee-ops/tasker-ops/commit/9dc7331243b382574cc4cf56abf46e0d7e420e33))
- catch error flow at action and loader hoc ([baf632b](https://gitlab.com/btaskee/btaskee-ops/tasker-ops/commit/baf632bc31c13cd5113cdd5fce2e45855a043008))
- catch error flow at action and loader hoc ([f56e6fd](https://gitlab.com/btaskee/btaskee-ops/tasker-ops/commit/f56e6fd2459bb316afbb73ec59a9bff183c4dbf5))
- change language and do not get all fields user profile ([1e6b162](https://gitlab.com/btaskee/btaskee-ops/tasker-ops/commit/1e6b162849621d5328c166473a971801a90e5666))
- change logic getting verifying profile ([36f486a](https://gitlab.com/btaskee/btaskee-ops/tasker-ops/commit/36f486a693dd00332aca1b9282a8b9e546818dd8))
- change tabid value to filter, add supplier and employee profile ([033c89b](https://gitlab.com/btaskee/btaskee-ops/tasker-ops/commit/033c89b7a5faa97f7cefe8c570004374348b7e84))
- change, remove redirect to checking permission ([7087244](https://gitlab.com/btaskee/btaskee-ops/tasker-ops/commit/708724452e06b7c387455a6dff61c3697f6daf0e))
- check condition showing successful noti again ([7147905](https://gitlab.com/btaskee/btaskee-ops/tasker-ops/commit/714790538e915464a47b0b8432e886744242f5d8))
- ci ([1748dc5](https://gitlab.com/btaskee/btaskee-ops/tasker-ops/commit/1748dc577b7737d261028ce8db553e1221ce8aff))
- ci ([862cd0e](https://gitlab.com/btaskee/btaskee-ops/tasker-ops/commit/862cd0ed9934e158f07c282357c6aae9be72382a))
- ci ([45ca54a](https://gitlab.com/btaskee/btaskee-ops/tasker-ops/commit/45ca54ab4f0d46b808c3f7c46c4698c3aeebeb1e))
- ci ([e341bd3](https://gitlab.com/btaskee/btaskee-ops/tasker-ops/commit/e341bd3043a09051141c3b344b40feba5630c0ad))
- ci ([3802287](https://gitlab.com/btaskee/btaskee-ops/tasker-ops/commit/3802287222d73d5482b8851411d7314eb68d0fe4))
- ci for testing unit ([6c60918](https://gitlab.com/btaskee/btaskee-ops/tasker-ops/commit/6c609184ad23e1f816b41e9030fb551311777614))
- cicd ([caf565e](https://gitlab.com/btaskee/btaskee-ops/tasker-ops/commit/caf565e4def271cc791aa8ed2ce55d030eb45617))
- cicd ([11d4fa9](https://gitlab.com/btaskee/btaskee-ops/tasker-ops/commit/11d4fa9f0ae21f63cb8f53fbf7f69c756fee6824))
- cicd ([9bd2062](https://gitlab.com/btaskee/btaskee-ops/tasker-ops/commit/9bd206257b23cc8af10620d680147f2eef2eb1f7))
- clean code and fix conflict ([1eba58d](https://gitlab.com/btaskee/btaskee-ops/tasker-ops/commit/1eba58d6274657a3305b28fff22811945463dd38))
- clean code range date picker, add full path for @ component ([fb107af](https://gitlab.com/btaskee/btaskee-ops/tasker-ops/commit/fb107afc1bf95ae13ec1a391a7586c0b843a9f55))
- clean code, add decorator pattern to query func ([7ec9291](https://gitlab.com/btaskee/btaskee-ops/tasker-ops/commit/7ec9291e300f6baabc6aff6e998a24b782fcb534))
- clean css of tailwind ([80ab5e6](https://gitlab.com/btaskee/btaskee-ops/tasker-ops/commit/80ab5e63a50210b97dd425bbed4308442a7cc5d4))
- clean prettier after the commit ([ceace1c](https://gitlab.com/btaskee/btaskee-ops/tasker-ops/commit/ceace1c91c2cc303fb37e896b68d459ce94fb624))
- clean route of tasker profile, add breadcrumbs ([d081b81](https://gitlab.com/btaskee/btaskee-ops/tasker-ops/commit/d081b81c5be273c6ab208991eeb7c0c9cabd362b))
- clean type for ui, query, decorator ([1e057fa](https://gitlab.com/btaskee/btaskee-ops/tasker-ops/commit/1e057fadfc9c8931a1bb3f6edf28f585ec9e864a))
- conflict from main ([a395f08](https://gitlab.com/btaskee/btaskee-ops/tasker-ops/commit/a395f08bab4619c4018f22d3ce7eb0a6d238a822))
- conflict from staging ([30ff89d](https://gitlab.com/btaskee/btaskee-ops/tasker-ops/commit/30ff89df7ea1e413ee0b32b881736f61c5f04770))
- conflict from staging ([bf5ad4c](https://gitlab.com/btaskee/btaskee-ops/tasker-ops/commit/bf5ad4cc2ca4ab2297b157322694320405a09aad))
- conflict from staging ([ed9738d](https://gitlab.com/btaskee/btaskee-ops/tasker-ops/commit/ed9738d43d6f024ab4fdb5c530d625d7d4396c2f))
- conflict hoc action, model from staging ([a42e632](https://gitlab.com/btaskee/btaskee-ops/tasker-ops/commit/a42e632b465f2bb067473c8f0c0502d079b9abed))
- connect test ([ebc5868](https://gitlab.com/btaskee/btaskee-ops/tasker-ops/commit/ebc58688414638a0719b97925fb154f4961e9459))
- correct spelling ([aabd5c5](https://gitlab.com/btaskee/btaskee-ops/tasker-ops/commit/aabd5c5add3d823fe89287c35754adc219c37c26))
- delete group and delete nearest children ([4f75f28](https://gitlab.com/btaskee/btaskee-ops/tasker-ops/commit/4f75f28f29af94277bdaf3d75571ae8827f88d86))
- disable updating profile if not write permission ([15eea6c](https://gitlab.com/btaskee/btaskee-ops/tasker-ops/commit/15eea6ccea1b2f22cb63606bd64202a92767640c))
- eslint ([8b5c073](https://gitlab.com/btaskee/btaskee-ops/tasker-ops/commit/8b5c073632a95061be16f6a7dac01ecc1f3ddfb7))
- fix conflict ([6cc38af](https://gitlab.com/btaskee/btaskee-ops/tasker-ops/commit/6cc38af15db587953380d48c60be1ef895f9bb31))
- improve loader after apply btaskee package on tasker profile ([09ab2e5](https://gitlab.com/btaskee/btaskee-ops/tasker-ops/commit/09ab2e521d923a0339bc9a3711dca856fbe8e1b8))
- just view on profile detail page ([db2b626](https://gitlab.com/btaskee/btaskee-ops/tasker-ops/commit/db2b626c15747083762ff36b194c29cef157d212))
- label of status option on useconfirm, translation ([24e4363](https://gitlab.com/btaskee/btaskee-ops/tasker-ops/commit/24e436366e3ce7c73f0881a17b3e32d8f78c21ff))
- login and add userid after that ([3962c37](https://gitlab.com/btaskee/btaskee-ops/tasker-ops/commit/3962c376f0107b4bcaf1fbdfd50866579625f57d))
- move users collection into isocode ([272f728](https://gitlab.com/btaskee/btaskee-ops/tasker-ops/commit/272f7286b7a5634dc678b9a1a9672677eb1df7f6))
- prettier after commit by my forgot ([ae1c067](https://gitlab.com/btaskee/btaskee-ops/tasker-ops/commit/ae1c06718d09f6ebc25559ac57370cbe52808dd7))
- push null value ([8955450](https://gitlab.com/btaskee/btaskee-ops/tasker-ops/commit/89554503ff417fba708e675ba4fc9db86688f1b1))
- rbac test snapshots ([01b89d2](https://gitlab.com/btaskee/btaskee-ops/tasker-ops/commit/01b89d2ba0c5b97214d5038f7ee3ac67209e96b2))
- redirect login ([d47c964](https://gitlab.com/btaskee/btaskee-ops/tasker-ops/commit/d47c9641986efa9756811ea72cdf4094aa26cbd2))
- redirect login ([7a115f7](https://gitlab.com/btaskee/btaskee-ops/tasker-ops/commit/7a115f79f880af55f89f15589ef314b6db5838e3))
- remove any type within unit testing ([fa2432b](https://gitlab.com/btaskee/btaskee-ops/tasker-ops/commit/fa2432bb39a6e1caa8c2ba13bec478d14a479de7))
- remove console.log ([409c834](https://gitlab.com/btaskee/btaskee-ops/tasker-ops/commit/409c83485cd9fc040e87e38ed3cee790de57709e))
- remove default variant on toast message, fix total records ([4e2e2c4](https://gitlab.com/btaskee/btaskee-ops/tasker-ops/commit/4e2e2c407f9e35e9b0a7257038a0b631e1dc39fc))
- remove hoc404 out of profile table loader ([c28b88a](https://gitlab.com/btaskee/btaskee-ops/tasker-ops/commit/c28b88addb243cbacf851caaca9e6d6462d7bf93))
- remove mock permission data ([a826f85](https://gitlab.com/btaskee/btaskee-ops/tasker-ops/commit/a826f851af5fa50ac6c04dd12816ac6d40d58183))
- remove unnecessary i18 on handle of breadcrumb ([55fdfa4](https://gitlab.com/btaskee/btaskee-ops/tasker-ops/commit/55fdfa47a2c1dc89475154cf82053bf279caaa72))
- rename role setting url constants ([a7e375c](https://gitlab.com/btaskee/btaskee-ops/tasker-ops/commit/a7e375c5c0e3e3d9ebf956d31f8f42c2a4a8e760))
- rename tasker profile to tasker onboarding ([ddb83e9](https://gitlab.com/btaskee/btaskee-ops/tasker-ops/commit/ddb83e962196aee1becf2fb1e03e5aa2a731519a))
- test module ([b19943e](https://gitlab.com/btaskee/btaskee-ops/tasker-ops/commit/b19943ec5a53dd93d629ca7d6e08e98b4a22dd34))
- translate toast message ([9e42e68](https://gitlab.com/btaskee/btaskee-ops/tasker-ops/commit/9e42e68e537dc76f06cf1b2057d670e9768243e8))
- translation, fix unit testing, set default status to query func ([01e0e68](https://gitlab.com/btaskee/btaskee-ops/tasker-ops/commit/01e0e681121c4a6223ca2d8e67545051df700098))
- type from staging ([642a701](https://gitlab.com/btaskee/btaskee-ops/tasker-ops/commit/642a70125450ee0ed31db0dc5163123e8f399a42))
- typescript ([ae22096](https://gitlab.com/btaskee/btaskee-ops/tasker-ops/commit/ae2209624f0b048c80b9eb8eb01362365dba03c8))
- update permission rule for tasker onboarding ([90ec2ec](https://gitlab.com/btaskee/btaskee-ops/tasker-ops/commit/90ec2ec3ccc9ad7bb532863d2c13b8dff383df64))
- use statusbadge for profile status, add translation for status to common file ([3900c2c](https://gitlab.com/btaskee/btaskee-ops/tasker-ops/commit/3900c2c280bb523a914157f4801561a55341175c))

### Features

- action history and create common btaskee table, clean code ([c7c6980](https://gitlab.com/btaskee/btaskee-ops/tasker-ops/commit/c7c6980f5f4424f9b33a7a10e7c7d080f3b0ce8f))
- action history for logged in ([fe47064](https://gitlab.com/btaskee/btaskee-ops/tasker-ops/commit/fe47064635c8ab1f5b0188c86e78babcfe98dea5))
- add auto generate release notes ([57cc2b3](https://gitlab.com/btaskee/btaskee-ops/tasker-ops/commit/57cc2b3e6c3a7f0f91e3da5f322a13cafbf8992e))
- add CHANGELOG ([40363f3](https://gitlab.com/btaskee/btaskee-ops/tasker-ops/commit/40363f3888e6f66c0de2783d4f84f380dff71a26))
- add confirmation before cud ([432d341](https://gitlab.com/btaskee/btaskee-ops/tasker-ops/commit/432d34114dcfa15dfe46046e248f6ec2bda90905))
- add error page, clean ui ([cc7da12](https://gitlab.com/btaskee/btaskee-ops/tasker-ops/commit/cc7da1267da25f9c8edb78ce772ebde4f7db5ac9))
- add feature updating profile status into the dropdown on table ([30cbe53](https://gitlab.com/btaskee/btaskee-ops/tasker-ops/commit/30cbe53223adc251ba13b3a7ea2bb4e38a13eeb6))
- add feature updating role detail ([94dce4b](https://gitlab.com/btaskee/btaskee-ops/tasker-ops/commit/94dce4b623ccfe5cf11f8dcdfb07e21644c3e3d4))
- add groups when create user ([5a1b720](https://gitlab.com/btaskee/btaskee-ops/tasker-ops/commit/5a1b72050b33952abc1fe90987d08ee75c4674a5))
- add hocLoader ([66958c4](https://gitlab.com/btaskee/btaskee-ops/tasker-ops/commit/66958c41cfe78127cd47db01c61f7133143c57f6))
- add loading global, font family ([48ec491](https://gitlab.com/btaskee/btaskee-ops/tasker-ops/commit/48ec4914ee9cb8b28e20b9eb42230bc9f0206d50))
- add logger ([503cebf](https://gitlab.com/btaskee/btaskee-ops/tasker-ops/commit/503cebf39ee9324d6cf0a93a16dc323b1b6a5d17))
- add more common components ([3ff4077](https://gitlab.com/btaskee/btaskee-ops/tasker-ops/commit/3ff40772bdf537a95f58f0c93501b8fb76d2e656))
- add more toast successful ([384e890](https://gitlab.com/btaskee/btaskee-ops/tasker-ops/commit/384e890e7626cbe936eb6885dcf3ee054bcc4a10))
- add nearestChildren field into groups ([fa249f5](https://gitlab.com/btaskee/btaskee-ops/tasker-ops/commit/fa249f59f1071eca1f921aeb839d469c555b3c91))
- add new dialog confirm ([c18410a](https://gitlab.com/btaskee/btaskee-ops/tasker-ops/commit/c18410a80e2e72b71e93cd20f30d31e9d995bdbd))
- add rechart and base component ([6a9aeaf](https://gitlab.com/btaskee/btaskee-ops/tasker-ops/commit/6a9aeafeb4bbd9d2d1dd31d2f35ce9b42262a5e9))
- add rejected tab, eliminated tab, approved tab, profile detail ([eed3351](https://gitlab.com/btaskee/btaskee-ops/tasker-ops/commit/eed33513fdfca82c4dac5f4cd6385ba991856612))
- add sending notification when change profile status ([382091b](https://gitlab.com/btaskee/btaskee-ops/tasker-ops/commit/382091bc2b66b8fff2823d367fb4971c089e4a18))
- add template for PR ([cfc28e2](https://gitlab.com/btaskee/btaskee-ops/tasker-ops/commit/cfc28e2e93b16d9d1c9adb38d63d41fb4a26c133))
- add the feature updaing profile status ([239058e](https://gitlab.com/btaskee/btaskee-ops/tasker-ops/commit/239058e84e3de1ca64ebeb926493937835ee8708))
- add translate with i18n and update ui rbac ([b8ed78c](https://gitlab.com/btaskee/btaskee-ops/tasker-ops/commit/b8ed78c159db60005ff5390c2eb91f128788bbeb))
- add translation to all language for entire notification ([e562aff](https://gitlab.com/btaskee/btaskee-ops/tasker-ops/commit/e562affe25f6dc5fe4b3e662f9181ca2814dc079))
- add typing for rbac ([1725fa6](https://gitlab.com/btaskee/btaskee-ops/tasker-ops/commit/1725fa6f4768a3bf1f32f75083f85588682417bb))
- add typing mapping between server and client, 20% ([1dd5f5b](https://gitlab.com/btaskee/btaskee-ops/tasker-ops/commit/1dd5f5bc33493ac0f9f90a7c2457a78ea340be15))
- add typing mapping between server and client, 40% ([81c6818](https://gitlab.com/btaskee/btaskee-ops/tasker-ops/commit/81c68181e20775dc70e4189dd4d89a74dbc0e128))
- add typing mapping between server and client, 60% ([5c0a957](https://gitlab.com/btaskee/btaskee-ops/tasker-ops/commit/5c0a9573014ae4f0928cd0d8aa50360a105d7d3c))
- add typing mapping between server and client, 80% ([389f70e](https://gitlab.com/btaskee/btaskee-ops/tasker-ops/commit/389f70e5ae0483cb1b1458983eddfb845a35bfae))
- add typing mapping between server and client, completed ([23a581a](https://gitlab.com/btaskee/btaskee-ops/tasker-ops/commit/23a581afca610bc61ef60abdd16f6af1a26d0a10))
- add updating image status, create common tasker profile table ([536492f](https://gitlab.com/btaskee/btaskee-ops/tasker-ops/commit/536492f17721bf5b5477dff9a5c800554696a622))
- add verifying tab on tasker profile page ([dff3a9c](https://gitlab.com/btaskee/btaskee-ops/tasker-ops/commit/dff3a9cb47510fd592c9dbce24547bc2b72ceffe))
- add zod to verify env variables ([8d6aed6](https://gitlab.com/btaskee/btaskee-ops/tasker-ops/commit/8d6aed6f890a26d4d5db70fec99b7ce8fdfcb006))
- apply controller hook form into open ref ([e74a260](https://gitlab.com/btaskee/btaskee-ops/tasker-ops/commit/e74a2608fd6a42d8016a59a13c3e5643122de425))
- apply mongoose ([8b4a75d](https://gitlab.com/btaskee/btaskee-ops/tasker-ops/commit/8b4a75d2cc21599422bd82e48ca77a112e1c6869))
- apply prometheus for Remix server ([401569a](https://gitlab.com/btaskee/btaskee-ops/tasker-ops/commit/401569a687ed14734f6b8db04a0d2ef4a695107b))
- apply prometheus for Remix server ([5fc93d6](https://gitlab.com/btaskee/btaskee-ops/tasker-ops/commit/5fc93d6e25d71e02c4a2fe68ea35cfdcbe42ec9e))
- apply redirect and toast with flash session ([022c2ae](https://gitlab.com/btaskee/btaskee-ops/tasker-ops/commit/022c2ae4460eb986ec0537525308eb1b50568ba1))
- apply SerializeFrom between match type tanstack and remix loader ([fba5280](https://gitlab.com/btaskee/btaskee-ops/tasker-ops/commit/fba528026db08587d85641125de864fcf2873ad5))
- auto move to first tab when go to page, remove active process ([b56e42e](https://gitlab.com/btaskee/btaskee-ops/tasker-ops/commit/b56e42e58a2b3a6da7425ce1a95fd98d4008c004))
- cd for deployment into dockerhub ([e5dbd34](https://gitlab.com/btaskee/btaskee-ops/tasker-ops/commit/e5dbd34921ca2112c39c9559121e1fa77a48aa9e))
- cd for deployment into dockerhub ([897c7eb](https://gitlab.com/btaskee/btaskee-ops/tasker-ops/commit/897c7eb3b20c5e216376d74044d55cc9dbafc43c))
- cd for deployment into dockerhub ([286d3e0](https://gitlab.com/btaskee/btaskee-ops/tasker-ops/commit/286d3e0a243a09bb1896ee81181a24c241aba95a))
- cd for deployment into dockerhub ([f53bc94](https://gitlab.com/btaskee/btaskee-ops/tasker-ops/commit/f53bc94386e0718b5001abf20113a9fb5a7b7970))
- cd for deployment into dockerhub ([12acc42](https://gitlab.com/btaskee/btaskee-ops/tasker-ops/commit/12acc423c677e0b0f61b3d29571fdf21ac0bb420))
- cd for deployment into dockerhub ([b4adefc](https://gitlab.com/btaskee/btaskee-ops/tasker-ops/commit/b4adefcc88bfb74e61c84d6f9004874da1eab216))
- cd for deployment into dockerhub ([72595a0](https://gitlab.com/btaskee/btaskee-ops/tasker-ops/commit/72595a0a86a69cb9a301880ddc9d2003d75d5266))
- cd for deployment into dockerhub ([6e799e9](https://gitlab.com/btaskee/btaskee-ops/tasker-ops/commit/6e799e9aba3d8d8b5cdea7a74e4a42329c08ce30))
- cd for deployment into dockerhub ([66d4c3b](https://gitlab.com/btaskee/btaskee-ops/tasker-ops/commit/66d4c3b6329554aa05fc97a3644a6f822a2d5e87))
- cd for deployment into dockerhub ([c21131e](https://gitlab.com/btaskee/btaskee-ops/tasker-ops/commit/c21131e60356fbbf27ef2800cc1e32475db455cc))
- cd for deployment into dockerhub ([6dfcc6c](https://gitlab.com/btaskee/btaskee-ops/tasker-ops/commit/6dfcc6ce09c024f8004e9a8f7fe018d4d43e3dc9))
- cd for deployment into dockerhub ([fc5cbb8](https://gitlab.com/btaskee/btaskee-ops/tasker-ops/commit/fc5cbb8223f60376f30bc23294f9225a63b49747))
- cd for deployment into dockerhub ([11854ef](https://gitlab.com/btaskee/btaskee-ops/tasker-ops/commit/11854efe21ee76b7f3f864a3e50fc90ea5f9b853))
- cd for deployment into dockerhub ([35b70ae](https://gitlab.com/btaskee/btaskee-ops/tasker-ops/commit/35b70aeccac8fe2e137e88b6facbcdb00386d1a9))
- cd for deployment into dockerhub ([d0298f9](https://gitlab.com/btaskee/btaskee-ops/tasker-ops/commit/d0298f92b553b936e0cdedee7ce14b4eb95ca701))
- change language for user ([c1b83fd](https://gitlab.com/btaskee/btaskee-ops/tasker-ops/commit/c1b83fd8c4490da71875e499e0f9c0ec94d9a425))
- change user avatar, improve s3 upload ([8a6b1d5](https://gitlab.com/btaskee/btaskee-ops/tasker-ops/commit/8a6b1d5ec9532cc25f982acddec4e2b035e01c6a))
- check multi permissions at loader ([ef11f9b](https://gitlab.com/btaskee/btaskee-ops/tasker-ops/commit/ef11f9bf29c646fdeff99182c561bd4e27fa3e7a))
- check multi permissions at loader ([3562df1](https://gitlab.com/btaskee/btaskee-ops/tasker-ops/commit/3562df1f87ac361205451b790f14fb419a1c4a5b))
- clean architecture for hook and context ([3fe9275](https://gitlab.com/btaskee/btaskee-ops/tasker-ops/commit/3fe927516c1c1781fa5af9ec0565a1b6475174c7))
- clean architecture for hook and context ([519555e](https://gitlab.com/btaskee/btaskee-ops/tasker-ops/commit/519555e212ad7cd97515556be3f2a76da34f06e6))
- clean structure folder ([551a98d](https://gitlab.com/btaskee/btaskee-ops/tasker-ops/commit/551a98de43e4e512ecea550a85e6dbd953755bdb))
- clean ux, remove unnecessary code ([5cc53f1](https://gitlab.com/btaskee/btaskee-ops/tasker-ops/commit/5cc53f1c8589baec31b7b676321291ef558f91bc))
- complete full flow authentication with pass and verification code ([a32afde](https://gitlab.com/btaskee/btaskee-ops/tasker-ops/commit/a32afde04fba6cddf2f0f0651042e693fc77081f))
- complete session flow ([4226281](https://gitlab.com/btaskee/btaskee-ops/tasker-ops/commit/4226281820cb4633eba231425bdae50e6b8583c0))
- config prettier cspell commitlint with husky ([8e1ff4a](https://gitlab.com/btaskee/btaskee-ops/tasker-ops/commit/8e1ff4ad06f62305ef3eaf0f59181611d93831b8))
- config send mail to verification code ([0212317](https://gitlab.com/btaskee/btaskee-ops/tasker-ops/commit/021231772dd216266a0e84070e39991b26e22336))
- connect mongo, setup shacdn, taiwindcss, config express ([004f165](https://gitlab.com/btaskee/btaskee-ops/tasker-ops/commit/004f165e351076820de9dec93f44b54d39565c59))
- create all shadcn ([4f65db9](https://gitlab.com/btaskee/btaskee-ops/tasker-ops/commit/4f65db908a93ca251eaf98d04d335217c41c177f))
- create combo hooks for outlet context ([c3f1de8](https://gitlab.com/btaskee/btaskee-ops/tasker-ops/commit/c3f1de87e464be1c3b1716a7fe53faf45a771819))
- create error page ([4492ffd](https://gitlab.com/btaskee/btaskee-ops/tasker-ops/commit/4492ffd4b747a1fa5f0e1fcfcb017fabd6c35c3d))
- create genealogy for group ([b116b52](https://gitlab.com/btaskee/btaskee-ops/tasker-ops/commit/b116b52ae9df8c00073d5e5841f5127da686f2c5))
- create group, flow for root permission ([21497c0](https://gitlab.com/btaskee/btaskee-ops/tasker-ops/commit/21497c02116c930235cbdc633642738896829f95))
- create layout for auth and non auth ([afc4d8c](https://gitlab.com/btaskee/btaskee-ops/tasker-ops/commit/afc4d8ca6a93de9bd1f7fa280fc36bb46efb3717))
- create marketing routes and clean code ([90625d1](https://gitlab.com/btaskee/btaskee-ops/tasker-ops/commit/90625d1888f249e23c3896d5f0b31173282611cc))
- create middleware for action history modified ([2b588bc](https://gitlab.com/btaskee/btaskee-ops/tasker-ops/commit/2b588bc3f8d42025f7fb3f762f362e1867baa778))
- create new user ([e561759](https://gitlab.com/btaskee/btaskee-ops/tasker-ops/commit/e561759063476e10af2d154b67cfd99869483c18))
- define scenario for view group detail ([742990b](https://gitlab.com/btaskee/btaskee-ops/tasker-ops/commit/742990b972838542b4567d088049f84d198749b1))
- delete role and delete all assigned role for children group ([7168508](https://gitlab.com/btaskee/btaskee-ops/tasker-ops/commit/7168508121a2bb3a9e580b7e09f34f200a59eab0))
- Dockerfile ([e47fd8a](https://gitlab.com/btaskee/btaskee-ops/tasker-ops/commit/e47fd8ab426f849c0c8f1adde273016fbefe56ec))
- DockerFile ([6d96ced](https://gitlab.com/btaskee/btaskee-ops/tasker-ops/commit/6d96ced601eb1528e1b98c4f5320087a99bd31e4))
- edit user ([348fde7](https://gitlab.com/btaskee/btaskee-ops/tasker-ops/commit/348fde78974b373ea0db3526923ed32c50a7ca57))
- fetch api utils and move secret session into env ([99010ed](https://gitlab.com/btaskee/btaskee-ops/tasker-ops/commit/99010ed75795b2dd8cf3bfbc7841720ef23d770e))
- filter data action permissions by groups of manager ([b8fc8ed](https://gitlab.com/btaskee/btaskee-ops/tasker-ops/commit/b8fc8ed005dded11178d047491f426a8775dc0d9))
- fix typescript ([f1cc4fd](https://gitlab.com/btaskee/btaskee-ops/tasker-ops/commit/f1cc4fd27ee90062e001e3b1205a4a25f6ff3209))
- get cities from workingplaces ([8f9edde](https://gitlab.com/btaskee/btaskee-ops/tasker-ops/commit/8f9edde71759eca466c03f1c3a61451ab4f2e667))
- get cities from workingplaces ([6f4b668](https://gitlab.com/btaskee/btaskee-ops/tasker-ops/commit/6f4b668f695203dd37ef45ecd36cd61f45a86f77))
- get model mongoose by name and isocode ([99c38d3](https://gitlab.com/btaskee/btaskee-ops/tasker-ops/commit/99c38d39cc6baa241ddae701c050f8b1698c3382))
- get user profile ([badf4fd](https://gitlab.com/btaskee/btaskee-ops/tasker-ops/commit/badf4fd4ccbb6f37c8f5b2585e994d430ade7e00))
- import image for remix run in ci cycle ([d4bb3e9](https://gitlab.com/btaskee/btaskee-ops/tasker-ops/commit/d4bb3e934aa03d1a3d9fd82e5fcb2e64a5cd40f4))
- improve form validation and translation ([fda27a6](https://gitlab.com/btaskee/btaskee-ops/tasker-ops/commit/fda27a6ac2cf85c327d4ade4fd5fc4da4040ca62))
- improve throw error in fetch api, add feature sending notification, add isocode to collection ([224dad1](https://gitlab.com/btaskee/btaskee-ops/tasker-ops/commit/224dad1c4a76b2a3494bc5940a2e323a2191fbb1))
- improve translate, add new common component for rbac ([b097459](https://gitlab.com/btaskee/btaskee-ops/tasker-ops/commit/b097459b6a39931ab2eb20f36fd9df4085386ada))
- improve ui for roles and permission ([dcc5eae](https://gitlab.com/btaskee/btaskee-ops/tasker-ops/commit/dcc5eaefd6b2474b2d507c948214a65e145cc2d6))
- improve ui on rabc ([0d1d93c](https://gitlab.com/btaskee/btaskee-ops/tasker-ops/commit/0d1d93c5e24b670b9822c79354f228d71819b806))
- improve ui rbac ([aa91a6d](https://gitlab.com/btaskee/btaskee-ops/tasker-ops/commit/aa91a6d7e12b36c2d53b970a5b91e75771c2e736))
- insert role ([f1a5016](https://gitlab.com/btaskee/btaskee-ops/tasker-ops/commit/f1a50167c15c0f479023e593d9aa9453777a8c08))
- loading with useNavigation ([e19502e](https://gitlab.com/btaskee/btaskee-ops/tasker-ops/commit/e19502eae4c5aed01f799bf4dc3076657963b13a))
- measure the strength password ([430a10e](https://gitlab.com/btaskee/btaskee-ops/tasker-ops/commit/430a10ead6dfb9cd330a4a7fc853cac257540b36))
- multi select async, complete insert children groups ([d5942f3](https://gitlab.com/btaskee/btaskee-ops/tasker-ops/commit/d5942f38af93373e7876ba94b29a6fffd57ed3a6))
- oauth2 with google ([248790b](https://gitlab.com/btaskee/btaskee-ops/tasker-ops/commit/248790bb4d7ca0bc104f5d409ae7a149c9296b06))
- proxy get api, get collection name ([6391fe1](https://gitlab.com/btaskee/btaskee-ops/tasker-ops/commit/6391fe1fe496da5572e4ba52d82b4756f31c8e7d))
- query get role detail ([074931d](https://gitlab.com/btaskee/btaskee-ops/tasker-ops/commit/074931d7d5261d9910808230df1cc223c9c340f0))
- refactor structure folder, code ([489f830](https://gitlab.com/btaskee/btaskee-ops/tasker-ops/commit/489f830e1a6e4ce695e91b7c44e58ffd576807fa))
- remix table configuration ([dc94d61](https://gitlab.com/btaskee/btaskee-ops/tasker-ops/commit/dc94d61332db59b479e5cbe6f8b4f785ce5264c6))
- remove group and children, roles deeply ([8593b85](https://gitlab.com/btaskee/btaskee-ops/tasker-ops/commit/8593b85d02f811c2b4ff8a7d6181a6e821947255))
- remove login with google ([f848ebd](https://gitlab.com/btaskee/btaskee-ops/tasker-ops/commit/f848ebd4c19d7ccac9722e366912603542ab717c))
- remove roles, group children ([b6337bb](https://gitlab.com/btaskee/btaskee-ops/tasker-ops/commit/b6337bb5e21950ca3b6f42ed5705293f11ff50fe))
- remove unwind in query, add user app, service channel, use mongo client app ([401f8fa](https://gitlab.com/btaskee/btaskee-ops/tasker-ops/commit/401f8fa093f0a69defb3104a96c57065289f34f4))
- remove user ([921133e](https://gitlab.com/btaskee/btaskee-ops/tasker-ops/commit/921133e4e9b041b927f2ee60629cdaced71a3faa))
- reset password and logout, add navbar ([cc1342f](https://gitlab.com/btaskee/btaskee-ops/tasker-ops/commit/cc1342ff54256ea3de375abf2ff1ce56a539e7dc))
- role in child must be get from parent ([dacfd4f](https://gitlab.com/btaskee/btaskee-ops/tasker-ops/commit/dacfd4f56a7742253d24711a8222cb40964778f4))
- roles management page ([15593cf](https://gitlab.com/btaskee/btaskee-ops/tasker-ops/commit/15593cf044c50df451509de815c6c6dba323b8c5))
- save action permission with hoc action remix ([15c187b](https://gitlab.com/btaskee/btaskee-ops/tasker-ops/commit/15c187b594cdc09499444343ee60661500b83d91))
- seeding data ([3321cc5](https://gitlab.com/btaskee/btaskee-ops/tasker-ops/commit/3321cc5fcc227702075e48f3fd8940091547a042))
- seeding data ([652b139](https://gitlab.com/btaskee/btaskee-ops/tasker-ops/commit/652b139d969e7c3651df563f23d823afdb662131))
- server error page ([bcffb69](https://gitlab.com/btaskee/btaskee-ops/tasker-ops/commit/bcffb6934df1d93489a9d6e6fadd1781a3be80ca))
- setup i18n and basic auth flow ([1c34f6a](https://gitlab.com/btaskee/btaskee-ops/tasker-ops/commit/1c34f6a5d96c306ea0a9705a68cb8b92142d502b))
- setup jest for ci ([2ce80bb](https://gitlab.com/btaskee/btaskee-ops/tasker-ops/commit/2ce80bb062a9e1453aa714c0b9fe8746c232d780))
- solution for translation from server ([33c9c60](https://gitlab.com/btaskee/btaskee-ops/tasker-ops/commit/33c9c60fdcb4288e49d8b938a3ea94f4607aaf84))
- throw email service error ([5fdc614](https://gitlab.com/btaskee/btaskee-ops/tasker-ops/commit/5fdc61424d531467f1509c20c73d614e940676e9))
- translation from loader server ([22ad408](https://gitlab.com/btaskee/btaskee-ops/tasker-ops/commit/22ad4087071f344d14b11334fefee00f9fbe93d7))
- typescript advanced for loader, action, remix flow ([1587c07](https://gitlab.com/btaskee/btaskee-ops/tasker-ops/commit/1587c07026d9f64d4dfa5a3e85eb16adf528f4f0))
- ui for permission ([17ef5cf](https://gitlab.com/btaskee/btaskee-ops/tasker-ops/commit/17ef5cfdffea7ec649b8bf5e12aa05604dd3d492))
- update env ([c14cafa](https://gitlab.com/btaskee/btaskee-ops/tasker-ops/commit/c14cafa8c7cca55c4ecc9b3e2dde83f4ba326da4))
- update env ([ed727e9](https://gitlab.com/btaskee/btaskee-ops/tasker-ops/commit/ed727e917835c692b69b1dac5b02f0024e9a488d))
- update group feature ([6f69178](https://gitlab.com/btaskee/btaskee-ops/tasker-ops/commit/6f691782d3cddb838ca0b66f027c16e925d321a9))
- update query and roles ui ([3f7d253](https://gitlab.com/btaskee/btaskee-ops/tasker-ops/commit/3f7d2539ed3e1673c01a3f5148c1b9573e91b09f))
- update roles assigned must be verify role created at children ([228c49a](https://gitlab.com/btaskee/btaskee-ops/tasker-ops/commit/228c49a1530d9daa48a8c6077c3c8c343fd50022))
- update ui for 404 page ([84369d2](https://gitlab.com/btaskee/btaskee-ops/tasker-ops/commit/84369d2e4ea87226fa52f7aa40419f7ea3c6cdff))
- update ui groups page ([df7de1b](https://gitlab.com/btaskee/btaskee-ops/tasker-ops/commit/df7de1b7a92fe57993497710d1e3d8f2b033fe75))
- update ui groups page ([d9693ec](https://gitlab.com/btaskee/btaskee-ops/tasker-ops/commit/d9693ec96241c0b3487244efbc4e2452dac360ce))
- upgrade action history ([7655eb4](https://gitlab.com/btaskee/btaskee-ops/tasker-ops/commit/7655eb4ad4d600ab454cd4542087c71d51bb2bc3))
- upgrade action history ([9ffb039](https://gitlab.com/btaskee/btaskee-ops/tasker-ops/commit/9ffb039a190b5915a17224e90b33f828bc91730c))
- use react hook form with submit remix ([0e6cb16](https://gitlab.com/btaskee/btaskee-ops/tasker-ops/commit/0e6cb16fa3e0f28d88b35962bdb91bf518d81291))
- verify 404 page ([b997e7c](https://gitlab.com/btaskee/btaskee-ops/tasker-ops/commit/b997e7cfcece99fc5cde837408d4fe8d1b517c03))

#### 2024-03-12

##### Chores

- remove head (651a7a75)
- update ui for roles (54d39ac1)
- update ui for roles (fc673caa)
- roles base ui updated (30204c35)
- get more detail roles (9e26eed0)
- complete feature update group (b1056cc9)
- update create role feature (0443c28a)
- remove button add group (ed7d0baa)
- add link to reset password (15f14cd8)

##### New Features

- update query and roles ui (3f7d2539)
- create genealogy for group (b116b52a)
- query get role detail (074931d7)
- update group feature (6f691782)
- update ui groups page (df7de1b7)
- insert role (f1a50167)
- update ui groups page (d9693ec9)
- multi select async, complete insert children groups (d5942f38)
- config prettier cspell commitlint with husky (8e1ff4ad)
- roles management page (15593cf0)
- add hocLoader (66958c41)
- create group, flow for root permission (21497c02)
- update ui roles page (36ed300d)
- save action permission with hoc action remix (15c187b5)
- create middleware for action history modified (2b588bc3)
- improve ui for roles and permission (dcc5eaef)
- apply controller hook form into open ref (e74a2608)
- create new user (e5617590)
- remix table configuration (dc94d613)
- ui for permission (17ef5cfd)
- use react hook form with submit remix (0e6cb16f)
- loading with useNavigation (e19502ea)
- add zod to verify env variables (8d6aed6f)
- create layout for auth and non auth (afc4d8ca)
- reset password and logout, add navbar (cc1342ff)
- complete full flow authentication with pass and verification code (a32afde0)
- complete session flow (42262818)
- setup i18n and basic auth flow (1c34f6a5)
- connect mongo, setup shacdn, taiwindcss, config express (004f165e)

##### Bug Fixes

- push null value (89554503)
- fix conflict (6cc38af1)

##### Other Changes

- leminh.nguyen/remixrun-boilerplate (b042d3f0)
- leminh.nguyen/remixrun-boilerplate (70592e92)
- leminh.nguyen/remixrun-boilerplate (57419a4b)
- leminh.nguyen/remixrun-boilerplate (2176c9f2)
- leminh.nguyen/remixrun-boilerplate (2d828afa)
- leminh.nguyen/remixrun-boilerplate (6ab9385b)
- from remix tutorial (c972ae4d)
