PORT=3000
ORIGINAL_DOMAIN=https://localhost:3000

# database Remix backend
MONGO_URI=mongodb://127.0.0.1:27017/remix1

# database App
MONGO_URI_APP=mongodb://127.0.0.1:27017/remix2

# hash password
BCRYPT_SALT_ROUND=10
BCRYPT_PLAIN_TEXT=btaskee

# SMTP host
MAIL_SERVER_PASSWORD=1
MAIL_SERVER_USERNAME=1
MAIL_HOST=smtp.mailgun.org

# Cookie
MAX_AGE_SESSION=31536000 # 1year
SESSION_SECRET=s3cr3t
SESSION_NAME=_taskerops

# AWS S3
STORAGE_ACCESS_KEY=dumpkey
STORAGE_SECRET=dumpkey
STORAGE_REGION=dumpkey
STORAGE_BUCKET=dumpkey

# Go Rest API
GO_REST_API_URI=http://localhost:8080/api
ACCESS_KEY=dumpkey

# Loggly
LOGGLY_TOKEN=dumpkey
LOGGLY_SUBDOMAIN=btaskeestag
LOGGLY_TAG=tasker-ops

GOOGLE_API_KEY=dumpkey
GOOGLE_MAP_URL=google.map.com

# Feature flags, accept on | off
Feature_Course_Deadline_Review=on
Feature_Course_City=on
Feature_CourseSendNotificationTaskerEligibility=on
Feature_SpecialCampaign=on
Feature_CourseNumberQuizCollection=on
Feature_CourseApplyForAllCities=on
Feature_SpecialCampaignCity=on
