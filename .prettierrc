{"printWidth": 80, "tabWidth": 4, "useTabs": false, "semi": true, "singleQuote": true, "trailingComma": "all", "bracketSpacing": true, "bracketSameLine": true, "arrowParens": "avoid", "endOfLine": "auto", "plugins": ["@trivago/prettier-plugin-sort-imports"], "importOrder": ["^@core/(.*)$", "^@server/(.*)$", "^@ui/(.*)$", "^[./]"], "importOrderSeparation": true, "importOrderSortSpecifiers": true, "overrides": [{"files": ["*.tsx", ".eslintrc.cjs"], "options": {"tabWidth": 2}}]}