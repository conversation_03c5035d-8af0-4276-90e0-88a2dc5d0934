FROM node:20.17-alpine3.20

ENV PNPM_HOME="/pnpm"
ENV PATH="$PNPM_HOME:$PATH"
RUN SHA_SUM=$(npm view pnpm@10.1.0 dist.shasum) && \
    corepack enable pnpm && \
    corepack install -g pnpm@10.1.0+sha1.$SHA_SUM

WORKDIR /app

# Install only the necessary packages with apk (Alpine's package manager)
RUN apk add --no-cache openssh-client bash git

# Use secret to securely mount SSH private key
RUN --mount=type=secret,id=ssh_key \
    mkdir -p /root/.ssh/ && \
    cat /run/secrets/ssh_key > /root/.ssh/id_rsa && \
    chmod 600 /root/.ssh/id_rsa && \
    ssh-keyscan -t rsa gitlab.com >> /root/.ssh/known_hosts

COPY .npmrc package.json pnpm-lock.yaml ./

RUN pnpm install
COPY . .

RUN pnpm build

# Clean up SSH private key after use
RUN rm -f /root/.ssh/id_rsa

# Create a non-root user and switch to it
# RUN adduser -D nonrootuser
# USER nonrootuse

EXPOSE 3000

CMD ["pnpm", "run", "deploy"]
