{"TERMINATE_THE_INSTALLMENT_PLAN": "Tasker <PERSON><PERSON><PERSON><PERSON> chấm dứt gói trả góp", "MAKE_AN_EARLY_PAYMENT": "Tasker <PERSON><PERSON><PERSON><PERSON> thự<PERSON> hiện trả sớm gói trả góp", "CONFIRM": "<PERSON><PERSON><PERSON>", "CANCEL": "Huỷ bỏ", "CHOOSE_REASON": "<PERSON><PERSON><PERSON>:", "DEDUCT_INSTALLMENT_PAYMENTS": "Deduct Installment Payments.", "TASKER_NAME": "<PERSON><PERSON><PERSON>", "TITLE_CONFIRM_DEDUCT_INSTALLMENT_PAYMENT": "<PERSON><PERSON> lý chấm dứt góp mua trước trả sau", "DESCRIPTION_CONFIRM_DEDUCT_INSTALLMENT_PAYMENT": "Bạn chắc chắn muốn chấm dứt tiến trình góp mua trước trả sau này ?", "NOTE_DEDUCT_INSTALLMENT_PAYMENT": "<PERSON><PERSON> thống sẽ thực hiện cấn trừ toàn bộ số tiền trả góp còn lại {{amount}}{{code}} vào tài khoản chính của Tasker. H<PERSON><PERSON> chắc chắn rằng bạn đã tư vấn rõ cho Tasker về quy định này.", "TASKER_BNPL_PROCESS_NOT_FOUND": "Tasker BNPL process not found", "UPDATE_TRANSACTION_HISTORY_IN_BNPL_TRANSACTION_FAILED": "Update transaction history for BNPL failed", "DEDUCT_INSTALLMENT_PAYMENT_FAILED": "Deduct installment payment failed"}