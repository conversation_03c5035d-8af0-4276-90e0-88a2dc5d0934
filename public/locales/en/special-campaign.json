{"SPECIAL_CAMPAIGN": "Special Campaign", "CREATE": "Create", "SEARCH_BY_NAME": "Search by name", "TYPE": "Type", "STATUS": "Status", "ACTION": "Action", "VIEW": "View", "UPDATE": "Update", "CAMPAIGN_NAME": "Campaign Name", "VALIDITY_PERIOD": "Validity Period", "UPDATED_AT": "Updated At", "UPDATED_BY": "Updated By", "REFERRAL_CAMPAIGN": "Referral Campaign", "TASK_CAMPAIGN": "Task Campaign", "ACTIVE": "Active", "INACTIVE": "Inactive", "CREATE_SPECIAL_CAMPAIGN": "Create Special Campaign", "GENERAL": "General", "IMAGE_URL_SPECIAL_CAMPAIGN": "Upload Image Special Campaign", "IMAGE_IS_REQUIRED": "Image is required", "ENTER_CAMPAIGN_NAME": "Enter campaign name", "NO_OF_REFEREE": "No. of Referee", "NO_OF_TASK": "No. of Task", "CHOOSE_VALUE": "Choose value", "VALUE_MUST_BE_GREATER_THAN_ZERO": "Value must be greater than 0", "DISPLAY_NAME": "Display Name", "ENTER_DISPLAY_NAME": "Enter display name", "DESCRIPTION": "Description", "ENTER_DESCRIPTION": "Enter description", "REWARDS": "Rewards", "REWARDS_INFORMATION": "Rewards Information {{value}}", "BPOINT": "bPoint", "MONEY": "Money", "PROMOTION": "Promotion", "ENTER_AMOUNT": "Enter amount", "ADD_REWARDS": "Add <PERSON>", "AMOUNT": "Amount", "REQUIRED": "Required", "CANCEL": "Cancel", "SUBMIT": "Submit", "CONFIRM": "Confirm", "ARE_YOU_SURE_YOUR_INFORMATION_IS_CORRECT": "Are you sure your information is correct?", "CREATE_SPECIAL_CAMPAIGN_SUCCESSFULLY": "Create special campaign successfully!", "UPDATE_SPECIAL_CAMPAIGN_SUCCESSFULLY": "Update special campaign successfully!", "SPECIAL_CAMPAIGN_DETAIL": "Special Campaign Details", "GENERAL_INFORMATION_TITLE": "General Information", "CREATED_BY": "Created By", "CREATED_AT": "Created At", "UPDATE_SPECIAL_CAMPAIGN": "Update Special Campaign", "SAVE_CHANGE": "Save Change", "CITY": "City", "CHOOSE_CITY": "Choose City", "TOOL_TIP_APPLY_FOR_ALL_CITIES": "Not selecting applies to all cities", "OPTIONAL": "Optional", "ALL_CITIES": "All Cities", "ACCOUNT_TYPE": "Account Type", "TASKER_JOURNEY_LEVELS": "Tasker Journey Levels", "MIN_RATE_TASK": "Min Rate Task", "SERVICE": "Service", "MAIN_ACCOUNT": "Main Account", "PROMOTION_ACCOUNT": "Promotion Account", "SELECT_TASKER_JOURNEY_LEVELS": "Select Tasker Journey Levels", "SELECT_SERVICE": "Select Service", "CHOOSE_MIN_RATE_TASK": "Choose min rate task", "TOOL_TIP_APPLY_FOR_ALL_SERVICES": "Not selecting applies to all services", "ALL_SERVICE": "All Services", "SUBSCRIPTION": "Subscription"}