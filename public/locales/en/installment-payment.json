{"INSTALLMENT_PAYMENT": "Installment Payment", "SEARCH_NAME_PHONE": "Search name or phone ...", "STATUS": "Status", "TASKER_NAME": "Tasker Name", "TASKER_PHONE": "Tasker ID/Phone No.", "SERVICE": "Service", "REGION": "Region", "CREATED_AT": "Created At", "INSTALLMENT_AMOUNT": "Installment Amt.", "PAID_AMOUNT": "<PERSON><PERSON>.", "REMAINING_AMOUNT": "Remaining Amt.", "PAYMENT_DATE": "Payment Date", "TASK_ID": "Task ID", "AMOUNT_OF_TASK": "Amount of Task", "AMOUNT_PAID": "Amount <PERSON>", "TYPE": "Type", "GENERAL": "General", "PAYMENT_DETAIL": "Payment Detail", "TOTAL_AMOUNT_ON_TASK": "Total Installment Amount", "TOTAL_AMOUNT_PAID": "Total Amount Paid", "TOTAL_REMAINING_AMOUNT": "Total Remaining Amount", "INSTALLMENT_PAYMENT_DETAIL": "Installment Detail", "EXPIRED_AT": "Due Date", "PAYING": "In Progress", "DONE": "Done", "CHARGE_MAIN_ACCOUNT_SINCE_DUE_DATE": "Charge Main Account Since Due Date", "CHARGE_BNPL": "Charge BNPL", "OVER_DUE": "Over Due", "STOPPED": "Stopped", "DEDUCT_INSTALLMENT_PAYMENTS": "Deduct Installment Payments", "DEDUCT_INSTALLMENT_PAYMENT_IN_TASKER_BNPL_SUCCESSFULLY": "Deduct installment payment in Tasker BNPL successfully", "INVALID_TRANSACTION_TYPE": "Invalid type", "DEDUCT_INSTALLMENT_PAYMENT": "Deduct installment payment", "TASKER_REGISTERED_SERVICES": "Service", "AMOUNT": "Installment Amt.", "TASKER_CITY": "Region"}