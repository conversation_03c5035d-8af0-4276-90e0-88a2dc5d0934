{"TERMINATE_THE_INSTALLMENT_PLAN": "Tasker wants to terminate the installment plan", "MAKE_AN_EARLY_PAYMENT": "Tasker wants to make an early payment for the installment plan", "CONFIRM": "Confirm", "CANCEL": "Cancel", "CHOOSE_REASON": "Choose Reason:", "DEDUCT_INSTALLMENT_PAYMENTS": "Deduct Installment Payments.", "TASKER_NAME": "Tasker Name", "TITLE_CONFIRM_DEDUCT_INSTALLMENT_PAYMENT": "Deduct Installment Payment", "DESCRIPTION_CONFIRM_DEDUCT_INSTALLMENT_PAYMENT": "Are you sure you want to deduct this installment payment ?", "NOTE_DEDUCT_INSTALLMENT_PAYMENT": "The system will automatically deduct the remaining installment amount of {{amount}}{{code}} from Task<PERSON>'s main account. Please make sure you have clearly explained this policy to <PERSON><PERSON>.", "TASKER_BNPL_PROCESS_NOT_FOUND": "Tasker BNPL process not found", "UPDATE_TRANSACTION_HISTORY_IN_BNPL_TRANSACTION_FAILED": "Update transaction history for BNPL failed", "DEDUCT_INSTALLMENT_PAYMENT_FAILED": "Deduct installment payment failed"}