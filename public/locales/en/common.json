{"SEND_EMAIL": "Send Email", "TODAY": "Today", "YESTERDAY": "Yesterday", "THIS_MONTH": "This month", "LAST_MONTH": "Last month", "THIS_YEAR": "This year", "LAST_YEAR": "Last year", "UPDATE": "Update", "CANCEL": "Cancel", "TOTAL_RECORDS": "{{total}} records", "SEARCH_TASKER_NAME_OR_PHONE": "Search name or phone ...", "SETTINGS": "Setting", "GO_BACK": "Go back", "SUCCESS": "Success", "INFORMATION": "Information", "ERROR": "Error", "WARNING": "Warning", "PARENT_GROUP_NOT_FOUND": "Parent group not found", "USER_DONT_HAVE_PERMISSION": "User don't have permission", "EMAIL_SERVICE_ERROR": "Email service error", "ENV_ERROR": "There is an error with the server environment variables", "LOGIN_FAILURE": "Login failure", "INCORRECT_ACCOUNT": "Incorrect account", "EMAIL_INCORRECT": "Email incorrect", "CODE_INCORRECT_OR_EXPIRED": "Code incorrect or expired", "LOGOUT": "Logout", "ACTIONS_HISTORY": "Action History", "USERS_MANAGEMENT": "Users Management", "PROFILE": "Profile", "GROUPS": "Groups", "EDIT_ROLE": "Edit role", "EDIT_GROUP": "Edit group", "NO_ITEMS": "No items", "ACCESS_DENIED": "Access Denied", "NOT_PERMISSION": "You do not have permission to access this page.", "PAGE_NOT_FOUND": "Sorry, the page cannot be found", "PAGE_NOT_FOUND_DESCRIPTION": "The page you are looking for does not exist!", "SEARCH": "Search ...", "CREATE_ROLE": "Create role", "CREATE_GROUP": "Create group", "GROUPS_MANAGEMENT": "Group", "SEARCH_PLACEHOLDER": "Search something ...", "SELECT_PLACEHOLDER": "Select ...", "TASKER_ONBOARDING": "Tasker Onboarding", "TASKER_PROFILE_DETAIL": "Tasker Profile Details", "UPDATE_USER": "Update user", "ERROR_BY_MAX_FILE_SIZE": "Exceeded image file size limit.", "IMAGE_PREVIEW": "Image preview", "UPLOAD": "Upload", "STAFF_PROFILE_DETAIL": "Staff Profile Details", "DATA_TESTING": "Data testing", "FAIL_INTERVIEW": "Fail interview", "ACTIVE": "Active", "RESTORED": "Restored", "FAIL_ELIMINATED": "<PERSON><PERSON> eliminated", "ELIMINATED": "Eliminated", "RESCHEDULE": "Reschedule", "VERIFYING": "Verifying", "FAIL_CALLING": "Fail calling", "APPROVED": "Approved", "REJECTED": "Rejected", "PASSWORD_NOT_MATCH": "Password not match", "UNKNOWN_ERROR": "Unknown error", "CLEAR_FILTERS": "Clear filters", "SELECTED": "Selected", "REPORT": "Report", "HISTORY_BREWARD": "History bReward", "CREATE_USER": "Create user", "TASKER_OPERATION": "Tasker", "NO_RESULTS_FOUND": "No result found", "INACTIVE": "Inactive", "TEST": "Test", "REVIEW": "Review", "TASKER_TRAINING": "Tasker Training", "TEST_AND_REVIEW": "Test & Review", "CREATE_TEST_AND_REVIEW": "Create Test/Review", "LAST_7_DAYS": "Last 7 days", "LAST_14_DAYS": "Last 14 days", "LAST_30_DAYS": "Last 30 days", "APPLY": "Apply", "TRAINING_QUIZ_COLLECTION_DETAIL": "Quiz Collection Details", "UPLOAD_IMAGE": "Upload image", "IMAGE_MAX_SIZE": "File should be less than {{size}}", "DESCRIPTION": "Description", "ENTER_DESCRIPTION": "Enter description", "UPLOAD_VIDEO": "Upload video", "YOUTUBE_LINK": "Youtube link", "ENTER_YOUTUBE_LINK": "Enter youtube link", "VIDEO_DESCRIPTION": "Video description", "ENTER_VIDEO_DESCRIPTION": "Enter video description", "UPLOAD_CSV_FILE": "Upload CSV file", "TRAINING_QUIZ": "Quiz", "CREATE_QUIZ": "Create Quiz", "EDIT_TRAINING_QUIZ": "Update Quiz", "TRAINING_QUIZ_DETAIL": "Quiz details", "CREATE_QUIZ_COLLECTION": "Create Quiz Collection", "TRAINING_QUIZ_COLLECTION": "Quiz Collection", "CREATE_TRAINING_QUIZ_COLLECTION": "Create Quiz Collection", "CREATE_TRAINING_QUIZ": "Create Training Quiz", "EDIT_TRAINING_QUIZ_COLLECTION": "Update Quiz Collection", "TEST_DETAIL": "Test Details", "NOT_STARTED": "Not Started", "IN_PROGRESS": "In Progress", "COMPLETED": "Completed", "FAILED": "Failed", "PASSED": "Passed", "OPENED": "Opened", "NOT_OPENED": "Not Opened", "BLOCKED": "Blocked", "TRAINING_HISTORY_DETAIL": "Training History Details", "NONE": "None", "EXPIRED": "Expired", "TRAINING_HISTORY": "Training History", "TASKER": "Tasker", "REVIEW_DETAIL": "Review Details", "UPDATE_TEST": "Update Test", "UPDATE_REVIEW": "Update Review", "PAYING": "In Progress", "DONE": "Done", "INSTALLMENT_PAYMENT": "Installment Payment", "INSTALLMENT_PAYMENT_DETAIL": "Installment Detail", "FULL_PAYMENT": "Full Payment", "OVER_DUE": "Over Due", "STOPPED": "Stopped", "ORDER_MANAGEMENT": "Order Management", "TOOLKIT": "<PERSON><PERSON><PERSON>", "TOOLKIT_MANAGEMENT": "Toolkit Management", "CREATE_TOOLKIT": "<PERSON><PERSON> <PERSON><PERSON><PERSON>", "APPLY_FOR_ALL": "Apply for all", "TOOLKIT_DETAIL": "Toolkit Details", "UPDATE_TOOLKIT": "Update Toolkit", "REMOVE": "Remove", "TOOL_MANAGEMENT": "Tool Management", "CREATE_TOOL": "Create Tool", "UPDATE_TOOL": "Update Tool", "COMMUNITY": "Community", "USER_POST": "User Post", "USER_MANAGEMENT": "User Management", "CONFIGURATION": "Configuration", "PERSONAL_PAGE_MANAGEMENT": "Personal Page Management", "UPDATE_PERSONAL_PAGE_MANAGEMENT": "Update Personal Page Management", "RESET": "Reset", "ADD_REASON": "Add Reason", "LIST_REASONS": "List Reasons", "EDIT_REASON": "Edit Reason", "FAIL_UPDATED": "Failed update", "NEEDS_UPDATE": "Needs update", "REJECT_DOCUMENTS": "Reject documents", "TASKER_PROFILE": "Tasker Profile", "SUPPLIER_PROFILE": "Supplier Profile", "STAFF_PROFILE": "Staff Profile", "SUPPLIER_PROFILE_DETAIL": "Supplier Profile Details", "UPDATED": "Updated", "LIST_OFFICES": "List Offices", "OFFICE_DETAIL": "Office Details", "CREATE_OFFICE": "Create Office", "UPDATE_OFFICE": "Update Office", "TASKER_DETAIL": "Tasker Det<PERSON>", "LIST_OFFICE": "List Offices", "ALL_TASKER": "All Tasker", "DISABLED": "Disabled", "IN_PROBATION": "In Probation", "LOCKED": "Locked", "UNVERIFIED": "Unverified", "UNLOCKED": "Unlocked", "PASSED_INTERVIEW": "Passed Interview", "SCHEDULED": "Scheduled", "SELECT_DATE_RANGE_ON_BTASKEE_TABLE": "Select a date range", "ROW_PER_PAGES_ON_BTASKEE_TABLE": "Rows per page", "VIEW_TOGGLE_COLLUMN_BUTTON_ON_BTASKEE_BTASKEE_TABLE": "View", "LIST_TOGGLE_COLUMNS_ON_BTASKEE_TABLE": "Toggle columns", "PAGINATION_DISPLAYING_ON_BTASKEE_TABLE": "Page {{from}} of {{to}}", "SELECTED_ROW_DISPLAYING_ON_BTASKEE_TABLE": "{{numOfSelected}} of {{total}} row(s) selected", "ONBOARDING": "Onboarding", "TRAINING": "Training", "PERMISSION_NOT_FOUND": "Permission not found", "LIST_PERMISSIONS": "Permission group details", "LIST_HOLIDAYS": "List Holidays", "TEST_STATUS": "Test Status", "PERMISSION": "Permission", "USER_NOT_FOUND": "User not found", "YOU_CANNOT_ADD_YOURSELF_TO_THE_GROUP": "You cannot add yourself to the group", "USER_ALREADY_EXISTS": "User already exists", "INTERNAL_SERVER_ERROR": "Internal server error", "PAGE_NOT_EXIST": "The page you are looking for does not exist.", "SERVER_ERROR": "Server Error", "TRY_AGAIN_LATER": "An error occurred. Please try again later.", "IMAGE_SHOULD_BE_MORE_5KB_AND_LESS_THAN_64KB": "Image should be more than 5kb and less than 64kb", "RATIO": "Aspect ratio ({{ratio}}:1)", "ERROR_INVALID_ASPECT_RATIO": "Please choose an image with the required ratio", "MARKETING": "Marketing", "SPECIAL_CAMPAIGN": "Special Campaign", "CREATE_SPECIAL_CAMPAIGN": "Create Special Campaign", "SPECIAL_CAMPAIGN_DETAIL": "Special Campaign Details", "UPDATE_SPECIAL_CAMPAIGN": "Update Special Campaign", "SPECIAL_CAMPAIGN_TRANSACTION": "Special Campaign Transaction", "ACCOUNT_INACTIVE": "Account is not activated", "TASKER_LOCATION_DETECTED": "Tasker Location Detected", "IDENTITY_INFORMATION_FOR_TASKER": "Identity Information For Tasker"}