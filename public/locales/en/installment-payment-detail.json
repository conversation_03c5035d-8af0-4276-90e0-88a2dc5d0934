{"INSTALLMENT_PAYMENT_DETAIL": "Installment Detail", "GENERAL": "General", "TASKER_NAME": "Tasker Name", "TASKER_PHONE": "Tasker ID/Phone Number", "SERVICE": "Service", "REGION": "Region", "CREATED_AT": "Created At", "REASON": "Reason", "CREATED_DATE": "Created At", "DUE_DATE": "Due Date", "INSTALLMENT_AMOUNT": "Installment Amount", "REMAINING_AMOUNT": "Remaining Amount", "PAYMENT_DETAIL": "Payment Detail", "TASK_ID": "Task ID/Transaction ID", "AMOUNT_OF_TASK": "Amount of Task", "AMOUNT": "Amount <PERSON>", "TYPE": "Type", "PAYMENT_DATE": "Payment Date", "STATUS": "Status", "DEDUCT_INSTALLMENT_PAYMENT": "Deduct installment payment", "CHARGE_MAIN_ACCOUNT_SINCE_DUE_DATE": "Charge Main Account Since Due Date", "CHARGE_BNPL": "Charge BNPL", "PAID_AMOUNT": "<PERSON><PERSON>"}